{"editor.formatOnSave": true, "search.followSymlinks": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.options": {"overrideConfigFile": "eslint.config.mjs"}, "eslint.experimental.useFlatConfig": true, "todohighlight.isEnable": true, "todohighlight.isCaseSensitive": true, "todohighlight.keywords": ["NOTE"], "makefile.configureOnOpen": false, "i18n-ally.localesPaths": ["apps/web/messages", "apps/web/src/i18n"], "typescript.tsdk": "node_modules/typescript/lib", "nxConsole.generateAiAgentRules": true}