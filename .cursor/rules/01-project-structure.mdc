---
description: 
globs: 
alwaysApply: false
---
# Byreal Frontend Monorepo 项目结构

这是一个使用 pnpm workspace 管理的 monorepo 项目。

## 核心目录结构

- `apps/` - 包含所有应用程序
- `libs/` - 包含所有共享库
- `node_modules/` - 依赖包
- `.nx/` - Nx 构建工具配置
- `.verdaccio/` - 私有 npm 仓库配置

## 主要配置文件

- [package.json](mdc:package.json) - 项目主配置文件
- [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml) - pnpm workspace 配置
- [tsconfig.base.json](mdc:tsconfig.base.json) - TypeScript 基础配置
- [nx.json](mdc:nx.json) - Nx 工具配置
- [eslint.config.mjs](mdc:eslint.config.mjs) - ESLint 配置

## 开发工具配置

- [.prettierrc](mdc:.prettierrc) - Prettier 代码格式化配置
- [.editorconfig](mdc:.editorconfig) - 编辑器通用配置
- [.gitignore](mdc:.gitignore) - Git 忽略文件配置
