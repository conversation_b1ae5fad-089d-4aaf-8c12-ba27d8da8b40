---
description: 
globs: 
alwaysApply: false
---
# 开发指南

## 开发环境设置

1. 安装 pnpm
2. 克隆仓库后运行 `pnpm install`
3. 使用 `pnpm run dev` 启动开发环境

## 项目规范

### 代码风格
- 使用 [.prettierrc](mdc:.prettierrc) 进行代码格式化
- 遵循 [eslint.config.mjs](mdc:eslint.config.mjs) 中定义的代码规范
- 编辑器配置参考 [.editorconfig](mdc:.editorconfig)

### TypeScript
- 所有代码必须使用 TypeScript 编写
- 基础配置在 [tsconfig.base.json](mdc:tsconfig.base.json)
- 每个应用/库可以扩展基础配置

### 依赖管理
- 使用 pnpm workspace 管理依赖
- 新增依赖时优先考虑是否可以共享
- 版本控制遵循 semver 规范

### 构建工具
- 使用 Nx 进行构建和任务编排
- 配置文件：[nx.json](mdc:nx.json)
- 支持增量构建和缓存

### 测试
- 使用 Vitest 进行单元测试
- 测试文件使用 `.spec.ts` 或 `.test.ts` 后缀
- 保持测试覆盖率在合理水平

## apps/web 开发说明

### 技术栈
- Next.js - React 框架，用于服务端渲染和路由管理
- Chakra UI - UI 组件库，提供现代化的设计系统
- SWR - 数据请求和缓存管理
- Zustand - 状态管理库
- React - UI 开发框架

### 样式规范
- 严禁使用 Tailwind CSS
- 使用 Chakra UI 的主题系统进行样式定制
- 样式方案优先级：
  1. Chakra UI 内置组件和样式
  2. Chakra UI 的 style props
  3. emotion/styled-components（特殊场景）

### 组件设计规范
1. 组件分类：
   - 页面组件（Page Components）：位于 `pages` 目录
   - 业务组件（Business Components）：位于 `components/business` 目录
   - 通用组件（Common Components）：位于 `components/common` 目录
   - 布局组件（Layout Components）：位于 `components/layouts` 目录

2. 组件命名：
   - 使用 PascalCase 命名
   - 页面组件以 Page 结尾（如 `TradingPage`）
   - 组件文件夹包含 index.tsx 和 types.ts

3. 组件结构：
   ```typescript
   // 推荐的组件文件结构
   import { FC } from 'react'
   import { Box } from '@chakra-ui/react'
   import type { ComponentProps } from './types'

   export const MyComponent: FC<ComponentProps> = (props) => {
     return <Box {...props}>...</Box>
   }
   ```

### 页面设计规范
1. 目录结构：
   - `pages/` - 页面路由
   - `pages/api/` - API 路由
   - `views/` - 页面视图组件

2. 数据获取：
   - 使用 SWR 进行数据请求和缓存
   - 页面级数据在 getServerSideProps/getStaticProps 中预获取
   - 客户端数据更新使用 SWR 的 mutate

3. 状态管理：
   - 全局状态使用 Zustand store
   - 组件内部状态使用 React useState
   - 复杂表单状态使用 React Hook Form

4. 性能优化：
   - 合理使用 React.memo 和 useMemo
   - 图片使用 Next.js Image 组件
   - 组件懒加载使用 dynamic import

