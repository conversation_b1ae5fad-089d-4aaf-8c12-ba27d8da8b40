# Git Hooks

本项目使用 [Hu<PERSON>](https://typicode.github.io/husky/) 和 [lint-staged](https://github.com/okonet/lint-staged) 来管理 Git 钩子。

## 已配置的钩子

### pre-commit

在提交代码前，会自动运行以下检查：

- 使用 Prettier 对暂存的文件进行格式化
- 使用 ESLint 对暂存的 JavaScript/TypeScript 文件进行 lint 检查并自动修复问题

### commit-msg

使用 [commitlint](https://commitlint.js.org/) 检查提交信息是否符合 [Conventional Commits](https://www.conventionalcommits.org/zh-hans/v1.0.0/) 规范。

格式要求：`type(scope): description`

类型（type）必须是以下之一：

- feat: 新功能
- fix: 修复 bug
- docs: 文档更新
- style: 代码风格调整（不影响代码功能）
- refactor: 代码重构（不是新功能也不是修复 bug）
- perf: 性能优化
- test: 添加或修改测试
- build: 构建系统或外部依赖项的更改
- ci: CI 配置文件和脚本的更改
- chore: 其他不修改 src 或测试文件的更改
- revert: 撤销之前的提交

示例：

- `feat(auth): 添加用户登录功能`
- `fix(ui): 修复按钮样式问题`

详细规则配置请查看项目根目录下的 `commitlint.config.js` 文件。

## 跳过钩子

在特殊情况下，可以使用 `--no-verify` 参数跳过 Git 钩子：

```bash
git commit --no-verify -m "commit message"
git push --no-verify
```

但请谨慎使用，尽量遵循项目规范。
