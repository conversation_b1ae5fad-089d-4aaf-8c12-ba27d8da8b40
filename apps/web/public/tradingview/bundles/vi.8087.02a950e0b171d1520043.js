(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (t) => {
      t.exports = ['Thêm'];
    },
    53585: (t) => {
      t.exports = ['Thêm màu tùy chỉnh'];
    },
    81865: (t) => {
      t.exports = ['Độ mờ'];
    },
    73755: (t) => {
      t.exports = ['Mã giao dịch khác'];
    },
    16936: (t) => {
      t.exports = ['Quay lại'];
    },
    88046: (t) => {
      t.exports = ['Mã giao dịch biểu đồ chính'];
    },
    9898: (t) => {
      t.exports = ['Phải'];
    },
    20036: (t) => {
      t.exports = ['Hủy bỏ'];
    },
    23398: (t) => {
      t.exports = ['Thay đổi mã giao dịch'];
    },
    94551: (t) => {
      t.exports = ['Biểu đồ'];
    },
    64498: (t) => {
      t.exports = ['Tất cả các nguồn'];
    },
    73226: (t) => {
      t.exports = ['Áp dụng'];
    },
    79852: (t) => {
      t.exports = ['Trái phiếu'];
    },
    56095: (t) => {
      t.exports = ['Giảm'];
    },
    29601: (t) => {
      t.exports = ['Mô tả'];
    },
    46812: (t) => {
      t.exports = ['Tăng'];
    },
    89298: (t) => {
      t.exports = ['Bù đắp'];
    },
    68988: (t) => {
      t.exports = 'Ok';
    },
    29673: (t) => {
      t.exports = ['Không có sàn giao dịch nào khớp với yêu cầu của bạn'];
    },
    41379: (t) => {
      t.exports = ['Không có mã giao dịch nào khớp với tiêu chí của bạn'];
    },
    35563: (t) => {
      t.exports = ['Định dạng số không hợp lệ.'];
    },
    19724: (t) => {
      t.exports = ['Nguồn'];
    },
    59877: (t) => {
      t.exports = [
        'Đặt {inputInline} thời gian và giá cho {studyShortDescription}',
      ];
    },
    18571: (t) => {
      t.exports = ['Đặt {inputTitle} thời gian cho {studyShortDescription}'];
    },
    58552: (t) => {
      t.exports = ['Đặt {inputTitle} giá cho {studyShortDescription}'];
    },
    80481: (t) => {
      t.exports = ['Đặt thời gian và giá cho "{studyShortDescription}"'];
    },
    42917: (t) => {
      t.exports = ['Đặt thời gian cho "{studyShortDescription}"'];
    },
    6083: (t) => {
      t.exports = ['Đặt giá cho "{studyShortDescription}"'];
    },
    52298: (t) => {
      t.exports = ['Tìm kiếm'];
    },
    13269: (t) => {
      t.exports = ['Chọn nguồn'];
    },
    2607: (t) => {
      t.exports = [
        'Giá trị đã chỉ định lớn hơn giá trị tối đa của công cụ là {max}.',
      ];
    },
    53669: (t) => {
      t.exports = [
        'Giá trị đã chỉ định nhỏ hơn giá trị tối thiểu của công cụ là {min}.',
      ];
    },
    89053: (t) => {
      t.exports = ['Mã'];
    },
    48490: (t) => {
      t.exports = ['Mã giao dịch & mô tả'];
    },
    99983: (t) => {
      t.exports = ['Tìm kiếm Mã giao dịch'];
    },
    54336: (t) => {
      t.exports = ['Bỏ màu'];
    },
    60142: (t) => {
      t.exports = ['Độ dày'];
    },
    87592: (t) => {
      t.exports = 'cfd';
    },
    17023: (t) => {
      t.exports = ['Thay đổi Độ mờ'];
    },
    13066: (t) => {
      t.exports = ['Thay đổi Màu sắc'];
    },
    95657: (t) => {
      t.exports = ['Thay đổi Độ dày'];
    },
    18567: (t) => {
      t.exports = ['thay đổi thuộc tính {propertyName}'];
    },
    36962: (t) => {
      t.exports = ['đóng cửa'];
    },
    8448: (t) => {
      t.exports = ['tiền điện tử'];
    },
    1328: (t) => {
      t.exports = 'dr';
    },
    88720: (t) => {
      t.exports = ['kinh tế'];
    },
    39512: (t) => {
      t.exports = 'forex';
    },
    81859: (t) => {
      t.exports = ['hợp đồng tương lai'];
    },
    39337: (t) => {
      t.exports = ['cao'];
    },
    91815: (t) => {
      t.exports = 'hl2';
    },
    40771: (t) => {
      t.exports = 'hlc3';
    },
    9523: (t) => {
      t.exports = 'hlcc4';
    },
    12754: (t) => {
      t.exports = ['chỉ số'];
    },
    38071: (t) => {
      t.exports = ['các chỉ báo'];
    },
    12504: (t) => {
      t.exports = 'ohlc4';
    },
    38466: (t) => {
      t.exports = ['mở cửa'];
    },
    3919: (t) => {
      t.exports = ['thấp'];
    },
    36931: (t) => {
      t.exports = ['cổ phiếu'];
    },
  },
]);
