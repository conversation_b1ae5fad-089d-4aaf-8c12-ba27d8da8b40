(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (t) => {
      t.exports = ['增加'];
    },
    53585: (t) => {
      t.exports = ['添加自定义颜色'];
    },
    81865: (t) => {
      t.exports = ['不透明度'];
    },
    73755: (t) => {
      t.exports = ['其他代码'];
    },
    16936: (t) => {
      t.exports = ['返回'];
    },
    88046: (t) => {
      t.exports = ['主图表代码'];
    },
    9898: (t) => {
      t.exports = ['认股权'];
    },
    20036: (t) => {
      t.exports = ['取消'];
    },
    23398: (t) => {
      t.exports = ['变更品种'];
    },
    94551: (t) => {
      t.exports = ['图表'];
    },
    64498: (t) => {
      t.exports = ['全部来源'];
    },
    73226: (t) => {
      t.exports = ['应用'];
    },
    79852: (t) => {
      t.exports = ['债券'];
    },
    56095: (t) => {
      t.exports = ['减少'];
    },
    29601: (t) => {
      t.exports = ['描述'];
    },
    46812: (t) => {
      t.exports = ['增加'];
    },
    89298: (t) => {
      t.exports = ['偏移'];
    },
    68988: (t) => {
      t.exports = ['确认'];
    },
    29673: (t) => {
      t.exports = ['没有交易所符合您的条件'];
    },
    41379: (t) => {
      t.exports = ['没有代码符合您的条件'];
    },
    35563: (t) => {
      t.exports = ['号码格式无效。'];
    },
    19724: (t) => {
      t.exports = ['来源'];
    },
    59877: (t) => {
      t.exports = ['为{studyShortDescription}设置{inputInline}时间和价格'];
    },
    18571: (t) => {
      t.exports = ['为{studyShortDescription}设置{inputTitle}时间'];
    },
    58552: (t) => {
      t.exports = ['为{studyShortDescription}设置{inputTitle}价格'];
    },
    80481: (t) => {
      t.exports = ['设置“{studyShortDescription}”的时间和价格'];
    },
    42917: (t) => {
      t.exports = ['设置“{studyShortDescription}”的时间'];
    },
    6083: (t) => {
      t.exports = ['设置“{studyShortDescription}”的价格'];
    },
    52298: (t) => {
      t.exports = ['搜索'];
    },
    13269: (t) => {
      t.exports = ['选择来源'];
    },
    2607: (t) => {
      t.exports = ['指定值大于商品最大值{max}。'];
    },
    53669: (t) => {
      t.exports = ['指定值小于商品最小值{min}。'];
    },
    89053: (t) => {
      t.exports = ['商品代码'];
    },
    48490: (t) => {
      t.exports = ['商品和描述'];
    },
    99983: (t) => {
      t.exports = ['商品代码搜索'];
    },
    54336: (t) => {
      t.exports = ['移除颜色'];
    },
    60142: (t) => {
      t.exports = ['厚度'];
    },
    87592: (t) => {
      t.exports = ['差价合约'];
    },
    17023: (t) => {
      t.exports = ['更改不透明度'];
    },
    13066: (t) => {
      t.exports = ['更改颜色'];
    },
    95657: (t) => {
      t.exports = ['更改厚度'];
    },
    18567: (t) => {
      t.exports = ['更改{propertyName}属性'];
    },
    36962: (t) => {
      t.exports = ['收盘'];
    },
    8448: (t) => {
      t.exports = ['加密'];
    },
    1328: (t) => {
      t.exports = 'dr';
    },
    88720: (t) => {
      t.exports = ['经济'];
    },
    39512: (t) => {
      t.exports = ['外汇'];
    },
    81859: (t) => {
      t.exports = ['期货'];
    },
    39337: (t) => {
      t.exports = ['最高'];
    },
    91815: (t) => {
      t.exports = ['(h+l)/2'];
    },
    40771: (t) => {
      t.exports = ['(h+l+c)/3'];
    },
    9523: (t) => {
      t.exports = 'hlcc4';
    },
    12754: (t) => {
      t.exports = ['指数'];
    },
    38071: (t) => {
      t.exports = ['指数'];
    },
    12504: (t) => {
      t.exports = ['(o+h+l+c)/4'];
    },
    38466: (t) => {
      t.exports = ['开盘'];
    },
    3919: (t) => {
      t.exports = ['最低'];
    },
    36931: (t) => {
      t.exports = ['股票'];
    },
  },
]);
