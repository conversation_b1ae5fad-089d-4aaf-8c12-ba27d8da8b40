(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (r) => {
      r.exports = ['Lägg till'];
    },
    53585: (r) => {
      r.exports = ['Lägg till en anpassad färg'];
    },
    81865: (r) => {
      r.exports = ['Opacitet'];
    },
    73755: (r) => {
      r.exports = ['Annan symbol'];
    },
    16936: (r) => {
      r.exports = ['Tillbaka'];
    },
    88046: (r) => {
      r.exports = ['Huvuddiagramsymbol'];
    },
    9898: (r) => {
      r.exports = ['Höger'];
    },
    20036: (r) => {
      r.exports = ['Avbryt'];
    },
    23398: (r) => {
      r.exports = ['Ändra tickersymbol'];
    },
    94551: (r) => {
      r.exports = ['Diagram'];
    },
    64498: (r) => {
      r.exports = ['Samtliga källor'];
    },
    73226: (r) => {
      r.exports = ['Verkställ'];
    },
    79852: (r) => {
      r.exports = ['Obligation'];
    },
    56095: (r) => {
      r.exports = ['Minska'];
    },
    29601: (r) => {
      r.exports = ['Beskrivning'];
    },
    46812: (r) => {
      r.exports = ['Öka'];
    },
    89298: (r) => {
      r.exports = ['Kompensation'];
    },
    68988: (r) => {
      r.exports = ['OK'];
    },
    29673: (r) => {
      r.exports = ['Inga börser matchar dina kriterier'];
    },
    41379: (r) => {
      r.exports = ['Inga symboler matchar dina kriterier'];
    },
    35563: (r) => {
      r.exports = ['Nummerformatet är felaktigt.'];
    },
    19724: (r) => {
      r.exports = ['Källor'];
    },
    59877: (r) => {
      r.exports = [
        'Ställ in {inputInline} tid och pris för {studyShortDescription}',
      ];
    },
    18571: (r) => {
      r.exports = ['Ställ in {inputTitle} pris för {studyShortDescription}'];
    },
    58552: (r) => {
      r.exports = ['Ställ in {inputTitle} pris för {studyShortDescription}'];
    },
    80481: (r) => {
      r.exports = ['Ställ in tid och pris för "{studyShortDescription}"'];
    },
    42917: (r) => {
      r.exports = ['Ställ in tid för "{studyShortDescription}"'];
    },
    6083: (r) => {
      r.exports = ['Ställ in pris för "{studyShortDescription}"'];
    },
    52298: (r) => {
      r.exports = ['Sök'];
    },
    13269: (r) => {
      r.exports = ['Välj källa'];
    },
    2607: (r) => {
      r.exports = [
        'Det angivna värdet är större än instrumentets minimum av {max}.',
      ];
    },
    53669: (r) => {
      r.exports = [
        'Det angivna värdet är mindre än instrumentets minimum av {min}.',
      ];
    },
    89053: (r) => {
      r.exports = 'Symbol';
    },
    48490: (r) => {
      r.exports = ['Symbol & beskrivning'];
    },
    99983: (r) => {
      r.exports = ['Symbolsök'];
    },
    54336: (r) => {
      r.exports = ['Ta bort färg'];
    },
    60142: (r) => {
      r.exports = ['Tjocklek'];
    },
    87592: (r) => {
      r.exports = 'cfd';
    },
    17023: (r) => {
      r.exports = ['Ändra opacitet'];
    },
    13066: (r) => {
      r.exports = ['Ändra färg'];
    },
    95657: (r) => {
      r.exports = ['Ändra tjocklek'];
    },
    18567: (r) => {
      r.exports = ['ändra egenskapen {propertyName}'];
    },
    36962: (r) => {
      r.exports = ['stängning'];
    },
    8448: (r) => {
      r.exports = ['krypto'];
    },
    1328: (r) => {
      r.exports = 'dr';
    },
    88720: (r) => {
      r.exports = ['ekonomi'];
    },
    39512: (r) => {
      r.exports = ['valutor'];
    },
    81859: (r) => {
      r.exports = ['terminer'];
    },
    39337: (r) => {
      r.exports = ['högsta'];
    },
    91815: (r) => {
      r.exports = 'hl2';
    },
    40771: (r) => {
      r.exports = 'hlc3';
    },
    9523: (r) => {
      r.exports = 'hlcc4';
    },
    12754: (r) => {
      r.exports = 'index';
    },
    38071: (r) => {
      r.exports = ['index'];
    },
    12504: (r) => {
      r.exports = 'ohlc4';
    },
    38466: (r) => {
      r.exports = ['öppning'];
    },
    3919: (r) => {
      r.exports = ['lägsta'];
    },
    36931: (r) => {
      r.exports = ['aktier'];
    },
  },
]);
