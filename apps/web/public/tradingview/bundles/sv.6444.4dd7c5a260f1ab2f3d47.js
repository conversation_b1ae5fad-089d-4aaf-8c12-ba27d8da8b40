(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6444],
  {
    50831: (r) => {
      r.exports = ['staplar'];
    },
    19648: (r) => {
      r.exports = ['12-timmars'];
    },
    55838: (r) => {
      r.exports = ['24-timmars'];
    },
    88364: (r) => {
      r.exports = ['Basstilar för diagram'];
    },
    46720: (r) => {
      r.exports = ['Sikte'];
    },
    50985: (r) => {
      r.exports = ['Valuta'];
    },
    17319: (r) => {
      r.exports = ['Valuta och enhet'];
    },
    19481: (r) => {
      r.exports = ['Utseende'];
    },
    68791: (r) => {
      r.exports = ['Argument'];
    },
    95036: (r) => {
      r.exports = ['Genomsnittligt stängningspris'];
    },
    27331: (r) => {
      r.exports = ['Bakgrund'];
    },
    22519: (r) => {
      r.exports = ['Stapelvärdesföränding'];
    },
    87845: (r) => {
      r.exports = ['Knappar'];
    },
    25209: (r) => {
      r.exports = ['Datumformat'];
    },
    55090: (r) => {
      r.exports = ['Veckodagar på etiketter'];
    },
    29601: (r) => {
      r.exports = ['Beskrivning'];
    },
    26897: (r) => {
      r.exports = ['Evenemang'];
    },
    95338: (r) => {
      r.exports = ['Horisontellt rutnät'];
    },
    60971: (r) => {
      r.exports = ['Högt och lågt pris'];
    },
    61142: (r) => {
      r.exports = ['Indikatorer'];
    },
    34905: (r) => {
      r.exports = ['Indikatorers värde'];
    },
    29687: (r) => {
      r.exports = ['Indikatorer och finansiella värden'];
    },
    25084: (r) => {
      r.exports = ['Indikatorer och finansiella namn'];
    },
    9654: (r) => {
      r.exports = ['Indikatornamn'];
    },
    99487: (r) => {
      r.exports = ['OHLC värden'];
    },
    75991: (r) => {
      r.exports = ['Öppen marknadsstatus'];
    },
    96073: (r) => {
      r.exports = 'Long Description';
    },
    70500: (r) => {
      r.exports = ['Pengar'];
    },
    66653: (r) => {
      r.exports = ['Marginaler'];
    },
    42502: (r) => {
      r.exports = ['Ingen överlappning'];
    },
    74343: (r) => {
      r.exports = 'Navigation';
    },
    43115: (r) => {
      r.exports = ['Skalor'];
    },
    53224: (r) => {
      r.exports = ['Placering av skalor'];
    },
    79194: (r) => {
      r.exports = ['Statusrad'];
    },
    89053: (r) => {
      r.exports = 'Symbol';
    },
    35383: (r) => {
      r.exports = ['Symbolnamn'];
    },
    27767: (r) => {
      r.exports = ['Symbol senaste pris'];
    },
    40847: (r) => {
      r.exports = ['Symbolen för föregående dags stängningsvärde'];
    },
    50446: (r) => {
      r.exports = 'Pane';
    },
    73908: (r) => {
      r.exports = ['Panelseparatörer'];
    },
    36014: (r) => {
      r.exports = ['Pocentsats'];
    },
    78621: (r) => {
      r.exports = 'Pips';
    },
    74823: (r) => {
      r.exports = ['Före/Eftermarknadspris'];
    },
    64859: (r) => {
      r.exports = ['prisskala'];
    },
    76523: (r) => {
      r.exports = ['Pris och procentvärde'];
    },
    40187: (r) => {
      r.exports = 'Right Margin';
    },
    77705: (r) => {
      r.exports = ['Vattenstämpel'];
    },
    67369: (r) => {
      r.exports = ['Titel'];
    },
    31326: (r) => {
      r.exports = ['Titlar'];
    },
    23097: (r) => {
      r.exports = ['Tickersymbol'];
    },
    82168: (r) => {
      r.exports = ['Ticker och beskrivning'];
    },
    43637: (r) => {
      r.exports = ['Tidsskala'];
    },
    97316: (r) => {
      r.exports = ['Tidsformat timmar'];
    },
    90801: (r) => {
      r.exports = 'Trading';
    },
    77534: (r) => {
      r.exports = ['Enhet'];
    },
    1111: (r) => {
      r.exports = ['Volym'];
    },
    80170: (r) => {
      r.exports = ['Värde enligt skala'];
    },
    91322: (r) => {
      r.exports = ['Värden'];
    },
    70353: (r) => {
      r.exports = ['Vert Grid Linjer'];
    },
    57889: (r) => {
      r.exports = ['ändra synlighet för OHLC-värden'];
    },
    35646: (r) => {
      r.exports = ['byt navigeringsknappar för vattenmärkessymbol'];
    },
    18644: (r) => {
      r.exports = ['ändra synlighet för öppen marknadsstatus'];
    },
    45110: (r) => {
      r.exports = ['ändra synlighet för stapeländringar'];
    },
    10349: (r) => {
      r.exports = ['ändra bottenmarginal'];
    },
    88161: (r) => {
      r.exports = ['ändra synlighet för valuta och enhetsetiketter'];
    },
    79570: (r) => {
      r.exports = ['ändra synlighet för valutaetiketter'];
    },
    99011: (r) => {
      r.exports = ['ändra diagrammets bakgrundsfärg'];
    },
    72458: (r) => {
      r.exports = ['ändra digrammets bakgrundstyp'];
    },
    37034: (r) => {
      r.exports = ['ändra bredd för crosshair'];
    },
    29951: (r) => {
      r.exports = ['ändra färg för crosshair'];
    },
    92027: (r) => {
      r.exports = ['ändra stil för crosshair'];
    },
    50457: (r) => {
      r.exports = ['ändra datumformat'];
    },
    7104: (r) => {
      r.exports = ['byt dag på etiketter'];
    },
    88096: (r) => {
      r.exports = ['ändra horzrutnätets färg'];
    },
    2523: (r) => {
      r.exports = ['ändra linjestil på horzrutnätets linjer'];
    },
    31325: (r) => {
      r.exports = ['ändra synlighet för indikatortitel'];
    },
    99774: (r) => {
      r.exports = ['ändra synlighet för indikatorvärden'];
    },
    96162: (r) => {
      r.exports = ['ändra synlighet för indikatorargument'];
    },
    59820: (r) => {
      r.exports = [
        'ändra synlighet för indikatorer och finansiella namnetiketter',
      ];
    },
    90512: (r) => {
      r.exports = [
        'ändra synlighet för indikatorer och finansiella värdeetiketter',
      ];
    },
    97956: (r) => {
      r.exports = ['ändra transparens för teckenförklaring'];
    },
    61061: (r) => {
      r.exports = ['ändra synlighet för teckenförklaring'];
    },
    37730: (r) => {
      r.exports = ['ändra synligheten för rutknapparna'];
    },
    89032: (r) => {
      r.exports = ['ändra färgen på panelerna'];
    },
    35636: (r) => {
      r.exports = ['ändra högermarginalen'];
    },
    66601: (r) => {
      r.exports = 'change right margin percentage';
    },
    25616: (r) => {
      r.exports = ['ändra färg för vattenmärkessymbol'];
    },
    87159: (r) => {
      r.exports = ['ändra synligheten för vattenmärkessymbol'];
    },
    26717: (r) => {
      r.exports = ['ändra synlighet för symbolbeskrivning'];
    },
    6091: (r) => {
      r.exports = 'change symbol field visibility';
    },
    28741: (r) => {
      r.exports = ['ändra symbol för senaste värdeläge'];
    },
    95071: (r) => {
      r.exports = ['ändra format för teckenförklarimg'];
    },
    35065: (r) => {
      r.exports = ['ändra textfärg för skalorna'];
    },
    84382: (r) => {
      r.exports = ['ändra teckenstorlek för skalorna'];
    },
    12468: (r) => {
      r.exports = ['ändra färg för skalorna'];
    },
    71589: (r) => {
      r.exports = ['ändra synligheten för sessionsgränser'];
    },
    15035: (r) => {
      r.exports = ['ändra bredd för sessionsgränser'];
    },
    1579: (r) => {
      r.exports = ['ändra färg för sessionsgränser'];
    },
    21460: (r) => {
      r.exports = ['ändra stil för sessionsgränser'];
    },
    76991: (r) => {
      r.exports = ['ändra tidsformat timmar'];
    },
    98905: (r) => {
      r.exports = ['ändra toppmarginal'];
    },
    7011: (r) => {
      r.exports = ['ändra synlighet för enhetsetiketter'];
    },
    22722: (r) => {
      r.exports = ['ändra färg på rutnätets linjer'];
    },
    22867: (r) => {
      r.exports = ['ändra linjestil på rutnätets linjer'];
    },
    9455: (r) => {
      r.exports = ['ändra synlighet för volymvärden'];
    },
  },
]);
