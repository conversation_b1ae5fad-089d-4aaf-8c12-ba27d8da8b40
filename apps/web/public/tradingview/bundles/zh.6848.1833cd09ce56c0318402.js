(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (e) => {
      e.exports = ['增加'];
    },
    53585: (e) => {
      e.exports = ['添加自定义颜色'];
    },
    81865: (e) => {
      e.exports = ['不透明度'];
    },
    60558: (e) => {
      e.exports = ['动物&自然'];
    },
    14232: (e) => {
      e.exports = ['活动'];
    },
    35305: (e) => {
      e.exports = ['食物&饮料'];
    },
    49546: (e) => {
      e.exports = ['旗帜'];
    },
    72302: (e) => {
      e.exports = ['物品'];
    },
    96330: (e) => {
      e.exports = ['笑脸&人像'];
    },
    6878: (e) => {
      e.exports = ['符号'];
    },
    15426: (e) => {
      e.exports = ['最近使用'];
    },
    15395: (e) => {
      e.exports = ['旅游&地点'];
    },
    73755: (e) => {
      e.exports = ['其他代码'];
    },
    16936: (e) => {
      e.exports = ['返回'];
    },
    88046: (e) => {
      e.exports = ['主图表代码'];
    },
    9898: (e) => {
      e.exports = ['认股权'];
    },
    20036: (e) => {
      e.exports = ['取消'];
    },
    72171: (e) => {
      e.exports = ['中心'];
    },
    23398: (e) => {
      e.exports = ['变更品种'];
    },
    94551: (e) => {
      e.exports = ['图表'];
    },
    64498: (e) => {
      e.exports = ['全部来源'];
    },
    91757: (e) => {
      e.exports = ['底部'];
    },
    79852: (e) => {
      e.exports = ['债券'];
    },
    16079: (e) => {
      e.exports = ['渐变'];
    },
    42973: (e) => {
      e.exports = ['点虚线'];
    },
    59317: (e) => {
      e.exports = ['短虚线'];
    },
    56095: (e) => {
      e.exports = ['减少'];
    },
    29601: (e) => {
      e.exports = ['描述'];
    },
    77405: (e) => {
      e.exports = ['横式'];
    },
    46812: (e) => {
      e.exports = ['增加'];
    },
    89298: (e) => {
      e.exports = ['偏移'];
    },
    68988: (e) => {
      e.exports = ['确认'];
    },
    19286: (e) => {
      e.exports = ['左'];
    },
    76476: (e) => {
      e.exports = ['中间'];
    },
    29673: (e) => {
      e.exports = ['没有交易所符合您的条件'];
    },
    41379: (e) => {
      e.exports = ['没有代码符合您的条件'];
    },
    55362: (e) => {
      e.exports = ['正常'];
    },
    35563: (e) => {
      e.exports = ['号码格式无效。'];
    },
    19724: (e) => {
      e.exports = ['来源'];
    },
    35637: (e) => {
      e.exports = 'Solid';
    },
    52298: (e) => {
      e.exports = ['搜索'];
    },
    13269: (e) => {
      e.exports = ['选择来源'];
    },
    2607: (e) => {
      e.exports = ['指定值大于商品最大值{max}。'];
    },
    53669: (e) => {
      e.exports = ['指定值小于商品最小值{min}。'];
    },
    89053: (e) => {
      e.exports = ['商品代码'];
    },
    48490: (e) => {
      e.exports = ['商品和描述'];
    },
    99983: (e) => {
      e.exports = ['商品代码搜索'];
    },
    54336: (e) => {
      e.exports = ['移除颜色'];
    },
    21141: (e) => {
      e.exports = ['右'];
    },
    65994: (e) => {
      e.exports = ['顶部'];
    },
    92960: (e) => {
      e.exports = ['文字对齐'];
    },
    90581: (e) => {
      e.exports = ['文字方向'];
    },
    60142: (e) => {
      e.exports = ['厚度'];
    },
    78019: (e) => {
      e.exports = [
        '使用特殊的数学符号替换选定的图形：+,-,/,* 表示价格，+,- 表示K线指数。',
      ];
    },
    44085: (e) => {
      e.exports = ['竖式'];
    },
    87592: (e) => {
      e.exports = ['差价合约'];
    },
    17023: (e) => {
      e.exports = ['更改不透明度'];
    },
    13066: (e) => {
      e.exports = ['更改颜色'];
    },
    95657: (e) => {
      e.exports = ['更改厚度'];
    },
    18567: (e) => {
      e.exports = ['更改{propertyName}属性'];
    },
    36962: (e) => {
      e.exports = ['收盘'];
    },
    8448: (e) => {
      e.exports = ['加密'];
    },
    1328: (e) => {
      e.exports = 'dr';
    },
    76080: (e) => {
      e.exports = ['例如 +1'];
    },
    95166: (e) => {
      e.exports = ['例如 /2'];
    },
    88720: (e) => {
      e.exports = ['经济'];
    },
    39512: (e) => {
      e.exports = ['外汇'];
    },
    81859: (e) => {
      e.exports = ['期货'];
    },
    39337: (e) => {
      e.exports = ['最高'];
    },
    91815: (e) => {
      e.exports = ['(h+l)/2'];
    },
    40771: (e) => {
      e.exports = ['(h+l+c)/3'];
    },
    9523: (e) => {
      e.exports = 'hlcc4';
    },
    12754: (e) => {
      e.exports = ['指数'];
    },
    38071: (e) => {
      e.exports = ['指数'];
    },
    12504: (e) => {
      e.exports = ['(o+h+l+c)/4'];
    },
    38466: (e) => {
      e.exports = ['开盘'];
    },
    3919: (e) => {
      e.exports = ['最低'];
    },
    36931: (e) => {
      e.exports = ['股票'];
    },
  },
]);
