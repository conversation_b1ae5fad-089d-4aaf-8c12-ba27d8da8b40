!(function (e) {
  function a(a) {
    for (
      var c, t, f = a[0], b = a[1], o = a[2], r = 0, n = [];
      r < f.length;
      r++
    )
      (t = f[r]), s[t] && n.push(s[t][0]), (s[t] = 0);
    for (c in b) Object.prototype.hasOwnProperty.call(b, c) && (e[c] = b[c]);
    for (i && i(a); n.length; ) n.shift()();
    return g.push.apply(g, o || []), d();
  }
  function d() {
    var e, a, d, t, f, b;
    for (a = 0; a < g.length; a++) {
      for (d = g[a], t = !0, f = 1; f < d.length; f++)
        (b = d[f]), 0 !== s[b] && (t = !1);
      t && (g.splice(a--, 1), (e = c((c.s = d[0]))));
    }
    return e;
  }
  function c(a) {
    if (n[a]) return n[a].exports;
    var d = (n[a] = { i: a, l: !1, exports: {} });
    return e[a].call(d.exports, d, d.exports, c), (d.l = !0), d.exports;
  }
  var t,
    f,
    b,
    o,
    r,
    i,
    n = {},
    l = { runtime: 0 },
    s = { runtime: 0 },
    g = [];
  for (
    c.e = function (e) {
      var a,
        d,
        t,
        f,
        b,
        o = [],
        r = {
          2: 1,
          3: 1,
          5: 1,
          6: 1,
          7: 1,
          11: 1,
          14: 1,
          15: 1,
          17: 1,
          19: 1,
          20: 1,
          21: 1,
          22: 1,
          26: 1,
          28: 1,
          29: 1,
          31: 1,
          32: 1,
          33: 1,
          34: 1,
          35: 1,
          36: 1,
          37: 1,
          38: 1,
          39: 1,
          43: 1,
          48: 1,
          49: 1,
          50: 1,
          51: 1,
          52: 1,
          53: 1,
          54: 1,
          55: 1,
          56: 1,
          57: 1,
          58: 1,
          59: 1,
          60: 1,
          61: 1,
          62: 1,
          63: 1,
          64: 1,
          65: 1,
          66: 1,
          67: 1,
          68: 1,
          69: 1,
          70: 1,
          71: 1,
          72: 1,
          73: 1,
          74: 1,
          75: 1,
          76: 1,
          77: 1,
          'dialogs-core': 1,
        };
      return (
        l[e]
          ? o.push(l[e])
          : 0 !== l[e] &&
            r[e] &&
            o.push(
              (l[e] = new Promise(function (a, d) {
                var t,
                  f,
                  b,
                  o,
                  r,
                  i =
                    e +
                    '.' +
                    {
                      0: '31d6cfe0d16ae931b73c',
                      1: '31d6cfe0d16ae931b73c',
                      2: '77b8ba8b09f81844b20a',
                      3: '1ddc0c30ba6da910062a',
                      4: '31d6cfe0d16ae931b73c',
                      5: '57324ba6c625fe9f3888',
                      6: '393285d9e192509d4631',
                      7: '6734ba5b78052f3f6ece',
                      8: '31d6cfe0d16ae931b73c',
                      9: '31d6cfe0d16ae931b73c',
                      10: '31d6cfe0d16ae931b73c',
                      11: '32a5cad0aabb333286d3',
                      12: '31d6cfe0d16ae931b73c',
                      13: '31d6cfe0d16ae931b73c',
                      14: '209e3b38b8b016091a8e',
                      15: 'dd24e3d870a387445f72',
                      16: '31d6cfe0d16ae931b73c',
                      17: 'c5c4224993aa2f9295b6',
                      18: '31d6cfe0d16ae931b73c',
                      19: '70ef3f970b49162cb710',
                      20: 'e5e4e7ffa1b51093f856',
                      21: '35dee0cfeb8a4d70732c',
                      22: '91e812e56ce1687ad279',
                      23: '31d6cfe0d16ae931b73c',
                      24: '31d6cfe0d16ae931b73c',
                      25: '31d6cfe0d16ae931b73c',
                      26: '653f6d3c0b5b0935e3f6',
                      27: '31d6cfe0d16ae931b73c',
                      28: '7eaa7965dc29ab9cbfef',
                      29: '1b9c252ca3fbb092acbf',
                      30: '31d6cfe0d16ae931b73c',
                      31: '0986501df04d6f95ebb7',
                      32: 'f75162343321d7d9178c',
                      33: '87833b9ae0c659ec1293',
                      34: '3cb36efb2b5105f68452',
                      35: 'b88362e122620162b4ce',
                      36: 'a425e87dab4328b1bc8b',
                      37: '9532bf3bf84dfbdecded',
                      38: 'f97701c8ae8d902768a3',
                      39: 'fc24c2c028c08753d55b',
                      40: '31d6cfe0d16ae931b73c',
                      41: '31d6cfe0d16ae931b73c',
                      42: '31d6cfe0d16ae931b73c',
                      43: '7212ef507d09509703b0',
                      44: '31d6cfe0d16ae931b73c',
                      45: '31d6cfe0d16ae931b73c',
                      46: '31d6cfe0d16ae931b73c',
                      47: '31d6cfe0d16ae931b73c',
                      48: 'c5d2c36647459f288b52',
                      49: '3d9b01f3369bda85da52',
                      50: '31f0ded17f7e82d1407c',
                      51: '47b9d16b3fa10b495a11',
                      52: '2b4d346d4577b85c7746',
                      53: '99400d6fec42273a305c',
                      54: 'cf821da701b9fd226d39',
                      55: '37261c7b30180bb9483a',
                      56: '8e09bb9c4585b8288a85',
                      57: 'fdedddcf3b52ceeda20c',
                      58: '0ec628ae6bd0f6a58160',
                      59: '69bdd2c2552c9fe6fde6',
                      60: '04fe0d91beb34a097404',
                      61: '7c10fbaca3777ffedc28',
                      62: '6cbd8c47e9891d4773dd',
                      63: '373135af1f0a1e6f7b84',
                      64: '141786e70c665e6f00bd',
                      65: 'a38c3dcda01a32dde55a',
                      66: 'edb0a07715c0c9faed8d',
                      67: '52f53507478d24c79010',
                      68: '8ef206860e4a873fc95e',
                      69: '3f6d736abe33683640bc',
                      70: 'cde7c3b60eae98334eaa',
                      71: 'ddbee16e7fe6cd9bff91',
                      72: 'aa0962259cf0806255b2',
                      73: '3f98f1eb055238a52282',
                      74: '6f64040487ad074141df',
                      75: 'e9a6bec06ee11d2c2d4a',
                      76: '9281221e65242ee1b2dd',
                      77: '325b9405f45d6fda33a6',
                      'general-property-page': '31d6cfe0d16ae931b73c',
                      'study-property-pages-with-definitions':
                        '31d6cfe0d16ae931b73c',
                      'lt-property-pages-with-definitions':
                        '31d6cfe0d16ae931b73c',
                      editobjectdialog: '31d6cfe0d16ae931b73c',
                      'ds-property-pages': '31d6cfe0d16ae931b73c',
                      symbolsearch: '31d6cfe0d16ae931b73c',
                      'change-interval-dialog': '31d6cfe0d16ae931b73c',
                      'line-tools-icons': '31d6cfe0d16ae931b73c',
                      'floating-toolbars': '31d6cfe0d16ae931b73c',
                      'chart-widget-gui': '31d6cfe0d16ae931b73c',
                      'dialogs-core': '8bff3c55eb11e669fe56',
                      'create-dialog': '31d6cfe0d16ae931b73c',
                      objecttreedialog: '31d6cfe0d16ae931b73c',
                      'study-market': '31d6cfe0d16ae931b73c',
                      'add-compare-dialog': '31d6cfe0d16ae931b73c',
                      'export-data': '31d6cfe0d16ae931b73c',
                      'ie-fallback-logos': '31d6cfe0d16ae931b73c',
                      'lt-pane-views': '31d6cfe0d16ae931b73c',
                      react: '31d6cfe0d16ae931b73c',
                      'take-chart-image-dialog-impl': '31d6cfe0d16ae931b73c',
                      'new-edit-object-dialog': '31d6cfe0d16ae931b73c',
                      'general-chart-properties-dialog': '31d6cfe0d16ae931b73c',
                      'drawings-settings-dialog': '31d6cfe0d16ae931b73c',
                      'new-confirm-inputs-dialog': '31d6cfe0d16ae931b73c',
                      'load-chart-dialog': '31d6cfe0d16ae931b73c',
                      'go-to-date-dialog-impl': '31d6cfe0d16ae931b73c',
                      'symbol-info-dialog-impl': '31d6cfe0d16ae931b73c',
                      'study-template-dialog': '31d6cfe0d16ae931b73c',
                      'context-menu-renderer': '31d6cfe0d16ae931b73c',
                      'restricted-toolset': '31d6cfe0d16ae931b73c',
                      'chart-bottom-toolbar': '31d6cfe0d16ae931b73c',
                      'drawing-toolbar': '31d6cfe0d16ae931b73c',
                      'header-toolbar': '31d6cfe0d16ae931b73c',
                      'touch-painting-hint': '31d6cfe0d16ae931b73c',
                      'series-pane-views': '31d6cfe0d16ae931b73c',
                      'study-pane-views': '31d6cfe0d16ae931b73c',
                      'lazy-jquery-ui': '31d6cfe0d16ae931b73c',
                      hammerjs: '31d6cfe0d16ae931b73c',
                      'lazy-velocity': '31d6cfe0d16ae931b73c',
                      'series-icons-map': '31d6cfe0d16ae931b73c',
                      clipboard: '31d6cfe0d16ae931b73c',
                      'show-theme-save-dialog': '31d6cfe0d16ae931b73c',
                    }[e] +
                    ('rtl' === document.dir ? '.rtl.css' : '.css'),
                  n = c.p + i,
                  s = document.getElementsByTagName('link');
                for (t = 0; t < s.length; t++)
                  if (
                    ((b =
                      (f = s[t]).getAttribute('data-href') ||
                      f.getAttribute('href')),
                    'stylesheet' === f.rel && (b === i || b === n))
                  )
                    return a();
                for (
                  o = document.getElementsByTagName('style'), t = 0;
                  t < o.length;
                  t++
                )
                  if (
                    (b = (f = o[t]).getAttribute('data-href')) === i ||
                    b === n
                  )
                    return a();
                ((r = document.createElement('link')).rel = 'stylesheet'),
                  (r.type = 'text/css'),
                  (r.onload = a),
                  (r.onerror = function (a) {
                    var c = (a && a.target && a.target.src) || n,
                      t = new Error(
                        'Loading CSS chunk ' + e + ' failed.\n(' + c + ')',
                      );
                    (t.request = c),
                      delete l[e],
                      r.parentNode.removeChild(r),
                      d(t);
                  }),
                  (r.href = n),
                  document.getElementsByTagName('head')[0].appendChild(r);
              }).then(function () {
                l[e] = 0;
              })),
            ),
        0 === (a = s[e]) ||
          {
            2: 1,
            3: 1,
            5: 1,
            6: 1,
            7: 1,
            11: 1,
            14: 1,
            15: 1,
            17: 1,
            19: 1,
            20: 1,
            21: 1,
            22: 1,
            26: 1,
            28: 1,
            29: 1,
            31: 1,
            32: 1,
            33: 1,
            34: 1,
            35: 1,
            36: 1,
            37: 1,
            38: 1,
            39: 1,
            43: 1,
            48: 1,
            49: 1,
            50: 1,
            51: 1,
            52: 1,
            53: 1,
            54: 1,
            55: 1,
            56: 1,
            57: 1,
            58: 1,
            59: 1,
            60: 1,
            61: 1,
            62: 1,
            63: 1,
            64: 1,
            65: 1,
            66: 1,
            67: 1,
            68: 1,
            69: 1,
            70: 1,
            71: 1,
            72: 1,
            73: 1,
            74: 1,
            75: 1,
            76: 1,
            77: 1,
          }[e] ||
          (a
            ? o.push(a[2])
            : ((d = new Promise(function (d, c) {
                a = s[e] = [d, c];
              })),
              o.push((a[2] = d)),
              ((t = document.createElement('script')).charset = 'utf-8'),
              (t.timeout = 120),
              c.nc && t.setAttribute('nonce', c.nc),
              (t.src = (function (e) {
                return (
                  c.p +
                  '' +
                  ({
                    'general-property-page': 'general-property-page',
                    'study-property-pages-with-definitions':
                      'study-property-pages-with-definitions',
                    'lt-property-pages-with-definitions':
                      'lt-property-pages-with-definitions',
                    editobjectdialog: 'editobjectdialog',
                    'ds-property-pages': 'ds-property-pages',
                    symbolsearch: 'symbolsearch',
                    'change-interval-dialog': 'change-interval-dialog',
                    'line-tools-icons': 'line-tools-icons',
                    'floating-toolbars': 'floating-toolbars',
                    'chart-widget-gui': 'chart-widget-gui',
                    'dialogs-core': 'dialogs-core',
                    'create-dialog': 'create-dialog',
                    objecttreedialog: 'objecttreedialog',
                    'study-market': 'study-market',
                    'add-compare-dialog': 'add-compare-dialog',
                    'export-data': 'export-data',
                    'ie-fallback-logos': 'ie-fallback-logos',
                    'lt-pane-views': 'lt-pane-views',
                    react: 'react',
                    'take-chart-image-dialog-impl':
                      'take-chart-image-dialog-impl',
                    'new-edit-object-dialog': 'new-edit-object-dialog',
                    'general-chart-properties-dialog':
                      'general-chart-properties-dialog',
                    'drawings-settings-dialog': 'drawings-settings-dialog',
                    'new-confirm-inputs-dialog': 'new-confirm-inputs-dialog',
                    'load-chart-dialog': 'load-chart-dialog',
                    'go-to-date-dialog-impl': 'go-to-date-dialog-impl',
                    'symbol-info-dialog-impl': 'symbol-info-dialog-impl',
                    'study-template-dialog': 'study-template-dialog',
                    'context-menu-renderer': 'context-menu-renderer',
                    'restricted-toolset': 'restricted-toolset',
                    'chart-bottom-toolbar': 'chart-bottom-toolbar',
                    'drawing-toolbar': 'drawing-toolbar',
                    'header-toolbar': 'header-toolbar',
                    'touch-painting-hint': 'touch-painting-hint',
                    'series-pane-views': 'series-pane-views',
                    'study-pane-views': 'study-pane-views',
                    'lazy-jquery-ui': 'lazy-jquery-ui',
                    hammerjs: 'hammerjs',
                    'lazy-velocity': 'lazy-velocity',
                    'series-icons-map': 'series-icons-map',
                    clipboard: 'clipboard',
                    'show-theme-save-dialog': 'show-theme-save-dialog',
                  }[e] || e) +
                  '.' +
                  {
                    0: 'c2df1ae4d38efcd3dc37',
                    1: 'b64c6e462261f7276e08',
                    2: '195070ea59b3395625da',
                    3: '5b21937564f08ea4c6e1',
                    4: '90dade8c8bd3cf734756',
                    5: '7940dd157255d24611f9',
                    6: '902d5f3923d45b49b876',
                    7: 'fc0941206f7b7d32812d',
                    8: '64feb472d73c26e9bc6b',
                    9: 'b007f594eb5810aedb57',
                    10: '7d7478963adcbd9a35fb',
                    11: 'dd520838f92e45cd91e3',
                    12: '7ec666bbb2e51247fe2e',
                    13: '50b1b6fbd8839e93053f',
                    14: 'c5cfd9461da8d8b48f8a',
                    15: 'a3b9fcccfac2e3cd6fd7',
                    16: '6034eca0d6a18bc824e7',
                    17: '3f086dbf98c41fd496c6',
                    18: 'a91f6cb1b702e3645de3',
                    19: 'c5542d290eefbb001433',
                    20: '2416da4fc4c075b56691',
                    21: 'fc856808959a5b8734f7',
                    22: 'c118eafc7686081984c8',
                    23: 'c8b69d55b8f2369ad4ce',
                    24: '27b7ffb036911e389b33',
                    25: '83eedb0f1f7380cf2018',
                    26: '95792ea2824ce1176e82',
                    27: '5673bac18c6bf6dfbbd5',
                    28: 'af1d68bcc50b923c5419',
                    29: 'ade1e7acfb87aa7dbe7d',
                    30: '0ea1a18407c992328696',
                    31: 'd081df3316799b489847',
                    32: '48df7a8cdc38d60b308b',
                    33: '4a74c62095be3045c87e',
                    34: '17e0ce399a577f17ba55',
                    35: '58433cec10095e3c1b7e',
                    36: '2ee80b40751fcc88a65c',
                    37: '1735365b01406a8d696d',
                    38: '9ae2eea9402c30aa3046',
                    39: '7e524b82ef9947f0f19f',
                    40: 'ee166414faa0dac19aa0',
                    41: '32bf4aaf42994c025bf1',
                    42: 'ac477bbef936fe4ab452',
                    43: '4ae432f1b8259dbfd0e5',
                    44: '81e96e0912745bdfadae',
                    45: 'a102bff39b1b9d70e0ba',
                    46: '8ca5c4c904560cd57b9a',
                    47: '075870f1f6d693a2244f',
                    48: '9d08141ee2d55bcad3e7',
                    49: '929acbc67c2613c57f58',
                    50: '2c50aad369bf63f77061',
                    51: '30c5804303a9f1c455e3',
                    52: 'c212ca3684de16c6f115',
                    53: '7217742e39b70fc9d431',
                    54: '9eb4ca2a30197d95fe82',
                    55: '7707e6ae9f2ec8cfb656',
                    56: '83cd8456e872f49059c3',
                    57: '6384b62456dc4fed6ffb',
                    58: '8077d6b199609737b3d5',
                    59: 'd0a724d08ea630e0e2da',
                    60: '2d9503f1ec536f6b62e8',
                    61: '52eeb3a0e98509d968d6',
                    62: 'cb08a9d336bfd7dc2e2b',
                    63: '6694bc942881443ad8ae',
                    64: '52910ef6a8e6facefb26',
                    65: '1d736e1021a4f299c806',
                    66: '6a7d0634a2cb7f129866',
                    67: '0c6dbacd5dae1240c286',
                    68: '65d1e6be85ed6cc3750e',
                    69: '7766feb820b7bb9d8281',
                    70: 'b019d898491be3c566cf',
                    71: '48b1540cd418ee70b749',
                    72: '08848145ddc50e5f4fa5',
                    73: '3278ed2629f782ad759c',
                    74: '436cb1c5c8e0b34267e7',
                    75: '9ff5a15127ee92dc45e6',
                    76: 'f66fd5c1cc567d831396',
                    77: 'fbf036ab87746356518e',
                    'general-property-page': 'db9e5956c575f2e1ebb1',
                    'study-property-pages-with-definitions':
                      'c9e6bee04f7366a94a48',
                    'lt-property-pages-with-definitions':
                      '31cdedae8f7b83333ddb',
                    editobjectdialog: 'd668da116156237e195c',
                    'ds-property-pages': '61cd8ffd6ebf8f60c948',
                    symbolsearch: '2c3fdef503c36745096f',
                    'change-interval-dialog': '10ad94a5cf766b62324c',
                    'line-tools-icons': '508b6d21f9080fcc867b',
                    'floating-toolbars': '0536b8a3626d09797b72',
                    'chart-widget-gui': '71bec77b9f2111aee565',
                    'dialogs-core': 'ae349ff7ce7ae7c99557',
                    'create-dialog': '64ea1b8d204c3e3ff68e',
                    objecttreedialog: '344cfa5ca8fd0432bea3',
                    'study-market': '080c577ff80c8ae8b0a6',
                    'add-compare-dialog': 'a3bfc1b9dd37c21175ca',
                    'export-data': 'e6e609e88b0c04f9aeef',
                    'ie-fallback-logos': '589046871bfa17cbfbda',
                    'lt-pane-views': '99fb8f6738985d70c954',
                    react: 'e08e8f125fdcb2092328',
                    'take-chart-image-dialog-impl': '3e3d860de24bc284a817',
                    'new-edit-object-dialog': '102ccd36393dffd45e1c',
                    'general-chart-properties-dialog': '60008d2d71b29cc82ad1',
                    'drawings-settings-dialog': 'f7f8bd5dfb82f930cb07',
                    'new-confirm-inputs-dialog': '580e5a8beceea53630ce',
                    'load-chart-dialog': '165c2aaffae0c2ab1471',
                    'go-to-date-dialog-impl': 'ac0a0e37796337294d3f',
                    'symbol-info-dialog-impl': 'dc7988315e077b7de8d0',
                    'study-template-dialog': 'fbe6aacf90c5b145b17a',
                    'context-menu-renderer': '017fc40c2d22ab321ce3',
                    'restricted-toolset': 'a91dbdd6d884576dc3c2',
                    'chart-bottom-toolbar': 'e257650695a57bcd135d',
                    'drawing-toolbar': 'f548f9c7e5b6603158eb',
                    'header-toolbar': 'a49aceb83f84b2c30cca',
                    'touch-painting-hint': 'a4abf3e7b381285fcac5',
                    'series-pane-views': '581752ef781229a43425',
                    'study-pane-views': '95b8d8cc670ff199d1aa',
                    'lazy-jquery-ui': '3a9fe36168ca8e6cacb8',
                    hammerjs: '46686dd839f22b742351',
                    'lazy-velocity': 'd040cf1092d3b2920dde',
                    'series-icons-map': '3772601335516d86d8f2',
                    clipboard: '9c2f6e824ce22f3158f4',
                    'show-theme-save-dialog': '6f679443ac931bcb64a5',
                  }[e] +
                  '.js'
                );
              })(e)),
              (f = function (a) {
                var d, c, f, o;
                (t.onerror = t.onload = null),
                  clearTimeout(b),
                  0 !== (d = s[e]) &&
                    (d &&
                      ((c = a && ('load' === a.type ? 'missing' : a.type)),
                      (f = a && a.target && a.target.src),
                      ((o = new Error(
                        'Loading chunk ' +
                          e +
                          ' failed.\n(' +
                          c +
                          ': ' +
                          f +
                          ')',
                      )).type = c),
                      (o.request = f),
                      d[1](o)),
                    (s[e] = void 0));
              }),
              (b = setTimeout(function () {
                f({ type: 'timeout', target: t });
              }, 12e4)),
              (t.onerror = t.onload = f),
              document.head.appendChild(t))),
        Promise.all(o)
      );
    },
      c.m = e,
      c.c = n,
      c.d = function (e, a, d) {
        c.o(e, a) || Object.defineProperty(e, a, { enumerable: !0, get: d });
      },
      c.r = function (e) {
        'undefined' != typeof Symbol &&
          Symbol.toStringTag &&
          Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
          Object.defineProperty(e, '__esModule', { value: !0 });
      },
      c.t = function (e, a) {
        var d, t;
        if ((1 & a && (e = c(e)), 8 & a)) return e;
        if (4 & a && 'object' == typeof e && e && e.__esModule) return e;
        if (
          ((d = Object.create(null)),
          c.r(d),
          Object.defineProperty(d, 'default', { enumerable: !0, value: e }),
          2 & a && 'string' != typeof e)
        )
          for (t in e)
            c.d(
              d,
              t,
              function (a) {
                return e[a];
              }.bind(null, t),
            );
        return d;
      },
      c.n = function (e) {
        var a =
          e && e.__esModule
            ? function () {
                return e.default;
              }
            : function () {
                return e;
              };
        return c.d(a, 'a', a), a;
      },
      c.o = function (e, a) {
        return Object.prototype.hasOwnProperty.call(e, a);
      },
      c.p = 'bundles/',
      c.p = window.WEBPACK_PUBLIC_PATH || c.p,
      t = c.e,
      f = Object.create(null),
      c.e = function (e) {
        if (!f[e]) {
          f[e] = (function e(a, d) {
            return t(a).catch(function () {
              return new Promise(function (c) {
                var f = function () {
                  window.removeEventListener('online', f, !1),
                    !1 === navigator.onLine
                      ? window.addEventListener('online', f, !1)
                      : c(d < 2 ? e(a, d + 1) : t(a));
                };
                setTimeout(f, d * d * 1e3);
              });
            });
          })(e, 0);
          var a = function () {
            delete f[e];
          };
          f[e].then(a, a);
        }
        return f[e];
      },
      c.oe = function (e) {
        throw (console.error(e), e);
      },
      o = (b = window.webpackJsonp = window.webpackJsonp || []).push.bind(b),
      b.push = a,
      b = b.slice(),
      r = 0;
    r < b.length;
    r++
  )
    a(b[r]);
  (i = o), d();
})([]);
