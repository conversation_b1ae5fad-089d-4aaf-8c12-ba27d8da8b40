(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6682],
  {
    9671: (e) => {
      e.exports = '#{count} (price, bar)';
    },
    4639: (e) => {
      e.exports = ['Coördinaten'];
    },
    22192: (e) => {
      e.exports = 'Days';
    },
    37067: (e) => {
      e.exports = 'Displacement (price, bar)';
    },
    13611: (e) => {
      e.exports = ['Rek lijnen uit'];
    },
    63099: (e) => {
      e.exports = 'Hours';
    },
    66304: (e) => {
      e.exports = ['Invoer'];
    },
    95543: (e) => {
      e.exports = 'Months';
    },
    28134: (e) => {
      e.exports = 'Minutes';
    },
    71129: (e) => {
      e.exports = 'Seconds';
    },
    86672: (e) => {
      e.exports = 'Ranges';
    },
    21594: (e) => {
      e.exports = 'Weeks';
    },
    23723: (e) => {
      e.exports = 'change bar X coordinate';
    },
    66266: (e) => {
      e.exports = 'change price Y coordinate';
    },
    13355: (e) => {
      e.exports = 'change {title} days to';
    },
    41377: (e) => {
      e.exports = 'change {title} days from';
    },
    96902: (e) => {
      e.exports = 'change {title} extend lines';
    },
    35388: (e) => {
      e.exports = 'change {title} hours from';
    },
    78586: (e) => {
      e.exports = 'change {title} hours to';
    },
    59635: (e) => {
      e.exports = 'change {title} months from';
    },
    74266: (e) => {
      e.exports = 'change {title} months to';
    },
    91633: (e) => {
      e.exports = 'change {title} minutes to';
    },
    15106: (e) => {
      e.exports = 'change {title} minutes from';
    },
    66161: (e) => {
      e.exports = 'change {title} seconds to';
    },
    2822: (e) => {
      e.exports = 'change {title} seconds from';
    },
    21339: (e) => {
      e.exports = 'change {title} weeks from';
    },
    68643: (e) => {
      e.exports = 'change {title} weeks to';
    },
    30810: (e) => {
      e.exports = 'change {title} visibility on ticks';
    },
    24941: (e) => {
      e.exports = 'change {title} visibility on weeks';
    },
    29088: (e) => {
      e.exports = 'change {title} visibility on days';
    },
    68971: (e) => {
      e.exports = 'change {title} visibility on hours';
    },
    64370: (e) => {
      e.exports = 'change {title} visibility on minutes';
    },
    6659: (e) => {
      e.exports = 'change {title} visibility on months';
    },
    29091: (e) => {
      e.exports = 'change {title} visibility on ranges';
    },
    46948: (e) => {
      e.exports = 'change {title} visibility on seconds';
    },
    18567: (e) => {
      e.exports = 'change {propertyName} property';
    },
    82211: (e) => {
      e.exports = ['Days'];
    },
    33486: (e) => {
      e.exports = 'days to';
    },
    14077: (e) => {
      e.exports = 'days from';
    },
    3143: (e) => {
      e.exports = ['Hours'];
    },
    84775: (e) => {
      e.exports = 'hours from';
    },
    11255: (e) => {
      e.exports = 'hours to';
    },
    72223: (e) => {
      e.exports = 'move drawings';
    },
    58964: (e) => {
      e.exports = ['Months'];
    },
    71770: (e) => {
      e.exports = 'months from';
    },
    37179: (e) => {
      e.exports = 'months to';
    },
    16465: (e) => {
      e.exports = ['Minutes'];
    },
    72317: (e) => {
      e.exports = 'minutes to';
    },
    25586: (e) => {
      e.exports = 'minutes from';
    },
    32925: (e) => {
      e.exports = 'seconds';
    },
    39017: (e) => {
      e.exports = 'seconds to';
    },
    6049: (e) => {
      e.exports = 'seconds from';
    },
    13604: (e) => {
      e.exports = ['Ranges'];
    },
    93016: (e) => {
      e.exports = 'weeks';
    },
    32002: (e) => {
      e.exports = 'weeks from';
    },
    28091: (e) => {
      e.exports = 'weeks to';
    },
    59523: (e) => {
      e.exports = 'ticks';
    },
  },
]);
