(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [7076],
  {
    20747: (t) => {
      t.exports = ['מחדש'];
    },
    14642: (t) => {
      t.exports = ['כהה'];
    },
    69841: (t) => {
      t.exports = ['בהיר'];
    },
    673: (t) => {
      (t.exports = Object.create(null)),
        (t.exports.d_dates = ['תאריך']),
        (t.exports.h_dates = ['שעות']),
        (t.exports.m_dates = ['דקות']),
        (t.exports.s_dates = ['שניות']),
        (t.exports.in_dates = ['בתוך']);
    },
    97840: (t) => {
      t.exports = ['תאריך'];
    },
    64302: (t) => {
      t.exports = ['שעות'];
    },
    79442: (t) => {
      t.exports = ['דקות'];
    },
    22448: (t) => {
      t.exports = ['שניות'];
    },
    16493: (t) => {
      t.exports = ['{title} העתק'];
    },
    13395: (t) => {
      t.exports = ['יום'];
    },
    37720: (t) => {
      t.exports = ['חודש'];
    },
    69838: (t) => {
      t.exports = 'R';
    },
    59231: (t) => {
      t.exports = 'T';
    },
    85521: (t) => {
      t.exports = ['שבוע'];
    },
    13994: (t) => {
      t.exports = ['שעה'];
    },
    6791: (t) => {
      t.exports = ['דקה'];
    },
    2949: (t) => {
      t.exports = 's';
    },
    77297: (t) => {
      t.exports = ['ס'];
    },
    56723: (t) => {
      t.exports = ['ג'];
    },
    5801: (t) => {
      t.exports = 'HL2';
    },
    98865: (t) => {
      t.exports = 'HLC3';
    },
    42659: (t) => {
      t.exports = 'OHLC4';
    },
    4292: (t) => {
      t.exports = ['נ'];
    },
    78155: (t) => {
      t.exports = ['פ'];
    },
    88601: (t) => {
      (t.exports = Object.create(null)),
        (t.exports.Close_input = ['סגירה']),
        (t.exports.Back_input = ['חזור']),
        (t.exports['Hull MA_input'] = ['ממוצע נע Hull']),
        (t.exports['{number} item_combobox_input'] = [
          'פריט ‎‎‎{number}‎',
          '‎{number}‎ פריטים',
          '‎{number}‎ פריטים',
          '‎{number}‎ פריטים',
        ]),
        (t.exports.Length_input = ['אורך']),
        (t.exports.Plot_input = 'Plot'),
        (t.exports.Zero_input = ['אפס']),
        (t.exports.Signal_input = ['אות']),
        (t.exports.Long_input = 'Long'),
        (t.exports.Short_input = 'Short'),
        (t.exports.UpperLimit_input = ['גבול עליון']),
        (t.exports.LowerLimit_input = 'LowerLimit'),
        (t.exports.Offset_input = ['קיזוז']),
        (t.exports.length_input = 'length'),
        (t.exports.mult_input = ['מרובה']),
        (t.exports.short_input = 'short'),
        (t.exports.long_input = 'long'),
        (t.exports.Limit_input = ['לימיט']),
        (t.exports.Move_input = ['מהלך']),
        (t.exports.Value_input = ['ערך']),
        (t.exports.Method_input = ['שיטה']),
        (t.exports['Values in status line_input'] = ['ערכים בשורת המצב']),
        (t.exports['Labels on price scale_input'] = ['תוויות בסולם מחירים']),
        (t.exports['Accumulation/Distribution_input'] = ['איסוף והפצה']),
        (t.exports.ADR_B_input = 'ADR_B'),
        (t.exports['Equality Line_input'] = ['קו שוויון']),
        (t.exports['Window Size_input'] = ['גודל חלון']),
        (t.exports.Sigma_input = ['סיגמא']),
        (t.exports['Aroon Up_input'] = 'Aroon Up'),
        (t.exports['Aroon Down_input'] = 'Aroon Down'),
        (t.exports.Upper_input = ['עליון']),
        (t.exports.Lower_input = ['תחתון']),
        (t.exports.Deviation_input = ['סטייה']),
        (t.exports['Levels Format_input'] = ['פורמט רמות']),
        (t.exports['Labels Position_input'] = ['מיקום תוויות']),
        (t.exports['0 Level Color_input'] = ['רמת צבע 0']),
        (t.exports['0.236 Level Color_input'] = ['רמת צבע 0.236']),
        (t.exports['0.382 Level Color_input'] = ['רמת צבע 0.382']),
        (t.exports['0.5 Level Color_input'] = ['רמת צבע 0.5']),
        (t.exports['0.618 Level Color_input'] = ['רמת צבע 0.618']),
        (t.exports['0.65 Level Color_input'] = ['רמת צבע 0.65']),
        (t.exports['0.786 Level Color_input'] = ['רמת צבע 0.786']),
        (t.exports['1 Level Color_input'] = ['רמת צבע 1']),
        (t.exports['1.272 Level Color_input'] = ['רמת צבע 1.272']),
        (t.exports['1.414 Level Color_input'] = ['רמת צבע 1.414']),
        (t.exports['1.618 Level Color_input'] = ['רמת צבע 1.618']),
        (t.exports['1.65 Level Color_input'] = ['רמת צבע 1.65']),
        (t.exports['2.618 Level Color_input'] = ['רמת צבע 2.618']),
        (t.exports['2.65 Level Color_input'] = ['רמת צבע 2.65']),
        (t.exports['3.618 Level Color_input'] = ['רמת צבע 3.618']),
        (t.exports['3.65 Level Color_input'] = ['רמת צבע 3.65']),
        (t.exports['4.236 Level Color_input'] = ['רמת צבע 4.236']),
        (t.exports['-0.236 Level Color_input'] = ['רמת צבע 0.236-']),
        (t.exports['-0.382 Level Color_input'] = ['רמת צבע 0.382-']),
        (t.exports['-0.618 Level Color_input'] = ['רמת צבע 0.618-']),
        (t.exports['-0.65 Level Color_input'] = ['רמת צבע 0.65-']),
        (t.exports.ADX_input = 'ADX'),
        (t.exports['ADX Smoothing_input'] = ['החלקת ADX']),
        (t.exports['DI Length_input'] = ['אורך DI']),
        (t.exports.Smoothing_input = 'Smoothing'),
        (t.exports.ATR_input = ['ממוצע טווח אמיתי']),
        (t.exports.Growing_input = ['עולה']),
        (t.exports.Falling_input = ['יורד']),
        (t.exports['Color 0_input'] = ['צבע 0']),
        (t.exports['Color 1_input'] = ['צבע 1']),
        (t.exports.Source_input = ['מקור']),
        (t.exports.StdDev_input = 'StdDev'),
        (t.exports.Basis_input = ['בּסיס']),
        (t.exports.Median_input = ['חציון']),
        (t.exports['Bollinger Bands %B_input'] = ["רצועות בולינג'ר %B"]),
        (t.exports.Overbought_input = ['קניית יתר']),
        (t.exports.Oversold_input = ['מכירת יתר']),
        (t.exports['Bollinger Bands Width_input'] = ["רוחב רצועות בולינג'ר"]),
        (t.exports['RSI Length_input'] = ['אורך RSI']),
        (t.exports['UpDown Length_input'] = ['אורך UpDown']),
        (t.exports['ROC Length_input'] = ['אורך ROC']),
        (t.exports.MF_input = 'MF'),
        (t.exports.resolution_input = ['רזולוציה']),
        (t.exports['Fast Length_input'] = ['אורך מהיר']),
        (t.exports['Slow Length_input'] = ['אורך איטי']),
        (t.exports['Chaikin Oscillator_input'] = ['מתנד חייקין']),
        (t.exports.P_input = 'P'),
        (t.exports.X_input = 'X'),
        (t.exports.Q_input = 'Q'),
        (t.exports.p_input = 'p'),
        (t.exports.x_input = 'x'),
        (t.exports.q_input = 'q'),
        (t.exports.Price_input = ['מחיר']),
        (t.exports['Chande MO_input'] = 'Chande MO'),
        (t.exports['Zero Line_input'] = ['קו אפס']),
        (t.exports['Color 2_input'] = ['צבע 2']),
        (t.exports['Color 3_input'] = ['צבע 3']),
        (t.exports['Color 4_input'] = ['צבע 4']),
        (t.exports['Color 5_input'] = ['צבע 5']),
        (t.exports['Color 6_input'] = ['צבע 6']),
        (t.exports['Color 7_input'] = ['צבע 7']),
        (t.exports['Color 8_input'] = ['צבע 8']),
        (t.exports.CHOP_input = 'CHOP'),
        (t.exports['Upper Band_input'] = ['רצועה עליונה']),
        (t.exports['Lower Band_input'] = ['רצועה תחתונה']),
        (t.exports.CCI_input = 'CCI'),
        (t.exports['Smoothing Line_input'] = ['קו החלקה']),
        (t.exports['Smoothing Length_input'] = ['אורך החלקה']),
        (t.exports['WMA Length_input'] = ['אורך WMA']),
        (t.exports['Long RoC Length_input'] = 'Long RoC Length'),
        (t.exports['Short RoC Length_input'] = 'Short RoC Length'),
        (t.exports.sym_input = 'sym'),
        (t.exports.Symbol_input = ['סימול']),
        (t.exports.Correlation_input = ['קורלציה']),
        (t.exports.Period_input = ['פרק זמן']),
        (t.exports.Centered_input = ['מְרוּכָּז']),
        (t.exports['Detrended Price Oscillator_input'] =
          'Detrended Price Oscillator'),
        (t.exports.isCentered_input = 'isCentered'),
        (t.exports.DPO_input = 'DPO'),
        (t.exports['ADX smoothing_input'] = ['החלקת ADX']),
        (t.exports['+DI_input'] = '+DI'),
        (t.exports['-DI_input'] = '-DI'),
        (t.exports.DEMA_input = 'DEMA'),
        (t.exports.Divisor_input = ['מחלק']),
        (t.exports.EOM_input = 'EOM'),
        (t.exports["Elder's Force Index_input"] = "Elder's Force Index"),
        (t.exports.Percent_input = ['אחוז']),
        (t.exports.Exponential_input = ['אקספוננציאלי']),
        (t.exports.Average_input = ['ממוצע']),
        (t.exports['Upper Percentage_input'] = ['אחוז עליון']),
        (t.exports['Lower Percentage_input'] = ['אחוז תחתון']),
        (t.exports.Fisher_input = 'Fisher'),
        (t.exports.Trigger_input = 'Trigger'),
        (t.exports.Level_input = ['רָמָה']),
        (t.exports['Trader EMA 1 length_input'] = ['אורך טריידר EMA 1']),
        (t.exports['Trader EMA 2 length_input'] = ['אורך טריידר EMA 2']),
        (t.exports['Trader EMA 3 length_input'] = ['אורך טריידר EMA 3']),
        (t.exports['Trader EMA 4 length_input'] = ['אורך טריידר EMA 4']),
        (t.exports['Trader EMA 5 length_input'] = ['אורך טריידר EMA 5']),
        (t.exports['Trader EMA 6 length_input'] = ['אורך טריידר EMA 6']),
        (t.exports['Investor EMA 1 length_input'] = ['משקיע אורך EMA 1']),
        (t.exports['Investor EMA 2 length_input'] = ['משקיע אורך EMA 2']),
        (t.exports['Investor EMA 3 length_input'] = ['משקיע אורך EMA 3']),
        (t.exports['Investor EMA 4 length_input'] = ['משקיע אורך EMA 4']),
        (t.exports['Investor EMA 5 length_input'] = ['משקיע אורך EMA 5']),
        (t.exports['Investor EMA 6 length_input'] = ['משקיע אורך EMA 6']),
        (t.exports.HV_input = 'HV'),
        (t.exports['Conversion Line Periods_input'] = ['תקופות קו המרה']),
        (t.exports['Base Line Periods_input'] = ['תקופות קו הבסיס']),
        (t.exports['Lagging Span_input'] = 'Lagging Span'),
        (t.exports['Conversion Line_input'] = ['קו המרה']),
        (t.exports['Base Line_input'] = ['קו בסיס']),
        (t.exports['Leading Span A_input'] = ['מוביל 1']),
        (t.exports['Leading Span B_input'] = ['Lagging Span 2 Periods']),
        (t.exports['Plots Background_input'] = 'Plots Background'),
        (t.exports['yay Color 0_input'] = 'yay Color 0'),
        (t.exports['yay Color 1_input'] = 'yay Color 1'),
        (t.exports.Multiplier_input = ['מַכפִּיל']),
        (t.exports['Bands style_input'] = ['סגנון רצועות']),
        (t.exports.Middle_input = ['אמצע']),
        (t.exports.useTrueRange_input = 'useTrueRange'),
        (t.exports.ROCLen1_input = 'ROCLen1'),
        (t.exports.ROCLen2_input = 'ROCLen2'),
        (t.exports.ROCLen3_input = 'ROCLen3'),
        (t.exports.ROCLen4_input = 'ROCLen4'),
        (t.exports.SMALen1_input = 'SMALen1'),
        (t.exports.SMALen2_input = 'SMALen2'),
        (t.exports.SMALen3_input = 'SMALen3'),
        (t.exports.SMALen4_input = 'SMALen4'),
        (t.exports.SigLen_input = 'SigLen'),
        (t.exports.KST_input = 'KST'),
        (t.exports.Sig_input = 'Sig'),
        (t.exports.roclen1_input = 'roclen1'),
        (t.exports.roclen2_input = 'roclen2'),
        (t.exports.roclen3_input = 'roclen3'),
        (t.exports.roclen4_input = 'roclen4'),
        (t.exports.smalen1_input = 'smalen1'),
        (t.exports.smalen2_input = 'smalen2'),
        (t.exports.smalen3_input = 'smalen3'),
        (t.exports.smalen4_input = 'smalen4'),
        (t.exports.siglen_input = ['אורך-אות']),
        (t.exports['Upper Deviation_input'] = ['סטיית עליון']),
        (t.exports['Lower Deviation_input'] = 'Lower Deviation'),
        (t.exports['Use Upper Deviation_input'] = ['השתמש בסטייה עליונה']),
        (t.exports['Use Lower Deviation_input'] = ['השתמש בסטייה תחתונה']),
        (t.exports.Count_input = ['לספור']),
        (t.exports.Crosses_input = ['חוצה']),
        (t.exports.MOM_input = 'MOM'),
        (t.exports.MA_input = ['ממוצע נע']),
        (t.exports['Length EMA_input'] = ['אורך ממוצע נע אקספוננציאלי']),
        (t.exports['Length MA_input'] = ['אורך ממוצע נע']),
        (t.exports['Fast length_input'] = ['אורך מהיר']),
        (t.exports['Slow length_input'] = 'Slow length'),
        (t.exports['Signal smoothing_input'] = ['החלקת אותות']),
        (t.exports['Simple ma(oscillator)_input'] = 'Simple ma(oscillator)'),
        (t.exports['Simple ma(signal line)_input'] = 'Simple ma(signal line)'),
        (t.exports.Histogram_input = ['היסטוגרמה']),
        (t.exports.MACD_input = 'MACD'),
        (t.exports.fastLength_input = 'fastLength'),
        (t.exports.slowLength_input = 'slowLength'),
        (t.exports.signalLength_input = ['אורך-האות']),
        (t.exports.NV_input = 'NV'),
        (t.exports.OnBalanceVolume_input = 'OnBalanceVolume'),
        (t.exports.Start_input = ['התחל']),
        (t.exports.Increment_input = 'Increment'),
        (t.exports['Max value_input'] = ['ערך מקסימלי']),
        (t.exports.ParabolicSAR_input = 'ParabolicSAR'),
        (t.exports.start_input = ['התחל']),
        (t.exports.increment_input = ['תוספת']),
        (t.exports.maximum_input = ['מקסימום']),
        (t.exports['Short length_input'] = 'Short length'),
        (t.exports['Long length_input'] = 'Long length'),
        (t.exports.OSC_input = 'OSC'),
        (t.exports.shortlen_input = 'shortlen'),
        (t.exports.longlen_input = 'longlen'),
        (t.exports.PVT_input = 'PVT'),
        (t.exports.ROC_input = 'ROC'),
        (t.exports.RSI_input = 'RSI'),
        (t.exports.RVGI_input = 'RVGI'),
        (t.exports.RVI_input = 'RVI'),
        (t.exports['Long period_input'] = 'Long period'),
        (t.exports['Short period_input'] = 'Short period'),
        (t.exports['Signal line period_input'] = ['תקופת קו אות']),
        (t.exports.SMI_input = 'SMI'),
        (t.exports['SMI Ergodic Oscillator_input'] = ['מתנד SMI ארגודיק']),
        (t.exports.Indicator_input = ['אינדיקטור']),
        (t.exports.Oscillator_input = ['מתנד']),
        (t.exports.K_input = 'K'),
        (t.exports.D_input = 'D'),
        (t.exports.smoothK_input = 'smoothK'),
        (t.exports.smoothD_input = 'smoothD'),
        (t.exports['%K_input'] = '%K'),
        (t.exports['%D_input'] = '%D'),
        (t.exports['Stochastic Length_input'] = ['אורך סטוקסטיק']),
        (t.exports['RSI Source_input'] = ['מקור RSI']),
        (t.exports.lengthRSI_input = 'lengthRSI'),
        (t.exports.lengthStoch_input = 'lengthStoch'),
        (t.exports.TRIX_input = 'TRIX'),
        (t.exports.TEMA_input = 'TEMA'),
        (t.exports['Long Length_input'] = 'Long Length'),
        (t.exports['Short Length_input'] = 'Short Length'),
        (t.exports['Signal Length_input'] = ['אורך אות']),
        (t.exports.Length1_input = ['אורך1']),
        (t.exports.Length2_input = ['אורך2']),
        (t.exports.Length3_input = ['אורך3']),
        (t.exports.length7_input = 'length7'),
        (t.exports.length14_input = 'length14'),
        (t.exports.length28_input = 'length28'),
        (t.exports.UO_input = 'UO'),
        (t.exports.VWMA_input = 'VWMA'),
        (t.exports.len_input = 'len'),
        (t.exports['VI +_input'] = 'VI +'),
        (t.exports['VI -_input'] = 'VI -'),
        (t.exports['%R_input'] = '%R'),
        (t.exports['Jaw Length_input'] = ['אורך הלסת']),
        (t.exports['Teeth Length_input'] = 'Teeth Length'),
        (t.exports['Lips Length_input'] = ['אורך השפתיים']),
        (t.exports.Jaw_input = ['לסת']),
        (t.exports.Teeth_input = 'Teeth'),
        (t.exports.Lips_input = ['שפתיים']),
        (t.exports['Jaw Offset_input'] = 'Jaw Offset'),
        (t.exports['Teeth Offset_input'] = 'Teeth Offset'),
        (t.exports['Lips Offset_input'] = 'Lips Offset'),
        (t.exports['Down fractals_input'] = ['פרקטלי ירידה']),
        (t.exports['Up fractals_input'] = ['פרקטל עולה']),
        (t.exports.Periods_input = ['תקופות']),
        (t.exports.Shapes_input = ['צורות']),
        (t.exports['show MA_input'] = ['הצג MA']),
        (t.exports['MA Length_input'] = ['אורך ממוצע נע']),
        (t.exports['Color based on previous close_input'] = [
          'צבע מבוסס על סגירה קודמת',
        ]),
        (t.exports['Rows Layout_input'] = ['פריסת שורות']),
        (t.exports['Row Size_input'] = ['גודל שורה']),
        (t.exports.Volume_input = ['ווליום']),
        (t.exports['Value Area volume_input'] = ['ווליום איזור ערך']),
        (t.exports['Extend Right_input'] = ['הרחב ימינה']),
        (t.exports['Extend POC Right_input'] = 'Extend POC Right'),
        (t.exports['Extend VAH Right_input'] = ['הרחב VAH ימינה']),
        (t.exports['Extend VAL Right_input'] = ['הרחב VAL ימינה']),
        (t.exports['Value Area Volume_input'] = [
          'איזור ערך הווליום Value Area Volume',
        ]),
        (t.exports.Placement_input = ['מיקום']),
        (t.exports.POC_input = ['הוכחת היתכנות POC']),
        (t.exports['Developing Poc_input'] = ['פיתוח POC']),
        (t.exports['Up Volume_input'] = ['ווליום עולה']),
        (t.exports['Down Volume_input'] = ['ווליום יורד']),
        (t.exports['Value Area_input'] = ['אזור ערך']),
        (t.exports['Histogram Box_input'] = ['קופסת היסטוגרמה']),
        (t.exports['Value Area Up_input'] = ['איזור ערך עולה']),
        (t.exports['Value Area Down_input'] = ['איזור ערך יורד']),
        (t.exports['Number Of Rows_input'] = ['מספר שורות']),
        (t.exports['Ticks Per Row_input'] = ['טיקים לכל שורה']),
        (t.exports['Up/Down_input'] = ['מעלה/מטה']),
        (t.exports.Total_input = ['סך הכל']),
        (t.exports.Delta_input = ['דלתא']),
        (t.exports.Bar_input = ['בר']),
        (t.exports.Day_input = ['יום']),
        (t.exports['Deviation (%)_input'] = ['סטייה (%)']),
        (t.exports.Depth_input = ['עומק']),
        (t.exports['Extend to last bar_input'] = ['הרחב לנר האחרון']),
        (t.exports.Simple_input = ['פשוט']),
        (t.exports.Weighted_input = ['משוקלל']),
        (t.exports["Wilder's Smoothing_input"] = ['החלקה של ויילדר']),
        (t.exports['1st Period_input'] = ['התקופה הראשונה']),
        (t.exports['2nd Period_input'] = ['התקופה השנייה']),
        (t.exports['3rd Period_input'] = ['התקופה השלישית']),
        (t.exports['4th Period_input'] = ['התקופה הרביעית']),
        (t.exports['5th Period_input'] = ['התקופה החמישית']),
        (t.exports['6th Period_input'] = ['התקופה השישית']),
        (t.exports['Rate of Change Lookback_input'] = ['שיעור שינוי לאחור']),
        (t.exports['Instrument 1_input'] = ['כלי 1']),
        (t.exports['Instrument 2_input'] = ['כלי 2']),
        (t.exports['Rolling Period_input'] = ['תקופה מתגלגלת']),
        (t.exports['Standard Errors_input'] = ['שגיאות תקן']),
        (t.exports['Averaging Periods_input'] = ['תקופות ממוצעות']),
        (t.exports['Days Per Year_input'] = ['ימים בשנה']),
        (t.exports['Market Closed Percentage_input'] = ['אחוז סגירת שוק']),
        (t.exports['ATR Mult_input'] = ['ממוצע טווח אמיתי מרובה']),
        (t.exports.VWAP_input = ['VWAP ויאפ']),
        (t.exports['Anchor Period_input'] = ['תקופת הפניה']),
        (t.exports.Session_input = ['סשן']),
        (t.exports.Week_input = ['שבוע']),
        (t.exports.Month_input = ['חודש']),
        (t.exports.Year_input = ['שָׁנָה']),
        (t.exports.Decade_input = ['עשור']),
        (t.exports.Century_input = ['מאה']),
        (t.exports.Sessions_input = ['סשנים']),
        (t.exports['Each (pre-market, market, post-market)_input'] = [
          'כל (טרום מסחר, מרקט, מסחר מאוחר)',
        ]),
        (t.exports['Pre-market only_input'] = ['רק מסחר מוקדם פרה-מרקט']),
        (t.exports['Market only_input'] = ['רק מרקט']),
        (t.exports['Post-market only_input'] = ['רק מסחר מאוחר פוסט-מרקט']),
        (t.exports['Main chart symbol_input'] = ['סימול הגרף הראשי']),
        (t.exports['Another symbol_input'] = ['סימול נוסף']),
        (t.exports.Line_input = ['קו']),
        (t.exports['Nothing selected_combobox_input'] = ['לא נבחר דבר']),
        (t.exports['All items_combobox_input'] = ['כל הפריטים']),
        (t.exports.Cancel_input = ['לְבַטֵל']),
        (t.exports.Open_input = ['פתח']);
    },
    54138: (t) => {
      t.exports = ['הפוך גרף'];
    },
    47807: (t) => {
      t.exports = ['צמוד ל-100'];
    },
    34727: (t) => {
      t.exports = ['לוגריתמי'];
    },
    19238: (t) => {
      t.exports = ['ללא תוויות חופפות'];
    },
    70361: (t) => {
      t.exports = ['אחוז'];
    },
    72116: (t) => {
      t.exports = ['רגיל'];
    },
    33021: (t) => {
      t.exports = 'ETH';
    },
    75610: (t) => {
      t.exports = ['שעות מסחר אלקטרוניות'];
    },
    97442: (t) => {
      t.exports = ['שעות מסחר מורחבות'];
    },
    32929: (t) => {
      t.exports = ['מאוחר'];
    },
    56137: (t) => {
      t.exports = ['טרום'];
    },
    98801: (t) => {
      t.exports = ['מסחר מאוחר'];
    },
    56935: (t) => {
      t.exports = ['טרום מסחר'];
    },
    63798: (t) => {
      t.exports = 'RTH';
    },
    24380: (t) => {
      t.exports = ['שעות מסחר רגילות'];
    },
    27991: (t) => {
      t.exports = ['מאי'];
    },
    68716: (t) => {
      (t.exports = Object.create(null)),
        (t.exports['Amortization of Intangibles_study'] = [
          'הפחתת נכסים בלתי מוחשיים',
        ]),
        (t.exports['Amortization of Deferred Charges_study'] = [
          'הפחתת חיובים דחויים',
        ]),
        (t.exports['Average Day Range_study'] = ['טווח ימים ממוצע']),
        (t.exports['Bull Bear Power_study'] = ['כח שורי דובי']),
        (t.exports['Directional Movement Index_study'] =
          'Directional Movement Index'),
        (t.exports['Ichimoku Cloud_study'] = ["ענן איצ'ימוקו"]),
        (t.exports.Ichimoku_study = ["Ichimoku איצ'ימוקו"]),
        (t.exports['Moving Average Convergence Divergence_study'] =
          'Moving Average Convergence Divergence'),
        (t.exports['Volume Weighted Average Price_study'] = [
          'נפח מחיר ממוצע משוקלל VWAP',
        ]),
        (t.exports['Volume Weighted Moving Average_study'] = [
          'ממוצע נע משוקלל נפח',
        ]),
        (t.exports['Williams Percent Range_study'] = [
          'טווח אחוזים של וויליאמס',
        ]),
        (t.exports.Doji_study = ["דוג'י"]),
        (t.exports['Spinning Top Black_study'] = 'Spinning Top Black'),
        (t.exports['Spinning Top White_study'] = 'Spinning Top White'),
        (t.exports.Technicals_study = ['טכני']),
        (t.exports['Accounts payable_study'] = ['חשבונות לתשלום']),
        (t.exports['Accounts receivables, gross_study'] = [
          'חשבונות חייבים, ברוטו',
        ]),
        (t.exports['Accounts receivable - trade, net_study'] = [
          'חשבונות חייבים - מסחר, נטו',
        ]),
        (t.exports.Accruals_study = ['צבירות']),
        (t.exports['Accrued payroll_study'] = ['שכר שנצבר']),
        (t.exports['Accumulated depreciation, total_study'] = [
          'פחת מצטבר, סך הכל',
        ]),
        (t.exports['Additional paid-in capital/Capital surplus_study'] = [
          'הון נוסף שנפרע/עודף הון',
        ]),
        (t.exports['After tax other income/expense_study'] = [
          'לאחר מס הכנסה/הוצאות אחרות',
        ]),
        (t.exports['Altman Z-score_study'] = ['ציון Z של אלטמן']),
        (t.exports.Amortization_study = 'Amortization'),
        (t.exports['Amortization of intangibles_study'] = [
          'הפחתת נכסים בלתי מוחשיים',
        ]),
        (t.exports['Amortization of deferred charges_study'] = [
          'הפחתת חיובים דחויים',
        ]),
        (t.exports['Asset turnover_study'] = ['מחזור נכסים']),
        (t.exports['Average basic shares outstanding_study'] = [
          'ממוצע מניות בסיסיות קיימות',
        ]),
        (t.exports['Bad debt / Doubtful accounts_study'] = [
          'חוב אבוד / חשבונות מפוקפקים',
        ]),
        (t.exports['Basic EPS_study'] = ['EPS בסיסי']),
        (t.exports['Basic earnings per share (Basic EPS)_study'] = [
          'רווח בסיסי למניה (EPS בסיסי)',
        ]),
        (t.exports['Beneish M-score_study'] = ['בנייש M-score']),
        (t.exports['Book value per share_study'] = ['הון מוחשי למניה']),
        (t.exports['Buyback yield %_study'] = ['Buyback תשואה%']),
        (t.exports['Capital and operating lease obligations_study'] = [
          'התחייבויות הון וחכירה תפעולית',
        ]),
        (t.exports['Capital expenditures_study'] = ['הוצאות הון']),
        (t.exports['Capital expenditures - fixed assets_study'] = [
          'הוצאות הון - רכוש קבוע',
        ]),
        (t.exports['Capital expenditures - other assets_study'] = [
          'הוצאות הון - נכסים אחרים',
        ]),
        (t.exports['Capitalized lease obligations_study'] = [
          'התחייבויות חכירה מהוונות',
        ]),
        (t.exports['Cash and short term investments_study'] = [
          'מזומן והשקעות לטווח קצר',
        ]),
        (t.exports['Cash conversion cycle_study'] = ['מחזור המרה במזומן']),
        (t.exports['Cash & equivalents_study'] = ['שווי מזומנים']),
        (t.exports['Cash from financing activities_study'] = [
          'מזומנים מפעילות פיננסית',
        ]),
        (t.exports['Cash from investing activities_study'] = [
          'מזומנים מפעולות השקעה',
        ]),
        (t.exports['Cash from operating activities_study'] = [
          'מזומנים מפעילות תפעולית',
        ]),
        (t.exports['Cash to debt ratio_study'] = ['מכפיל מזומן לחוב']),
        (t.exports['Change in accounts payable_study'] = [
          'שינוי בחשבונות התשלום',
        ]),
        (t.exports['Change in accounts receivable_study'] = [
          'שינוי בחשבונות חייבים',
        ]),
        (t.exports['Change in accrued expenses_study'] = [
          'שינוי בהוצאות שנצברו',
        ]),
        (t.exports['Change in inventories_study'] = ['שינוי במאגר']),
        (t.exports['Change in other assets/liabilities_study'] = [
          'שינוי בנכסים/התחייבויות אחרים',
        ]),
        (t.exports['Change in taxes payable_study'] = ['שינוי במסים לתשלום']),
        (t.exports['Changes in working capital_study'] = ['שינויים בהון חוזר']),
        (t.exports['COGS to revenue ratio_study'] = ['מכפיל COGS להכנסות']),
        (t.exports['Common dividends paid_study'] = [
          'דיבידנדים משותפים ששולמו',
        ]),
        (t.exports['Common equity, total_study'] = ['הון משותף, סך הכל']),
        (t.exports['Common stock par/Carrying value_study'] =
          'Common stock par/Carrying value'),
        (t.exports['Cost of goods_study'] = ['עלות טובין']),
        (t.exports['Cost of goods sold_study'] = ['עלות סחורה שנמכרה']),
        (t.exports['Current portion of LT debt and capital leases_study'] = [
          'חלק שוטף של חובות LT וחכירות הוניות',
        ]),
        (t.exports['Current ratio_study'] = ['מכפיל נוכחי']),
        (t.exports['Days inventory_study'] = ['מאגר של ימים']),
        (t.exports['Days payable_study'] = ['ימים בתשלום']),
        (t.exports['Days sales outstanding_study'] = ['ימי מכירות']),
        (t.exports['Debt to assets ratio_study'] = ['מכפיל חוב לנכסים']),
        (t.exports['Debt to EBITDA ratio_study'] = ['מכפיל חוב ל-EBITDA']),
        (t.exports['Debt to equity ratio_study'] = ['מכפיל חוב להון']),
        (t.exports['Debt to revenue ratio_study'] = ['מכפיל חוב להכנסות']),
        (t.exports['Deferred income, current_study'] =
          'Deferred income, current'),
        (t.exports['Deferred income, non-current_study'] = [
          'הכנסה נדחית, לא שוטפת',
        ]),
        (t.exports['Deferred tax assets_study'] = ['נכסי מס נדחים']),
        (t.exports['Deferred taxes (cash flow)_study'] = [
          'מסים נדחים (תזרים מזומנים)',
        ]),
        (t.exports['Deferred tax liabilities_study'] = ['התחייבויות מס נדחות']),
        (t.exports.Depreciation_study = ['פְּחָת']),
        (t.exports['Deprecation and amortization_study'] = [
          'פחת והפחתות Deprecation and amortization',
        ]),
        (t.exports['Depreciation & amortization (cash flow)_study'] = [
          'פחת והפחתות (תזרים מזומנים)',
        ]),
        (t.exports['Depreciation/depletion_study'] = [
          'פחת/דלדול Depreciation/depletion',
        ]),
        (t.exports['Diluted EPS_study'] = ['רווח למניה מדולל']),
        (t.exports['Diluted earnings per share (Diluted EPS)_study'] = [
          'רווח מדולל למניה (מדולל EPS)',
        ]),
        (t.exports[
          'Diluted net income available to common stockholders_study'
        ] = ['הכנסה נטו מדוללת זמינה לבעלי מניות רגילות']),
        (t.exports['Diluted shares outstanding_study'] = ['מניות בדילול מלא']),
        (t.exports['Dilution adjustment_study'] = ['התאמת דילול']),
        (t.exports['Discontinued operations_study'] = ['פעילות שהופסקה']),
        (t.exports['Dividend payout ratio %_study'] = ['מכפיל חלוקת דיבידנד']),
        (t.exports['Dividends payable_study'] = ['דיבידנדים לתשלום']),
        (t.exports['Dividends per share - common stock primary issue_study'] = [
          'דיבידנדים למניה - הנפקה עיקרית של מניות רגילות',
        ]),
        (t.exports['Dividend yield %_study'] = ['תשואת דיבידנד %']),
        (t.exports['Earnings yield_study'] = ['תשואת רווחים']),
        (t.exports.EBIT_study = 'EBIT'),
        (t.exports.EBITDA_study = ['רווח לפני ריבית, מסים, פחת והפחתות']),
        (t.exports['EBITDA margin %_study'] = ["מרג'ין % EBITDA"]),
        (t.exports['Effective interest rate on debt %_study'] = [
          'שיעור ריבית אפקטיבית על החוב %',
        ]),
        (t.exports['Enterprise value_study'] = ['ערך ארגוני']),
        (t.exports['Enterprise value to EBITDA ratio_study'] = [
          'ערך ארגוני למכפיל EBITDA',
        ]),
        (t.exports['Enterprise value to EBIT ratio_study'] = [
          'מכפיל ערך ארגוני ל-EBIT',
        ]),
        (t.exports['Enterprise value to revenue ratio_study'] = [
          'מכפיל ערך ארגוני ל-EBIT',
        ]),
        (t.exports['EPS basic one year growth_study'] = [
          'EPS צמיחה בסיסית לשנה אחת',
        ]),
        (t.exports['EPS diluted one year growth_study'] = [
          'EPS מדולל צמיחה של שנה אחת',
        ]),
        (t.exports['EPS estimates_study'] = ['הערכות EPS']),
        (t.exports['Equity in earnings_study'] = ['הון עצמי ברווחים']),
        (t.exports['Equity to assets ratio_study'] = ['יחס הון עצמי לנכסים']),
        (t.exports['Financing activities – other sources_study'] = [
          'פעילויות מימון - מקורות נוספים',
        ]),
        (t.exports['Financing activities – other uses_study'] = [
          'פעילויות פיננסיות - שימושים אחרים',
        ]),
        (t.exports['Float shares outstanding_study'] = [
          'מניות צפות outstanding',
        ]),
        (t.exports['Free cash flow_study'] = ['תזרים מזומנים חופשי']),
        (t.exports['Free cash flow margin %_study'] = [
          "% מרג'ין תזרים מזומנים חופשי",
        ]),
        (t.exports['Fulmer H factor_study'] = ['גורם פולמר H']),
        (t.exports['Funds from operations_study'] = ['כספים מפעילות תפעולית']),
        (t.exports['Goodwill, net_study'] = ['מוניטין, נטו']),
        (t.exports['Goodwill to assets ratio_study'] = [
          'מכפיל מוניטין לנכסים',
        ]),
        (t.exports["Graham's number_study"] = ['המספר של גרהם']),
        (t.exports['Gross margin %_study'] = ["% מרג'ין גולמי"]),
        (t.exports['Gross profit_study'] = ['רווח גולמי']),
        (t.exports['Gross profit to assets ratio_study'] = [
          'מכפיל רווח גולמי לנכסים',
        ]),
        (t.exports['Gross property/plant/equipment_study'] = [
          'רכוש/מפעל/ציוד גולמי',
        ]),
        (t.exports.Impairments_study = ['ליקויים']),
        (t.exports['Income Tax Credits_study'] = ['זיכוי מס הכנסה']),
        (t.exports['Income tax, current_study'] = ['מס הכנסה, שוטף']),
        (t.exports['Income tax, current - domestic_study'] = [
          'מס הכנסה, שוטף - מקומי',
        ]),
        (t.exports['Income Tax, current - foreign_study'] = [
          'מס הכנסה, שוטף - זר',
        ]),
        (t.exports['Income tax, deferred_study'] = ['מס הכנסה, נדחה']),
        (t.exports['Income tax, deferred - domestic_study'] = [
          'מס הכנסה, נדחה - מקומי',
        ]),
        (t.exports['Income tax, deferred - foreign_study'] = [
          'מס הכנסה, נדחה - זר',
        ]),
        (t.exports['Income tax payable_study'] = ['מס הכנסה לתשלום']),
        (t.exports['Interest capitalized_study'] = ['ריבית מהוונת']),
        (t.exports['Interest coverage_study'] = ['כיסוי ריבית']),
        (t.exports['Interest expense, net of interest capitalized_study'] = [
          'הוצאות ריבית, בניכוי ריבית מהוונת',
        ]),
        (t.exports['Interest expense on debt_study'] = [
          'הוצאות ריבית על החוב',
        ]),
        (t.exports['Inventories - finished goods_study'] = [
          'מלאי - סחורה מוגמרת',
        ]),
        (t.exports['Inventories - progress payments & other_study'] = [
          'מלאי - תשלומים בתהליך ואחרים',
        ]),
        (t.exports['Inventories - raw materials_study'] = ['מלאי - חומרי גלם']),
        (t.exports['Inventories - work in progress_study'] = [
          'מלאי - עבודות בתהליך',
        ]),
        (t.exports['Inventory to revenue ratio_study'] = [
          'מלאי למכפיל הכנסות',
        ]),
        (t.exports['Inventory turnover_study'] = ['מחזור מלאי']),
        (t.exports['Investing activities – other sources_study'] = [
          'פעילויות השקעה - מקורות נוספים',
        ]),
        (t.exports['Investing activities – other uses_study'] = [
          'פעילויות השקעה - שימושים אחרים',
        ]),
        (t.exports['Investments in unconsolidated subsidiaries_study'] = [
          'השקעות בחברות בת לא מאוחדות',
        ]),
        (t.exports['Issuance of long term debt_study'] = [
          'הנפקת חוב לטווח ארוך',
        ]),
        (t.exports['Issuance/retirement of debt, net_study'] = [
          'הקצאה/פרישת חוב, נטו',
        ]),
        (t.exports['Issuance/retirement of long term debt_study'] = [
          'הקצאה/פריסה של חוב לטווח ארוך',
        ]),
        (t.exports['Issuance/retirement of other debt_study'] = [
          'הקצאה/פרישה של חוב אחר',
        ]),
        (t.exports['Issuance/retirement of short term debt_study'] = [
          'הקצאה/פרישה של חוב לטווח קצר',
        ]),
        (t.exports['Issuance/retirement of stock, net_study'] = [
          'הקצאה/פרישת מלאי, נטו',
        ]),
        (t.exports['KZ index_study'] = ['מדד KZ']),
        (t.exports['Legal claim expense_study'] = ['הוצאות תביעה משפטית']),
        (t.exports['Long term debt_study'] = ['חוב לטווח ארוך']),
        (t.exports['Long term debt excl. lease liabilities_study'] = [
          'חוב לטווח ארוך excl. התחייבויות חכירה',
        ]),
        (t.exports['Long term debt to total assets ratio_study'] = [
          'מכפיל חוב לטווח ארוך לסך הנכסים',
        ]),
        (t.exports['Long term investments_study'] = ['השקעות לטווח ארוך']),
        (t.exports['Market capitalization_study'] = ['שווי שוק']),
        (t.exports['Minority interest_study'] = ['ריבית מינורית']),
        (t.exports['Miscellaneous non-operating expense_study'] = [
          'הוצאות שונות שאינן תפעוליות',
        ]),
        (t.exports['Net current asset value per share_study'] = [
          'שווי נכס נוכחי נטו למניה',
        ]),
        (t.exports['Net debt_study'] = ['חוב נטו']),
        (t.exports['Net income_study'] = ['הכנסה נטו']),
        (t.exports['Net income before discontinued operations_study'] = [
          'רווח נקי לפני פעילות שהופסקה',
        ]),
        (t.exports['Net income (cash flow)_study'] = [
          'רווח נקי (תזרים מזומנים)',
        ]),
        (t.exports['Net income per employee_study'] = ['הכנסה נטו לשכיר']),
        (t.exports['Net intangible assets_study'] = ['נכסים בלתי מוחשיים נטו']),
        (t.exports['Net margin %_study'] = ['נטו margin %']),
        (t.exports['Net property/plant/equipment_study'] = [
          'רכוש/מפעל/ציוד נטו',
        ]),
        (t.exports['Non-cash items_study'] = ['פריטים שאינם מזומנים']),
        (t.exports['Non-controlling/minority interest_study'] = [
          'זכויות חסרות שליטה/ מועטות',
        ]),
        (t.exports['Non-operating income, excl. interest expenses_study'] = [
          'הכנסה לא תפעולית, excl. הוצאות ריבית',
        ]),
        (t.exports['Non-operating income, total_study'] = [
          'הכנסה לא תפעולית, סך הכל',
        ]),
        (t.exports['Non-operating interest income_study'] = [
          'הכנסות ריבית לא תפעוליות',
        ]),
        (t.exports['Note receivable - long term_study'] = [
          'שטר חוב - לטווח ארוך',
        ]),
        (t.exports['Notes payable_study'] = ['תשלומים לתשלום']),
        (t.exports['Number of employees_study'] = ['מספר העובדים']),
        (t.exports['Number of shareholders_study'] = ['מספר בעלי המניות']),
        (t.exports['Operating earnings yield %_study'] = ['% רווחים תפעוליים']),
        (t.exports['Operating expenses (excl. COGS)_study'] = [
          'הוצאות תפעול (לא כולל COGS)',
        ]),
        (t.exports['Operating income_study'] = ['רווח תפעולי']),
        (t.exports['Operating lease liabilities_study'] = [
          'התחייבויות חכירה תפעולית',
        ]),
        (t.exports['Operating margin %_study'] = ["מרג'ין תפעולי %"]),
        (t.exports['Other COGS_study'] = ['COGS אחרים']),
        (t.exports['Other common equity_study'] = ['הון משותף אחר']),
        (t.exports['Other current assets, total_study'] = [
          'נכסים שוטפים אחרים, סך הכל',
        ]),
        (t.exports['Other current liabilities_study'] = [
          'התחייבויות שוטפות אחרות',
        ]),
        (t.exports['Other cost of goods sold_study'] = [
          'עלות אחרת של סחורה שנמכרה',
        ]),
        (t.exports['Other exceptional charges_study'] = [
          'חיובים חריגים אחרים',
        ]),
        (t.exports['Other financing cash flow items, total_study'] = [
          'פריטי תזרים מזומנים אחרים המממנים, סך הכל',
        ]),
        (t.exports['Other intangibles, net_study'] = [
          'נכסים בלתי מוחשיים אחרים, נטו',
        ]),
        (t.exports['Other investing cash flow items, total_study'] = [
          'פריטי תזרים מזומנים אחרים המשקיעים, סך הכל',
        ]),
        (t.exports['Other investments_study'] = ['השקעות אחרות']),
        (t.exports['Other liabilities, total_study'] = [
          'התחייבויות אחרות, סך הכל',
        ]),
        (t.exports['Other long term assets, total_study'] = [
          'נכסים אחרים לטווח ארוך, סך הכל',
        ]),
        (t.exports['Other non-current liabilities, total_study'] = [
          'התחייבויות לא שוטפות אחרות, סך הכל',
        ]),
        (t.exports['Other operating expenses, total_study'] = [
          'הוצאות תפעול אחרות, סך הכל',
        ]),
        (t.exports['Other receivables_study'] = ['חובות אחרים']),
        (t.exports['Other short term debt_study'] = ['חובות אחרים לטווח קצר']),
        (t.exports['Paid in capital_study'] = ['שילם בהון']),
        (t.exports['PEG ratio_study'] = ['מכפיל PEG']),
        (t.exports['Piotroski F-score_study'] = 'Piotroski F-score'),
        (t.exports['Preferred dividends_study'] = ['דיבידנדים מועדפים']),
        (t.exports['Preferred dividends paid_study'] = [
          'דיבידנדים מועדפים ששולמו',
        ]),
        (t.exports['Preferred stock, carrying value_study'] = [
          'מניה מועדפת, נושאת ערך',
        ]),
        (t.exports['Prepaid expenses_study'] = ['הוצאות משולמות מראש']),
        (t.exports['Pretax equity in earnings_study'] = [
          'הון עצמי לפני מס ברווחים',
        ]),
        (t.exports['Pretax income_study'] = ['הכנסה לפני מס']),
        (t.exports['Price earnings ratio forward_study'] = [
          'מכפיל רווחים במחיר קדימה',
        ]),
        (t.exports['Price sales ratio forward_study'] = [
          'מכפיל מכירות מחיר קדימה',
        ]),
        (t.exports['Price to book ratio_study'] = ['מכפיל מחיר לספר']),
        (t.exports['Price to cash flow ratio_study'] = [
          'מכפיל מחיר לתזרים מזומנים',
        ]),
        (t.exports['Price to earnings ratio_study'] = ['מכפיל מחיר לרווח']),
        (t.exports['Price to free cash flow ratio_study'] = [
          'מכפיל מחיר לתזרים מזומנים חופשי',
        ]),
        (t.exports['Price to sales ratio_study'] = ['מכפיל מחיר למכירות']),
        (t.exports['Price to tangible book ratio_study'] = [
          'מכפיל מחיר לספר מוחשי',
        ]),
        (t.exports['Provision for risks & charge_study'] = [
          'הפרשה לסיכונים וחיובים',
        ]),
        (t.exports['Purchase/acquisition of business_study'] = [
          'רכישה/רכישה של עסק',
        ]),
        (t.exports['Purchase of investments_study'] = ['רכישת השקעות']),
        (t.exports['Purchase/sale of business, net_study'] = [
          'רכישה / מכירה של עסקים, נטו',
        ]),
        (t.exports['Purchase/sale of investments, net_study'] = [
          'רכישה / מכירה של השקעות, נטו',
        ]),
        (t.exports['Quality ratio_study'] = ['מכפיל איכות']),
        (t.exports['Quick ratio_study'] = ['מכפיל מהיר']),
        (t.exports['Reduction of long term debt_study'] = [
          'הפחתת חוב לטווח ארוך',
        ]),
        (t.exports['Repurchase of common & preferred stock_study'] = [
          'רכישה חוזרת של מניות נפוצות ומועדפות',
        ]),
        (t.exports['Research & development_study'] = ['מחקר ופיתוח']),
        (t.exports['Research & development to revenue ratio_study'] = [
          'מחקר ופיתוח ביחס להכנסות',
        ]),
        (t.exports['Restructuring charge_study'] = ['תשלום ארגון מחדש']),
        (t.exports['Retained earnings_study'] = ['יתרת רווח']),
        (t.exports['Return on assets %_study'] = ['תשואה על נכסים %']),
        (t.exports['Return on equity %_study'] = ['תשואה להון %']),
        (t.exports['Return on equity adjusted to book value %_study'] = [
          'תשואה להון מותאמת לערך בספרים%',
        ]),
        (t.exports['Return on invested capital %_study'] = [
          'תשואה על ההון המושקע%',
        ]),
        (t.exports['Return on tangible assets %_study'] = [
          'תשואה על נכסים מוחשיים%',
        ]),
        (t.exports['Return on tangible equity %_study'] = [
          'תשואה להון מוחשי%',
        ]),
        (t.exports['Revenue estimates_study'] = ['אומדני הכנסות']),
        (t.exports['Revenue one year growth_study'] = ['הכנסות צמיחה לשנה']),
        (t.exports['Revenue per employee_study'] = ['הכנסה פר עובד']),
        (t.exports['Sale/maturity of investments_study'] = [
          'מכירה / חלות של השקעות Sale maturity of investments',
        ]),
        (t.exports['Sale of common & preferred stock_study'] = [
          'מכירת מניות נפוצות ומועדפות',
        ]),
        (t.exports['Sale of fixed assets & businesses_study'] = [
          'מכירת רכוש קבוע ועסקים',
        ]),
        (t.exports['Selling/general/admin expenses, other_study'] = [
          'מכירת / הוצאות כלליות / ניהול, אחר',
        ]),
        (t.exports['Selling/general/admin expenses, total_study'] = [
          'מכירת / הוצאות כלליות / ניהול, סה"כ',
        ]),
        (t.exports["Shareholders' equity_study"] = ['הון עצמי']),
        (t.exports['Shares buyback ratio %_study'] = [
          '%מכפיל רכישה חוזרת של מניות',
        ]),
        (t.exports["Elder's Force Index_study"] = "Elder's Force Index"),
        (t.exports['Short term debt_study'] = ['חוב לטווח קצר']),
        (t.exports['Short term debt excl. current portion of LT debt_study'] = [
          'חוב לטווח קצר לא כולל החלק הנוכחי של חוב LT',
        ]),
        (t.exports['Short term investments_study'] = ['השקעות לטווח קצר']),
        (t.exports['Sloan ratio %_study'] = [
          'מכפיל יציבות פיננסית Sloan ratio %',
        ]),
        (t.exports['Springate score_study'] = 'Springate score'),
        (t.exports['Sustainable growth rate_study'] = ['קצב צמיחה בר קיימא']),
        (t.exports['Tangible book value per share_study'] = [
          'הון מוחשי למניה',
        ]),
        (t.exports['Tangible common equity ratio_study'] = [
          'יחס הון משותף מוחשי',
        ]),
        (t.exports.Taxes_study = ['מיסים']),
        (t.exports["Tobin's Q (approximate)_study"] = ["Tobin's Q(משוער)"]),
        (t.exports['Total assets_study'] = ['סך כל הנכסים']),
        (t.exports['Total cash dividends paid_study'] = [
          'סך דיבידנדים במזומן ששולם',
        ]),
        (t.exports['Total common shares outstanding_study'] = [
          'סך המניות בידי הציבור',
        ]),
        (t.exports['Total current assets_study'] = ['סך כל הנכסים הנוכחיים']),
        (t.exports['Total current liabilities_study'] = [
          'סך כל ההתחייבויות הנוכחיות',
        ]),
        (t.exports['Total debt_study'] = ['סך כל החוב']),
        (t.exports['Total equity_study'] = ['סך הון עצמי']),
        (t.exports['Total inventory_study'] = ['מלאי כולל']),
        (t.exports['Total liabilities_study'] = ['סך כל ההתחייבויות']),
        (t.exports["Total liabilities & shareholders' equities_study"] = [
          'סך ההתחייבויות ומניות של בעלי המניות',
        ]),
        (t.exports['Total non-current assets_study'] = [
          'סה"כ נכסים לא שוטפים',
        ]),
        (t.exports['Total non-current liabilities_study'] = [
          'סך ההתחייבויות הלא שוטפות',
        ]),
        (t.exports['Total operating expenses_study'] = ['סך כל הוצאות התפעול']),
        (t.exports['Total receivables, net_study'] = ['סך החייבות, נטו']),
        (t.exports['Total revenue_study'] = ['סך כל ההכנסות']),
        (t.exports['Treasury stock - common_study'] = ['מניית אוצר - נפוץ']),
        (t.exports['Unrealized gain/loss_study'] = ['רווח/הפסד לא ממומשים']),
        (t.exports['Unusual income/expense_study'] = ['הכנסה / הוצאה חריגים']),
        (t.exports['Zmijewski score_study'] = ['ציון Zmijewski']),
        (t.exports['Valuation ratios_study'] = ['מכפילי הערכה']),
        (t.exports['Profitability ratios_study'] = ['מכפילי רווחיות']),
        (t.exports['Liquidity ratios_study'] = ['מכפילי נזילות']),
        (t.exports['Solvency ratios_study'] = ['מכפילי כושר פירעון']),
        (t.exports['Accumulation/Distribution_study'] = ['הצטברות / הפצה']),
        (t.exports['Accumulative Swing Index_study'] = ['מדד סווינג מצטבר']),
        (t.exports['Advance/Decline_study'] = 'Advance/Decline'),
        (t.exports['Arnaud Legoux Moving Average_study'] = [
          'ממוצעים נעים ארנולד לגוקס (ALMA)',
        ]),
        (t.exports.Aroon_study = ['הרון (Aroon)']),
        (t.exports.ASI_study = 'ASI'),
        (t.exports['Average Directional Index_study'] = ['מדד כיווני ממוצע']),
        (t.exports['Average True Range_study'] = ['ממוצע טווח אמיתי (ATR)']),
        (t.exports['Awesome Oscillator_study'] = ['מתנד אוסום (AO)']),
        (t.exports['Balance of Power_study'] = ['שיווי משקל כוח (BoP)']),
        (t.exports['Bollinger Bands %B_study'] = ['רוצועות בויילינגר %']),
        (t.exports['Bollinger Bands Width_study'] = ["רוחב רצועות בולינג'ר"]),
        (t.exports['Bollinger Bands_study'] = ["רצועות בולינג'ר"]),
        (t.exports['Chaikin Money Flow_study'] = ["זרימת כספים צ'איקין (CMF)"]),
        (t.exports['Chaikin Oscillator_study'] = ["מתנד צ'אייקין (CO)"]),
        (t.exports['Chande Kroll Stop_study'] = ["צ'אנד קרול סטופ (CKS)"]),
        (t.exports['Chande Momentum Oscillator_study'] = [
          "מתנד מומנטום צ'אנד (CMO)",
        ]),
        (t.exports['Chop Zone_study'] = ["צ'ופ-זון"]),
        (t.exports['Choppiness Index_study'] = ["מדד צ'ופינס"]),
        (t.exports['Commodity Channel Index_study'] = [
          'מדד ערוץ סחורות ׁׁ(CCI)',
        ]),
        (t.exports['Connors RSI_study'] = ['קונורס RSI']),
        (t.exports['Coppock Curve_study'] = ['עקומת קופוק']),
        (t.exports['Correlation Coefficient_study'] = ['מקדם התאמה']),
        (t.exports.CRSI_study = 'CRSI'),
        (t.exports['Detrended Price Oscillator_study'] =
          'Detrended Price Oscillator'),
        (t.exports['Directional Movement_study'] = ['תנועה כיוונית']),
        (t.exports['Donchian Channels_study'] = ["ערוצי דונצ'יאן"]),
        (t.exports['Double EMA_study'] = ['ממוצע נע אקספוננציאלי כפול']),
        (t.exports['Ease Of Movement_study'] = ['קלות תנועה']),
        (t.exports['Elder Force Index_study'] = ["Elder's Force Index"]),
        (t.exports['EMA Cross_study'] = ['ממוצע נע אקספוננציאלי חוצה']),
        (t.exports.Envelopes_study = ['מעטפות']),
        (t.exports['Fisher Transform_study'] = 'Fisher Transform'),
        (t.exports['Fixed Range_study'] = ['טווח קבוע']),
        (t.exports['Fixed Range Volume Profile_study'] = [
          'פרופיל ווליום טווח קבוע',
        ]),
        (t.exports['Guppy Multiple Moving Average_study'] = [
          'ממוצע נע מרובה גופים',
        ]),
        (t.exports['Historical Volatility_study'] = ['תנודתיות היסטורית']),
        (t.exports['Hull Moving Average_study'] = ['ממוצע נע Hull']),
        (t.exports['Keltner Channels_study'] = ['תעלות קלטנר']),
        (t.exports['Klinger Oscillator_study'] = ['מתנד קלינגר (KO)']),
        (t.exports['Know Sure Thing_study'] = ['דע בביטחון (Know sure thing)']),
        (t.exports['Least Squares Moving Average_study'] = [
          'ריבועי ממוצעים נעים פשוטים (LSMA)',
        ]),
        (t.exports['Linear Regression Curve_study'] = ['עקומת רגרסיה לינארית']),
        (t.exports['MA Cross_study'] = ['ממוצעים נעים חוצים']),
        (t.exports['MA with EMA Cross_study'] = ['ממוצע נע עם ממוצע נע חוצה']),
        (t.exports['MA/EMA Cross_study'] = ['ממוצע נע/ממוצע נע חוצה']),
        (t.exports.MACD_study = 'MACD'),
        (t.exports['Mass Index_study'] = ['מדד המונים']),
        (t.exports['McGinley Dynamic_study'] = ['מקינגלי דינאמי']),
        (t.exports.Median_study = ['חציון']),
        (t.exports.Momentum_study = ['מומנטום']),
        (t.exports['Money Flow_study'] = ['זרימת כספים MFI']),
        (t.exports['Moving Average Channel_study'] = ['ערוץ ממוצע נע']),
        (t.exports['Moving Average Exponential_study'] = [
          'ממוצע נע אקספוננציאלי',
        ]),
        (t.exports['Moving Average Weighted_study'] = ['ממוצע נע משוקלל']),
        (t.exports['Moving Average_study'] = ['ממוצע נע']),
        (t.exports['Net Volume_study'] = ['נפח נטו']),
        (t.exports['On Balance Volume_study'] = ['ווליום מאוזן OBV']),
        (t.exports['Parabolic SAR_study'] = ['פרבוליק SAR']),
        (t.exports['Pivot Points Standard_study'] = ['נקודות פיבוט רגיל']),
        (t.exports['Periodic Volume Profile_study'] = ['פרופיל ווליום תקופתי']),
        (t.exports['Price Channel_study'] = ['ערוץ מחיר']),
        (t.exports['Price Oscillator_study'] = ['מתנד מחיר (PO)']),
        (t.exports['Price Volume Trend_study'] = ['מגמת מחיר ונפח']),
        (t.exports['Rate Of Change_study'] = ['שיעור השינוי']),
        (t.exports['Relative Strength Index_study'] = [
          'מדד כוח יחסי (RSIׂׂׂ)',
        ]),
        (t.exports['Relative Vigor Index_study'] = ['מדד עוצמה יחסי (RVI)']),
        (t.exports['Relative Volatility Index_study'] = ['מדד תנודתיות יחסית']),
        (t.exports['Session Volume_study'] = ['סשן ווליום']),
        (t.exports['Session Volume HD_study'] = ['סשן ווליום HD']),
        (t.exports['Session Volume Profile_study'] = ['פרופיל סשן ווליום']),
        (t.exports['Session Volume Profile HD_study'] = [
          'פרופיל סשן ווליום HD',
        ]),
        (t.exports['SMI Ergodic Indicator/Oscillator_study'] =
          'SMI Ergodic Indicator/Oscillator'),
        (t.exports['Smoothed Moving Average_study'] = ['ממוצע נע חלק']),
        (t.exports.Stoch_study = 'Stoch'),
        (t.exports['Stochastic RSI_study'] = [
          'מדד כוח יחסי סטוכסטיק (Stochastic RSI)',
        ]),
        (t.exports.Stochastic_study = ['סטוכסטיק']),
        (t.exports['Triple EMA_study'] = [
          'ממוצעים נעים אקפוננציאלית פי 3 (Triple EMA)',
        ]),
        (t.exports.TRIX_study = ['טריקס (TRIX)']),
        (t.exports['True Strength Indicator_study'] = ['מדד כוח אמיתי ׁ(TSI)']),
        (t.exports['Ultimate Oscillator_study'] = ['אוסילטור אולטימטיבי']),
        (t.exports['Visible Range_study'] = ['טווח גלוי']),
        (t.exports['Visible Range Volume Profile_study'] = [
          'פרופיל ווליום טווח גלוי',
        ]),
        (t.exports['Volume Oscillator_study'] = [
          'חישוב מחזור ע"י יחס בין ממוצעים נעים (Oscillator)',
        ]),
        (t.exports.Volume_study = ['ווליום/נפח מסחר']),
        (t.exports.Vol_study = ['מחזור']),
        (t.exports['Vortex Indicator_study'] = ['מתנד וורטקס']),
        (t.exports.VWAP_study = 'VWAP'),
        (t.exports.VWMA_study = 'VWMA'),
        (t.exports['Williams %R_study'] = ['וויליאם %R']),
        (t.exports['Williams Alligator_study'] = ['תנין ויליאמס']),
        (t.exports['Williams Fractal_study'] = ['ויליאמס פרקטל']),
        (t.exports['Zig Zag_study'] = ['זיג-זג']),
        (t.exports['24-hour Volume_study'] = ['ווליום 24 שעות']),
        (t.exports['Ease of Movement_study'] = ['קלות תנועה']),
        (t.exports['Elders Force Index_study'] = ['מדד הכוח של אלדר']),
        (t.exports.Envelope_study = ['מעטפה']),
        (t.exports.Gaps_study = ['גאפים']),
        (t.exports['Linear Regression Channel_study'] = [
          'ערוץ רגרסיה ליניארי',
        ]),
        (t.exports['Moving Average Ribbon_study'] = ['ממוצע נע Ribbon']),
        (t.exports['Multi-Time Period Charts_study'] = [
          'תרשימי גרף רב-תקופתי',
        ]),
        (t.exports['Open Interest_study'] = 'Open Interest'),
        (t.exports['Rob Booker - Intraday Pivot Points_study'] = [
          'Rob Booker - נקודות פיווט תוך יומיות',
        ]),
        (t.exports['Rob Booker - Knoxville Divergence_study'] = [
          'Rob Booker - Knoxville סטייה',
        ]),
        (t.exports['Rob Booker - Missed Pivot Points_study'] = [
          'Rob Booker - נקודות פיווט שפוספסו',
        ]),
        (t.exports['Rob Booker - Reversal_study'] = ['Rob Booker - ריברסל']),
        (t.exports['Rob Booker - Ziv Ghost Pivots_study'] = [
          'Rob Booker - Ziv Ghost פיווט',
        ]),
        (t.exports.Supertrend_study = ['סופר טרנד']),
        (t.exports['Technical Ratings_study'] = ['דירוגים טכניים']),
        (t.exports['True Strength Index_study'] = ['מדד החוזק האמיתי']),
        (t.exports['Up/Down Volume_study'] = ['ווליום עולה/יורד']),
        (t.exports['Visible Average Price_study'] = ['מחיר ממוצע גלוי']),
        (t.exports['Williams Fractals_study'] = [
          'פרקטלס של וויליאמס Fractals',
        ]),
        (t.exports['Keltner Channels Strategy_study'] = [
          'אסטרטגיית ערוצי קלטנר',
        ]),
        (t.exports['Rob Booker - ADX Breakout_study'] = [
          'Rob Booker - פריצת ADX',
        ]),
        (t.exports['Supertrend Strategy_study'] = ['אסטרטגיית סופר טרנד']),
        (t.exports['Technical Ratings Strategy_study'] = [
          'אסטרטגיית דירוגים טכניים',
        ]),
        (t.exports['Auto Anchored Volume Profile_study'] = [
          'פרופיל ווליום מעוגן אוטומטי',
        ]),
        (t.exports['Auto Fib Extension_study'] = ["הרחבת פיבונאצ'י אוטומטית"]),
        (t.exports['Auto Fib Retracement_study'] = [
          'תיקוני פיבונאצי אוטומטיים',
        ]),
        (t.exports['Auto Pitchfork_study'] = ['Pitchfork אוטומטי']),
        (t.exports['Bearish Flag Chart Pattern_study'] = [
          'תבנית גרף דגל דובי',
        ]),
        (t.exports['Bullish Pennant Chart Pattern_study'] = [
          'תבנית גרף דגל שוורי',
        ]),
        (t.exports['Double Bottom Chart Pattern_study'] = [
          'תבנית גרף תחתית כפולה',
        ]),
        (t.exports['Double Top Chart Pattern_study'] = [
          'תבנית גרף תקרה כפולה',
        ]),
        (t.exports['Elliott Wave Chart Pattern_study'] = [
          'תבנית גרף גלי אליוט',
        ]),
        (t.exports['Falling Wedge Chart Pattern_study'] = [
          'תבנית גרף טריז נופל',
        ]),
        (t.exports['Head And Shoulders Chart Pattern_study'] = [
          'תבנית גרף ראש וכתפיים',
        ]),
        (t.exports['Inverse Head And Shoulders Chart Pattern_study'] = [
          'תבנית גרף ראש וכתפיים הפוכה',
        ]),
        (t.exports['Rectangle Chart Pattern_study'] = ['תבנית גרף מלבן']),
        (t.exports['Rising Wedge Chart Pattern_study'] = [
          'תבנית גרף טריז עולה',
        ]),
        (t.exports['Triangle Chart Pattern_study'] = ['תבנית גרף משולש']),
        (t.exports['Triple Bottom Chart Pattern_study'] = [
          'תבנית גרף משולש תחתון',
        ]),
        (t.exports['Triple Top Chart Pattern_study'] = [
          'תבנית גרף משולשת עליונה',
        ]),
        (t.exports['VWAP Auto Anchored_study'] = ['VWAP מעוגן אוטומטי']),
        (t.exports['*All Candlestick Patterns*_study'] = [
          '*כל דפוסי הנרות היפניים*',
        ]),
        (t.exports['Abandoned Baby - Bearish_study'] = ['תינוק נטוש - דובי']),
        (t.exports['Abandoned Baby - Bullish_study'] = ['תינוק נטוש - שוורי']),
        (t.exports['Dark Cloud Cover - Bearish_study'] = [
          'כיסוי ענן כהה - דובי',
        ]),
        (t.exports['Doji Star - Bearish_study'] = ["כוכב דוג'י - דובי"]),
        (t.exports['Doji Star - Bullish_study'] = ["כוכב דוג'י - שוורי"]),
        (t.exports['Downside Tasuki Gap - Bearish_study'] = [
          'Tasuki גאפ למטה - דובי',
        ]),
        (t.exports['Dragonfly Doji - Bullish_study'] = [
          "דרגון פליי Dragonfly דוג'י - שוורי",
        ]),
        (t.exports['Engulfing - Bearish_study'] = ['בולען - דובי']),
        (t.exports['Engulfing - Bullish_study'] = ['בולען - שוורי']),
        (t.exports['Evening Doji Star - Bearish_study'] = [
          "כוכב ערב דוג'י - דובי",
        ]),
        (t.exports['Evening Star - Bearish_study'] = ['כוכב ערב - דובי']),
        (t.exports['Falling Three Methods - Bearish_study'] = [
          'Falling Three Methods - דובי',
        ]),
        (t.exports['Falling Window - Bearish_study'] = [
          'Falling Window - דובי',
        ]),
        (t.exports['Gravestone Doji - Bearish_study'] = [
          "Gravestone דוג'י - שוורי",
        ]),
        (t.exports['Hammer - Bullish_study'] = ['פטיש - שוורי']),
        (t.exports['Hanging Man - Bearish_study'] = ['איש תלוי - דובי']),
        (t.exports['Harami - Bearish_study'] = ['Harami - דובי']),
        (t.exports['Harami - Bullish_study'] = ['Harami - שוורי']),
        (t.exports['Inverted Hammer - Bullish_study'] = ['פטיש הפוך - שוורי']),
        (t.exports['Kicking - Bearish_study'] = ['Kicking - דובי']),
        (t.exports['Kicking - Bullish_study'] = ['Kicking - שוורי']),
        (t.exports['Long Lower Shadow - Bullish_study'] = [
          'Long Lower Shadow - שוורי',
        ]),
        (t.exports['Long Upper Shadow - Bearish_study'] = [
          'Long Upper Shadow - דובי',
        ]),
        (t.exports['Marubozu Black - Bearish_study'] = [
          'מארובוזו שחור - דובי',
        ]),
        (t.exports['Marubozu White - Bullish_study'] = [
          'מארובוזו לבן - שוורי',
        ]),
        (t.exports['Morning Doji Star - Bullish_study'] = [
          "כוכב בקר דוג'י - שוורי",
        ]),
        (t.exports['Morning Star - Bullish_study'] = ['כוכב בקר - שוורי']),
        (t.exports['On Neck - Bearish_study'] = ['On Neck על הצוואר - דובי']),
        (t.exports['Piercing - Bullish_study'] = ['Piercing - שוורי']),
        (t.exports['Rising Three Methods - Bullish_study'] = [
          'Rising Three Methods - שוורי',
        ]),
        (t.exports['Rising Window - Bullish_study'] = [
          'Rising Window - שוורי',
        ]),
        (t.exports['Shooting Star - Bearish_study'] = ['Shooting Star - דובי']),
        (t.exports['Three Black Crows - Bearish_study'] = [
          'שלושה עורבים שחורים - דובי',
        ]),
        (t.exports['Three White Soldiers - Bullish_study'] = [
          'שלושה חיילים לבנים - שוורי',
        ]),
        (t.exports['Tri-Star - Bearish_study'] = ['Tri-Star - דובי']),
        (t.exports['Tri-Star - Bullish_study'] = ['Tri-Star - שוורי']),
        (t.exports['Tweezer Top - Bearish_study'] = [
          'Tweezer Top פינצטה טופ - דובי',
        ]),
        (t.exports['Upside Tasuki Gap - Bullish_study'] = [
          'Upside Tasuki Gap - שוורי',
        ]),
        (t.exports.SuperTrend_study = ['מגמת-על']),
        (t.exports['Average Price_study'] = ['מחיר ממוצע']),
        (t.exports['Typical Price_study'] = ['מחיר אופייני']),
        (t.exports['Median Price_study'] = ['מחיר חציון']),
        (t.exports['Money Flow Index_study'] = ['מדד זרימת כסף MFI']),
        (t.exports['Moving Average Double_study'] = ['ממוצע נע כפול']),
        (t.exports['Moving Average Triple_study'] = ['ממוצע נע משולש']),
        (t.exports['Moving Average Adaptive_study'] = ['ממוצע נע מסתגל']),
        (t.exports['Moving Average Hamming_study'] = [
          'ממוצע נע משוקלל Hamming',
        ]),
        (t.exports['Moving Average Modified_study'] = [
          'ממוצע נע מותאם Modified',
        ]),
        (t.exports['Moving Average Multiple_study'] = [
          'ממוצע נע מרובה Multiple',
        ]),
        (t.exports['Linear Regression Slope_study'] = [
          'רגרסיה לינארית משופעת',
        ]),
        (t.exports['Standard Error_study'] = ['שגיאת תקן']),
        (t.exports['Standard Error Bands_study'] = 'Standard Error Bands'),
        (t.exports['Correlation - Log_study'] = ['מתאם - התחברות']),
        (t.exports['Standard Deviation_study'] = ['סטיית תקן']),
        (t.exports['Chaikin Volatility_study'] = ['תנודתיות הייקין']),
        (t.exports['Volatility Close-to-Close_study'] = [
          'תנודתיות Close-to-Close',
        ]),
        (t.exports['Volatility Zero Trend Close-to-Close_study'] = [
          'תנודתיות מגמה אפס קרוב לסגירה',
        ]),
        (t.exports['Volatility O-H-L-C_study'] = ['תנודתיות O-H-L-C']),
        (t.exports['Volatility Index_study'] = ['מדד תנודתיות']),
        (t.exports['Trend Strength Index_study'] = ['מדד עוצמת מגמה']),
        (t.exports['Majority Rule_study'] = 'Majority Rule'),
        (t.exports['Advance Decline Line_study'] = ['קו ירידה מתקדם']),
        (t.exports['Advance Decline Ratio_study'] = [
          'Advance Decline Ratio ADR',
        ]),
        (t.exports['Advance/Decline Ratio (Bars)_study'] = [
          'Advance Decline Ratio ADR (ברים)',
        ]),
        (t.exports['BarUpDn Strategy_study'] = ['אסטרטגיית BarUpDn']),
        (t.exports['Bollinger Bands Strategy directed_study'] = [
          "אסטרטגיית רצועת בולינג'ר מכוון",
        ]),
        (t.exports['Bollinger Bands Strategy_study'] = [
          "אסטרטגית רצועות בולינג'ר",
        ]),
        (t.exports.ChannelBreakOutStrategy_study = ['אסטרטגיית-פריצת-ערוץ']),
        (t.exports.Compare_study = ['השוואה']),
        (t.exports['Conditional Expressions_study'] = ['ביטויים מותנים']),
        (t.exports.ConnorsRSI_study = 'ConnorsRSI'),
        (t.exports['Consecutive Up/Down Strategy_study'] = [
          'אסטרטגיית מעלה/מטה רציפה',
        ]),
        (t.exports['Cumulative Volume Index_study'] = ['מדד ווליום מצטבר']),
        (t.exports['Divergence Indicator_study'] = ['אינדיקטור הסתעפות']),
        (t.exports['Greedy Strategy_study'] = ['אסטרטגיה חמדנית']),
        (t.exports['InSide Bar Strategy_study'] = ['אסטרטגיית תוך-נר']),
        (t.exports['Keltner Channel Strategy_study'] = [
          'אסטרטגיית ערוצי קלנר',
        ]),
        (t.exports['Linear Regression_study'] = ['רגרסיה ליניארית']),
        (t.exports['MACD Strategy_study'] = ['אסטרטגיית MACD']),
        (t.exports['Momentum Strategy_study'] = ['אסטרטגיית מומנטום']),
        (t.exports['Moon Phases_study'] = ['מחזורי הירח']),
        (t.exports['Moving Average Convergence/Divergence_study'] = [
          'ממוצע נע התכנסות / סטייה',
        ]),
        (t.exports['MovingAvg Cross_study'] = ['ממוצע-נע חוצה']),
        (t.exports['MovingAvg2Line Cross_study'] = ['MovingAvg2Line חוצה']),
        (t.exports['OutSide Bar Strategy_study'] = ['אסטרטגיית מחוץ-לנר']),
        (t.exports.Overlay_study = ['שכבה']),
        (t.exports['Parabolic SAR Strategy_study'] = [
          'אסטרטגיית Parabolic SAR',
        ]),
        (t.exports['Pivot Extension Strategy_study'] = [
          'אסטרטגיית הרחבת פיבוט',
        ]),
        (t.exports['Pivot Points High Low_study'] = ['נקודות פיבוט גבוה נמוך']),
        (t.exports['Pivot Reversal Strategy_study'] = [
          'אסטרטגיית פיבוט ריוורסל',
        ]),
        (t.exports['Price Channel Strategy_study'] = ['אסטרטגיית ערוץ מחיר']),
        (t.exports['RSI Strategy_study'] = ['אסטרטגיית RSI']),
        (t.exports['SMI Ergodic Indicator_study'] = ['מחוון SMI ארגודי']),
        (t.exports['SMI Ergodic Oscillator_study'] = ['מתנד SMI ארגודי']),
        (t.exports['Stochastic Slow Strategy_study'] = [
          'אסטרטגיית סטוקסטית איטית',
        ]),
        (t.exports['Volatility Stop_study'] = ['סטופ תנודתי']),
        (t.exports['Volty Expan Close Strategy_study'] = [
          'סגור אסטרטגיית Volty Expan',
        ]),
        (t.exports['Woodies CCI_study'] = ['וודי CCI']);
    },
    40434: (t) => {
      t.exports = ['פרופיל ווליום טווח קבוע'];
    },
    32819: (t) => {
      t.exports = ['מחזור'];
    },
    66051: (t) => {
      t.exports = ['מינורי'];
    },
    86054: (t) => {
      t.exports = ['דקה'];
    },
    20936: (t) => {
      t.exports = ['טקסט'];
    },
    98478: (t) => {
      t.exports = ['לא ניתן היה להעתיק'];
    },
    34004: (t) => {
      t.exports = ['לא הצליח לגזור'];
    },
    96260: (t) => {
      t.exports = ['לא ניתן היה להדביק'];
    },
    94370: (t) => {
      t.exports = ['ספירה לאחור עד סגירת הנר'];
    },
    15168: (t) => {
      t.exports = ['קולומבו'];
    },
    36018: (t) => {
      t.exports = ['עמודות'];
    },
    19372: (t) => {
      t.exports = ['הערה'];
    },
    20229: (t) => {
      t.exports = ['השווה או הוסף סימול'];
    },
    46689: (t) => {
      t.exports = ['אשר קלט'];
    },
    43432: (t) => {
      t.exports = ['קופנהגן'];
    },
    35216: (t) => {
      t.exports = ['העתק'];
    },
    87898: (t) => {
      t.exports = ['העתק פריסת גרף'];
    },
    28851: (t) => {
      t.exports = ['העתק מחיר'];
    },
    94099: (t) => {
      t.exports = ['קהיר'];
    },
    64149: (t) => {
      t.exports = ['הסבר'];
    },
    63528: (t) => {
      t.exports = ['נרות יפנים'];
    },
    46837: (t) => {
      t.exports = ['קראקס'];
    },
    49329: (t) => {
      t.exports = ['שינוי'];
    },
    28089: (t) => {
      t.exports = ['שנה סימול'];
    },
    99374: (t) => {
      t.exports = ['שנה אינטרוול זמן'];
    },
    14412: (t) => {
      t.exports = ['מאפייני גרף'];
    },
    26619: (t) => {
      t.exports = ['גרף מאת TradingView'];
    },
    12011: (t) => {
      t.exports = ['תמונת הגרף הועתקה ללוח {emoji}'];
    },
    59884: (t) => {
      t.exports = ["איי צ'טהאם"];
    },
    28244: (t) => {
      t.exports = ['שיקגו'];
    },
    49648: (t) => {
      t.exports = ["צ'ונגצ'ינג"];
    },
    90068: (t) => {
      t.exports = ['מעגל'];
    },
    32234: (t) => {
      t.exports = ['לחץ על מנת לקבוע נקודה'];
    },
    52977: (t) => {
      t.exports = ['שכפל'];
    },
    31691: (t) => {
      t.exports = ['סגירה'];
    },
    50493: (t) => {
      t.exports = ['צור הוראה'];
    },
    52302: (t) => {
      t.exports = ['צור פקודת לימיט'];
    },
    29908: (t) => {
      t.exports = ['צלב'];
    },
    60997: (t) => {
      t.exports = ['קו חוצה'];
    },
    81520: (t) => {
      t.exports = ['מט"ח'];
    },
    98486: (t) => {
      t.exports = ['אינטרוול  נוכחי ומעלה'];
    },
    73106: (t) => {
      t.exports = ['אינטרוול נוכחי ומטה'];
    },
    85964: (t) => {
      t.exports = ['אינטרוול נוכחי בלבד'];
    },
    17206: (t) => {
      t.exports = ['עיקול'];
    },
    95176: (t) => {
      t.exports = ['מחזור'];
    },
    87761: (t) => {
      t.exports = ['קווים מחזוריים'];
    },
    27891: (t) => {
      t.exports = ['תבנית סייפר'];
    },
    56996: (t) => {
      t.exports = ['פריסה בשם זה כבר קיימת'];
    },
    30192: (t) => {
      t.exports = ['פריסה בשם זה כבר קיימת. האם אתה רוצה להחליף אותו?'];
    },
    32852: (t) => {
      t.exports = ['דפוס ABCD'];
    },
    88010: (t) => {
      t.exports = ['אמסטרדם'];
    },
    37422: (t) => {
      t.exports = ['נתח את מערך המסחר'];
    },
    66828: (t) => {
      t.exports = ['הערה מעוגנת'];
    },
    94782: (t) => {
      t.exports = ['טקסט מעוגן'];
    },
    61704: (t) => {
      t.exports = ['VWAP מעוגן'];
    },
    63597: (t) => {
      t.exports = ['הוסף קו אופקי'];
    },
    45743: (t) => {
      t.exports = ['הוסף סימול'];
    },
    8700: (t) => {
      t.exports = ['הוסף התראה'];
    },
    64885: (t) => {
      t.exports = ['הוסף התראה על ‎{drawing}‎'];
    },
    90830: (t) => {
      t.exports = ['הוסף התראה על ‎{series}‎'];
    },
    45986: (t) => {
      t.exports = ['הוסף התראה עבור ‎{title}‎'];
    },
    3612: (t) => {
      t.exports = ['הוסף ערך פיננסי עבור {instrumentName}'];
    },
    92206: (t) => {
      t.exports = ['הוסף אינדיקטור/אסטרטגיה ב- {studyTitle}'];
    },
    34810: (t) => {
      t.exports = ['הוסף הערת טקסט  ל-{symbol}'];
    },
    75669: (t) => {
      t.exports = ['הוסף מדד פיננסי זה לפריסה שלמה'];
    },
    64288: (t) => {
      t.exports = ['הוסף אינדיקטור זה לפריסה שלמה'];
    },
    77920: (t) => {
      t.exports = ['הוסף אסטרטגיה זו לפריסה שלמה'];
    },
    34059: (t) => {
      t.exports = ['הוסף סימול זה לפריסה שלמה'];
    },
    17365: (t) => {
      t.exports = ['אדלייד'];
    },
    9408: (t) => {
      t.exports = ['תמיד בלתי נראה'];
    },
    71997: (t) => {
      t.exports = ['תמיד גלוי'];
    },
    97305: (t) => {
      t.exports = ['כל האינדיקטורים וכלי הציור'];
    },
    59192: (t) => {
      t.exports = ['כל האינטרוולים'];
    },
    14452: (t) => {
      t.exports = ['אלמטי'];
    },
    5716: (t) => {
      t.exports = ['החל גלי אליוט'];
    },
    19263: (t) => {
      t.exports = ['החל גל אליוט עיקרי'];
    },
    15818: (t) => {
      t.exports = ['החל גלי אליוט קטן'];
    },
    50352: (t) => {
      t.exports = ['החל גלי אליוט טווח בינוני'];
    },
    66631: (t) => {
      t.exports = ['החל נקודת החלטה ידנית'];
    },
    15682: (t) => {
      t.exports = ['החל סיכון/סיכוי ידני'];
    },
    15644: (t) => {
      t.exports = ['החל גל WPT כלפי מטה'];
    },
    5897: (t) => {
      t.exports = ['החל גל WPT כלפי מעלה'];
    },
    13345: (t) => {
      t.exports = ['החל ברירת מחדל'];
    },
    95910: (t) => {
      t.exports = ['החל אינדיקטורים אלה על כל הפריסה'];
    },
    42762: (t) => {
      t.exports = ["אפר'"];
    },
    45104: (t) => {
      t.exports = ['קשת'];
    },
    42097: (t) => {
      t.exports = ['אזור'];
    },
    96237: (t) => {
      t.exports = ['חץ'];
    },
    48732: (t) => {
      t.exports = ['חץ למטה'];
    },
    82473: (t) => {
      t.exports = ['סמן חץ'];
    },
    8738: (t) => {
      t.exports = ['חץ למטה'];
    },
    35062: (t) => {
      t.exports = ['חץ שמאלה'];
    },
    92163: (t) => {
      t.exports = ['חץ ימינה'];
    },
    33196: (t) => {
      t.exports = ['חץ למעלה'];
    },
    10650: (t) => {
      t.exports = ['חץ למעלה'];
    },
    59340: (t) => {
      t.exports = ['אשחבד'];
    },
    13468: (t) => {
      t.exports = ['מקרוב'];
    },
    21983: (t) => {
      t.exports = ['אתונה'];
    },
    86951: (t) => {
      t.exports = ["אוטו'"];
    },
    50834: (t) => {
      t.exports = ["אוטו' (מתאים נתונים למסך)"];
    },
    38465: (t) => {
      t.exports = ["אוג'"];
    },
    8975: (t) => {
      t.exports = ['תווית מחיר ממוצעת קרובה'];
    },
    87899: (t) => {
      t.exports = ['קו מחיר סגירה ממוצע'];
    },
    22554: (t) => {
      t.exports = ['Avg ממוצע'];
    },
    54173: (t) => {
      t.exports = ['בוגוטה'];
    },
    53260: (t) => {
      t.exports = ['בחריין'];
    },
    40664: (t) => {
      t.exports = ['בלון'];
    },
    32376: (t) => {
      t.exports = ['בנגקוק'];
    },
    19149: (t) => {
      t.exports = [
        'הפעלת הפעלה חוזרת אינה זמינה עבור סוג גרף זה. האם ברצונך לצאת מהפעלה חוזרת?',
      ];
    },
    16812: (t) => {
      t.exports = ['גרף עמודות'];
    },
    98838: (t) => {
      t.exports = ['תבנית נרות'];
    },
    17712: (t) => {
      t.exports = ['קו -בסיס'];
    },
    54861: (t) => {
      t.exports = ['בלגרד'];
    },
    26825: (t) => {
      t.exports = ['ברלין'];
    },
    30251: (t) => {
      t.exports = ['מברשת'];
    },
    90204: (t) => {
      t.exports = ['בריסל'];
    },
    5262: (t) => {
      t.exports = ['ברטיסלבה'];
    },
    59901: (t) => {
      t.exports = ['הצג מקדימה'];
    },
    26354: (t) => {
      t.exports = ['הבא לחזית'];
    },
    11741: (t) => {
      t.exports = ['בריסביין'];
    },
    37728: (t) => {
      t.exports = ['בוקרשט'];
    },
    87143: (t) => {
      t.exports = ['בודפשט'];
    },
    82446: (t) => {
      t.exports = ['בואנוס איירס'];
    },
    82128: (t) => {
      t.exports = ['מאת TradingView'];
    },
    75190: (t) => {
      t.exports = ['עבור לתאריך'];
    },
    38342: (t) => {
      t.exports = ['עבור אל {lineToolName}'];
    },
    75139: (t) => {
      t.exports = ['הבנתי'];
    },
    81180: (t) => {
      t.exports = ['קופסת גאן'];
    },
    68102: (t) => {
      t.exports = ['מניפת גאן'];
    },
    66321: (t) => {
      t.exports = ['מרובע גאן'];
    },
    87107: (t) => {
      t.exports = ['רבוע Gann קבוע'];
    },
    34805: (t) => {
      t.exports = ['השג עוד חיבורים'];
    },
    7914: (t) => {
      t.exports = ['הזנת צל'];
    },
    18367: (t) => {
      t.exports = ['סופר גל גדול'];
    },
    97065: (t) => {
      t.exports = ["האם אתה בטוח שברצונך למחוק את תבנית המחקר '{name}'?"];
    },
    59368: (t) => {
      t.exports = ['עיקול כפול'];
    },
    35273: (t) => {
      t.exports = ['לחץ פעמיים על קצה כלשהו כדי לאפס את רשת הפריסה'];
    },
    5828: (t) => {
      t.exports = ['לחץ פעמיים כדי לסיים את הנתיב'];
    },
    63898: (t) => {
      t.exports = ['לחץ פעמיים כדי לסיים את Polyline'];
    },
    42660: (t) => {
      t.exports = ['גל ירידה 1 או A'];
    },
    44788: (t) => {
      t.exports = ['גל ירידה 2 או B'];
    },
    71263: (t) => {
      t.exports = ['גל ירידה 3'];
    },
    70573: (t) => {
      t.exports = ['גל ירידה 4'];
    },
    59560: (t) => {
      t.exports = ['גל ירידה 5'];
    },
    70437: (t) => {
      t.exports = ['גל ירידה C'];
    },
    93345: (t) => {
      t.exports = ['הנתונים מסופקים על ידי'];
    },
    76912: (t) => {
      t.exports = ['תאריך'];
    },
    60222: (t) => {
      t.exports = ['טווח תאריכים'];
    },
    79859: (t) => {
      t.exports = ['תאריך וטווח מחירים'];
    },
    92203: (t) => {
      t.exports = ["דצמ'"];
    },
    69479: (t) => {
      t.exports = ['מידה'];
    },
    57701: (t) => {
      t.exports = ['דנוור'];
    },
    73720: (t) => {
      t.exports = ['יהלום'];
    },
    3556: (t) => {
      t.exports = ['ערוץ משותף'];
    },
    62764: (t) => {
      t.exports = ['שינוי מיקום'];
    },
    22903: (t) => {
      t.exports = ['סרגל כלי ציור'];
    },
    21442: (t) => {
      t.exports = ['צייר קו אופקי'];
    },
    22429: (t) => {
      t.exports = ['דובאי'];
    },
    9497: (t) => {
      t.exports = ['דבלין'];
    },
    85223: (t) => {
      t.exports = ["אימוג'י"];
    },
    24435: (t) => {
      t.exports = ['הכנס שם חדש לפריסת הגרף'];
    },
    93512: (t) => {
      t.exports = ['ערוך התראה {title}'];
    },
    91215: (t) => {
      t.exports = ['תיקון גל אליוט (ABC)'];
    },
    80983: (t) => {
      t.exports = ['גלי אליוט קומבו כפול (WXY)'];
    },
    74118: (t) => {
      t.exports = ['גלי אליוט אימפולס (12345)'];
    },
    95840: (t) => {
      t.exports = ['משולש גלי אליוט (ABCDE)'];
    },
    66637: (t) => {
      t.exports = ['גל אליוט קומבו-משולש (WXYXZ)'];
    },
    69418: (t) => {
      t.exports = ['אליפסה'];
    },
    27558: (t) => {
      t.exports = ['הרחב קווי התראה'];
    },
    2578: (t) => {
      t.exports = ['קו מורחב'];
    },
    77295: (t) => {
      t.exports = ['בורסה'];
    },
    2899: (t) => {
      t.exports = ['חלון הנמצא מעל'];
    },
    53387: (t) => {
      t.exports = ['חלון הנמצא מתחת'];
    },
    36972: (t) => {
      t.exports = ['תחזית'];
    },
    17994: (t) => {
      t.exports = ['שמירת הספרייה נכשלה'];
    },
    87375: (t) => {
      t.exports = ['שמירת הסקריפט נכשלה'];
    },
    35050: (t) => {
      t.exports = ["פבר'"];
    },
    82719: (t) => {
      t.exports = ["ערוץ פיבונאצ'י"];
    },
    64192: (t) => {
      t.exports = ["מעגלי פיבונאצ'י"];
    },
    63835: (t) => {
      t.exports = ["תיקוני פיבונאצ'י"];
    },
    18072: (t) => {
      t.exports = ["קשתות התנגדות מהירות פיבונאצ'י"];
    },
    20877: (t) => {
      t.exports = ["מניפת התנגדות למהירות פיבונאצ'י"];
    },
    76783: (t) => {
      t.exports = ["ספירלת פיבונאצ'י"];
    },
    89037: (t) => {
      t.exports = ["אזור זמן פיבונאצ'י"];
    },
    72489: (t) => {
      t.exports = ["טריז פיבונאצ'י"];
    },
    21524: (t) => {
      t.exports = ['דגל'];
    },
    55678: (t) => {
      t.exports = ['סמן בדגל'];
    },
    29230: (t) => {
      t.exports = ['שיא/שפל חלק'];
    },
    92754: (t) => {
      t.exports = ['הפוך במאוזן'];
    },
    42015: (t) => {
      t.exports = ['חלק שבור אינו חוקי'];
    },
    47542: (t) => {
      t.exports = ['מחקרים פונדמנטאליים אינם זמינים עוד על הגרף'];
    },
    16245: (t) => {
      t.exports = ['כלכותה'];
    },
    3155: (t) => {
      t.exports = ['קטמנדו'];
    },
    92901: (t) => {
      t.exports = ['גרף קאגי'];
    },
    2693: (t) => {
      t.exports = ["קראצ'י"];
    },
    72374: (t) => {
      t.exports = ['כווית'];
    },
    87338: (t) => {
      t.exports = ["הו צ'י מין"];
    },
    61582: (t) => {
      t.exports = ['נרות חלולים'];
    },
    32918: (t) => {
      t.exports = ['הונג קונג'];
    },
    61351: (t) => {
      t.exports = ['הונולולו'];
    },
    60049: (t) => {
      t.exports = ['קו אופקי'];
    },
    76604: (t) => {
      t.exports = ['קרן אופקית'];
    },
    42616: (t) => {
      t.exports = ['ראש וכתפיים'];
    },
    40530: (t) => {
      t.exports = ['הייקין אשי'];
    },
    99820: (t) => {
      t.exports = ['הלסינקי'];
    },
    31971: (t) => {
      t.exports = ['הסתר'];
    },
    33911: (t) => {
      t.exports = ['הסתר הכל'];
    },
    95551: (t) => {
      t.exports = ['הסתר את כל כלי השרטוט'];
    },
    44312: (t) => {
      t.exports = ['הסתר את כל השרטוטים והאינדיקטורים'];
    },
    67927: (t) => {
      t.exports = ['הסתר את כל השרטוטים, האינדיקטורים, הפוזיציות והפקודות'];
    },
    86306: (t) => {
      t.exports = ['הסתר את כל האינדיקטורים'];
    },
    70803: (t) => {
      t.exports = ['הסתר את כל הפוזיציות והפקודות'];
    },
    13277: (t) => {
      t.exports = ['הסתר שרטוטים'];
    },
    8251: (t) => {
      t.exports = ['הסתר אירועים על הגרף'];
    },
    44177: (t) => {
      t.exports = ['הסתר אינדיקטורים'];
    },
    2441: (t) => {
      t.exports = ['הסתר סימנים על הנרות'];
    },
    90540: (t) => {
      t.exports = ['הסתר פוזיציות ופקודות'];
    },
    30777: (t) => {
      t.exports = ['גבוה'];
    },
    31994: (t) => {
      t.exports = ['גבוה-נמוך'];
    },
    60259: (t) => {
      t.exports = ['תוויות מחיר גבוה ונמוך'];
    },
    21803: (t) => {
      t.exports = ['קווי מחיר גבוה ונמוך'];
    },
    31895: (t) => {
      t.exports = ['מרקר מדגיש'];
    },
    69085: (t) => {
      t.exports = ['ההיסטוגרמה גדולה מדי, נא להגדיל את הקלט "גודל שורה".'];
    },
    8122: (t) => {
      t.exports = ["ההיסטוגרמה גדולה מדי, אנא צמצם את הקלט 'גודל שורה'."];
    },
    22712: (t) => {
      t.exports = ['אני בסדר תודה'];
    },
    23450: (t) => {
      t.exports = ['תמונה'];
    },
    71778: (t) => {
      t.exports = ['ביניים'];
    },
    14177: (t) => {
      t.exports = ['סימול לא קיים'];
    },
    32619: (t) => {
      t.exports = ['סימול שגוי'];
    },
    53239: (t) => {
      t.exports = ['הפוך גרף'];
    },
    20062: (t) => {
      t.exports = ['צמוד ל-100'];
    },
    81584: (t) => {
      t.exports = ['תוויות ערך של אינדיקטורים'];
    },
    31485: (t) => {
      t.exports = ['שמות תוויות של אינדיקטורים'];
    },
    27677: (t) => {
      t.exports = ['קו מידע'];
    },
    98767: (t) => {
      t.exports = ['הוסף אינדיקטור'];
    },
    9114: (t) => {
      t.exports = ['בתוך'];
    },
    12354: (t) => {
      t.exports = ['קילשון פנימי'];
    },
    26579: (t) => {
      t.exports = ['אייקון'];
    },
    37885: (t) => {
      t.exports = ['איסטנבול'];
    },
    87469: (t) => {
      t.exports = ['יוהנסבורג'];
    },
    52707: (t) => {
      t.exports = ["ג'קרטה"];
    },
    95425: (t) => {
      t.exports = ["ינו'"];
    },
    42890: (t) => {
      t.exports = ['ירושלים'];
    },
    6215: (t) => {
      t.exports = ["יול'"];
    },
    15224: (t) => {
      t.exports = ["יונ'"];
    },
    36253: (t) => {
      t.exports = ["ג'ונו"];
    },
    15241: (t) => {
      t.exports = ['משמאל'];
    },
    29404: (t) => {
      t.exports = ['מימין'];
    },
    850: (t) => {
      t.exports = ['אופס!'];
    },
    675: (t) => {
      t.exports = ['עץ אובייקטים'];
    },
    73546: (t) => {
      t.exports = ["אוק'"];
    },
    39280: (t) => {
      t.exports = ['פתיחה'];
    },
    25595: (t) => {
      t.exports = ['מקורי'];
    },
    82906: (t) => {
      t.exports = 'Oslo';
    },
    8136: (t) => {
      t.exports = ['נמוך'];
    },
    42284: (t) => {
      t.exports = ['נעל'];
    },
    1441: (t) => {
      t.exports = ['פתח/סגור'];
    },
    82232: (t) => {
      t.exports = ['נעל קו סמן אנכי לפי זמן'];
    },
    18219: (t) => {
      t.exports = ['נעל יחס מחיר לנר'];
    },
    12285: (t) => {
      t.exports = ['לוגריתמי'];
    },
    50286: (t) => {
      t.exports = ['לונדון'];
    },
    44604: (t) => {
      t.exports = ['עסקת לונג'];
    },
    87604: (t) => {
      t.exports = ["לוס אנג'לס"];
    },
    18528: (t) => {
      t.exports = ['תווית למטה'];
    },
    13046: (t) => {
      t.exports = ['תווית למעלה'];
    },
    94420: (t) => {
      t.exports = ['תוויות'];
    },
    89155: (t) => {
      t.exports = ['לאגוס'];
    },
    25846: (t) => {
      t.exports = ['לימה'];
    },
    1277: (t) => {
      t.exports = ['קו'];
    },
    63492: (t) => {
      t.exports = ['מקטע קו'];
    },
    83182: (t) => {
      t.exports = ['קווים'];
    },
    78104: (t) => {
      t.exports = ['קישור לתמונת הגרף שהועתק ללוח {emoji}'];
    },
    50091: (t) => {
      t.exports = ['ליסבון'];
    },
    64352: (t) => {
      t.exports = ['לוקסמבורג'];
    },
    11156: (t) => {
      t.exports = ['תוכנת מסחר MTPredictor'];
    },
    67861: (t) => {
      t.exports = ['הזז את הנקודה למיקום העוגן ולאחר מכן הקש כדי למקם'];
    },
    45828: (t) => {
      t.exports = ['הזז ל..'];
    },
    44302: (t) => {
      t.exports = ['הזז קנה מידה לשמאל'];
    },
    94338: (t) => {
      t.exports = ['הזז קנה מידה לימין'];
    },
    66276: (t) => {
      t.exports = ['שיף מותאם'];
    },
    18559: (t) => {
      t.exports = ['קילשון שיף מותאם'];
    },
    18665: (t) => {
      t.exports = ['מוסקבה'];
    },
    58038: (t) => {
      t.exports = ['מדריד'];
    },
    34190: (t) => {
      t.exports = ['מלטה'];
    },
    90271: (t) => {
      t.exports = ['מנילה'];
    },
    51369: (t) => {
      t.exports = ['מרץ'];
    },
    85095: (t) => {
      t.exports = ['מקסיקו סיטי'];
    },
    75633: (t) => {
      t.exports = ['מזג את כל סולמות המחיר לאחד'];
    },
    95093: (t) => {
      t.exports = ['מעורב'];
    },
    10931: (t) => {
      t.exports = ['מיקרו'];
    },
    58397: (t) => {
      t.exports = ['מילניום'];
    },
    85884: (t) => {
      t.exports = ['מינוט (Minuette)'];
    },
    9632: (t) => {
      t.exports = ['זעיר'];
    },
    63158: (t) => {
      t.exports = ['הפוך במאונך'];
    },
    42769: (t) => {
      t.exports = ['מוסקט'];
    },
    43088: (t) => {
      t.exports = 'N/A';
    },
    95222: (t) => {
      t.exports = ['אין כאן נתונים'];
    },
    3485: (t) => {
      t.exports = ['ללא קנה מידה (מסך-מלא)'];
    },
    8886: (t) => {
      t.exports = ['אין סנכרון'];
    },
    16971: (t) => {
      t.exports = ['אין נתוני נפח'];
    },
    75549: (t) => {
      t.exports = ['הערה'];
    },
    71230: (t) => {
      t.exports = ["נוב'"];
    },
    99203: (t) => {
      t.exports = ['אי נורפולק'];
    },
    79023: (t) => {
      t.exports = ['ניירובי'];
    },
    91203: (t) => {
      t.exports = ['ניו יורק'];
    },
    24143: (t) => {
      t.exports = ['ניו זילנד‏'];
    },
    40887: (t) => {
      t.exports = ['חלון חדש מעל'];
    },
    96712: (t) => {
      t.exports = ['חלון חדש מתחת'];
    },
    33566: (t) => {
      t.exports = ['ניקוסיה'];
    },
    64968: (t) => {
      t.exports = ['משהו השתבש. בבקשה נסה שוב מאוחר יותר.'];
    },
    10520: (t) => {
      t.exports = ['שמור פריסת גרף חדשה'];
    },
    9908: (t) => {
      t.exports = ['שמור בשם'];
    },
    68553: (t) => {
      t.exports = ['סן סלבדור'];
    },
    65412: (t) => {
      t.exports = ['סנטיאגו'];
    },
    13538: (t) => {
      t.exports = ['סאו פאולו'];
    },
    37207: (t) => {
      t.exports = ['הרחב סולם מחיר בלבד'];
    },
    51464: (t) => {
      t.exports = ['שיף'];
    },
    98114: (t) => {
      t.exports = ['קילשון שיף'];
    },
    1535: (t) => {
      t.exports = ['הסקריפט לט יתעדכן אם תעזוב את הדף.'];
    },
    89517: (t) => {
      t.exports = ['הגדרות'];
    },
    43247: (t) => {
      t.exports = ['הפיסקה של החלק השני אינו חוקי.'];
    },
    19796: (t) => {
      t.exports = ['שלח לאחור'];
    },
    23221: (t) => {
      t.exports = ['שלח אחורה'];
    },
    5961: (t) => {
      t.exports = ['סיאול'];
    },
    57902: (t) => {
      t.exports = ["ספט'"];
    },
    25866: (t) => {
      t.exports = ['שעות מסחר'];
    },
    59827: (t) => {
      t.exports = ['הפרד ימי מסחר'];
    },
    69240: (t) => {
      t.exports = ['שנחאי'];
    },
    37819: (t) => {
      t.exports = ['עסקת שורט'];
    },
    81428: (t) => {
      t.exports = ['הצג'];
    },
    98116: (t) => {
      t.exports = ['הצג את כל השרטוטים'];
    },
    39046: (t) => {
      t.exports = ['הצג את כל השרטוטים והאינדיקטורים'];
    },
    38293: (t) => {
      t.exports = ['הצג את כל השרטוטים, האינדיקטורים, הפוזיציות והפקודות'];
    },
    49982: (t) => {
      t.exports = ['הצג את כל האינדיקטורים'];
    },
    48284: (t) => {
      t.exports = ['הצג את כל הרעיונות'];
    },
    62632: (t) => {
      t.exports = ['הצג את כל הפוזיציות והפקודות'];
    },
    24620: (t) => {
      t.exports = ['הצג לחצן חוזה רציף'];
    },
    84813: (t) => {
      t.exports = ['הצג את תפוגת החוזה'];
    },
    66263: (t) => {
      t.exports = ['הצג דיבידנדים'];
    },
    46771: (t) => {
      t.exports = ['הצג דו"חות רווחים'];
    },
    87933: (t) => {
      t.exports = ['הצג רעיונות של משתמשים עוקבים'];
    },
    30709: (t) => {
      t.exports = ['הצג את החדשות העדכניות ביותר'];
    },
    58669: (t) => {
      t.exports = ['הצג את הרעיונות שלי בלבד'];
    },
    30816: (t) => {
      t.exports = ['הצג פיצולי מניות'];
    },
    68161: (t) => {
      t.exports = ['סימן דרך'];
    },
    56683: (t) => {
      t.exports = ['סינגפור'];
    },
    69502: (t) => {
      t.exports = ['קו סינוס'];
    },
    44904: (t) => {
      t.exports = ['רבוע'];
    },
    70213: (t) => {
      t.exports = [
        "חריגה ממגבלת המחקר: {number} מחקרים בכל פריסה.\nבבקשה, הסר מס' מחקרים.",
      ];
    },
    32733: (t) => {
      t.exports = ['עיצוב'];
    },
    65323: (t) => {
      t.exports = ['תאגד לשמאל'];
    },
    14113: (t) => {
      t.exports = ['תאגד לימין'];
    },
    93161: (t) => {
      t.exports = ['הישאר במצב ציור'];
    },
    48767: (t) => {
      t.exports = ['שטוקהולם'];
    },
    29662: (t) => {
      t.exports = 'Submicro';
    },
    9753: (t) => {
      t.exports = 'Submillennium';
    },
    71722: (t) => {
      t.exports = ['סאבמינוט (Subminuette)'];
    },
    91889: (t) => {
      t.exports = ['סופר גל'];
    },
    33820: (t) => {
      t.exports = 'Supermillennium';
    },
    11020: (t) => {
      t.exports = ['סינדי'];
    },
    89659: (t) => {
      t.exports = ['שגיאת סימול'];
    },
    90932: (t) => {
      t.exports = ['תווית שם הסימול'];
    },
    65986: (t) => {
      t.exports = ['מידע על הסימול'];
    },
    52054: (t) => {
      t.exports = ['תווית ערך אחרון לסימול'];
    },
    33606: (t) => {
      t.exports = ['סנכרון גלובלי'];
    },
    18008: (t) => {
      t.exports = ['סנכרון בפריסה'];
    },
    99969: (t) => {
      t.exports = ['גרף Point & Figure'];
    },
    53047: (t) => {
      t.exports = ['קווים מחוברים'];
    },
    34402: (t) => {
      t.exports = ['נָתִיב'];
    },
    70394: (t) => {
      t.exports = ['ערוץ מקביל'];
    },
    95995: (t) => {
      t.exports = ['פריז'];
    },
    29682: (t) => {
      t.exports = ['הדבק'];
    },
    51102: (t) => {
      t.exports = ['אחוז'];
    },
    35590: (t) => {
      t.exports = ["פרת'"];
    },
    19093: (t) => {
      t.exports = ['פניקס'];
    },
    22293: (t) => {
      t.exports = ['מניפת מחירים'];
    },
    43852: (t) => {
      t.exports = ['קילשון'];
    },
    37680: (t) => {
      t.exports = ['הצמד לסולם חדש משמאל'];
    },
    43707: (t) => {
      t.exports = ['הצמד לסולם חדש מימין'];
    },
    91130: (t) => {
      t.exports = ['הצמד לסולם השמאלי'];
    },
    61201: (t) => {
      t.exports = ['הצמד לסולם השמאלי (מוסתר)'];
    },
    764: (t) => {
      t.exports = ['הצמד לסולם הימני'];
    },
    20207: (t) => {
      t.exports = ['הצמד לסולם השמאלי‎ (מוסתר)'];
    },
    66156: (t) => {
      t.exports = ['הצמד לסולם (עכשיו שמאל)'];
    },
    54727: (t) => {
      t.exports = ['הצמד לסולם (כעת ללא קנה מידה)'];
    },
    76598: (t) => {
      t.exports = ['הצמד לסולם (עכשיו ימין)'];
    },
    39065: (t) => {
      t.exports = ['הצמד לסולם מחיר (כעת ‎{label}‎)'];
    },
    97324: (t) => {
      t.exports = ['הצמד לסולם ‎{label}‎'];
    },
    56948: (t) => {
      t.exports = ['הצמד לסולם ‎{label}‎ (מוסתר)'];
    },
    32156: (t) => {
      t.exports = ['צמוד לסולם השמאלי'];
    },
    8128: (t) => {
      t.exports = ['צמוד לסולם השמאלי (מוסתר)'];
    },
    3822: (t) => {
      t.exports = ['צמוד לסולם מחיר ‎ימין'];
    },
    44538: (t) => {
      t.exports = ['הצמד לסולם השמאלי‎ (מוסתר)'];
    },
    65810: (t) => {
      t.exports = ['צמוד לסולם ‎{label}‎'];
    },
    14125: (t) => {
      t.exports = ['צמוד לסולם ‎{label}‎ (מוסתר)'];
    },
    97378: (t) => {
      t.exports = ['כפתור פלוס'];
    },
    46669: (t) => {
      t.exports = [
        'אנא תן לנו הרשאת כתיבה ללוח בדפדפן שלך או הקש על ‎{keystroke}',
      ];
    },
    35963: (t) => {
      t.exports = [
        'לחץ לחיצה ממושכת על {key} תוך כדי הגדלה כדי לשמור על מיקום הגרף',
      ];
    },
    95921: (t) => {
      t.exports = ['תווית מחיר'];
    },
    28625: (t) => {
      t.exports = ['הערת מחיר'];
    },
    2032: (t) => {
      t.exports = ['טווח מחירים'];
    },
    32061: (t) => {
      t.exports = ['פורמט המחיר אינו חוקי.'];
    },
    91492: (t) => {
      t.exports = ['קו מחיר'];
    },
    48404: (t) => {
      t.exports = ['ראשי'];
    },
    87086: (t) => {
      t.exports = ['הקרנה'];
    },
    10160: (t) => {
      t.exports = ['פורסם בתאריך {customer}, {date}'];
    },
    19056: (t) => {
      t.exports = ['קטאר'];
    },
    9998: (t) => {
      t.exports = ['מלבן מסובב'];
    },
    74214: (t) => {
      t.exports = ['רומא'];
    },
    50470: (t) => {
      t.exports = ['קרן'];
    },
    90357: (t) => {
      t.exports = ['טווח'];
    },
    26833: (t) => {
      t.exports = ['רייקיאוויק'];
    },
    328: (t) => {
      t.exports = ['מלבן'];
    },
    41615: (t) => {
      t.exports = ['בצע שוב'];
    },
    35001: (t) => {
      t.exports = ['מגמת רגרסיה'];
    },
    34596: (t) => {
      t.exports = ['הסר'];
    },
    1434: (t) => {
      t.exports = ['הסר שרטוטים'];
    },
    13951: (t) => {
      t.exports = ['הסר מחוונים'];
    },
    4142: (t) => {
      t.exports = ['בחר שם חדש לפריסת הגרף'];
    },
    20801: (t) => {
      t.exports = ['גרף ראנקו'];
    },
    34301: (t) => {
      t.exports = ['אפס תצוגת גרף'];
    },
    17258: (t) => {
      t.exports = ['אפס סקלת מחיר'];
    },
    25333: (t) => {
      t.exports = ['אפס סולם זמן'];
    },
    52588: (t) => {
      t.exports = ['ריאד'];
    },
    5871: (t) => {
      t.exports = ['ריגה'];
    },
    33603: (t) => {
      t.exports = ['אזהרה'];
    },
    48474: (t) => {
      t.exports = ['ורשה'];
    },
    20466: (t) => {
      t.exports = ['טוקלאו'];
    },
    94284: (t) => {
      t.exports = ['טוקיו'];
    },
    83836: (t) => {
      t.exports = ['טורונטו'];
    },
    38788: (t) => {
      t.exports = ['טייפה'];
    },
    39108: (t) => {
      t.exports = ['טאלין'];
    },
    37229: (t) => {
      t.exports = ['טקסט'];
    },
    16267: (t) => {
      t.exports = ['טהרן'];
    },
    19611: (t) => {
      t.exports = ['תבנית'];
    },
    29198: (t) => {
      t.exports = ['ספק הנתונים אינו מספק נתוני נפח עבור סימול זה'];
    },
    8162: (t) => {
      t.exports = [
        'לא ניתן לטעון את התצוגה המקדימה של הפרסום. השבת את תוספי הדפדפן ונסה שוב.',
      ];
    },
    65943: (t) => {
      t.exports = ['מתנד זה אינו יכול להיות מיושם על מתנד נוסף'];
    },
    74986: (t) => {
      t.exports = [
        'הסקריפט הזה מיועד להזמנה בלבד. כדי לבקש גישה, אנא צור קשר עם המחבר.',
      ];
    },
    98538: (t) => {
      t.exports = ['תבנית Three Drives'];
    },
    30973: (t) => {
      t.exports = ['טיקים'];
    },
    31976: (t) => {
      t.exports = ['זמן'];
    },
    64375: (t) => {
      t.exports = ['אזור זמן'];
    },
    95005: (t) => {
      t.exports = ['מחזורי זמן'];
    },
    87085: (t) => {
      t.exports = ['מסחר'];
    },
    94770: (t) => {
      t.exports = ['זוית מגמה'];
    },
    23104: (t) => {
      t.exports = ['קו מגמה'];
    },
    15501: (t) => {
      t.exports = ["שלוחת פיבונאצ'י מבוססת מגמה"];
    },
    31196: (t) => {
      t.exports = ["זמן פיבונאצ'י מבוסס מגמה"];
    },
    29245: (t) => {
      t.exports = ['משולש'];
    },
    83356: (t) => {
      t.exports = ['משולש למטה'];
    },
    12390: (t) => {
      t.exports = ['תבנית משולש'];
    },
    28340: (t) => {
      t.exports = ['משולש למעלה'];
    },
    93855: (t) => {
      t.exports = ['תוניס'];
    },
    50406: (t) => {
      t.exports = ['אזור זמן'];
    },
    38587: (t) => {
      t.exports = ['מובן'];
    },
    81320: (t) => {
      t.exports = ['בטל'];
    },
    25933: (t) => {
      t.exports = ['יחידות'];
    },
    15101: (t) => {
      t.exports = ['בטל נעילה'];
    },
    34150: (t) => {
      t.exports = ['גל עולה 4'];
    },
    83927: (t) => {
      t.exports = ['גל עולה 5'];
    },
    58976: (t) => {
      t.exports = ['גל עולה 1 או A'];
    },
    11661: (t) => {
      t.exports = ['גל עולה 2 או B'];
    },
    53958: (t) => {
      t.exports = ['גל עולה 3'];
    },
    66560: (t) => {
      t.exports = ['גל עולה C'];
    },
    18426: (t) => {
      t.exports = ['טווח קבוע של פרופיל ווליום'];
    },
    61022: (t) => {
      t.exports = ['אינדיקטור פרופיל ווליום זמין רק בתוכניות המשודרגות שלנו.'];
    },
    15771: (t) => {
      t.exports = ['ונקובר'];
    },
    56211: (t) => {
      t.exports = ['קו אנכי'];
    },
    75354: (t) => {
      t.exports = ['וילנה'];
    },
    21852: (t) => {
      t.exports = ['ניראות'];
    },
    27557: (t) => {
      t.exports = ['נראות אינטרוולים'];
    },
    89960: (t) => {
      t.exports = ['הצג כשהעכבר מעל'];
    },
    22198: (t) => {
      t.exports = ['פקודה ויזואלית'];
    },
    7050: (t) => {
      t.exports = 'X Cross';
    },
    66527: (t) => {
      t.exports = ['תבנית XABCD'];
    },
    17126: (t) => {
      t.exports = ['לא ניתן להציג את מסגרת הזמן של פיבוט זה ברזולוציה הזו'];
    },
    69293: (t) => {
      t.exports = ['יאנגון'];
    },
    84301: (t) => {
      t.exports = ['ציריך'];
    },
    76020: (t) => {
      t.exports = ['שנה את רמת אליוט'];
    },
    83935: (t) => {
      t.exports = ['אין לשנות תוויות חופפות'];
    },
    39402: (t) => {
      t.exports = ['שנה את נראות תווית המחיר הממוצעת'];
    },
    98866: (t) => {
      t.exports = ['שנה את הנראות הממוצעת של קו מחיר קרוב'];
    },
    5100: (t) => {
      t.exports = ['שנה נראות של תוויות ביקוש והיצע bid/ask'];
    },
    32311: (t) => {
      t.exports = ['שנה נראות שורות היצע וביקוש bid/ask'];
    },
    22641: (t) => {
      t.exports = ['שנה מטבע'];
    },
    30501: (t) => {
      t.exports = ['שנה פריסת גרף ל-{title}'];
    },
    7017: (t) => {
      t.exports = ['שנה נראות מתג החלפה לחוזה רציף'];
    },
    58108: (t) => {
      t.exports = ['שנה את נראות הספירה לאחור לסגירת הבר'];
    },
    7151: (t) => {
      t.exports = ['שנה טווח תאריכים'];
    },
    84944: (t) => {
      t.exports = ['שנה את נראות הדיבידנדים'];
    },
    79574: (t) => {
      t.exports = ['שנה את נראות האירועים בגרף'];
    },
    88217: (t) => {
      t.exports = ['שנה את נראות הרווחים'];
    },
    28288: (t) => {
      t.exports = ['שינוי נראות תפוגה של חוזה עתידי'];
    },
    66805: (t) => {
      t.exports = ['לשנות את הנראות של תוויות מחיר גבוה ונמוך'];
    },
    92556: (t) => {
      t.exports = ['לשנות את נראות קווי המחירים הגבוהים והנמוכים'];
    },
    87027: (t) => {
      t.exports = ['שנה נראות של אינדיקטורים ושם תוויות'];
    },
    14922: (t) => {
      t.exports = ['שנה נראות תוויות ערך של אינדיקטורים'];
    },
    77578: (t) => {
      t.exports = ['שנה את נראות החדשות האחרונות'];
    },
    87510: (t) => {
      t.exports = ['לשנות את גובה החלונית'];
    },
    50190: (t) => {
      t.exports = ['שנה נראות של לחצן הפלוס'];
    },
    49889: (t) => {
      t.exports = ['שנה את נראות תווית מחיר פרה/פןסט מארקט'];
    },
    16750: (t) => {
      t.exports = ['שנה את נראות קווי מחיר פרה/פןסט מארקט'];
    },
    59883: (t) => {
      t.exports = ['שנה את הנראות של קו סגירת מחיר הקודם'];
    },
    67761: (t) => {
      t.exports = ['שנה את קו המחיר'];
    },
    69510: (t) => {
      t.exports = ['שנה את יחס המחיר לבר'];
    },
    32303: (t) => {
      t.exports = ['שנה רזולוציה'];
    },
    526: (t) => {
      t.exports = ['שנה סימול'];
    },
    9402: (t) => {
      t.exports = ['שנה את נראות תוויות הסימולים'];
    },
    53150: (t) => {
      t.exports = ['שנה את נראות סימול הערך האחרון'];
    },
    12707: (t) => {
      t.exports = ['שנה נראות ערך קרוב של סימול קודם'];
    },
    65303: (t) => {
      t.exports = ['שנה סשן'];
    },
    15403: (t) => {
      t.exports = ['שנה את נראות הפסקות הסשן'];
    },
    53438: (t) => {
      t.exports = ['שנה את סגנון הסדרה'];
    },
    74488: (t) => {
      t.exports = ['שנה את נראות הפיצולים/ספליט'];
    },
    20505: (t) => {
      t.exports = ['שנה אזור זמן'];
    },
    39028: (t) => {
      t.exports = ['שנה יחידה'];
    },
    21511: (t) => {
      t.exports = ['שנה את הנראות'];
    },
    16698: (t) => {
      t.exports = ['לשנות את הנראות באינטרוול הנוכחי'];
    },
    78422: (t) => {
      t.exports = ['לשנות את הנראות באינטרוול הנוכחי ומעלה'];
    },
    49529: (t) => {
      t.exports = ['שנה נראות באינטרוול הנוכחי ומטה'];
    },
    66927: (t) => {
      t.exports = ['לשנות את הנראות בכל האינטרוולים'];
    },
    74428: (t) => {
      t.exports = ['שנה סגנון {title}'];
    },
    72032: (t) => {
      t.exports = ['שנה נקודת {pointIndex}'];
    },
    65911: (t) => {
      t.exports = ['הגרפים באדיבות TradingView'];
    },
    5179: (t) => {
      t.exports = ['העתק כלי-קו'];
    },
    3195: (t) => {
      t.exports = ['צור קבוצת כלים לקווים'];
    },
    92659: (t) => {
      t.exports = ['צור קבוצת כלי שורה מהבחירה'];
    },
    81791: (t) => {
      t.exports = ['צור ‎{tool}‎'];
    },
    63649: (t) => {
      t.exports = ['חתוך מקורות'];
    },
    78755: (t) => {
      t.exports = ['חתוך ‎{title}‎'];
    },
    99113: (t) => {
      t.exports = ['הוסף כלי קו {lineTool} לקבוצה {name}'];
    },
    40242: (t) => {
      t.exports = ['הוסף כלי(ם) קו לקבוצה ‎{group}‎'];
    },
    22856: (t) => {
      t.exports = ['הוסף מדד פיננסי זה לפריסה שלמה'];
    },
    82388: (t) => {
      t.exports = ['הוסף אינדיקטור זה לפריסה שלמה'];
    },
    94292: (t) => {
      t.exports = ['הוסף אסטרטגיה זו לפריסה שלמה'];
    },
    27982: (t) => {
      t.exports = ['הוסף סימול זה לפריסה שלמה'];
    },
    66568: (t) => {
      t.exports = ['החל את נושא הגרף'];
    },
    64034: (t) => {
      t.exports = ['החל את כל מאפייני הגרף'];
    },
    49037: (t) => {
      t.exports = ['החל תבנית שרטוט'];
    },
    96996: (t) => {
      t.exports = ['החל את ברירת המחדל של היצרן על מקורות נבחרים'];
    },
    44547: (t) => {
      t.exports = ['להחיל אינדיקטורים על כל הפריסה'];
    },
    26065: (t) => {
      t.exports = ['החל תבנית לימודית ‎{template}‎'];
    },
    58570: (t) => {
      t.exports = ['להחיל נושא של סרגלי כלים'];
    },
    27195: (t) => {
      t.exports = ['הבא את הקבוצה {title} קדימה'];
    },
    78246: (t) => {
      t.exports = ['הבא את {title} לחזית'];
    },
    56763: (t) => {
      t.exports = ['הבא ‎{title}‎ קדימה'];
    },
    5607: (t) => {
      t.exports = ['מאת TradingView'];
    },
    90621: (t) => {
      t.exports = ['נעילת טווח תאריכים'];
    },
    12962: (t) => {
      t.exports = ['מחק קו רמה'];
    },
    63391: (t) => {
      t.exports = ['אל תכלול כלי קווים מהקבוצה {group}'];
    },
    59942: (t) => {
      t.exports = ['הפוך תבנית ברים'];
    },
    70301: (t) => {
      t.exports = ['הסתר ‎{title}‎'];
    },
    91842: (t) => {
      t.exports = ['הסתר תווית קווי התראות'];
    },
    54781: (t) => {
      t.exports = ['הסתר את כלי השרטוט'];
    },
    44974: (t) => {
      t.exports = ['הסתר סימנים על הנרות'];
    },
    28916: (t) => {
      t.exports = ['נעילת אינטרוול'];
    },
    94245: (t) => {
      t.exports = ['הפוך גרף'];
    },
    90743: (t) => {
      t.exports = ['הוסף ‎{title}‎'];
    },
    53146: (t) => {
      t.exports = ['הוסף {title} אחרי {targetTitle}'];
    },
    74055: (t) => {
      t.exports = ['הכנס ‎{title}‎ אחרי ‎{target}‎'];
    },
    11231: (t) => {
      t.exports = ['הכנס ‎{title}‎ לפני ‎{target}‎'];
    },
    67176: (t) => {
      t.exports = ['הכנס ‎{title}‎ לפני ‎{targetTitle}‎'];
    },
    54597: (t) => {
      t.exports = ['טען תבנית שרטוט ברירת מחדל'];
    },
    30295: (t) => {
      t.exports = ['טוען...'];
    },
    50193: (t) => {
      t.exports = ['נעל ‎{title}‎'];
    },
    4963: (t) => {
      t.exports = ['נעל קבוצה {group}'];
    },
    68163: (t) => {
      t.exports = ['נעל אובייקטים'];
    },
    47107: (t) => {
      t.exports = ['הזז'];
    },
    11303: (t) => {
      t.exports = ['הזז ‎{title}‎ לסולם חדש משמאל'];
    },
    45544: (t) => {
      t.exports = ['העבר את {title} לסולם ימני חדש'];
    },
    81898: (t) => {
      t.exports = ['הזז את כל סולמות המחיר לשמאל'];
    },
    22863: (t) => {
      t.exports = ['הזז את כל סולמות המחיר לימין'];
    },
    45356: (t) => {
      t.exports = ['העבר שירטוט(ים)'];
    },
    15086: (t) => {
      t.exports = ['הזז לשמאל'];
    },
    61711: (t) => {
      t.exports = ['הזז לימין'];
    },
    4184: (t) => {
      t.exports = ['הזז סולם מחיר'];
    },
    74642: (t) => {
      t.exports = ['הפוך את ‎{title}‎ ללא קנה מידה (מסך מלא)'];
    },
    45223: (t) => {
      t.exports = ['הפוך את הקבוצה {group} לבלתי נראית'];
    },
    87927: (t) => {
      t.exports = ['הפוך את הקבוצה {group} לגלויה'];
    },
    62153: (t) => {
      t.exports = ['מזג למטה'];
    },
    70746: (t) => {
      t.exports = ['מזג לחלונית'];
    },
    66143: (t) => {
      t.exports = ['מזג למעלה'];
    },
    81870: (t) => {
      t.exports = ['דפוס ברי מראה'];
    },
    16542: (t) => {
      t.exports = 'n/a';
    },
    47222: (t) => {
      t.exports = ['קנה מידה מחיר'];
    },
    99042: (t) => {
      t.exports = ['הרחב סולם מחיר בלבד'];
    },
    35962: (t) => {
      t.exports = ['זמן קנה מידה'];
    },
    68193: (t) => {
      t.exports = ['גלול'];
    },
    70009: (t) => {
      t.exports = ['זמן גלילה'];
    },
    69485: (t) => {
      t.exports = ['הגדר את אסטרטגיית בחירת סולם המחירים ל-{title}'];
    },
    16259: (t) => {
      t.exports = ['שלח {title} לאחור'];
    },
    66781: (t) => {
      t.exports = ['שלח את {title} לאחור'];
    },
    4998: (t) => {
      t.exports = ['שלח את הקבוצה {title} לאחור'];
    },
    64704: (t) => {
      t.exports = ['שתף כלי קו ברחבי העולם'];
    },
    77554: (t) => {
      t.exports = ['שיתוף כלי קו בפריסה'];
    },
    16237: (t) => {
      t.exports = ['הצג תווית קווי התראות'];
    },
    13622: (t) => {
      t.exports = ['הצג את כל הרעיונות'];
    },
    26267: (t) => {
      t.exports = ['הצג רעיונות של משתמשים במעקב'];
    },
    40061: (t) => {
      t.exports = ['הצג את הרעיונות שלי בלבד'];
    },
    52010: (t) => {
      t.exports = ['הישאר במצב שרטוט'];
    },
    98784: (t) => {
      t.exports = ['הפסק לסנכרן שרטוט'];
    },
    57011: (t) => {
      t.exports = ['הפסק לסנכרן כלי(ם) קו'];
    },
    92831: (t) => {
      t.exports = ['נעילת סימול'];
    },
    60635: (t) => {
      t.exports = ['סנכרן זמן'];
    },
    99769: (t) => {
      t.exports = ['מופעל ע"י'];
    },
    68111: (t) => {
      t.exports = ['מופעל על ידי Tradingview'];
    },
    96916: (t) => {
      t.exports = ['הדבק שרטוט'];
    },
    80611: (t) => {
      t.exports = ['הדבק אינדיקטור'];
    },
    41601: (t) => {
      t.exports = ['הדבק {title}'];
    },
    84018: (t) => {
      t.exports = ['הצמד לסולם השמאלי'];
    },
    22615: (t) => {
      t.exports = ['הצמד לסולם השמאלי'];
    },
    56015: (t) => {
      t.exports = ['הצמד לסולם ‎{label}‎'];
    },
    33348: (t) => {
      t.exports = ['ארגן מחדש את החלונות'];
    },
    15516: (t) => {
      t.exports = ['הסר תכנים הלימודיים'];
    },
    80171: (t) => {
      t.exports = ['הסר תכנים לימודיים וכלי ציור'];
    },
    59211: (t) => {
      t.exports = ['הסר כלי קו ריקים שלא נבחרו'];
    },
    44656: (t) => {
      t.exports = ['הסר שרטוטים'];
    },
    70653: (t) => {
      t.exports = ['הסר את קבוצת השרטוטים'];
    },
    66414: (t) => {
      t.exports = ['להסיר קו מקורות נתונים'];
    },
    47637: (t) => {
      t.exports = ['הסר חלונית'];
    },
    39859: (t) => {
      t.exports = ['הסר {title}'];
    },
    78811: (t) => {
      t.exports = ['הסרת קבוצת כלי קו {name}'];
    },
    16338: (t) => {
      t.exports = ['שנה שם קבוצה {group} ל- {newName}'];
    },
    30910: (t) => {
      t.exports = ['אפס גדלי פריסה'];
    },
    21948: (t) => {
      t.exports = ['אפס קנה מידה'];
    },
    55064: (t) => {
      t.exports = ['אפס סולם זמן'];
    },
    13034: (t) => {
      t.exports = ['שנה את גודל הפריסה'];
    },
    9608: (t) => {
      t.exports = ['שחזר ברירות מחדל'];
    },
    63060: (t) => {
      t.exports = ['החלף קנה מידה אוטומטי'];
    },
    98860: (t) => {
      t.exports = ['החלף באינדקס ל-100 קנה מידה'];
    },
    21203: (t) => {
      t.exports = ['החלף נעילת סולם'];
    },
    60166: (t) => {
      t.exports = ['החלף סקאלה לוגריתמית'];
    },
    68642: (t) => {
      t.exports = ['הפעל/כבה קנה מידה באחוזים'];
    },
    33714: (t) => {
      t.exports = ['החלף קנה מידה רגיל'];
    },
    47122: (t) => {
      t.exports = ['עקוב אחר הזמן'];
    },
    28068: (t) => {
      t.exports = ['כבה את שיתוף כלי הקו'];
    },
    66824: (t) => {
      t.exports = ['פתח אובייקטים'];
    },
    51114: (t) => {
      t.exports = ['בטל את נעילת הקבוצה {group}'];
    },
    92421: (t) => {
      t.exports = ['בטל את נעילת ‎{title}‎'];
    },
    20057: (t) => {
      t.exports = ['בטל את המיזוג לחלונית התחתונה החדשה'];
    },
    52540: (t) => {
      t.exports = ['בטל מיזוג למעלה'];
    },
    86949: (t) => {
      t.exports = ['בטל מיזוג למטה'];
    },
    50728: (t) => {
      t.exports = ['עדכן ‎סקריפט ‎{title}‎'];
    },
    33355: (t) => {
      t.exports = ['נרות {count}'];
    },
    88841: (t) => {
      t.exports = ['{symbol} פיננסים באדיבות TradingView'];
    },
    38641: (t) => {
      t.exports = ['{userName} פורסם ב-{customer}, {date}'];
    },
    59833: (t) => {
      t.exports = ['זום'];
    },
    19813: (t) => {
      t.exports = ['הגדל תצוגה'];
    },
    9645: (t) => {
      t.exports = ['הקטן תצוגה'];
    },
    30572: (t) => {
      t.exports = ['יום', 'יומיים', 'ימים', 'ימים'];
    },
    52254: (t) => {
      t.exports = ['שעה', 'שעות', 'שעות', 'שעות'];
    },
    99062: (t) => {
      t.exports = ['חודש', 'חודשים', 'חודשים', 'חודשים'];
    },
    69143: (t) => {
      t.exports = ['דקה', 'דקות', 'דקות', 'דקות'];
    },
    71787: (t) => {
      t.exports = ['שניה', 'שניות', 'שניות', 'שניות'];
    },
    82797: (t) => {
      t.exports = ['טווח', 'טווחים', 'טווחים', 'טווחים'];
    },
    47966: (t) => {
      t.exports = ['שבוע', 'שבועות', 'שבועות', 'שבועות'];
    },
    99136: (t) => {
      t.exports = ['טיק', 'טיקים', 'טיקים', 'טיקים'];
    },
    18562: (t) => {
      (t.exports = Object.create(null)),
        (t.exports['#AAPL-symbol-description'] = ['Apple בע"מ']),
        (t.exports['#AUDCAD-symbol-description'] = ['דולר אוסטרלי/דולר קנדי']),
        (t.exports['#AUDCHF-symbol-description'] = [
          'דולר אוסטרלי/פרנק שוויצרי',
        ]),
        (t.exports['#AUDJPY-symbol-description'] = ['דולר אוסטרלי/יין יפני']),
        (t.exports['#AUDNZD-symbol-description'] = [
          'דולר אוסטרלי/דולר ניו זילנד',
        ]),
        (t.exports['#AUDRUB-symbol-description'] = ['דולר אוסטרלי/רובל רוסי']),
        (t.exports['#AUDUSD-symbol-description'] = [
          'דולר אוסטרלי/דולר אמריקאי',
        ]),
        (t.exports['#BRLJPY-symbol-description'] = ['ריאל ברזילאי / יין יפני']),
        (t.exports['#BTCCAD-symbol-description'] = ['ביטקוין / דולר קנדי']),
        (t.exports['#BTCCNY-symbol-description'] = ['ביטקוין / יואן סיני']),
        (t.exports['#BTCEUR-symbol-description'] = ['ביטקוין / יורו']),
        (t.exports['#BTCKRW-symbol-description'] = [
          'ביטקוין / וואן דרום קוריאני',
        ]),
        (t.exports['#BTCRUR-symbol-description'] = ['ביטקוין / רובל']),
        (t.exports['#BTCUSD-symbol-description'] = ['ביטקוין / דולר אמריקאי']),
        (t.exports['#BVSP-symbol-description'] = ['מדד בווספה ברזיל']),
        (t.exports['#CADJPY-symbol-description'] = ['דולר קנדי / יין יפני']),
        (t.exports['#CB1!-symbol-description'] = ['נפט גולמי ברנט']),
        (t.exports['#CHFJPY-symbol-description'] = ['פרנק שוויצרי / יין יפני']),
        (t.exports['#COPPER-symbol-description'] = ['CFD על נחושת']),
        (t.exports['#ES1-symbol-description'] = ['חוזה ‏E-Mini אס אנד פי 500']),
        (t.exports['#ESP35-symbol-description'] = 'IBEX 35 Index'),
        (t.exports['#EUBUND-symbol-description'] = ['קרנות ארופאיות']),
        (t.exports['#EURAUD-symbol-description'] = ['יורו / דולר אוסטרלי']),
        (t.exports['#EURBRL-symbol-description'] = ['יורו / ריאל ברזילאי']),
        (t.exports['#EURCAD-symbol-description'] = ['יורו / דולר קנדי']),
        (t.exports['#EURCHF-symbol-description'] = ['יורו / פרנק שוויצרי']),
        (t.exports['#EURGBP-symbol-description'] = ['יורו / לירה שטרלינג']),
        (t.exports['#EURJPY-symbol-description'] = ['יורו / יין יפני']),
        (t.exports['#EURNZD-symbol-description'] = ['יורו / דולר ניו זילנד']),
        (t.exports['#EURRUB-symbol-description'] = ['יורו / רובל רוסי']),
        (t.exports['#EURRUB_TOM-symbol-description'] = ['יורו / רובל']),
        (t.exports['#EURSEK-symbol-description'] = ['יורו / קורונה שוודית']),
        (t.exports['#EURTRY-symbol-description'] = ['יורו / לירה טורקית']),
        (t.exports['#EURUSD-symbol-description'] = ['יורו / דולר אמריקאי']),
        (t.exports['#EUSTX50-symbol-description'] = ['‏מדד Euro Stoxx 50']),
        (t.exports['#FRA40-symbol-description'] = ['מדד CAC 40']),
        (t.exports['#GB10-symbol-description'] = [
          'אג"ח ממשלת בריטניה 10 שנים',
        ]),
        (t.exports['#GBPAUD-symbol-description'] = [
          'לירה שטרלינג / דולר אוסטרלי',
        ]),
        (t.exports['#GBPCAD-symbol-description'] = [
          'לירה שטרלינג / דולר קנדי',
        ]),
        (t.exports['#GBPCHF-symbol-description'] = [
          'לירה שטרלינג / פרנק שוויצרי',
        ]),
        (t.exports['#GBPEUR-symbol-description'] = ['לירה שטרלינג / יורו']),
        (t.exports['#GBPJPY-symbol-description'] = ['לירה שטרלינג / יין יפני']),
        (t.exports['#GBPNZD-symbol-description'] = [
          'לירה שטרלינג / דולר ניו זילנד',
        ]),
        (t.exports['#GBPRUB-symbol-description'] = [
          'לירה שטרלינג / רובל רוסי',
        ]),
        (t.exports['#GBPUSD-symbol-description'] = [
          'לירה שטרלינג / דולר אמריקאי',
        ]),
        (t.exports['#GER30-symbol-description'] = ['‏מדד DAX‏ גרמניה']),
        (t.exports['#GOOGL-symbol-description'] = ["אלפאבית (גוגל) דרגה א'"]),
        (t.exports['#ITA40-symbol-description'] = ['מדד FTSE MIB']),
        (t.exports['#JPN225-symbol-description'] = ['מדד ניקיי 225']),
        (t.exports['#JPYKRW-symbol-description'] = [
          'יין יפני / וואן דרום קוריאני',
        ]),
        (t.exports['#JPYRUB-symbol-description'] = ['יין יפני / רובל רוסי']),
        (t.exports['#KA1-symbol-description'] = ['חוזה סוכר #11']),
        (t.exports['#KG1-symbol-description'] = ['חוזה עתידי כותנה']),
        (t.exports['#KT1-symbol-description'] = ['KTCC - תאגיד קי. טרוניק']),
        (t.exports['#LKOH-symbol-description'] = 'LUKOIL'),
        (t.exports['#LTCBTC-symbol-description'] = ['לייטקוין / ביטקוין']),
        (t.exports['#MGNT-symbol-description'] = ['מגנית']),
        (t.exports['#MICEX-symbol-description'] = ['מדד MICEX']),
        (t.exports['#MNOD_ME.EQRP-symbol-description'] =
          'ADR GMK NORILSKIYNIKEL ORD SHS [REPO]'),
        (t.exports['#MSFT-symbol-description'] = [
          'Microsoft Corp חברת מיקרוסופט',
        ]),
        (t.exports['#NAS100-symbol-description'] = ['‏מדדנ נאסד"ק 100 ‏']),
        (t.exports['#NGAS-symbol-description'] = ['גז טבעי (HenryHub)']),
        (t.exports['#NKY-symbol-description'] = ['‏מדד ניקיי 225']),
        (t.exports['#NZDJPY-symbol-description'] = [
          'דולר ניו זילנד / יין יפני',
        ]),
        (t.exports['#NZDUSD-symbol-description'] = [
          'דולר ניו זילנד / דולר אמריקאי',
        ]),
        (t.exports['#RB1-symbol-description'] = ['חוזה עתידי גז RBOB']),
        (t.exports['#RTS-symbol-description'] = ['‏מדד RTS רוסיה']),
        (t.exports['#SBER-symbol-description'] = ['SBERBANK סברבנק']),
        (t.exports['#SPX500-symbol-description'] = ['מדד S&P 500 ‏']),
        (t.exports['#TWTR-symbol-description'] = ['TWITTER INC טוויטר']),
        (t.exports['#UK100-symbol-description'] = ['מדד FTSE 100']),
        (t.exports['#USDBRL-symbol-description'] = [
          'דולר אמריקאי / ריאל ברזילאי',
        ]),
        (t.exports['#USDCAD-symbol-description'] = [
          'דולר אמריקאי / דולר קנדי',
        ]),
        (t.exports['#USDCHF-symbol-description'] = [
          'דולר אמריקאי / פרנק שוויצרי',
        ]),
        (t.exports['#USDCNY-symbol-description'] = [
          'דולר אמריקאי / יואן סיני',
        ]),
        (t.exports['#USDDKK-symbol-description'] = [
          'דולר אמריקאי / קורנה דנמרק',
        ]),
        (t.exports['#USDHKD-symbol-description'] = [
          'דולר אמריקאי / דולר הונג קונג',
        ]),
        (t.exports['#USDIDR-symbol-description'] = ['דולר אמריקאי / רופיה']),
        (t.exports['#USDINR-symbol-description'] = [
          'דולר אמריקאי / רופי הודי',
        ]),
        (t.exports['#USDJPY-symbol-description'] = ['דולר אמריקאי / יין יפני']),
        (t.exports['#USDKRW-symbol-description'] = [
          'דולר אמריקאי / וואן דרום קוראני',
        ]),
        (t.exports['#USDMXN-symbol-description'] = [
          'דולר אמריקאי / פאסו מקסיקני',
        ]),
        (t.exports['#USDPHP-symbol-description'] = [
          'דולר אמריקאי / פאסו פיליפיני',
        ]),
        (t.exports['#USDRUB-symbol-description'] = [
          'דולר אמריקאי / רובל רוסי',
        ]),
        (t.exports['#USDRUB_TOM-symbol-description'] = [
          'דולר אמריקאי / רובל רוסי',
        ]),
        (t.exports['#USDSEK-symbol-description'] = [
          'דולר אמריקאי / קורונה שוודי',
        ]),
        (t.exports['#USDSGD-symbol-description'] = [
          'דולר אמריקאי / דולר סינגפור',
        ]),
        (t.exports['#USDTRY-symbol-description'] = [
          'דולר אמריקאי / לירה טורקית',
        ]),
        (t.exports['#VTBR-symbol-description'] = 'VTB'),
        (t.exports['#XAGUSD-symbol-description'] = ['כסף / דולר אמריקאי']),
        (t.exports['#XAUUSD-symbol-description'] = ['זהב / דולר אמריקאי']),
        (t.exports['#XPDUSD-symbol-description'] = ["CFD'S על פלאדיום"]),
        (t.exports['#XPTUSD-symbol-description'] = ['פלטיניום / דולר אמריקאי']),
        (t.exports['#ZS1-symbol-description'] = ['פולי סויה חוזה עתידי ECBT']),
        (t.exports['#ZW1-symbol-description'] = ['חוזה עתידי חיטה - ECBT']),
        (t.exports['#BTCGBP-symbol-description'] = ['ביטקוין / לירה שטרלינג']),
        (t.exports['#MICEXINDEXCF-symbol-description'] = [
          'MICEX אינדקס רוסיה',
        ]),
        (t.exports['#BTCAUD-symbol-description'] = ['ביטקוין / דולר אוסטרלי']),
        (t.exports['#BTCJPY-symbol-description'] = 'Bitcoin / Japanese Yen'),
        (t.exports['#BTCBRL-symbol-description'] = 'Bitcoin / Brazilian Real'),
        (t.exports['#PT10-symbol-description'] =
          'Portugal Government Bonds 10 yr'),
        (t.exports['#TXSX-symbol-description'] = 'TSX 60 Index'),
        (t.exports['#VIXC-symbol-description'] = ['מדד TSX 60 VIX']),
        (t.exports['#USDPLN-symbol-description'] = ['USD/PLN']),
        (t.exports['#EURPLN-symbol-description'] = ['EUR/PLN']),
        (t.exports['#BTCPLN-symbol-description'] = 'Bitcoin / Polish Zloty'),
        (t.exports['#CAC40-symbol-description'] = ['מדד CAC 40']),
        (t.exports['#XBTCAD-symbol-description'] = ['ביטקוין / דולר קנדי']),
        (t.exports['#ITI2!-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIF2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIF2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIF2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIG2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIG2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIG2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIH2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIH2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIH2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIJ2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIJ2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIJ2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIK2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIK2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIK2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIM2017-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIM2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIM2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIM2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIN2017-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIN2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIN2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIN2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIQ2017-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIQ2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIQ2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIQ2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIU2017-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIU2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIU2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIU2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIV2017-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIV2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIV2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIV2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIX2017-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIX2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIX2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIX2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIZ2017-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIZ2018-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIZ2019-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#ITIZ2020-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#AMEX:GXF-symbol-description'] =
          'Global x FTSE Nordic Region ETF'),
        (t.exports['#ASX:XAF-symbol-description'] =
          'S&P/ASX All Australian 50 Index'),
        (t.exports['#ASX:XAT-symbol-description'] =
          'S&P/ASX All Australian 200 Index'),
        (t.exports['#BIST:XU100-symbol-description'] = 'BIST 100 Index'),
        (t.exports['#GPW:WIG20-symbol-description'] = 'WIG20 Index'),
        (t.exports['#INDEX:JKSE-symbol-description'] =
          'Jakarta Composite Index'),
        (t.exports['#INDEX:KLSE-symbol-description'] = [
          'אינדקס בורסת מלזיה KLCI',
        ]),
        (t.exports['#INDEX:NZD-symbol-description'] = 'NZX 50 Index'),
        (t.exports['#INDEX:STI-symbol-description'] = 'STI Index'),
        (t.exports['#INDEX:XLY0-symbol-description'] =
          'Shanghai Composite Index'),
        (t.exports['#MOEX:MICEXINDEXCF-symbol-description'] =
          'MOEX Russia Index'),
        (t.exports['#NYMEX:KT1!-symbol-description'] = [
          'חוזים עתידיים על קפה',
        ]),
        (t.exports['#OANDA:NATGASUSD-symbol-description'] =
          'CFDs on Natural Gas'),
        (t.exports['#OANDA:USDPLN-symbol-description'] = [
          'דולר אמריקאי / זלוטי פולני',
        ]),
        (t.exports['#TSX:TX60-symbol-description'] = 'S&P/TSX 60 Index'),
        (t.exports['#TSX:VBU-symbol-description'] =
          'Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN'),
        (t.exports['#TSX:VIXC-symbol-description'] = 'S&P/TSX 60 VIX Index'),
        (t.exports['#TVC:CAC40-symbol-description'] = 'CAC 40 Index'),
        (t.exports['#TVC:ES10-symbol-description'] =
          'Spain Government Bonds 10 YR'),
        (t.exports['#TVC:EUBUND-symbol-description'] = 'Euro Bund'),
        (t.exports['#TVC:GB02-symbol-description'] =
          'UK Government Bonds 2 YR'),
        (t.exports['#TVC:GB10-symbol-description'] =
          'UK Government Bonds 10 YR'),
        (t.exports['#TVC:GOLD-symbol-description'] = [
          'CFDs על זהב (US$ / OZ)',
        ]),
        (t.exports['#TVC:ID03-symbol-description'] =
          'Indonesia Government Bonds 3 YR'),
        (t.exports['#TVC:ID10-symbol-description'] =
          'Indonesia Government Bonds 10 YR'),
        (t.exports['#TVC:PALLADIUM-symbol-description'] =
          'CFDs on Palladium (US$ / OZ)'),
        (t.exports['#TVC:PT10-symbol-description'] =
          'Portugal Government Bonds 10 YR'),
        (t.exports['#TVC:SILVER-symbol-description'] =
          'CFDs on Silver (US$ / OZ)'),
        (t.exports['#TVC:RUT-symbol-description'] = 'Russell 2000 Index'),
        (t.exports['#TSX:TSX-symbol-description'] = 'S&P/TSX Composite Index'),
        (t.exports['#OANDA:CH20CHF-symbol-description'] = 'Swiss 20 Index'),
        (t.exports['#TVC:SHCOMP-symbol-description'] =
          'Shanghai Composite Index'),
        (t.exports['#NZX:ALLC-symbol-description'] =
          'S&P/NZX All Index (Capital Index)'),
        (t.exports['#AMEX:SHYG-symbol-description'] =
          'Shares 0-5 YEAR High Yield Corporate Bond ETF'),
        (t.exports['#TVC:AU10-symbol-description'] =
          'Australia Government Bonds 10 YR'),
        (t.exports['#TVC:CN10-symbol-description'] =
          'China Government Bonds 10 YR'),
        (t.exports['#TVC:KR10-symbol-description'] =
          'Korea Government Bonds 10 YR'),
        (t.exports['#NYMEX:RB1!-symbol-description'] = [
          'חוזים עתידיים על בנזין RBOB',
        ]),
        (t.exports['#NYMEX:HO1!-symbol-description'] = [
          'חוזים עתידיים על נמל NY ULSD',
        ]),
        (t.exports['#NYMEX:AEZ1!-symbol-description'] = [
          'חוזים עתידיים אתנול ניו יורק',
        ]),
        (t.exports['#OANDA:XCUUSD-symbol-description'] =
          'CFDs on Copper (US$ / lb)'),
        (t.exports['#COMEX:ZA1!-symbol-description'] = ['חוזים עתידיים אבץ']),
        (t.exports['#CBOT:ZW1!-symbol-description'] = [
          'חוזים עתידיים על חיטה',
        ]),
        (t.exports['#NYMEX:KA1!-symbol-description'] = [
          'חוזים עתידיים סוכר #11',
        ]),
        (t.exports['#CBOT:QBC1!-symbol-description'] = [
          'חוזים עתידיים על תירס',
        ]),
        (t.exports['#CME:E61!-symbol-description'] = ['חוזים עתידיים אירו']),
        (t.exports['#CME:B61!-symbol-description'] = [
          'חוזים עתידיים על הפאונד הבריטי',
        ]),
        (t.exports['#CME:QJY1!-symbol-description'] = [
          'חוזים עתידיים ין יפני',
        ]),
        (t.exports['#CME:A61!-symbol-description'] = [
          'חוזים עתידיים דולר אוסטרלי',
        ]),
        (t.exports['#CME:D61!-symbol-description'] = [
          'חוזים עתידיים על דולר קנדי',
        ]),
        (t.exports['#CME:SP1!-symbol-description'] = ['חוזים עתידיים S&P 500']),
        (t.exports['#CME_MINI:NQ1!-symbol-description'] = [
          'חוזים עתידיים נאסד"ק 100 E-mini',
        ]),
        (t.exports['#CBOT_MINI:YM1!-symbol-description'] = [
          "חוזים עתידיים E-mini דאו ג'ונס ($5)",
        ]),
        (t.exports['#CME:NY1!-symbol-description'] = [
          'חוזים עתידיים ניקיי 225',
        ]),
        (t.exports['#EUREX:DY1!-symbol-description'] = ['מדד דאקס']),
        (t.exports['#CME:IF1!-symbol-description'] = [
          'חוזים עתידיים על מדד IBOVESPA-$',
        ]),
        (t.exports['#CBOT:TY1!-symbol-description'] = [
          'חוזים עתידיים על שטרות אג"ח ל-10 שנים',
        ]),
        (t.exports['#CBOT:FV1!-symbol-description'] = [
          'חוזים עתידיים על שטרות אג"ח ל-5 שנים',
        ]),
        (t.exports['#CBOT:ZE1!-symbol-description'] = [
          'חוזים עתידיים שטרות אג"ח -  לשלוש שנים',
        ]),
        (t.exports['#CBOT:TU1!-symbol-description'] = [
          'חוזים עתידיים על שטרות אג"ח לשנתיים',
        ]),
        (t.exports['#CBOT:FF1!-symbol-description'] = [
          'חוזים עתידיים על ריבית של קרנות FED ל-30 יום',
        ]),
        (t.exports['#CBOT:US1!-symbol-description'] = [
          'חוזים עתידיים על T-Bond',
        ]),
        (t.exports['#TVC:EXY-symbol-description'] = 'Euro Currency Index'),
        (t.exports['#TVC:JXY-symbol-description'] =
          'Japanese Yen Currency Index'),
        (t.exports['#TVC:BXY-symbol-description'] =
          'British Pound Currency Index'),
        (t.exports['#TVC:AXY-symbol-description'] =
          'Australian Dollar Currency Index'),
        (t.exports['#TVC:CXY-symbol-description'] =
          'Canadian Dollar Currency Index'),
        (t.exports['#FRED:GDP-symbol-description'] =
          'Gross Domestic Product, 1 Decimal'),
        (t.exports['#FRED:UNRATE-symbol-description'] =
          'Civilian Unemployment Rate'),
        (t.exports['#FRED:POP-symbol-description'] =
          'Total Population: All Ages Including Armed Forces Overseas'),
        (t.exports['#ETHUSD-symbol-description'] = ['אתריום / דולר אמריקאי']),
        (t.exports['#BMFBOVESPA:IBOV-symbol-description'] = 'IBovespa Index'),
        (t.exports['#BMFBOVESPA:IBRA-symbol-description'] = 'IBrasil Index'),
        (t.exports['#BMFBOVESPA:IBXL-symbol-description'] = 'IBRX 50 Index'),
        (t.exports['#COMEX:HG1!-symbol-description'] = [
          'חוזים עתידיים על נחושת',
        ]),
        (t.exports['#INDEX:HSCE-symbol-description'] =
          'Hang Seng China Enterprises Index'),
        (t.exports['#NYMEX:CL1!-symbol-description'] = [
          'חוזים עתידיים נפט גולמי',
        ]),
        (t.exports['#OTC:IHRMF-symbol-description'] = 'Ishares MSCI Japan SHS'),
        (t.exports['#TVC:DAX-symbol-description'] = ['מדד דאקס']),
        (t.exports['#TVC:DE10-symbol-description'] =
          'German Government Bonds 10 YR'),
        (t.exports['#TVC:DJI-symbol-description'] = ["מדד דאו ג'ונס"]),
        (t.exports['#TVC:DXY-symbol-description'] = ['מדד מטבע דולר אמריקאי']),
        (t.exports['#TVC:FR10-symbol-description'] =
          'France Government Bonds 10 YR'),
        (t.exports['#TVC:HSI-symbol-description'] = 'Hang Seng Index'),
        (t.exports['#TVC:IBEX35-symbol-description'] = 'IBEX 35 Index'),
        (t.exports['#FX:AUS200-symbol-description'] = 'S&P/ASX Index'),
        (t.exports['#AMEX:SHY-symbol-description'] =
          'Ishares 1-3 Year Treasury Bond ETF'),
        (t.exports['#ASX:XJO-symbol-description'] = 'S&P/ASX 200 Index'),
        (t.exports['#BSE:SENSEX-symbol-description'] = 'S&P BSE Sensex Index'),
        (t.exports['#INDEX:MIB-symbol-description'] = 'MIB Index'),
        (t.exports['#INDEX:MOY0-symbol-description'] = 'Euro Stoxx 50 Index'),
        (t.exports['#MOEX:RTSI-symbol-description'] = 'RTS Index'),
        (t.exports['#NSE:NIFTY-symbol-description'] = 'Nifty 50 Index'),
        (t.exports['#NYMEX:NG1!-symbol-description'] = [
          'חוזים עתידיים גז טבעי',
        ]),
        (t.exports['#NYMEX:ZC1!-symbol-description'] = ['חוזים עתידיים תירס']),
        (t.exports['#TVC:IN10-symbol-description'] =
          'India Government Bonds 10 YR'),
        (t.exports['#TVC:IT10-symbol-description'] =
          'Italy Government Bonds 10 YR'),
        (t.exports['#TVC:JP10-symbol-description'] =
          'Japan Government Bonds 10 YR'),
        (t.exports['#TVC:NDX-symbol-description'] = ['מדד נאסד"ק 100']),
        (t.exports['#TVC:NI225-symbol-description'] = ['מדד ניקיי 225']),
        (t.exports['#TVC:SPX-symbol-description'] = ['מדד S&P 500']),
        (t.exports['#TVC:SX5E-symbol-description'] = 'Euro Stoxx 50 Index'),
        (t.exports['#TVC:TR10-symbol-description'] =
          'Turkey Government Bonds 10 YR'),
        (t.exports['#TVC:UKOIL-symbol-description'] = ['CFDs על ברנט גולמי']),
        (t.exports['#TVC:UKX-symbol-description'] = 'UK 100 Index'),
        (t.exports['#TVC:US02-symbol-description'] =
          'US Government Bonds 2 YR'),
        (t.exports['#TVC:US05-symbol-description'] =
          'US Government Bonds 5 YR'),
        (t.exports['#TVC:US10-symbol-description'] =
          'US Government Bonds 10 YR'),
        (t.exports['#TVC:USOIL-symbol-description'] = 'CFDs on WTI Crude Oil'),
        (t.exports['#NYMEX:ITI1!-symbol-description'] = [
          'חוזים עתידיים על עפרות ברזל',
        ]),
        (t.exports['#NASDAQ:SHY-symbol-description'] =
          'Ishares 1-3 Year Treasury Bond ETF'),
        (t.exports['#AMEX:ALD-symbol-description'] = [
          'תעודת סל לחובות מקומיים WisdomTree אסיה',
        ]),
        (t.exports['#NASDAQ:AMD-symbol-description'] = [
          'מכשירי מיקרו מתקדמים בע"מ',
        ]),
        (t.exports['#NYSE:BABA-symbol-description'] = [
          'אחזקות קבוצת עליבאבא בע"מ',
        ]),
        (t.exports['#ICEEUR:CB-symbol-description'] = ['ברנט נפט גולמי']),
        (t.exports['#ICEEUR:CB1!-symbol-description'] = ['נפט גולמי ברנט']),
        (t.exports['#ICEUSA:CC-symbol-description'] = ['קקאו']),
        (t.exports['#NYMEX:CL-symbol-description'] = ['נפט גולמי WTI']),
        (t.exports['#ICEUSA:CT-symbol-description'] = ['כותנה #2']),
        (t.exports['#NASDAQ:CTRV-symbol-description'] = [
          'חברת ContraVir Pharmaceuticals Inc.',
        ]),
        (t.exports['#CME:DL-symbol-description'] = ['חלב בדרגה III']),
        (t.exports['#NYSE:F-symbol-description'] = ['פורד MTR CO DEL']),
        (t.exports['#MOEX:GAZP-symbol-description'] = 'GAZPROM'),
        (t.exports['#COMEX:GC-symbol-description'] = ['זהב']),
        (t.exports['#CME:GF-symbol-description'] = ['האכלת בקר']),
        (t.exports['#CME:HE-symbol-description'] = ['חזיר רזה']),
        (t.exports['#NASDAQ:IEF-symbol-description'] =
          'Ishares 7-10 Year Treasury Bond ETF'),
        (t.exports['#NASDAQ:IEI-symbol-description'] =
          'Ishares 3-7 Year Treasury Bond ETF'),
        (t.exports['#NYMEX:KA1-symbol-description'] = [
          'חוזים עתידיים סוכר #11',
        ]),
        (t.exports['#ICEUSA:KC-symbol-description'] = ['קפה']),
        (t.exports['#NYMEX:KG1-symbol-description'] = ['חוזים עתידיים כותנה']),
        (t.exports['#FWB:KT1-symbol-description'] = 'Key Tronic Corр.'),
        (t.exports['#CME:LE-symbol-description'] = ['בקר חי']),
        (t.exports['#ICEEUR:LO-symbol-description'] = ['נפט חימום ICE']),
        (t.exports['#CME:LS-symbol-description'] = ['עֵץ']),
        (t.exports['#MOEX:MGNT-symbol-description'] = ['מגנית']),
        (t.exports['#LSIN:MNOD-symbol-description'] =
          'ADR GMK NORILSKIYNIKEL ORD SHS [REPO]'),
        (t.exports['#NYMEX:NG-symbol-description'] = ['גז טבעי']),
        (t.exports['#ICEUSA:OJ-symbol-description'] = ['מיץ תפוזים']),
        (t.exports['#NYMEX:PA-symbol-description'] = ['פלדיום']),
        (t.exports['#NYSE:PBR-symbol-description'] =
          'PETROLEO BRASILEIRO SA PETROBR'),
        (t.exports['#NYMEX:PL-symbol-description'] = ['פלטינה']),
        (t.exports['#COMEX_MINI:QC-symbol-description'] = ['נחושת E-Mini']),
        (t.exports['#NYMEX:RB-symbol-description'] = ['בנזין RBOB']),
        (t.exports['#NYMEX:RB1-symbol-description'] = [
          'חוזים עתידיים גז RBOB',
        ]),
        (t.exports['#MOEX:SBER-symbol-description'] = 'SBERBANK'),
        (t.exports['#AMEX:SCHO-symbol-description'] =
          'Schwab Short-Term U.S. Treasury ETF'),
        (t.exports['#COMEX:SI-symbol-description'] = ['כסף']),
        (t.exports['#NASDAQ:TLT-symbol-description'] =
          'Ishares 20+ Year Treasury Bond ETF'),
        (t.exports['#TVC:VIX-symbol-description'] = ['תנודתיות המדד S&P 500']),
        (t.exports['#MOEX:VTBR-symbol-description'] = 'VTB'),
        (t.exports['#COMEX:ZA-symbol-description'] = ['אָבָץ']),
        (t.exports['#CBOT:ZC-symbol-description'] = ['תירס']),
        (t.exports['#CBOT:ZK-symbol-description'] = ['חוזים עתידיים אתנול']),
        (t.exports['#CBOT:ZL-symbol-description'] = ['שמן סויה']),
        (t.exports['#CBOT:ZO-symbol-description'] = ['שיבולת שועל']),
        (t.exports['#CBOT:ZR-symbol-description'] = ['אורז גולמי']),
        (t.exports['#CBOT:ZS-symbol-description'] = ['פולי סויה']),
        (t.exports['#CBOT:ZS1-symbol-description'] = 'Soybean Futures'),
        (t.exports['#CBOT:ZW-symbol-description'] = ['חיטה']),
        (t.exports['#CBOT:ZW1-symbol-description'] = 'Wheat Futures - ECBT'),
        (t.exports['#NASDAQ:ITI-symbol-description'] = ['איטריס בע"מ']),
        (t.exports['#NYMEX:ITI2!-symbol-description'] = 'Iron Ore Futures'),
        (t.exports['#CADUSD-symbol-description'] = [
          'דולר קנדי / דולר אמריקאי',
        ]),
        (t.exports['#CHFUSD-symbol-description'] = [
          'פרנק שוויצרי / דולר אמריקאי',
        ]),
        (t.exports['#GPW:ACG-symbol-description'] = 'Acautogaz'),
        (t.exports['#JPYUSD-symbol-description'] = ['ין יפני / דולר אמריקאי']),
        (t.exports['#USDAUD-symbol-description'] = [
          'דולר אמריקאי / דולר אוסטרלי',
        ]),
        (t.exports['#USDEUR-symbol-description'] = ['דולר אמריקאי / אירו']),
        (t.exports['#USDGBP-symbol-description'] = [
          'דולר אמריקאי / לירה שטרלינג',
        ]),
        (t.exports['#USDNZD-symbol-description'] = [
          'דולר אמריקאי / דולר ניו זילנדי',
        ]),
        (t.exports['#UKOIL-symbol-description'] = ['CFDs על נפט גולמי (ברנט)']),
        (t.exports['#USOIL-symbol-description'] = 'CFDs on Crude Oil (WTI)'),
        (t.exports['#US30-symbol-description'] = [
          "מדד דאו ג'ונס הממוצע התעשייתי",
        ]),
        (t.exports['#BCHUSD-symbol-description'] = [
          'ביטקוין קאש / דולר ארה"ב',
        ]),
        (t.exports['#ETCUSD-symbol-description'] = [
          'אתריום קלאסיק / דולר אמריקאי',
        ]),
        (t.exports['#GOOG-symbol-description'] =
          'Alphabet Inc (Google) Class C'),
        (t.exports['#LTCUSD-symbol-description'] = ['לייטקוין / דולר אמריקאי']),
        (t.exports['#XRPUSD-symbol-description'] = ['XRP / דולר אמריקאי']),
        (t.exports['#SP:SPX-symbol-description'] = ['מדד S&P 500']),
        (t.exports['#ETCBTC-symbol-description'] = ['אתריום קלאסיק / ביטקוין']),
        (t.exports['#ETHBTC-symbol-description'] = ['אתריום / ביטקוין']),
        (t.exports['#XRPBTC-symbol-description'] = ['ריפל / ביטקוין']),
        (t.exports['#TVC:US30-symbol-description'] =
          'US Government Bonds 30 YR'),
        (t.exports['#COMEX:SI1!-symbol-description'] = [
          'חוזים עתידיים על כסף',
        ]),
        (t.exports['#BTGUSD-symbol-description'] = [
          'ביטקוין גולד / דולר אמריקאי',
        ]),
        (t.exports['#IOTUSD-symbol-description'] = ['איוטא / דולר אמריקאי']),
        (t.exports['#CME:BTC1!-symbol-description'] = [
          'חוזים עתידיים ביטקוין - CME',
        ]),
        (t.exports['#COMEX:GC1!-symbol-description'] = ['חוזים עתידיים זהב']),
        (t.exports['#CORNUSD-symbol-description'] = 'CFDs on Corn'),
        (t.exports['#COTUSD-symbol-description'] = ['חוזי הפרשים על כותנה']),
        (t.exports['#DJ:DJA-symbol-description'] =
          'Dow Jones Composite Average Index'),
        (t.exports['#DJ:DJI-symbol-description'] = ["מדד דאו ג'ונס"]),
        (t.exports['#ETHEUR-symbol-description'] = ['אתריום/יורו']),
        (t.exports['#ETHGBP-symbol-description'] = ['אתריום / לירה שטרלינג']),
        (t.exports['#ETHJPY-symbol-description'] = ['אתריום / ין יפני']),
        (t.exports['#EURNOK-symbol-description'] = 'Euro / Norwegian Krone'),
        (t.exports['#GBPPLN-symbol-description'] =
          'British Pound / Polish Zloty'),
        (t.exports['#MOEX:BR1!-symbol-description'] = [
          'חוזים עתידיים על נפט ברנט',
        ]),
        (t.exports['#NYMEX:KG1!-symbol-description'] = ['חוזים עתידיים כותנה']),
        (t.exports['#NYMEX:PL1!-symbol-description'] = [
          'חוזים עתידיים על פלטינום',
        ]),
        (t.exports['#SOYBNUSD-symbol-description'] = 'CFDs on Soybeans'),
        (t.exports['#SUGARUSD-symbol-description'] = 'CFDs on Sugar'),
        (t.exports['#TVC:IXIC-symbol-description'] = ['מדד נאסד"ק Composite']),
        (t.exports['#TVC:RU-symbol-description'] = ['מדד Russel 1000']),
        (t.exports['#USDZAR-symbol-description'] = [
          'דולר אמריקאי / ראנד דרום אפריקני',
        ]),
        (t.exports['#WHEATUSD-symbol-description'] = 'CFDs on Wheat'),
        (t.exports['#XRPEUR-symbol-description'] = ['ריפל / אירו']),
        (t.exports['#CBOT:S1!-symbol-description'] = 'Soybean Futures'),
        (t.exports['#SP:MID-symbol-description'] = 'S&P 400 Index'),
        (t.exports['#TSX:XCUUSD-symbol-description'] = 'CFDs on Copper'),
        (t.exports['#TVC:NYA-symbol-description'] = 'NYSE Composite Index'),
        (t.exports['#TVC:PLATINUM-symbol-description'] =
          'CFDs on Platinum (US$ / OZ)'),
        (t.exports['#TVC:SSMI-symbol-description'] = ['מדד שוק שוויצרי']),
        (t.exports['#TVC:SXY-symbol-description'] =
          'Swiss Franc Currency Index'),
        (t.exports['#TVC:RUI-symbol-description'] = ['מדד Russel 1000']),
        (t.exports['#MOEX:RI1!-symbol-description'] = 'RTS Index Futures'),
        (t.exports['#MOEX:MX1!-symbol-description'] = 'MICEX Index Futures'),
        (t.exports['#CBOE:BG1!-symbol-description'] = 'Bitcoin CBOE Futures'),
        (t.exports['#TVC:MY10-symbol-description'] =
          'Malaysia Government Bonds 10 YR'),
        (t.exports['#CME:S61!-symbol-description'] = 'Swiss Franc Futures'),
        (t.exports['#TVC:DEU30-symbol-description'] = ['מדד דאקס']),
        (t.exports['#BCHEUR-symbol-description'] = ['ביטקוין קאש / אירו']),
        (t.exports['#TVC:ZXY-symbol-description'] =
          'New Zealand Dollar Currency Index'),
        (t.exports['#MIL:FTSEMIB-symbol-description'] = 'FTSE MIB Index'),
        (t.exports['#XETR:DAX-symbol-description'] = ['מדד דאקס']),
        (t.exports['#MOEX:IMOEX-symbol-description'] = 'MOEX Russia Index'),
        (t.exports['#FX:US30-symbol-description'] = ["מדד דאו ג'ונס"]),
        (t.exports['#MOEX:RUAL-symbol-description'] =
          'United Company RUSAL PLC'),
        (t.exports['#MOEX:MX2!-symbol-description'] = 'MICEX Index Futures'),
        (t.exports['#NEOUSD-symbol-description'] = ['נאו / דולר אמריקאי']),
        (t.exports['#XMRUSD-symbol-description'] = ['מונרו / דולר אמריקאי']),
        (t.exports['#ZECUSD-symbol-description'] = ['זיקאש / דולר אמריקאי']),
        (t.exports['#TVC:CAC-symbol-description'] = 'CAC 40 Index'),
        (t.exports['#NASDAQ:ZS-symbol-description'] = 'Zscaler Inc'),
        (t.exports['#TVC:GB10Y-symbol-description'] =
          'UK Government Bonds 10 YR Yield'),
        (t.exports['#TVC:AU10Y-symbol-description'] =
          'Australia Government Bonds 10 YR Yield'),
        (t.exports['#TVC:CN10Y-symbol-description'] =
          'China Government Bonds 10 YR Yield'),
        (t.exports['#TVC:DE10Y-symbol-description'] =
          'German Government Bonds 10 YR Yield'),
        (t.exports['#TVC:ES10Y-symbol-description'] =
          'Spain Government Bonds 10 YR Yield'),
        (t.exports['#TVC:FR10Y-symbol-description'] =
          'France Government Bonds 10 YR Yield'),
        (t.exports['#TVC:IN10Y-symbol-description'] = [
          'איגרות חוב ממשלתיות בהודו בתשואה של 10 שנים',
        ]),
        (t.exports['#TVC:IT10Y-symbol-description'] = [
          'איגרות חוב ממשלתיות באיטליה 10 שנים',
        ]),
        (t.exports['#TVC:JP10Y-symbol-description'] = [
          'איגרות חוב ממשלתיות של יפן בתשואה של 10 שנים',
        ]),
        (t.exports['#TVC:KR10Y-symbol-description'] =
          'Korea Government Bonds 10 YR Yield'),
        (t.exports['#TVC:MY10Y-symbol-description'] =
          'Malaysia Government Bonds 10 YR Yield'),
        (t.exports['#TVC:PT10Y-symbol-description'] =
          'Portugal Government Bonds 10 YR Yield'),
        (t.exports['#TVC:TR10Y-symbol-description'] = [
          'איגרות חוב ממשלתיות בטורקיה בתשואה של 10 שנים',
        ]),
        (t.exports['#TVC:US02Y-symbol-description'] = [
          'איגרות חוב ממשלתיות בארה"ב של 2 שנים',
        ]),
        (t.exports['#TVC:US05Y-symbol-description'] = [
          'איגרות חוב ממשלתיות בארה"ב בתשואה של 5 שנים',
        ]),
        (t.exports['#TVC:US10Y-symbol-description'] = [
          'איגרות חוב ממשלתיות בארה"ב בתשואה של 10 שנים',
        ]),
        (t.exports['#INDEX:TWII-symbol-description'] = ['מדד טאייוואן משוקלל']),
        (t.exports['#CME:J61!-symbol-description'] = [
          'חוזים עתידיים על ין יפני',
        ]),
        (t.exports['#CME_MINI:J71!-symbol-description'] = [
          'חוזים עתידיים E-mini על ין יפני',
        ]),
        (t.exports['#CME_MINI:WM1!-symbol-description'] = [
          'חוזים עתידיים E-micro יין יפני / דולר אמריקאי',
        ]),
        (t.exports['#CME:M61!-symbol-description'] = [
          'חוזים עתידיים על פזו מקסיקני',
        ]),
        (t.exports['#CME:T61!-symbol-description'] = [
          'חוזים עתידיים ראנד דרום אפריקאי',
        ]),
        (t.exports['#CME:SK1!-symbol-description'] = [
          'חוזים עתידיים על קרונה שוודית',
        ]),
        (t.exports['#CME:QT1!-symbol-description'] = [
          'חוזים עתידיים רנמינבי סיני / דולר אמריקאי',
        ]),
        (t.exports['#COMEX:AUP1!-symbol-description'] =
          'Aluminum MW U.S. Transaction Premium Platts (25MT) Futures'),
        (t.exports['#CME:L61!-symbol-description'] = [
          'חוזים עתידיים על ריאל ברזילאי',
        ]),
        (t.exports['#CME:WP1!-symbol-description'] = [
          'חוזים עתידיים על זלוטי פולני',
        ]),
        (t.exports['#CME:N61!-symbol-description'] = [
          'חוזים עתידיים על דולר ניו זילנדי',
        ]),
        (t.exports['#CME_MINI:MG1!-symbol-description'] = [
          'חוזים עתידיים E-micro דולר אוסטרלי / דולר אמריקאי',
        ]),
        (t.exports['#CME_MINI:WN1!-symbol-description'] = [
          'חוזים עתידיים E-micro פרנק שוויצרי / דולר אמריקאי',
        ]),
        (t.exports['#CME_MINI:MF1!-symbol-description'] = [
          'חוזים עתידיים E-micro אירו / דולר אמריקאי',
        ]),
        (t.exports['#CME_MINI:E71!-symbol-description'] = [
          'חוזים עתידיים E-mini יורו',
        ]),
        (t.exports['#CBOT:ZK1!-symbol-description'] =
          'Denatured Fuel Ethanol Futures'),
        (t.exports['#CME_MINI:MB1!-symbol-description'] = [
          'חוזים עתידיים E-micro לירה שטרלינג / דולר אמריקאי',
        ]),
        (t.exports['#NYMEX_MINI:QU1!-symbol-description'] = [
          'חוזים עתידיים E-mini בנזין',
        ]),
        (t.exports['#NYMEX_MINI:QX1!-symbol-description'] =
          'E-mini Heating Oil Futures'),
        (t.exports['#COMEX_MINI:QC1!-symbol-description'] = [
          'חוזים עתידיים E-mini נחושת',
        ]),
        (t.exports['#NYMEX_MINI:QG1!-symbol-description'] = [
          'חוזים עתידיים E-mini גז טבעי',
        ]),
        (t.exports['#CME:E41!-symbol-description'] = [
          'חוזים עתידיים דולר אמריקאי / לירה טורקית',
        ]),
        (t.exports['#COMEX_MINI:QI1!-symbol-description'] = [
          'חוזים עתידיים (Mini) כסף',
        ]),
        (t.exports['#CME:DL1!-symbol-description'] = 'Milk, Class III Futures'),
        (t.exports['#NYMEX:UX1!-symbol-description'] = [
          'חוזים עתידיים אורניום',
        ]),
        (t.exports['#CBOT:BO1!-symbol-description'] = [
          'חוזים עתידיים שמן סויה',
        ]),
        (t.exports['#CME:HE1!-symbol-description'] = 'Lean Hogs Futures'),
        (t.exports['#NYMEX:IAC1!-symbol-description'] =
          'Newcastle Coal Futures'),
        (t.exports['#NYMEX_MINI:QM1!-symbol-description'] =
          'E-mini Light Crude Oil Futures'),
        (t.exports['#NYMEX:JMJ1!-symbol-description'] = [
          'מיני ברנט חוזים עתידיים פיננסיים',
        ]),
        (t.exports['#COMEX:AEP1!-symbol-description'] =
          'Aluminium European Premium Futures'),
        (t.exports['#CBOT:ZQ1!-symbol-description'] =
          '30 Day Federal Funds Interest Rate Futures'),
        (t.exports['#CME:LE1!-symbol-description'] = 'Live Cattle Futures'),
        (t.exports['#CME:UP1!-symbol-description'] = [
          'חוזים עתידיים פרנק שוויצרי / ין יפני',
        ]),
        (t.exports['#CBOT:ZN1!-symbol-description'] = [
          'חוזים עתידיים 10 שנים T-Note',
        ]),
        (t.exports['#CBOT:ZB1!-symbol-description'] = [
          'חוזים עתידיים של T-Bond',
        ]),
        (t.exports['#CME:GF1!-symbol-description'] = 'Feeder Cattle Futures'),
        (t.exports['#CBOT:UD1!-symbol-description'] = 'Ultra T-Bond Futures'),
        (t.exports['#CME:I91!-symbol-description'] =
          'CME Housing Futures — Washington DC'),
        (t.exports['#CBOT:ZO1!-symbol-description'] = [
          'חוזים עתידיים שיבולת שועל',
        ]),
        (t.exports['#CBOT:ZM1!-symbol-description'] = [
          'חוזים עתידיים על ארוחות סויה',
        ]),
        (t.exports['#CBOT_MINI:XN1!-symbol-description'] = [
          'מיני חוזים עתידיים תירס',
        ]),
        (t.exports['#CBOT:ZC1!-symbol-description'] = ['חוזים עתידיים תירס']),
        (t.exports['#CME:LS1!-symbol-description'] = ['חוזים עתידיים על עץ']),
        (t.exports['#CBOT_MINI:XW1!-symbol-description'] = [
          'מיני חוזים עתידיים חיטה',
        ]),
        (t.exports['#CBOT_MINI:XK1!-symbol-description'] = [
          'מיני חוזים עתידיים על פולי סויה',
        ]),
        (t.exports['#CBOT:ZS1!-symbol-description'] = [
          'חוזים עתידיים על פולי סויה',
        ]),
        (t.exports['#NYMEX:PA1!-symbol-description'] = [
          'חוזים עתידיים פלדיום',
        ]),
        (t.exports['#CME:FTU1!-symbol-description'] =
          'E-mini FTSE 100 Index USD Futures'),
        (t.exports['#CBOT:ZR1!-symbol-description'] = ['חוזים עתידיים אורז']),
        (t.exports['#COMEX_MINI:GR1!-symbol-description'] = [
          'חוזים עתידיים זהב (E-micro)',
        ]),
        (t.exports['#COMEX_MINI:QO1!-symbol-description'] = [
          'חוזים עתידיים זהב (Mini)',
        ]),
        (t.exports['#CME_MINI:RL1!-symbol-description'] = [
          'חוזים עתידיים E-mini Russell 1000',
        ]),
        (t.exports['#CME_MINI:EW1!-symbol-description'] = [
          'חוזים עתידיים E-mini Midcap S&P 400',
        ]),
        (t.exports['#COMEX:LD1!-symbol-description'] = 'Lead Futures'),
        (t.exports['#CME_MINI:ES1!-symbol-description'] = [
          'חוזים עתידיים E-mini S&P 500',
        ]),
        (t.exports['#TVC:SA40-symbol-description'] = [
          'מדד Top 40 דרום אפריקה',
        ]),
        (t.exports['#BMV:ME-symbol-description'] = ['מדד IPC Mexico']),
        (t.exports['#BCBA:IMV-symbol-description'] = ['מדד MERVAL']),
        (t.exports['#HSI:HSI-symbol-description'] = ['מדד האנג סנג']),
        (t.exports['#BVL:SPBLPGPT-symbol-description'] =
          'S&P / BVL Peru General Index (PEN)'),
        (t.exports['#EGX:EGX30-symbol-description'] =
          'EGX 30 Price Return Index'),
        (t.exports['#BVC:IGBC-symbol-description'] =
          'Indice General de la Bolsa de Valores de Colombia'),
        (t.exports['#TWSE:TAIEX-symbol-description'] =
          'Taiwan Capitalization Weighted Stock Index'),
        (t.exports['#QSE:GNRI-symbol-description'] = ['מדד QE']),
        (t.exports['#BME:IBC-symbol-description'] = ['מדד IBEX 35']),
        (t.exports['#NZX:NZ50G-symbol-description'] =
          'S&P / NZX 50 Index Gross'),
        (t.exports['#SIX:SMI-symbol-description'] = ['מדד שוק שוויצרי']),
        (t.exports['#SZSE:399001-symbol-description'] = ['מדד SZSE Component']),
        (t.exports['#TADAWUL:TASI-symbol-description'] = [
          'מדד כל המניות של Tadawul',
        ]),
        (t.exports['#IDX:COMPOSITE-symbol-description'] = [
          'מדד IDX Composite',
        ]),
        (t.exports['#EURONEXT:PX1-symbol-description'] = ['מדד CAC 40']),
        (t.exports['#OMXHEX:OMXH25-symbol-description'] = [
          'מדד OMX הלסינקי 25',
        ]),
        (t.exports['#EURONEXT:BEL20-symbol-description'] = ['מדד BEL 20']),
        (t.exports['#TVC:STI-symbol-description'] = ['מדד Straits Times']),
        (t.exports['#DFM:DFMGI-symbol-description'] = ['מדד DFM']),
        (t.exports['#TVC:KOSPI-symbol-description'] = [
          'מדד מחירי המניות של קוריאה',
        ]),
        (t.exports['#FTSEMYX:FBMKLCI-symbol-description'] = [
          'מדד FTSE בורסת מלזיה KLCI',
        ]),
        (t.exports['#TASE:TA35-symbol-description'] = ['מדד תל אביב 35']),
        (t.exports['#OMXSTO:OMXS30-symbol-description'] = [
          'מדד OMX שטוקהולם 30',
        ]),
        (t.exports['#OMXICE:OMXI8-symbol-description'] = ['מדד OMX איסלנד 8']),
        (t.exports['#NSENG:NSE30-symbol-description'] = ['מדד NSE 30']),
        (t.exports['#BAHRAIN:BSEX-symbol-description'] = [
          'מדד כל מניות בחריין',
        ]),
        (t.exports['#OMXTSE:OMXTGI-symbol-description'] = 'OMX Tallinn GI'),
        (t.exports['#OMXCOP:OMXC25-symbol-description'] = [
          'מדד OMX קופנהגן 25',
        ]),
        (t.exports['#OMXRSE:OMXRGI-symbol-description'] = 'OMX Riga GI'),
        (t.exports['#BELEX:BELEX15-symbol-description'] = ['מדד BELEX 15']),
        (t.exports['#OMXVSE:OMXVGI-symbol-description'] = 'OMX Vilnius GI'),
        (t.exports['#EURONEXT:AEX-symbol-description'] = ['מדד AEX']),
        (t.exports['#CBOE:VIX-symbol-description'] = [
          'מדד Volatility S&P 500',
        ]),
        (t.exports['#NASDAQ:XAU-symbol-description'] =
          'PHLX Gold and Silver Sector Index'),
        (t.exports['#DJ:DJUSCL-symbol-description'] = [
          'מדד דאו ג\'ונס פחם ארה"ב',
        ]),
        (t.exports['#DJ:DJCIKC-symbol-description'] = [
          "מדד דאו ג'ונס סחורות קפה",
        ]),
        (t.exports['#DJ:DJCIEN-symbol-description'] = [
          "דאו ג'ונס מדד סחורות האנרגיה",
        ]),
        (t.exports['#NASDAQ:OSX-symbol-description'] =
          'PHLX Oil Service Sector Index'),
        (t.exports['#DJ:DJCISB-symbol-description'] = [
          "דאו ג'ונס מדד סחורות הסוכר",
        ]),
        (t.exports['#DJ:DJCICC-symbol-description'] = [
          "דאו ג'ונס מדד סחורות הקקאו",
        ]),
        (t.exports['#DJ:DJCIGR-symbol-description'] = [
          "מדד דאו ג'ונס סחורות דגנים",
        ]),
        (t.exports['#DJ:DJCIAGC-symbol-description'] =
          'Dow Jones Commodity Index Agriculture Capped Component'),
        (t.exports['#DJ:DJCISI-symbol-description'] = [
          "דאו ג 'ונס מדד סחורות כסף",
        ]),
        (t.exports['#DJ:DJCIIK-symbol-description'] = [
          "מדד דאו ג 'ונס סחורות ניקל",
        ]),
        (t.exports['#NASDAQ:HGX-symbol-description'] =
          'PHLX Housing Sector Index'),
        (t.exports['#DJ:DJCIGC-symbol-description'] = [
          "מדד דאו ג'ונס סחורות זהב",
        ]),
        (t.exports['#SP:SPGSCI-symbol-description'] =
          'S&P Goldman Sachs Commodity Index'),
        (t.exports['#NASDAQ:UTY-symbol-description'] =
          'PHLX Utility Sector Index'),
        (t.exports['#DJ:DJU-symbol-description'] =
          'Dow Jones Utility Average Index'),
        (t.exports['#SP:SVX-symbol-description'] = ['מדד S&P 500 Value']),
        (t.exports['#SP:OEX-symbol-description'] = ['מדד S&P 100']),
        (t.exports['#CBOE:OEX-symbol-description'] = ['מדד S&P 100']),
        (t.exports['#NASDAQ:SOX-symbol-description'] = [
          'מדד מוליכים למחצה פילדלפיה',
        ]),
        (t.exports['#RUSSELL:RUI-symbol-description'] = ['מדד Russel 1000']),
        (t.exports['#RUSSELL:RUA-symbol-description'] = ['מדד Russel 3000']),
        (t.exports['#RUSSELL:RUT-symbol-description'] = ['מדד Russel 2000']),
        (t.exports['#NYSE:XMI-symbol-description'] =
          'NYSE ARCA Major Market Index'),
        (t.exports['#NYSE:XAX-symbol-description'] = ['מדד AMEX Composite']),
        (t.exports['#NASDAQ:NDX-symbol-description'] = ['מדד נאסד"ק 100']),
        (t.exports['#NASDAQ:IXIC-symbol-description'] = [
          'מדד Nasdaq Composite',
        ]),
        (t.exports['#DJ:DJT-symbol-description'] = [
          "מדד דאו ג'ונס ממוצע תחבורה",
        ]),
        (t.exports['#NYSE:NYA-symbol-description'] = ['מדד NYSE Composite']),
        (t.exports['#NYMEX:CJ1!-symbol-description'] = [
          'חוזים עתידיים על קקאו',
        ]),
        (t.exports['#USDILS-symbol-description'] = [
          'דולר אמריקאי / שקל ישראלי',
        ]),
        (t.exports['#TSXV:F-symbol-description'] = ['פיורי זהב בע"מ']),
        (t.exports['#SIX:F-symbol-description'] = ['חברת פורד מוטור']),
        (t.exports['#BMV:F-symbol-description'] = ['חברת פורד מוטור']),
        (t.exports['#TWII-symbol-description'] = ['מדד טאייוואן משוקלל']),
        (t.exports['#TVC:PL10Y-symbol-description'] = [
          'אג"ח ממשלת פולין תשואה 10 שנים',
        ]),
        (t.exports['#TVC:PL05Y-symbol-description'] = [
          'אג"ח ממשלת פולין תשואה 5 שנים',
        ]),
        (t.exports['#SET:GC-symbol-description'] = [
          'חיבורים גלובליים חברה ציבורית',
        ]),
        (t.exports['#TSX:GC-symbol-description'] = [
          'תאגיד המשחקים הנהדר של קנדה',
        ]),
        (t.exports['#TVC:FTMIB-symbol-description'] =
          'Milano Italia Borsa Index'),
        (t.exports['#OANDA:SPX500USD-symbol-description'] = ['מדד S&P 500']),
        (t.exports['#BMV:CT-symbol-description'] = ['SX20 RT סין']),
        (t.exports['#TSXV:CT-symbol-description'] = ['חברת הכרייה סנטנרה']),
        (t.exports['#BYBIT:ETHUSD-symbol-description'] = ['חוזה תמידי ETHUSD']),
        (t.exports['#BYBIT:XRPUSD-symbol-description'] = ['חוזה תמידי XRPUSD']),
        (t.exports['#BYBIT:BTCUSD-symbol-description'] = ['חוזה תמידי BTCUSD']),
        (t.exports['#BITMEX:ETHUSD-symbol-description'] = [
          'ETHUSD  חוזים עתידיים תמידיים',
        ]),
        (t.exports['#DERIBIT:BTCUSD-symbol-description'] = [
          'BTCUSD חוזים עתידיים תמידיים',
        ]),
        (t.exports['#DERIBIT:ETHUSD-symbol-description'] = [
          'ETHUSD חוזים עתידיים תמידיים',
        ]),
        (t.exports['#USDHUF-symbol-description'] = [
          'דולר ארה"ב/פורינט הונגרי',
        ]),
        (t.exports['#USDTHB-symbol-description'] = ['דולר ארה"ב/בהט תאילנדי']),
        (t.exports['#FOREXCOM:US2000-symbol-description'] = [
          'Small Cap ארה"ב 2000',
        ]),
        (t.exports['#TSXV:PBR-symbol-description'] = ['Para Resources Inc.']),
        (t.exports['#NYSE:SI-symbol-description'] = [
          'חברת Silvergate Capital',
        ]),
        (t.exports['#NASDAQ:LE-symbol-description'] = ['Lands\' End בע"מ']),
        (t.exports['#CME:CB1!-symbol-description'] = [
          'חמאה חוזים עתידיים-מזומן (רציף: חוזה נוכחי מלפנים)',
        ]),
        (t.exports['#LSE:SCHO-symbol-description'] =
          'Scholium Group Plc Ord 1P'),
        (t.exports['#NEO:HE-symbol-description'] = [
          'חברת שירותי אנרגיה הוואי.',
        ]),
        (t.exports['#NYSE:HE-symbol-description'] = ['תעשיות חשמל הוואי']),
        (t.exports['#OMXCOP:SCHO-symbol-description'] = 'Schouw & Co A/S'),
        (t.exports['#TSX:HE-symbol-description'] = [
          'חברת שירותי אנרגיה הוואי.',
        ]),
        (t.exports['#BSE:ITI-symbol-description'] = ['ITI בע"מ']),
        (t.exports['#NSE:ITI-symbol-description'] = [
          'תעשיות טלפון הודיות בע"מ',
        ]),
        (t.exports['#TSX:LS-symbol-description'] = [
          'קרן הדיבידנד של Middlefield Healthcare & מדעי החיים',
        ]),
        (t.exports['#BITMEX:XBT-symbol-description'] = [
          'מדד ביטקוין/דולר ארה"ב',
        ]),
        (t.exports['#CME_MINI:RTY1!-symbol-description'] = [
          'E-Mini ראסל 2000 אינדקס חוזים עתידיים',
        ]),
        (t.exports['#CRYPTOCAP:TOTAL-symbol-description'] = [
          'שווי שוק קריפטו כולל, $',
        ]),
        (t.exports['#ICEUS:DX1!-symbol-description'] = [
          'חוזים עתידיים על מדד דולר ארה"ב',
        ]),
        (t.exports['#NYMEX:TT1!-symbol-description'] = ['חוזים עתידיים כותנה']),
        (t.exports['#PHEMEX:BTCUSD-symbol-description'] = [
          'חוזה עתידי תמידי של BTC',
        ]),
        (t.exports['#PHEMEX:ETHUSD-symbol-description'] = [
          'חוזה עתידי תמידי של ETH',
        ]),
        (t.exports['#PHEMEX:XRPUSD-symbol-description'] = [
          'חוזה עתידי תמידי של XRP',
        ]),
        (t.exports['#PHEMEX:LTCUSD-symbol-description'] = [
          'חוזה עתידי תמידי של LTC',
        ]),
        (t.exports['#BITCOKE:BCHUSD-symbol-description'] = 'BCH Quanto Swap'),
        (t.exports['#BITCOKE:BTCUSD-symbol-description'] = 'BTC Quanto Swap'),
        (t.exports['#BITCOKE:ETHUSD-symbol-description'] = 'ETH Quanto Swap'),
        (t.exports['#BITCOKE:LTCUSD-symbol-description'] = 'LTC Quanto Swap'),
        (t.exports['#TVC:CA10-symbol-description'] = [
          'אגרות חוב ממשלתיות קנדיות, 10 שנים',
        ]),
        (t.exports['#TVC:CA10Y-symbol-description'] = [
          'איגרות חוב ממשלתיות קנדיות בתשואה של 10 שנים',
        ]),
        (t.exports['#TVC:ID10Y-symbol-description'] = [
          'איגרות חוב ממשלתיות באינדונזיה בתשואה של 10 שנים',
        ]),
        (t.exports['#TVC:NL10-symbol-description'] = [
          'אגרות חוב ממשלתיות בהולנד, 10 שנים',
        ]),
        (t.exports['#TVC:NL10Y-symbol-description'] = [
          'איגרות חוב של ממשלת הולנד בתשואה של 10 שנים',
        ]),
        (t.exports['#TVC:NZ10-symbol-description'] = [
          'איגרות חוב ממשלתיות בניו זילנד, 10 שנים',
        ]),
        (t.exports['#TVC:NZ10Y-symbol-description'] = [
          'איגרות חוב ממשלתיות בניו זילנד בתשואה של 10 שנים',
        ]),
        (t.exports['#SOLUSD-symbol-description'] = ['סולנה / ארה"ב דוֹלָר']),
        (t.exports['#LUNAUSD-symbol-description'] = ['לונה / ארה"ב דוֹלָר']),
        (t.exports['#UNIUSD-symbol-description'] = ['Uniswap / ארה"ב דוֹלָר']),
        (t.exports['#LTCBRL-symbol-description'] = ['לייטקוין / ריאל ברזילאי']),
        (t.exports['#ETCEUR-symbol-description'] = ["את'ריום קלאסיק / אירו"]),
        (t.exports['#ETHKRW-symbol-description'] = [
          "את'ריום / וון דרום קוריאני",
        ]),
        (t.exports['#BTCRUB-symbol-description'] = ['ביטקוין / רובל רוסי']),
        (t.exports['#BTCTHB-symbol-description'] = ['ביטקוין / באט תאילנדי']),
        (t.exports['#ETHTHB-symbol-description'] = ["את'ריום / בהט תאילנדי"]),
        (t.exports['#TVC:EU10YY-symbol-description'] = [
          '#TVC:EU10YY-תיאור-סימול',
        ]);
    },
  },
]);
