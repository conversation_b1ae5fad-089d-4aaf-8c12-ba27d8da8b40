(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (t) => {
      t.exports = ['增加'];
    },
    53585: (t) => {
      t.exports = ['增加自訂顏色'];
    },
    81865: (t) => {
      t.exports = ['不透明度'];
    },
    73755: (t) => {
      t.exports = ['另一個代碼'];
    },
    16936: (t) => {
      t.exports = ['返回'];
    },
    88046: (t) => {
      t.exports = ['主圖表代碼'];
    },
    9898: (t) => {
      t.exports = ['認股權'];
    },
    20036: (t) => {
      t.exports = ['取消'];
    },
    23398: (t) => {
      t.exports = ['變更商品'];
    },
    94551: (t) => {
      t.exports = ['圖表'];
    },
    64498: (t) => {
      t.exports = ['全部來源'];
    },
    73226: (t) => {
      t.exports = ['套用'];
    },
    79852: (t) => {
      t.exports = ['債券'];
    },
    56095: (t) => {
      t.exports = ['減少'];
    },
    29601: (t) => {
      t.exports = ['描述'];
    },
    46812: (t) => {
      t.exports = ['增加'];
    },
    89298: (t) => {
      t.exports = ['偏移'];
    },
    68988: (t) => {
      t.exports = ['確認'];
    },
    29673: (t) => {
      t.exports = ['沒有交易所符合您的條件'];
    },
    41379: (t) => {
      t.exports = ['沒有商品符合您的條件'];
    },
    35563: (t) => {
      t.exports = ['號碼格式無效。'];
    },
    19724: (t) => {
      t.exports = ['來源'];
    },
    59877: (t) => {
      t.exports = ['為{studyShortDescription}設定{inputInline}時間和價格'];
    },
    18571: (t) => {
      t.exports = ['為{studyShortDescription}設定{inputTitle}時間'];
    },
    58552: (t) => {
      t.exports = ['為{studyShortDescription}設定{inputTitle}價格'];
    },
    80481: (t) => {
      t.exports = ['設定“{studyShortDescription}”的時間和價格'];
    },
    42917: (t) => {
      t.exports = ['設定“{studyShortDescription}”的時間'];
    },
    6083: (t) => {
      t.exports = ['設定“{studyShortDescription}”的價格'];
    },
    52298: (t) => {
      t.exports = ['搜尋'];
    },
    13269: (t) => {
      t.exports = ['選擇來源'];
    },
    2607: (t) => {
      t.exports = ['指定值大於商品最大值{max}。'];
    },
    53669: (t) => {
      t.exports = ['指定值小於商品最小值{min}。'];
    },
    89053: (t) => {
      t.exports = ['商品代碼'];
    },
    48490: (t) => {
      t.exports = ['商品&描述'];
    },
    99983: (t) => {
      t.exports = ['商品搜尋'];
    },
    54336: (t) => {
      t.exports = ['移除顏色'];
    },
    60142: (t) => {
      t.exports = ['厚度'];
    },
    87592: (t) => {
      t.exports = 'cfd';
    },
    17023: (t) => {
      t.exports = ['改變不透明度'];
    },
    13066: (t) => {
      t.exports = ['更改顏色'];
    },
    95657: (t) => {
      t.exports = ['改變密度'];
    },
    18567: (t) => {
      t.exports = ['更改{propertyName}屬性'];
    },
    36962: (t) => {
      t.exports = ['收盤'];
    },
    8448: (t) => {
      t.exports = 'crypto';
    },
    1328: (t) => {
      t.exports = 'dr';
    },
    88720: (t) => {
      t.exports = ['經濟'];
    },
    39512: (t) => {
      t.exports = ['外匯'];
    },
    81859: (t) => {
      t.exports = ['期貨'];
    },
    39337: (t) => {
      t.exports = ['高點'];
    },
    91815: (t) => {
      t.exports = ['高低2'];
    },
    40771: (t) => {
      t.exports = ['高低3'];
    },
    9523: (t) => {
      t.exports = 'hlcc4';
    },
    12754: (t) => {
      t.exports = ['指數'];
    },
    38071: (t) => {
      t.exports = ['指數'];
    },
    12504: (t) => {
      t.exports = ['開高低收4'];
    },
    38466: (t) => {
      t.exports = ['開盤'];
    },
    3919: (t) => {
      t.exports = ['低點'];
    },
    36931: (t) => {
      t.exports = ['股票'];
    },
  },
]);
