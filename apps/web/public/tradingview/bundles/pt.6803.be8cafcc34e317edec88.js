(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6803],
  {
    22353: (r) => {
      r.exports = ['(Abr.+Máx.+Mín.+Fch.)/4'];
    },
    94884: (r) => {
      r.exports = ['(Máx.+Mín.+Fch.)/3'];
    },
    10591: (r) => {
      r.exports = ['(Máx.+Mín.)/2'];
    },
    63243: (r) => {
      r.exports = ['Colorir barra de acordo com o fechamento anterior'];
    },
    72171: (r) => {
      r.exports = ['Centro'];
    },
    9994: (r) => {
      r.exports = ['Ajustar dados de dividendos'];
    },
    10989: (r) => {
      r.exports = ['Ajustes para mudanças no contrato'];
    },
    70816: (r) => {
      r.exports = ['Média de fechamento'];
    },
    91757: (r) => {
      r.exports = ['Em baixo'];
    },
    50430: (r) => {
      r.exports = ['Linha de Base'];
    },
    83760: (r) => {
      r.exports = ['Corpo'];
    },
    72269: (r) => {
      r.exports = ['Contorno'];
    },
    7445: (r) => {
      r.exports = ['Nível Base'];
    },
    47586: (r) => {
      r.exports = ['Compra e venda'];
    },
    39667: (r) => {
      r.exports = ['Barras Baixistas'];
    },
    87151: (r) => {
      r.exports = ['Cor abaixo'];
    },
    81285: (r) => {
      r.exports = ['Modificação dos dados'];
    },
    4329: (r) => {
      r.exports = ['Padrão'];
    },
    86846: (r) => {
      r.exports = ['Preencher'];
    },
    58747: (r) => {
      r.exports = ['Preencher Área Superior'];
    },
    11157: (r) => {
      r.exports = ['Preencher Área Inferior'];
    },
    86953: (r) => {
      r.exports = ['Barras HLC'];
    },
    77405: (r) => {
      r.exports = 'Horizontal';
    },
    39292: (r) => {
      r.exports = ['Máxima e Mínima'];
    },
    15107: (r) => {
      r.exports = ['Preço'];
    },
    19286: (r) => {
      r.exports = ['Esquerda'];
    },
    76476: (r) => {
      r.exports = ['No meio'];
    },
    27879: (r) => {
      r.exports = ['Simples'];
    },
    2391: (r) => {
      r.exports = ['Degrau'];
    },
    6350: (r) => {
      r.exports = ['Pré/pós mercado'];
    },
    62521: (r) => {
      r.exports = ['Histórico das horas pré/pós mercado'];
    },
    73947: (r) => {
      r.exports = ['Precisão'];
    },
    8094: (r) => {
      r.exports = ['Fechamento dia anterior'];
    },
    77986: (r) => {
      r.exports = ['Linhas de preço'];
    },
    24248: (r) => {
      r.exports = ['Fonte de preço'];
    },
    94089: (r) => {
      r.exports = ['Barras de projeção altista'];
    },
    5704: (r) => {
      r.exports = ['Barras de projeção baixista'];
    },
    29881: (r) => {
      r.exports = [
        'Preços reais na escala de preços (em vez do preço Heikin-Ashi)',
      ];
    },
    21141: (r) => {
      r.exports = ['Direita'];
    },
    44673: (r) => {
      r.exports = ['Com marcadores'];
    },
    26458: (r) => {
      r.exports = ['Pavio'];
    },
    65994: (r) => {
      r.exports = ['Em cima'];
    },
    57417: (r) => {
      r.exports = ['Linha de Topo'];
    },
    92960: (r) => {
      r.exports = ['Alinhamento do texto'];
    },
    90581: (r) => {
      r.exports = ['Orientação do Texto'];
    },
    55314: (r) => {
      r.exports = ['Barras Finas'];
    },
    87492: (r) => {
      r.exports = ['Fuso Horário'];
    },
    58416: (r) => {
      r.exports = ['Tipo'];
    },
    5536: (r) => {
      r.exports = ['Cor Acima'];
    },
    83610: (r) => {
      r.exports = ['Barras Altistas'];
    },
    23500: (r) => {
      r.exports = ['Usar a liquidação como fechamento no intervalo diário'];
    },
    44085: (r) => {
      r.exports = 'Vertical';
    },
    30792: (r) => {
      r.exports = ['vela'];
    },
    55740: (r) => {
      r.exports = ['alterar barras HLC'];
    },
    90168: (r) => {
      r.exports = ['alterar espessura da linha do preço médio de fechamento'];
    },
    30385: (r) => {
      r.exports = ['alterar cor da linha do preço médio de fechamento'];
    },
    97008: (r) => {
      r.exports = ['alterar a cor de preenchimento da área'];
    },
    6610: (r) => {
      r.exports = ['alterar a espessura da linha de área'];
    },
    661: (r) => {
      r.exports = ['alterar a cor da linha de área'];
    },
    1316: (r) => {
      r.exports = ['alterar a área do preço fonte'];
    },
    29180: (r) => {
      r.exports = ['alterar cor da linha de venda'];
    },
    31547: (r) => {
      r.exports = ['mudar o nível base'];
    },
    4164: (r) => {
      r.exports = ['alterar a cor da base da linha de base'];
    },
    38990: (r) => {
      r.exports = ['alterar a largura da base da linha de base'];
    },
    73163: (r) => {
      r.exports = [
        'alterar a cor de preenchimento da base da área da linha de base',
      ];
    },
    12673: (r) => {
      r.exports = [
        'alterar a cor de preenchimento do topo da área da linha de base',
      ];
    },
    56819: (r) => {
      r.exports = ['alterar da linha de base do preço fonte'];
    },
    68621: (r) => {
      r.exports = ['alterar a cor da linha de base'];
    },
    35339: (r) => {
      r.exports = ['alterar a lagura do topo da linha de base'];
    },
    76804: (r) => {
      r.exports = ['alterar cor da barra para cima'];
    },
    71816: (r) => {
      r.exports = ['alterar cor da barra para baixo'];
    },
    36703: (r) => {
      r.exports = ['alterar a cor da linha de compra'];
    },
    29353: (r) => {
      r.exports = ['alterar cor da barra baseado no fechamento anterior'];
    },
    85709: (r) => {
      r.exports = ['alterar a cor da coluna acima'];
    },
    12155: (r) => {
      r.exports = ['alterar a cor da coluna abaixo'];
    },
    66890: (r) => {
      r.exports = ['alterar preço fonte da coluna'];
    },
    71809: (r) => {
      r.exports = ['alterar casas decimais'];
    },
    31317: (r) => {
      r.exports = ['alterar cor das horas estendidas'];
    },
    60944: (r) => {
      r.exports = ['alterar cor da linha de preço máximo e mínimo'];
    },
    83708: (r) => {
      r.exports = ['alterar espessura da linha de preço máximo e mínimo'];
    },
    81080: (r) => {
      r.exports = ['alterar a cor do corpo do máx-min'];
    },
    30033: (r) => {
      r.exports = ['alterar a visibilidade do corpo do máx-min'];
    },
    76885: (r) => {
      r.exports = ['alterar a cor da borda do máx-min'];
    },
    79236: (r) => {
      r.exports = ['alterar a visibilidade das bordas do máx-min'];
    },
    42981: (r) => {
      r.exports = ['alterar a visibilidade da legenda do máx-min'];
    },
    31937: (r) => {
      r.exports = ['alterar a cor da legenda do máx-min'];
    },
    87828: (r) => {
      r.exports = ['alterar a cor da linha'];
    },
    17119: (r) => {
      r.exports = ['alterar a linha do preço fonte'];
    },
    69125: (r) => {
      r.exports = ['alterar a espessura da linha'];
    },
    70054: (r) => {
      r.exports = ['alterar o tipo de linha'];
    },
    49973: (r) => {
      r.exports = ['alterar cor do pós-mercado'];
    },
    5969: (r) => {
      r.exports = ['alterar cor da linha do pós-mercado'];
    },
    50393: (r) => {
      r.exports = ['mudar a visibilidade das linhas de preço pré/pós mercado'];
    },
    46257: (r) => {
      r.exports = ['alterar cor do pré-mercado'];
    },
    60852: (r) => {
      r.exports = ['alterar a cor da linha do pré-mercado'];
    },
    91183: (r) => {
      r.exports = ['alterar cor da linha do preço do fechamento anterior'];
    },
    87631: (r) => {
      r.exports = ['alterar largura da linha do preço do fechamento anterior'];
    },
    77640: (r) => {
      r.exports = ['alterar cor da linha de preço'];
    },
    97322: (r) => {
      r.exports = ['alterar largura da linha de preço'];
    },
    28143: (r) => {
      r.exports = ['alterar o range de barras finas'];
    },
    75986: (r) => {
      r.exports = ['mudar a cor da parte inferior do pavio renko'];
    },
    7747: (r) => {
      r.exports = ['mudar a cor da parte superior pavio renko'];
    },
    9473: (r) => {
      r.exports = ['mudar a visibilidade do pavio renko'];
    },
    39783: (r) => {
      r.exports = [
        'alterar a exibição dos preços reais na escala de preços (em vez do preço Heiken-Ashi)',
      ];
    },
    72886: (r) => {
      r.exports = ['alterar a espessura da barra'];
    },
    5464: (r) => {
      r.exports = ['alterar a cor da borda superior de {candleType}'];
    },
    61118: (r) => {
      r.exports = ['alterar a cor da parte superior de {candleType}'];
    },
    60164: (r) => {
      r.exports = ['alterar a cor da parte inferior do pavil de {candleType}'];
    },
    45543: (r) => {
      r.exports = ['alterar a cor da parte superior do pavil de {candleType}'];
    },
    39987: (r) => {
      r.exports = ['alterar a visibilidade do pavil de {candleType}'];
    },
    47202: (r) => {
      r.exports = ['alterar visibilidade do corpo de {candleType}'];
    },
    23986: (r) => {
      r.exports = ['alterar a visibildiade da borda de {candleType}'];
    },
    92330: (r) => {
      r.exports = ['alterar a cor da borda inferior de {candleType}'];
    },
    36320: (r) => {
      r.exports = ['alterar a cor da parte inferior de {candleType}'];
    },
    79088: (r) => {
      r.exports = [
        'alterar a cor da parte inferior da borda da barra de {chartType}',
      ];
    },
    11107: (r) => {
      r.exports = [
        'alterar a cor da parte superior da borda da barra de {chartType}',
      ];
    },
    85503: (r) => {
      r.exports = ['alterar a cor da parte inferior de {chartType}'];
    },
    61250: (r) => {
      r.exports = [
        'alterar a cor da parte superior da borda da barra projetada de {chartType}',
      ];
    },
    18465: (r) => {
      r.exports = [
        'alterar a cor da parte inferior da barra projetada de {chartType}',
      ];
    },
    50453: (r) => {
      r.exports = [
        'alterar a cor da parte superior da barra projetada de {chartType}',
      ];
    },
    59414: (r) => {
      r.exports = ['alterar a cor da parte superior de {chartType}'];
    },
    21547: (r) => {
      r.exports = ['alterar propriedade de {inputName}'];
    },
    42390: (r) => {
      r.exports = ['ajustar dados de dividendos'];
    },
    99511: (r) => {
      r.exports = ['ajustes para mudanças no contrato'];
    },
    75165: (r) => {
      r.exports = ['candles cazios'];
    },
    18995: (r) => {
      r.exports = 'range';
    },
    47500: (r) => {
      r.exports = ['Renko'];
    },
    98402: (r) => {
      r.exports = ['usar a liquidação como próximo no intervalo diário'];
    },
  },
]);
