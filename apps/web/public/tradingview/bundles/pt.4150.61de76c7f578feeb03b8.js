(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4150],
  {
    74274: (e) => {
      e.exports = ['Cruz'];
    },
    41596: (e) => {
      e.exports = ['Legendas na escala de preço'];
    },
    23545: (e) => {
      e.exports = ['Valores na linha de status'];
    },
    39495: (e) => {
      e.exports = ['Círculos'];
    },
    41389: (e) => {
      e.exports = ['Acima da barra'];
    },
    29520: (e) => {
      e.exports = ['Absoluto'];
    },
    58102: (e) => {
      e.exports = ['Aplicar padrões'];
    },
    65262: (e) => {
      e.exports = ['Área com quebras'];
    },
    83760: (e) => {
      e.exports = ['Corpo'];
    },
    48848: (e) => {
      e.exports = ['Contorno'];
    },
    27331: (e) => {
      e.exports = ['Fundo'];
    },
    78626: (e) => {
      e.exports = ['Abaixo da barra'];
    },
    41361: (e) => {
      e.exports = ['Inferior'];
    },
    22192: (e) => {
      e.exports = ['Dias'];
    },
    31577: (e) => {
      e.exports = ['VA em desenvolvimento'];
    },
    4329: (e) => {
      e.exports = ['Padrão'];
    },
    98938: (e) => {
      e.exports = ['Padrões'];
    },
    63099: (e) => {
      e.exports = ['Horas'];
    },
    11091: (e) => {
      e.exports = ['Histograma'];
    },
    66304: (e) => {
      e.exports = ['Valores'];
    },
    40297: (e) => {
      e.exports = ['Saídas'];
    },
    36993: (e) => {
      e.exports = ['Alterar Tick Mín.'];
    },
    64606: (e) => {
      e.exports = ['Fonte da Legenda'];
    },
    54934: (e) => {
      e.exports = ['Linha com quebras'];
    },
    95543: (e) => {
      e.exports = ['Meses'];
    },
    28134: (e) => {
      e.exports = ['Minutos'];
    },
    18229: (e) => {
      e.exports = ['Salvar como padrão'];
    },
    71129: (e) => {
      e.exports = ['Segundos'];
    },
    86520: (e) => {
      e.exports = ['Legendas do Sinal'];
    },
    79511: (e) => {
      e.exports = ['Linha de Referência'];
    },
    64108: (e) => {
      e.exports = 'Step line with breaks';
    },
    67767: (e) => {
      e.exports = ['Linha de níveis com losangos'];
    },
    21861: (e) => {
      e.exports = ['Posição'];
    },
    73947: (e) => {
      e.exports = ['Precisão'];
    },
    66596: (e) => {
      e.exports = ['Quantidade'];
    },
    86672: (e) => {
      e.exports = 'Ranges';
    },
    79782: (e) => {
      e.exports = ['Redefinir configurações'];
    },
    21594: (e) => {
      e.exports = ['Semanas'];
    },
    26458: (e) => {
      e.exports = ['Pavio'];
    },
    95247: (e) => {
      e.exports = ['Largura (% da caixa)'];
    },
    19221: (e) => {
      e.exports = ['Cor do texto'];
    },
    7138: (e) => {
      e.exports = ['Negociações no gráfico'];
    },
    98802: (e) => {
      e.exports = ['Superior'];
    },
    14414: (e) => {
      e.exports = ['Perfil de Volume'];
    },
    91322: (e) => {
      e.exports = ['Valores'];
    },
    20834: (e) => {
      e.exports = ['Mudar o Tick Mínimo'];
    },
    98491: (e) => {
      e.exports = ['Mudar Ícone'];
    },
    7378: (e) => {
      e.exports = ['Alterar Tamanho da Fonte'];
    },
    28691: (e) => {
      e.exports = ['Mudar Estilo da Linha'];
    },
    38361: (e) => {
      e.exports = ['Mudar Localização'];
    },
    51081: (e) => {
      e.exports = ['Alterar Alcance Porcentual'];
    },
    47634: (e) => {
      e.exports = ['Alterar Posicionamento'];
    },
    15683: (e) => {
      e.exports = ['Mudar Tipo de Plotagem'];
    },
    164: (e) => {
      e.exports = ['Mudar Precisão'];
    },
    86888: (e) => {
      e.exports = ['Mudar Formato'];
    },
    50463: (e) => {
      e.exports = ['Mudar Valor'];
    },
    12628: (e) => {
      e.exports = ['mudar a visibilidade dos valores'];
    },
    13355: (e) => {
      e.exports = ['alterar {title} em dias para'];
    },
    41377: (e) => {
      e.exports = ['alterar {title} em dias de'];
    },
    35388: (e) => {
      e.exports = ['alterar {title} em horas de'];
    },
    78586: (e) => {
      e.exports = ['alterar {title} em horas para'];
    },
    59635: (e) => {
      e.exports = ['alterar {title} em meses de'];
    },
    74266: (e) => {
      e.exports = ['alterar {title} em meses para'];
    },
    91633: (e) => {
      e.exports = ['alterar {title} em minutos para'];
    },
    15106: (e) => {
      e.exports = ['alterar {title} em minutos de'];
    },
    66161: (e) => {
      e.exports = ['alterar {title} em segundos para'];
    },
    2822: (e) => {
      e.exports = ['alterar {title} em segundos de'];
    },
    21339: (e) => {
      e.exports = ['alterar {title} em semanas de'];
    },
    68643: (e) => {
      e.exports = ['alterar {title} em semanas para'];
    },
    30810: (e) => {
      e.exports = ['alterar a visibilidade de {title} nos ticks'];
    },
    24941: (e) => {
      e.exports = ['alterar visibilidade em semanas de {title}'];
    },
    29088: (e) => {
      e.exports = ['alterar visibilidade em dias de {title}'];
    },
    68971: (e) => {
      e.exports = ['alterar a visibilidade em horas de {title}'];
    },
    64370: (e) => {
      e.exports = ['alterar visibilidade em minutos de {title}'];
    },
    6659: (e) => {
      e.exports = ['alterar visibilidade em meses de {title}'];
    },
    29091: (e) => {
      e.exports = ['alterar visibilidade no range de {title}'];
    },
    46948: (e) => {
      e.exports = ['alterar a visibilidade em segundos de {title}'];
    },
    82211: (e) => {
      e.exports = ['Dias'];
    },
    33486: (e) => {
      e.exports = ['dias para'];
    },
    14077: (e) => {
      e.exports = ['dias a partir de'];
    },
    3143: (e) => {
      e.exports = ['Horas'];
    },
    84775: (e) => {
      e.exports = ['horas a partir de'];
    },
    11255: (e) => {
      e.exports = ['horas para'];
    },
    58964: (e) => {
      e.exports = ['Meses'];
    },
    71770: (e) => {
      e.exports = ['meses a partir de'];
    },
    37179: (e) => {
      e.exports = ['meses para'];
    },
    16465: (e) => {
      e.exports = ['Minutos'];
    },
    72317: (e) => {
      e.exports = ['minutos para'];
    },
    25586: (e) => {
      e.exports = ['minutos a partir de'];
    },
    32925: (e) => {
      e.exports = ['segundos'];
    },
    39017: (e) => {
      e.exports = ['segundos para'];
    },
    6049: (e) => {
      e.exports = ['segundos a partir de'];
    },
    13604: (e) => {
      e.exports = ['Ranges'];
    },
    93016: (e) => {
      e.exports = ['semanas'];
    },
    32002: (e) => {
      e.exports = ['semanas a partir de'];
    },
    28091: (e) => {
      e.exports = ['semanas para'];
    },
    59523: (e) => {
      e.exports = ['Ticks'];
    },
  },
]);
