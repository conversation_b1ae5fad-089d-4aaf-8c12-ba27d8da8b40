(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6803],
  {
    22353: (o) => {
      o.exports = '(O + H + L + C)/4';
    },
    94884: (o) => {
      o.exports = '(H + L + C)/3';
    },
    10591: (o) => {
      o.exports = '(H + L)/2';
    },
    63243: (o) => {
      o.exports = ['Kolor słupków na podstawie poprzedniego zamknięcia'];
    },
    72171: (o) => {
      o.exports = ['Środek'];
    },
    9994: (o) => {
      o.exports = ['Dopasuj dane według dywidend'];
    },
    10989: (o) => {
      o.exports = ['Dostosuj do zmian w kontrakcie'];
    },
    70816: (o) => {
      o.exports = ['Średnie zamknięcie'];
    },
    91757: (o) => {
      o.exports = ['Dno'];
    },
    50430: (o) => {
      o.exports = ['Linia dolna'];
    },
    83760: (o) => {
      o.exports = ['Korpus'];
    },
    72269: (o) => {
      o.exports = ['Granice'];
    },
    7445: (o) => {
      o.exports = ['Poziom bazowy'];
    },
    47586: (o) => {
      o.exports = ['Ceny bid i ask'];
    },
    39667: (o) => {
      o.exports = ['Słupki dół'];
    },
    87151: (o) => {
      o.exports = ['Kolor dół'];
    },
    81285: (o) => {
      o.exports = ['Modyfikacja danych'];
    },
    4329: (o) => {
      o.exports = ['Domyślnie'];
    },
    86846: (o) => {
      o.exports = ['Zapełnij'];
    },
    58747: (o) => {
      o.exports = ['Wypełnij górny obszar'];
    },
    11157: (o) => {
      o.exports = ['Dolne wypełnienie'];
    },
    86953: (o) => {
      o.exports = ['Słupki HLC'];
    },
    77405: (o) => {
      o.exports = ['Poziomo'];
    },
    39292: (o) => {
      o.exports = ['High i low'];
    },
    15107: (o) => {
      o.exports = ['Ostatnia cena'];
    },
    19286: (o) => {
      o.exports = ['Lewo'];
    },
    76476: (o) => {
      o.exports = ['Środek'];
    },
    27879: (o) => {
      o.exports = ['Prosty'];
    },
    2391: (o) => {
      o.exports = ['Krok'];
    },
    6350: (o) => {
      o.exports = 'Pre/post market';
    },
    62521: (o) => {
      o.exports = ['Tło sesji Pre/Post market'];
    },
    73947: (o) => {
      o.exports = ['Precyzja'];
    },
    8094: (o) => {
      o.exports = ['Zamknięcie poprzedniego dnia'];
    },
    77986: (o) => {
      o.exports = ['Linie ceny'];
    },
    24248: (o) => {
      o.exports = ['Źródło cen'];
    },
    94089: (o) => {
      o.exports = ['Projekcja słupków w górę'];
    },
    5704: (o) => {
      o.exports = ['Projekcja słupków w dół'];
    },
    29881: (o) => {
      o.exports = [
        'Rzeczywiste ceny na skali cenowej (zamiast ceny Heikin-Ashi)',
      ];
    },
    21141: (o) => {
      o.exports = ['Prawy'];
    },
    44673: (o) => {
      o.exports = ['Ze znacznikami'];
    },
    26458: (o) => {
      o.exports = ['Knot'];
    },
    65994: (o) => {
      o.exports = ['Szczyt'];
    },
    57417: (o) => {
      o.exports = ['Linia górna'];
    },
    92960: (o) => {
      o.exports = ['Wyrównanie tekstu'];
    },
    90581: (o) => {
      o.exports = ['Kierunek tekstu'];
    },
    55314: (o) => {
      o.exports = ['Wąskie słupki'];
    },
    87492: (o) => {
      o.exports = ['Strefa czasowa'];
    },
    58416: (o) => {
      o.exports = ['Typ'];
    },
    5536: (o) => {
      o.exports = ['Kolor góra'];
    },
    83610: (o) => {
      o.exports = ['Słupki góra'];
    },
    23500: (o) => {
      o.exports = ['Użyj ceny rozliczenia jako dziennej ceny zamknięcia'];
    },
    44085: (o) => {
      o.exports = ['Pionowo'];
    },
    30792: (o) => {
      o.exports = ['świeca'];
    },
    55740: (o) => {
      o.exports = ['zmień słupki HLC'];
    },
    90168: (o) => {
      o.exports = ['zmień średnią szerokość linii ceny zamknięcia'];
    },
    30385: (o) => {
      o.exports = ['zmień kolor średniej ceny zamknięcia'];
    },
    97008: (o) => {
      o.exports = ['zmień kolor wypełnienia obszaru'];
    },
    6610: (o) => {
      o.exports = ['zmień grubość linii obszaru'];
    },
    661: (o) => {
      o.exports = ['zmień kolor linii obszaru'];
    },
    1316: (o) => {
      o.exports = ['zmień źródło ceny obszaru'];
    },
    29180: (o) => {
      o.exports = ['zmień kolor linii ask'];
    },
    31547: (o) => {
      o.exports = ['zmień poziom podstawowy'];
    },
    4164: (o) => {
      o.exports = ['zmień kolor dolnej linii w linii bazowej'];
    },
    38990: (o) => {
      o.exports = ['zmień szerokość dolnej linii w linii bazowej'];
    },
    73163: (o) => {
      o.exports = ['zmień kolor wypełnienia dolnej części linii bazowej'];
    },
    12673: (o) => {
      o.exports = ['zmień kolor wypełnienia górnego obszaru linii bazowej'];
    },
    56819: (o) => {
      o.exports = ['zmień linię bazową źródła ceny'];
    },
    68621: (o) => {
      o.exports = ['zmień kolor linii bazowej górnej linii'];
    },
    35339: (o) => {
      o.exports = ['zmień grubość górnej linii linii bazowej'];
    },
    76804: (o) => {
      o.exports = ['zmień kolor góry słupka'];
    },
    71816: (o) => {
      o.exports = ['zmień kolor dołu słupka'];
    },
    36703: (o) => {
      o.exports = ['zmień kolor linii bid'];
    },
    29353: (o) => {
      o.exports = ['zmień kolor słupków na podstawie poprzedniego zamknięcia'];
    },
    85709: (o) => {
      o.exports = ['zmiana koloru słupków wzrostowych'];
    },
    12155: (o) => {
      o.exports = ['zmiana koloru słupków spadkowych'];
    },
    66890: (o) => {
      o.exports = ['zmień źródło ceny kolumny'];
    },
    71809: (o) => {
      o.exports = ['zmień miejsca dziesiętne'];
    },
    31317: (o) => {
      o.exports = ['zmień kolor wydłużonych godzin'];
    },
    60944: (o) => {
      o.exports = ['zmień kolor linii cen high i low'];
    },
    83708: (o) => {
      o.exports = ['zmień szerokość linii high i low'];
    },
    81080: (o) => {
      o.exports = ['zmień kolor korpusu high-low'];
    },
    30033: (o) => {
      o.exports = ['zmień widoczność korpusu high-low'];
    },
    76885: (o) => {
      o.exports = ['zmień kolor obramowania high-low'];
    },
    79236: (o) => {
      o.exports = ['zmień widoczność granic high-low'];
    },
    42981: (o) => {
      o.exports = ['zmień widoczność etykiet high-low'];
    },
    31937: (o) => {
      o.exports = ['zmień kolor etykiet high-low'];
    },
    87828: (o) => {
      o.exports = ['zmień kolor linii'];
    },
    17119: (o) => {
      o.exports = ['zmień źródło ceny linii'];
    },
    69125: (o) => {
      o.exports = ['zmień grubość linii'];
    },
    70054: (o) => {
      o.exports = ['zmień rodzaj linii'];
    },
    49973: (o) => {
      o.exports = ['zmień kolor dla post market'];
    },
    5969: (o) => {
      o.exports = ['zmień kolor linii post market'];
    },
    50393: (o) => {
      o.exports = ['zmień widoczność linii cen pre/post market'];
    },
    46257: (o) => {
      o.exports = ['zmień kolor dla pre market'];
    },
    60852: (o) => {
      o.exports = ['zmień kolor linii pre market'];
    },
    91183: (o) => {
      o.exports = ['zmień kolor linii ceny poprzedniego zamknięcia'];
    },
    87631: (o) => {
      o.exports = ['zmień grubość linii ceny poprzedniego zamknięcia'];
    },
    77640: (o) => {
      o.exports = ['zmień kolor linii ceny'];
    },
    97322: (o) => {
      o.exports = ['zmień grubość linii ceny'];
    },
    28143: (o) => {
      o.exports = ['zmień zakres cienkich słupków'];
    },
    75986: (o) => {
      o.exports = ['zmień kolor dolnego knota renko'];
    },
    7747: (o) => {
      o.exports = ['zmień kolor górnego knota renko'];
    },
    9473: (o) => {
      o.exports = ['zmień widoczność knota renko'];
    },
    39783: (o) => {
      o.exports = [
        'zmień wyświetlanie rzeczywistych cen na skali cen (zamiast ceny Heiken-Ashi)',
      ];
    },
    72886: (o) => {
      o.exports = ['zmień cienkie słupki'];
    },
    5464: (o) => {
      o.exports = ['zmień {candleType} górny kolor obramowania'];
    },
    61118: (o) => {
      o.exports = ['zmień kolor góry {candleType}'];
    },
    60164: (o) => {
      o.exports = ['zmiana koloru knota świec opadających - {candleType}'];
    },
    45543: (o) => {
      o.exports = ['zmień kolor górnego knota {candleType}'];
    },
    39987: (o) => {
      o.exports = ['zmień widoczność knota {candleType}'];
    },
    47202: (o) => {
      o.exports = ['zmień widoczność korpusu {candleType}'];
    },
    23986: (o) => {
      o.exports = ['zmień widoczność obramowania {candleType}'];
    },
    92330: (o) => {
      o.exports = ['zmień kolor dolnego obramowania {candleType}'];
    },
    36320: (o) => {
      o.exports = ['zmień kolor dołu {candleType}'];
    },
    79088: (o) => {
      o.exports = ['zmień kolor obramowania dolnego słupka {chartType}'];
    },
    11107: (o) => {
      o.exports = ['zmień kolor projekcji górnego słupka {chartType}'];
    },
    85503: (o) => {
      o.exports = ['zmień kolor kolor spodu {chartType}'];
    },
    61250: (o) => {
      o.exports = [
        'zmień kolor obramowania projekcji górnego słupka {chartType}',
      ];
    },
    18465: (o) => {
      o.exports = ['zmień kolor projekcji dolnego słupka {chartType}'];
    },
    50453: (o) => {
      o.exports = ['zmień kolor projekcji słupka rosnącego {chartType}'];
    },
    59414: (o) => {
      o.exports = ['zmień kolor góry {chartType}'];
    },
    21547: (o) => {
      o.exports = ['zmień właściwość {inputName}'];
    },
    42390: (o) => {
      o.exports = ['dostosuj dane według dywidend'];
    },
    99511: (o) => {
      o.exports = ['dostosuj do zmian w kontrakcie'];
    },
    75165: (o) => {
      o.exports = ['Puste Świece'];
    },
    18995: (o) => {
      o.exports = ['zakres', 'zakresy', 'zakresów', 'zakresów'];
    },
    47500: (o) => {
      o.exports = ['Renko'];
    },
    98402: (o) => {
      o.exports = ['użyj rozliczenia jako zamknięcia na interwale dziennym'];
    },
  },
]);
