(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6444],
  {
    50831: (e) => {
      e.exports = ['แท่ง'];
    },
    19648: (e) => {
      e.exports = ['12 ชั่วโมง'];
    },
    55838: (e) => {
      e.exports = ['24 ชั่วโมง'];
    },
    88364: (e) => {
      e.exports = ['รูปแบบพื้นฐานของแผนภูมิ'];
    },
    46720: (e) => {
      e.exports = ['เส้นกากบาท'];
    },
    50985: (e) => {
      e.exports = ['สกุลเงิน'];
    },
    17319: (e) => {
      e.exports = ['สกุลเงินและหน่วย'];
    },
    19481: (e) => {
      e.exports = ['รูปลักษณ์'];
    },
    68791: (e) => {
      e.exports = ['โต้เถียง'];
    },
    95036: (e) => {
      e.exports = ['เส้นราคาปิดเฉลี่ย'];
    },
    27331: (e) => {
      e.exports = ['พื้นหลัง'];
    },
    22519: (e) => {
      e.exports = ['ค่าเปลี่ยนแปลงของแท่ง'];
    },
    87845: (e) => {
      e.exports = ['ปุ่ม'];
    },
    25209: (e) => {
      e.exports = ['รูปแบบวันที่'];
    },
    55090: (e) => {
      e.exports = ['วันในสัปดาห์บนป้ายกำกับ'];
    },
    29601: (e) => {
      e.exports = ['คำอธิบาย'];
    },
    26897: (e) => {
      e.exports = ['เหตุการณ์'];
    },
    95338: (e) => {
      e.exports = ['เส้นกริดแนวนอน'];
    },
    60971: (e) => {
      e.exports = ['ราคาสูงและต่ำ'];
    },
    61142: (e) => {
      e.exports = ['อินดิเคเตอร์'];
    },
    34905: (e) => {
      e.exports = ['มูลค่าของอินดิเคเตอร์'];
    },
    29687: (e) => {
      e.exports = ['อินดิเคเตอร์และฉลากมูลค่าทางการเงิน'];
    },
    25084: (e) => {
      e.exports = ['ป้ายชื่ออินดิเคเตอร์และการเงิน'];
    },
    9654: (e) => {
      e.exports = ['ป้ายชื่ออินดิเคเตอร์'];
    },
    99487: (e) => {
      e.exports = ['ราคา เปิด สูง ต่ำ และปิด OHLC ของแท่งเทียน'];
    },
    75991: (e) => {
      e.exports = ['สถานะตลาดเปิด'];
    },
    96073: (e) => {
      e.exports = 'Long Description';
    },
    70500: (e) => {
      e.exports = ['เงิน'];
    },
    66653: (e) => {
      e.exports = ['มาร์จิ้น'];
    },
    42502: (e) => {
      e.exports = ['ไม่มีป้ายที่ทับซ้อนกัน'];
    },
    74343: (e) => {
      e.exports = ['ปุ่มนำทาง'];
    },
    43115: (e) => {
      e.exports = ['มาตราส่วน'];
    },
    53224: (e) => {
      e.exports = ['การวางตำแหน่งสเกล'];
    },
    79194: (e) => {
      e.exports = ['เส้นสถานะ'];
    },
    89053: (e) => {
      e.exports = ['สัญลักษณ์'];
    },
    35383: (e) => {
      e.exports = ['ชื่อสัญลักษณ์'];
    },
    27767: (e) => {
      e.exports = ['ป้ายค่าราคาล่าสุดของตัวย่อ'];
    },
    40847: (e) => {
      e.exports = ['ป้ายค่าราคาปิดวันก่อนหน้าของตัวย่อ'];
    },
    50446: (e) => {
      e.exports = ['หน้าต่างใหม่'];
    },
    73908: (e) => {
      e.exports = ['ตัวแยกบานหน้าต่าง'];
    },
    36014: (e) => {
      e.exports = ['เปอร์เซ็นต์'];
    },
    78621: (e) => {
      e.exports = ['ปิ๊ป'];
    },
    74823: (e) => {
      e.exports = ['ป้ายราคาก่อน/หลังตลาดเปิดปิด'];
    },
    64859: (e) => {
      e.exports = ['สเกลราคา'];
    },
    76523: (e) => {
      e.exports = ['ราคาและค่าเปอร์เซ็นต์'];
    },
    40187: (e) => {
      e.exports = 'Right Margin';
    },
    77705: (e) => {
      e.exports = ['ลายน้ำ'];
    },
    67369: (e) => {
      e.exports = ['หัวเรื่อง'];
    },
    31326: (e) => {
      e.exports = ['ชื่อเรื่อง'];
    },
    23097: (e) => {
      e.exports = ['ทิกเกอร์'];
    },
    82168: (e) => {
      e.exports = ['ทิกเกอร์และคำอธิบาย'];
    },
    43637: (e) => {
      e.exports = ['ขนาดเวลา'];
    },
    97316: (e) => {
      e.exports = ['รูปแบบชั่วโมงเวลา'];
    },
    90801: (e) => {
      e.exports = ['การซื้อขาย'];
    },
    77534: (e) => {
      e.exports = ['หน่วย'];
    },
    1111: (e) => {
      e.exports = ['ปริมาณการซื้อขาย'];
    },
    80170: (e) => {
      e.exports = ['ค่าตามสเกล'];
    },
    91322: (e) => {
      e.exports = ['มูลค่า'];
    },
    70353: (e) => {
      e.exports = ['เส้นกริดแนวตั้ง'];
    },
    57889: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นค่า OHLC'];
    },
    35646: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นปุ่มนำทาง'];
    },
    18644: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นสถานะตลาดเปิด'];
    },
    45110: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นการเปลี่ยนบาร์'];
    },
    10349: (e) => {
      e.exports = ['เปลี่ยนระยะขอบด้านล่าง'];
    },
    88161: (e) => {
      e.exports = ['เปลี่ยนสกุลเงินและการมองเห็นป้ายหน่วย'];
    },
    79570: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นป้ายสกุลเงิน'];
    },
    99011: (e) => {
      e.exports = ['เปลี่ยนสีพื้นหลังชาร์ต'];
    },
    72458: (e) => {
      e.exports = ['เปลี่ยนประเภทพื้นหลังของชาร์ต'];
    },
    37034: (e) => {
      e.exports = ['เปลี่ยนความกว้างเส้นตัด'];
    },
    29951: (e) => {
      e.exports = ['เปลี่ยนสีเส้นเส้นตัด'];
    },
    92027: (e) => {
      e.exports = ['เปลี่ยนรูปแบบเส้นตัด'];
    },
    50457: (e) => {
      e.exports = ['เปลี่ยนรูปแบบวันที่'];
    },
    7104: (e) => {
      e.exports = ['เปลี่ยนวันในสัปดาห์บนป้ายกำกับ'];
    },
    88096: (e) => {
      e.exports = ['เปลี่ยนสีเส้นกริดแนวนอน'];
    },
    2523: (e) => {
      e.exports = ['เปลี่ยนรูปแบบเส้นกริดแนวนอน'];
    },
    31325: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นชื่ออินดิเคเตอร์'];
    },
    99774: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นค่าอินดิเคเตอร์'];
    },
    96162: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นอาร์กิวเมนต์อินดิเคเตอร์'];
    },
    59820: (e) => {
      e.exports = ['เปลี่ยนอินดิเคเตอร์และการมองเห็นป้ายชื่อการเงิน'];
    },
    90512: (e) => {
      e.exports = ['เปลี่ยนอินดิเคเตอร์และการมองเห็นฉลากมูลค่าทางการเงิน'];
    },
    97956: (e) => {
      e.exports = ['เปลี่ยนความโปร่งใสพื้นหลังคำอธิบาย'];
    },
    61061: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นพื้นหลังคำอธิบาย'];
    },
    37730: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นปุ่มหน้าต่างเสริม'];
    },
    89032: (e) => {
      e.exports = ['เปลี่ยนสีตัวคั่นบานหน้าต่าง'];
    },
    35636: (e) => {
      e.exports = ['เปลี่ยนระยะขอบด้านขวา'];
    },
    66601: (e) => {
      e.exports = 'change right margin percentage';
    },
    25616: (e) => {
      e.exports = ['เปลี่ยนสีสัญลักษณ์ลายน้ำ'];
    },
    87159: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นสัญลักษณ์ลายน้ำ'];
    },
    26717: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นคำอธิบายสัญลักษณ์'];
    },
    6091: (e) => {
      e.exports = 'change symbol field visibility';
    },
    28741: (e) => {
      e.exports = ['เปลี่ยนโหมดสัญลักษณ์ค่าสุดท้าย'];
    },
    95071: (e) => {
      e.exports = ['เปลี่ยนรูปแบบคำอธิบายสัญลักษณ์'];
    },
    35065: (e) => {
      e.exports = ['เปลี่ยนสีสเกลข้อความ'];
    },
    84382: (e) => {
      e.exports = ['เปลี่ยนขนาดสเกลตัวอักษร'];
    },
    12468: (e) => {
      e.exports = ['เปลี่ยนสีสเกลเส้น'];
    },
    71589: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นตัวแบ่งเซสชั่น'];
    },
    15035: (e) => {
      e.exports = ['เปลี่ยนความกว้างตัวแบ่งเซสชั่น'];
    },
    1579: (e) => {
      e.exports = ['เปลี่ยนสีตัวแบ่งเซสชั่น'];
    },
    21460: (e) => {
      e.exports = ['เปลี่ยนรูปแบบตัวแบ่งเซสชั่น'];
    },
    76991: (e) => {
      e.exports = ['เปลี่ยนรูปแบบชั่วโมงเวลา'];
    },
    98905: (e) => {
      e.exports = ['เปลี่ยนระยะขอบด้านบน'];
    },
    7011: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นป้ายหน่วย'];
    },
    22722: (e) => {
      e.exports = ['เปลี่ยนสีเส้นกริดแนวตั้ง'];
    },
    22867: (e) => {
      e.exports = ['เปลี่ยนรูปแบบเส้นกริดแนวตั้ง'];
    },
    9455: (e) => {
      e.exports = ['เปลี่ยนการมองเห็นค่าวอลุ่ม'];
    },
  },
]);
