(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [7076],
  {
    20747: (o) => {
      o.exports = 'Re';
    },
    14642: (o) => {
      o.exports = ['Escuro'];
    },
    69841: (o) => {
      o.exports = ['Claro'];
    },
    673: (o) => {
      (o.exports = Object.create(null)),
        (o.exports.d_dates = 'd'),
        (o.exports.h_dates = 'h'),
        (o.exports.m_dates = 'm'),
        (o.exports.s_dates = 's'),
        (o.exports.in_dates = ['em']);
    },
    97840: (o) => {
      o.exports = 'd';
    },
    64302: (o) => {
      o.exports = 'h';
    },
    79442: (o) => {
      o.exports = 'm';
    },
    22448: (o) => {
      o.exports = 's';
    },
    16493: (o) => {
      o.exports = ['Cópia de {title}'];
    },
    13395: (o) => {
      o.exports = 'D';
    },
    37720: (o) => {
      o.exports = 'M';
    },
    69838: (o) => {
      o.exports = 'R';
    },
    59231: (o) => {
      o.exports = 'T';
    },
    85521: (o) => {
      o.exports = ['S'];
    },
    13994: (o) => {
      o.exports = 'h';
    },
    6791: (o) => {
      o.exports = 'm';
    },
    2949: (o) => {
      o.exports = 's';
    },
    77297: (o) => {
      o.exports = ['Fch'];
    },
    56723: (o) => {
      o.exports = ['Máx.'];
    },
    5801: (o) => {
      o.exports = 'HL2';
    },
    98865: (o) => {
      o.exports = 'HLC3';
    },
    42659: (o) => {
      o.exports = 'OHLC4';
    },
    4292: (o) => {
      o.exports = ['Mín.'];
    },
    78155: (o) => {
      o.exports = ['Abr'];
    },
    88601: (o) => {
      (o.exports = Object.create(null)),
        (o.exports.Close_input = ['Fechar']),
        (o.exports.Back_input = ['Voltar']),
        (o.exports['Hull MA_input'] = ['MM de Hull']),
        (o.exports['{number} item_combobox_input'] = '{number} item'),
        (o.exports.Length_input = ['Período']),
        (o.exports.Plot_input = ['Traço']),
        (o.exports.Zero_input = 'Zero'),
        (o.exports.Signal_input = ['Sinal']),
        (o.exports.Long_input = ['Compra']),
        (o.exports.Short_input = ['Venda']),
        (o.exports.UpperLimit_input = ['Limite superior']),
        (o.exports.LowerLimit_input = ['Limite inferior']),
        (o.exports.Offset_input = ['Deslocamento']),
        (o.exports.length_input = ['período']),
        (o.exports.mult_input = 'mult'),
        (o.exports.short_input = ['Venda']),
        (o.exports.long_input = ['compra']),
        (o.exports.Limit_input = ['Limite']),
        (o.exports.Move_input = ['Movimento']),
        (o.exports.Value_input = ['Valor']),
        (o.exports.Method_input = ['Método']),
        (o.exports['Values in status line_input'] = [
          'Valores na linha de status',
        ]),
        (o.exports['Labels on price scale_input'] = [
          'Legendas na escala de preço',
        ]),
        (o.exports['Accumulation/Distribution_input'] = [
          'Acumulação / Distribuição',
        ]),
        (o.exports.ADR_B_input = 'ADR_B'),
        (o.exports['Equality Line_input'] = ['Linha de Igualdade']),
        (o.exports['Window Size_input'] = ['Tamanho da janela']),
        (o.exports.Sigma_input = 'Sigma'),
        (o.exports['Aroon Up_input'] = ['Aroon de Baixa']),
        (o.exports['Aroon Down_input'] = ['Aroon de Alta']),
        (o.exports.Upper_input = ['Superior']),
        (o.exports.Lower_input = ['Inferior']),
        (o.exports.Deviation_input = ['Desvio']),
        (o.exports['Levels Format_input'] = ['Unidade dos Níveis']),
        (o.exports['Labels Position_input'] = ['Posição das Legendas']),
        (o.exports['0 Level Color_input'] = ['Cor do Nível 0']),
        (o.exports['0.236 Level Color_input'] = ['Cor do Nível 236']),
        (o.exports['0.382 Level Color_input'] = ['Cor do Nível 0.382']),
        (o.exports['0.5 Level Color_input'] = ['Cor do Nível 0.5']),
        (o.exports['0.618 Level Color_input'] = ['Cor do Nível 0.618']),
        (o.exports['0.65 Level Color_input'] = ['Cor do Nível 0.65']),
        (o.exports['0.786 Level Color_input'] = ['Cor do Nível 0.786']),
        (o.exports['1 Level Color_input'] = ['Cor do Nível 1']),
        (o.exports['1.272 Level Color_input'] = ['Cor do Nível 1.272']),
        (o.exports['1.414 Level Color_input'] = ['Cor do Nível 1.414']),
        (o.exports['1.618 Level Color_input'] = ['Cor do Nível 1.618']),
        (o.exports['1.65 Level Color_input'] = ['Cor do Nível 1.65']),
        (o.exports['2.618 Level Color_input'] = ['Cor do Nível 2.618']),
        (o.exports['2.65 Level Color_input'] = ['Cor do Nível 2.65']),
        (o.exports['3.618 Level Color_input'] = ['Cor do Nível 3.618']),
        (o.exports['3.65 Level Color_input'] = ['Cor do Nível 3.65']),
        (o.exports['4.236 Level Color_input'] = ['Cor do Nível 4.236']),
        (o.exports['-0.236 Level Color_input'] = ['Cor do Nível -0.236']),
        (o.exports['-0.382 Level Color_input'] = ['Cor do Nível -0.382']),
        (o.exports['-0.618 Level Color_input'] = ['Cor do Nível -0.618']),
        (o.exports['-0.65 Level Color_input'] = ['Cor do Nível -0.65']),
        (o.exports.ADX_input = 'ADX'),
        (o.exports['ADX Smoothing_input'] = ['ADX suavizado']),
        (o.exports['DI Length_input'] = ['Comprimento DI']),
        (o.exports.Smoothing_input = ['Suavização']),
        (o.exports.ATR_input = 'ATR'),
        (o.exports.Growing_input = ['Comprador']),
        (o.exports.Falling_input = ['Vendedor']),
        (o.exports['Color 0_input'] = ['Cor 0']),
        (o.exports['Color 1_input'] = ['Cor 1']),
        (o.exports.Source_input = ['Fonte']),
        (o.exports.StdDev_input = ['Desvio Padrão']),
        (o.exports.Basis_input = ['Base']),
        (o.exports.Median_input = ['Mediana']),
        (o.exports['Bollinger Bands %B_input'] = ['Bandas de Bollinger % B']),
        (o.exports.Overbought_input = ['Sobrecomprado']),
        (o.exports.Oversold_input = ['Sobrevendido']),
        (o.exports['Bollinger Bands Width_input'] = [
          'Largura de bandas de Bollinger',
        ]),
        (o.exports['RSI Length_input'] = ['Período IFR']),
        (o.exports['UpDown Length_input'] = ['Período de AltaBaixa']),
        (o.exports['ROC Length_input'] = ['Período ROC']),
        (o.exports.MF_input = 'MF'),
        (o.exports.resolution_input = ['resolução']),
        (o.exports['Fast Length_input'] = ['Período Rápido']),
        (o.exports['Slow Length_input'] = ['Período Lento']),
        (o.exports['Chaikin Oscillator_input'] = ['Oscilador Chaikin']),
        (o.exports.P_input = 'P'),
        (o.exports.X_input = 'X'),
        (o.exports.Q_input = 'Q'),
        (o.exports.p_input = 'p'),
        (o.exports.x_input = 'x'),
        (o.exports.q_input = 'q'),
        (o.exports.Price_input = ['Preço']),
        (o.exports['Chande MO_input'] = 'Chande MO'),
        (o.exports['Zero Line_input'] = ['Linha Zero']),
        (o.exports['Color 2_input'] = ['Cor 2']),
        (o.exports['Color 3_input'] = ['Cor 3']),
        (o.exports['Color 4_input'] = ['Cor 4']),
        (o.exports['Color 5_input'] = ['Cor 5']),
        (o.exports['Color 6_input'] = ['Cor 6']),
        (o.exports['Color 7_input'] = ['Cor 7']),
        (o.exports['Color 8_input'] = ['Cor 8']),
        (o.exports.CHOP_input = 'CHOP'),
        (o.exports['Upper Band_input'] = ['Banda superior']),
        (o.exports['Lower Band_input'] = ['Banda inferior']),
        (o.exports.CCI_input = 'CCI'),
        (o.exports['Smoothing Line_input'] = ['Suavização da Linha']),
        (o.exports['Smoothing Length_input'] = ['Período da Suavização']),
        (o.exports['WMA Length_input'] = ['Período WMA']),
        (o.exports['Long RoC Length_input'] = ['RoC Período Longo']),
        (o.exports['Short RoC Length_input'] = ['Período RoC curto']),
        (o.exports.sym_input = 'sym'),
        (o.exports.Symbol_input = ['Símbolo']),
        (o.exports.Correlation_input = ['Correlação']),
        (o.exports.Period_input = ['Período']),
        (o.exports.Centered_input = ['Centralizado']),
        (o.exports['Detrended Price Oscillator_input'] = [
          'Oscilador de Preço Destendenciado',
        ]),
        (o.exports.isCentered_input = ['Está centrado']),
        (o.exports.DPO_input = 'DPO'),
        (o.exports['ADX smoothing_input'] = ['ADX suavizado']),
        (o.exports['+DI_input'] = '+DI'),
        (o.exports['-DI_input'] = '-DI'),
        (o.exports.DEMA_input = 'DEMA'),
        (o.exports.Divisor_input = 'Divisor'),
        (o.exports.EOM_input = 'EOM'),
        (o.exports["Elder's Force Index_input"] = ['Índice de força antigo']),
        (o.exports.Percent_input = ['Porcentagem']),
        (o.exports.Exponential_input = ['Exponencial']),
        (o.exports.Average_input = ['Média']),
        (o.exports['Upper Percentage_input'] = ['Porcentagem Superior']),
        (o.exports['Lower Percentage_input'] = ['Porcentagem Inferior']),
        (o.exports.Fisher_input = 'Fisher'),
        (o.exports.Trigger_input = ['Gatilho']),
        (o.exports.Level_input = ['Nível']),
        (o.exports['Trader EMA 1 length_input'] = ['MME Trader 1 período']),
        (o.exports['Trader EMA 2 length_input'] = ['MME Trader 2 períodos']),
        (o.exports['Trader EMA 3 length_input'] = ['MME Trader 3 períodos']),
        (o.exports['Trader EMA 4 length_input'] = ['MME Trader 4 períodos']),
        (o.exports['Trader EMA 5 length_input'] = ['MME Trader 5 períodos']),
        (o.exports['Trader EMA 6 length_input'] = ['MME Trader 6 períodos']),
        (o.exports['Investor EMA 1 length_input'] = [
          'MME Investidor 1 período',
        ]),
        (o.exports['Investor EMA 2 length_input'] = [
          'MME Investidor 2 períodos',
        ]),
        (o.exports['Investor EMA 3 length_input'] = [
          'MME Investidor 3 períodos',
        ]),
        (o.exports['Investor EMA 4 length_input'] = [
          'MME Investidor 4 períodos',
        ]),
        (o.exports['Investor EMA 5 length_input'] = [
          'MME Investidor 5 períodos',
        ]),
        (o.exports['Investor EMA 6 length_input'] = [
          'MME Investidor 6 períodos',
        ]),
        (o.exports.HV_input = 'HV'),
        (o.exports['Conversion Line Periods_input'] = [
          'Períodos da linha de conversão',
        ]),
        (o.exports['Base Line Periods_input'] = ['Períodos da Linha Base']),
        (o.exports['Lagging Span_input'] = ['Intervalo de atraso']),
        (o.exports['Conversion Line_input'] = ['Linha de Conversão']),
        (o.exports['Base Line_input'] = ['Linha Base']),
        (o.exports['Leading Span A_input'] = 'Leading Span A'),
        (o.exports['Leading Span B_input'] = 'Leading Span B'),
        (o.exports['Plots Background_input'] = ['Fundo da Plotagem']),
        (o.exports['yay Color 0_input'] = ['yay Cor 0']),
        (o.exports['yay Color 1_input'] = ['yay Cor 1']),
        (o.exports.Multiplier_input = ['Multiplicador']),
        (o.exports['Bands style_input'] = ['Estilo de bandas']),
        (o.exports.Middle_input = ['Centro']),
        (o.exports.useTrueRange_input = ['FaixadeUsoVerdadeira']),
        (o.exports.ROCLen1_input = 'ROCLen1'),
        (o.exports.ROCLen2_input = 'ROCLen2'),
        (o.exports.ROCLen3_input = 'ROCLen3'),
        (o.exports.ROCLen4_input = 'ROCLen4'),
        (o.exports.SMALen1_input = 'SMALen1'),
        (o.exports.SMALen2_input = 'SMALen2'),
        (o.exports.SMALen3_input = 'SMALen3'),
        (o.exports.SMALen4_input = 'SMALen4'),
        (o.exports.SigLen_input = 'SigLen'),
        (o.exports.KST_input = 'KST'),
        (o.exports.Sig_input = 'Sig'),
        (o.exports.roclen1_input = 'roclen1'),
        (o.exports.roclen2_input = 'roclen2'),
        (o.exports.roclen3_input = 'roclen3'),
        (o.exports.roclen4_input = 'roclen4'),
        (o.exports.smalen1_input = 'smalen1'),
        (o.exports.smalen2_input = 'smalen2'),
        (o.exports.smalen3_input = 'smalen3'),
        (o.exports.smalen4_input = 'smalen4'),
        (o.exports.siglen_input = 'siglen'),
        (o.exports['Upper Deviation_input'] = ['Desvio Superior']),
        (o.exports['Lower Deviation_input'] = ['Desvio Inferior']),
        (o.exports['Use Upper Deviation_input'] = ['Usar Desvio Superior']),
        (o.exports['Use Lower Deviation_input'] = ['Usar Desvio Inferior']),
        (o.exports.Count_input = ['Contagem']),
        (o.exports.Crosses_input = ['Cruzamentos']),
        (o.exports.MOM_input = 'MOM'),
        (o.exports.MA_input = 'MA'),
        (o.exports['Length EMA_input'] = ['MME do Período']),
        (o.exports['Length MA_input'] = ['MM do Período']),
        (o.exports['Fast length_input'] = ['Período curto']),
        (o.exports['Slow length_input'] = ['Período lento']),
        (o.exports['Signal smoothing_input'] = ['Suavização do sinal']),
        (o.exports['Simple ma(oscillator)_input'] = ['MM Simples (oscilador)']),
        (o.exports['Simple ma(signal line)_input'] = [
          'MM Simples (linha de sinal)',
        ]),
        (o.exports.Histogram_input = ['Histograma']),
        (o.exports.MACD_input = 'MACD'),
        (o.exports.fastLength_input = ['Período Rápido']),
        (o.exports.slowLength_input = ['PeríodoLento']),
        (o.exports.signalLength_input = ['PeríodoSinal']),
        (o.exports.NV_input = 'NV'),
        (o.exports.OnBalanceVolume_input = ['BalançodeVolume']),
        (o.exports.Start_input = ['Início']),
        (o.exports.Increment_input = ['Incremento']),
        (o.exports['Max value_input'] = ['Valor máximo']),
        (o.exports.ParabolicSAR_input = ['SAR Parabólico']),
        (o.exports.start_input = ['Início']),
        (o.exports.increment_input = ['incremento']),
        (o.exports.maximum_input = ['máx']),
        (o.exports['Short length_input'] = ['Período curto']),
        (o.exports['Long length_input'] = ['Período longo']),
        (o.exports.OSC_input = 'OSC'),
        (o.exports.shortlen_input = ['período curto']),
        (o.exports.longlen_input = ['período longo']),
        (o.exports.PVT_input = 'PVT'),
        (o.exports.ROC_input = 'ROC'),
        (o.exports.RSI_input = ['IFR']),
        (o.exports.RVGI_input = 'RVGI'),
        (o.exports.RVI_input = 'RVI'),
        (o.exports['Long period_input'] = ['Período longo']),
        (o.exports['Short period_input'] = ['Período curto']),
        (o.exports['Signal line period_input'] = ['Período de linha de sinal']),
        (o.exports.SMI_input = 'SMI'),
        (o.exports['SMI Ergodic Oscillator_input'] = ['Oscilador Ergodic SMI']),
        (o.exports.Indicator_input = ['Indicador']),
        (o.exports.Oscillator_input = ['Oscilador']),
        (o.exports.K_input = 'K'),
        (o.exports.D_input = 'D'),
        (o.exports.smoothK_input = ['suaveK']),
        (o.exports.smoothD_input = ['suaveD']),
        (o.exports['%K_input'] = '%K'),
        (o.exports['%D_input'] = '%D'),
        (o.exports['Stochastic Length_input'] = ['Período estocástico']),
        (o.exports['RSI Source_input'] = ['Fonte do IFR']),
        (o.exports.lengthRSI_input = ['PeríodoIFR']),
        (o.exports.lengthStoch_input = ['PeríodoEstocástico']),
        (o.exports.TRIX_input = 'TRIX'),
        (o.exports.TEMA_input = 'TEMA'),
        (o.exports['Long Length_input'] = ['Período longo']),
        (o.exports['Short Length_input'] = ['Período curto']),
        (o.exports['Signal Length_input'] = ['Comprimento do sinal']),
        (o.exports.Length1_input = ['Período1']),
        (o.exports.Length2_input = ['Período 2']),
        (o.exports.Length3_input = ['Período 3']),
        (o.exports.length7_input = ['Período7']),
        (o.exports.length14_input = ['Período14']),
        (o.exports.length28_input = ['Período28']),
        (o.exports.UO_input = 'UO'),
        (o.exports.VWMA_input = 'VWMA'),
        (o.exports.len_input = 'len'),
        (o.exports['VI +_input'] = 'VI +'),
        (o.exports['VI -_input'] = 'VI -'),
        (o.exports['%R_input'] = '%R'),
        (o.exports['Jaw Length_input'] = ['Comprimento do maxilar']),
        (o.exports['Teeth Length_input'] = ['Período dos dentes']),
        (o.exports['Lips Length_input'] = ['Período dos lábios']),
        (o.exports.Jaw_input = ['Maxilar']),
        (o.exports.Teeth_input = ['Dentes']),
        (o.exports.Lips_input = ['Lábios']),
        (o.exports['Jaw Offset_input'] = 'Jaw Offset'),
        (o.exports['Teeth Offset_input'] = 'Teeth Offset'),
        (o.exports['Lips Offset_input'] = 'Lips Offset'),
        (o.exports['Down fractals_input'] = ['Fractais de baixa']),
        (o.exports['Up fractals_input'] = ['Fractais de alta']),
        (o.exports.Periods_input = ['Períodos']),
        (o.exports.Shapes_input = ['Formas']),
        (o.exports['show MA_input'] = ['mostrar MA']),
        (o.exports['MA Length_input'] = ['Período MA']),
        (o.exports['Color based on previous close_input'] = [
          'Cor baseado no fechamento anterior',
        ]),
        (o.exports['Rows Layout_input'] = ['Tipo de Coluna']),
        (o.exports['Row Size_input'] = ['Largura da Coluna']),
        (o.exports.Volume_input = 'Volume'),
        (o.exports['Value Area volume_input'] = ['Volume da área de valor']),
        (o.exports['Extend Right_input'] = ['Estender à Direita']),
        (o.exports['Extend POC Right_input'] = ['Estender POC à direita']),
        (o.exports['Extend VAH Right_input'] = ['Estender VAH à Direita']),
        (o.exports['Extend VAL Right_input'] = ['Estender VAL à Direita']),
        (o.exports['Value Area Volume_input'] = ['Volume da Área do Valor']),
        (o.exports.Placement_input = ['Posicionameto']),
        (o.exports.POC_input = 'POC'),
        (o.exports['Developing Poc_input'] = ['Poc Em Desenvolvimento']),
        (o.exports['Up Volume_input'] = ['Volume de alta']),
        (o.exports['Down Volume_input'] = ['Volume de baixa']),
        (o.exports['Value Area_input'] = ['Área de Valor']),
        (o.exports['Histogram Box_input'] = ['Caixa de Histograma']),
        (o.exports['Value Area Up_input'] = ['Área de Valor Crescente']),
        (o.exports['Value Area Down_input'] = ['Área de Valor Decrescente']),
        (o.exports['Number Of Rows_input'] = ['Numero De Linhas']),
        (o.exports['Ticks Per Row_input'] = ['Ticks por Linha']),
        (o.exports['Up/Down_input'] = ['Comprador/Vendedor']),
        (o.exports.Total_input = 'Total'),
        (o.exports.Delta_input = 'Delta'),
        (o.exports.Bar_input = ['Barra']),
        (o.exports.Day_input = ['Dia']),
        (o.exports['Deviation (%)_input'] = ['Desvio (%)']),
        (o.exports.Depth_input = ['Profundidade']),
        (o.exports['Extend to last bar_input'] = ['Extender à última barra']),
        (o.exports.Simple_input = ['Simples']),
        (o.exports.Weighted_input = ['Ponderada']),
        (o.exports["Wilder's Smoothing_input"] = ['Suavização de Wilder']),
        (o.exports['1st Period_input'] = ['1º Período']),
        (o.exports['2nd Period_input'] = ['2º Período']),
        (o.exports['3rd Period_input'] = ['3º Período']),
        (o.exports['4th Period_input'] = ['4º Período']),
        (o.exports['5th Period_input'] = ['5º Período']),
        (o.exports['6th Period_input'] = ['6º Período']),
        (o.exports['Rate of Change Lookback_input'] = ['Taxa de Variação']),
        (o.exports['Instrument 1_input'] = ['Instrumento 1']),
        (o.exports['Instrument 2_input'] = ['Instrumento 2']),
        (o.exports['Rolling Period_input'] = ['Período Contínuo']),
        (o.exports['Standard Errors_input'] = ['Erros Padrões']),
        (o.exports['Averaging Periods_input'] = ['Períodos Médios']),
        (o.exports['Days Per Year_input'] = ['Dias Por Ano']),
        (o.exports['Market Closed Percentage_input'] = [
          'Porcentagem do Mercado Fechado',
        ]),
        (o.exports['ATR Mult_input'] = ['Múlt ATR']),
        (o.exports.VWAP_input = 'VWAP'),
        (o.exports['Anchor Period_input'] = ['Período Ancora']),
        (o.exports.Session_input = ['Sessão']),
        (o.exports.Week_input = ['Semana']),
        (o.exports.Month_input = ['Mês']),
        (o.exports.Year_input = ['Ano']),
        (o.exports.Decade_input = ['Década']),
        (o.exports.Century_input = ['Centenário']),
        (o.exports.Sessions_input = ['Sessão']),
        (o.exports['Each (pre-market, market, post-market)_input'] = [
          'Cada (pré-mercado, mercado, pós-mercado)',
        ]),
        (o.exports['Pre-market only_input'] = ['Somente o pré-mercado']),
        (o.exports['Market only_input'] = ['Somente o mercado']),
        (o.exports['Post-market only_input'] = ['Somente o pós-mercado']),
        (o.exports['Main chart symbol_input'] = [
          'Símbolo do gráfico principal',
        ]),
        (o.exports['Another symbol_input'] = ['Outro Símbolo']),
        (o.exports.Line_input = ['Linha']),
        (o.exports['Nothing selected_combobox_input'] = ['Nada selecionado']),
        (o.exports['All items_combobox_input'] = ['Todos os itens']),
        (o.exports.Cancel_input = ['Cancelar']),
        (o.exports.Open_input = ['Abrir']);
    },
    54138: (o) => {
      o.exports = ['Inverter escala'];
    },
    47807: (o) => {
      o.exports = ['Indexada em 100'];
    },
    34727: (o) => {
      o.exports = ['Logarítmica'];
    },
    19238: (o) => {
      o.exports = ['Sem legendas sobrepostas'];
    },
    70361: (o) => {
      o.exports = ['Percentual'];
    },
    72116: (o) => {
      o.exports = 'Regular';
    },
    33021: (o) => {
      o.exports = 'ETH';
    },
    75610: (o) => {
      o.exports = ['Horário de pregão eletrônico'];
    },
    97442: (o) => {
      o.exports = ['Horário de Negociação Estendido'];
    },
    32929: (o) => {
      o.exports = ['PÓS'];
    },
    56137: (o) => {
      o.exports = ['PRÉ'];
    },
    98801: (o) => {
      o.exports = 'Postmarket';
    },
    56935: (o) => {
      o.exports = ['Pré-market'];
    },
    63798: (o) => {
      o.exports = 'RTH';
    },
    24380: (o) => {
      o.exports = ['Horário regular de negociação'];
    },
    27991: (o) => {
      o.exports = ['Мaio'];
    },
    68716: (o) => {
      (o.exports = Object.create(null)),
        (o.exports['Amortization of Intangibles_study'] = [
          'Amortização de Intangíveis',
        ]),
        (o.exports['Amortization of Deferred Charges_study'] = [
          'Amortização de Despesas Diferidas',
        ]),
        (o.exports['Average Day Range_study'] = ['Range Diário Médio']),
        (o.exports['Bull Bear Power_study'] = 'Bull Bear Power'),
        (o.exports['Directional Movement Index_study'] = [
          'Índice de Movimento Direcional',
        ]),
        (o.exports['Ichimoku Cloud_study'] = ['Nuvem Ichimoku']),
        (o.exports.Ichimoku_study = 'Ichimoku'),
        (o.exports['Moving Average Convergence Divergence_study'] = [
          'Média Móvel Convergente e Divergente',
        ]),
        (o.exports['Volume Weighted Average Price_study'] = [
          'Preço Médio Ponderado por Volume',
        ]),
        (o.exports['Volume Weighted Moving Average_study'] = [
          'Média Móvel Ponderada Pelo Volume',
        ]),
        (o.exports['Williams Percent Range_study'] = [
          'Range Percentual de Williams',
        ]),
        (o.exports.Doji_study = 'Doji'),
        (o.exports['Spinning Top Black_study'] = ['Spinning Top Escuro']),
        (o.exports['Spinning Top White_study'] = ['Spinning Top Claro']),
        (o.exports.Technicals_study = ['Sinais técnicos']),
        (o.exports['Accounts payable_study'] = ['Despesas a pagar']),
        (o.exports['Accounts receivables, gross_study'] = [
          'Contas a receber, bruto',
        ]),
        (o.exports['Accounts receivable - trade, net_study'] = [
          'Contas a receber - negócios, líquido',
        ]),
        (o.exports.Accruals_study = 'Accruals'),
        (o.exports['Accrued payroll_study'] = ['Folha de pagamento acumulada']),
        (o.exports['Accumulated depreciation, total_study'] = [
          'Depreciação acumulada, total',
        ]),
        (o.exports['Additional paid-in capital/Capital surplus_study'] = [
          'Capital integralizado adicional/Capital excedente',
        ]),
        (o.exports['After tax other income/expense_study'] = [
          'Depois de impostos outras receitas/despesas',
        ]),
        (o.exports['Altman Z-score_study'] = ['Z-score de Altman']),
        (o.exports.Amortization_study = ['Amortização']),
        (o.exports['Amortization of intangibles_study'] = [
          'Amortização de intangíveis',
        ]),
        (o.exports['Amortization of deferred charges_study'] = [
          'Amortização de despesas diferidas',
        ]),
        (o.exports['Asset turnover_study'] = ['Giro de ativos']),
        (o.exports['Average basic shares outstanding_study'] = [
          'Média de ações ordinárias em circulação',
        ]),
        (o.exports['Bad debt / Doubtful accounts_study'] = [
          'Crédito em liquidação / Crédito de liquidação duvidosa',
        ]),
        (o.exports['Basic EPS_study'] = ['EPS Básico']),
        (o.exports['Basic earnings per share (Basic EPS)_study'] = [
          'Lucro básico por ação (EPS Básico)',
        ]),
        (o.exports['Beneish M-score_study'] = ['Escore-M de Beneish']),
        (o.exports['Book value per share_study'] = [
          'Valor patrimonial por ação',
        ]),
        (o.exports['Buyback yield %_study'] = ['Retorno de recompra %']),
        (o.exports['Capital and operating lease obligations_study'] = [
          'Obrigações de capital e lease operacional',
        ]),
        (o.exports['Capital expenditures_study'] = ['Dispêndios de capital']),
        (o.exports['Capital expenditures - fixed assets_study'] = [
          'Despesas de capital - ativos fixos',
        ]),
        (o.exports['Capital expenditures - other assets_study'] = [
          'Despesas de capital - outros ativos',
        ]),
        (o.exports['Capitalized lease obligations_study'] = [
          'Obrigações de lease capitalizados',
        ]),
        (o.exports['Cash and short term investments_study'] = [
          'Caixa e investimentos de curto prazo',
        ]),
        (o.exports['Cash conversion cycle_study'] = [
          'Ciclo de conversão de caixa',
        ]),
        (o.exports['Cash & equivalents_study'] = ['Caixa e equivalentes']),
        (o.exports['Cash from financing activities_study'] = [
          'Fluxo de Caixa das Atividades de Financiamento',
        ]),
        (o.exports['Cash from investing activities_study'] = [
          'Fluxo de Caixa das Atividades de Investimento',
        ]),
        (o.exports['Cash from operating activities_study'] = [
          'Fluxo de Caixa das Atividades Operacionais',
        ]),
        (o.exports['Cash to debt ratio_study'] = ['Relação caixa/dívida']),
        (o.exports['Change in accounts payable_study'] = [
          'Variação nas contas a pagar',
        ]),
        (o.exports['Change in accounts receivable_study'] = [
          'Variação nas contas a receber',
        ]),
        (o.exports['Change in accrued expenses_study'] = [
          'Variação nas despesas acumuladas',
        ]),
        (o.exports['Change in inventories_study'] = ['Variação nos estoques']),
        (o.exports['Change in other assets/liabilities_study'] = [
          'Variação em outros ativos/passivos',
        ]),
        (o.exports['Change in taxes payable_study'] = [
          'Variação nos impostos a pagar',
        ]),
        (o.exports['Changes in working capital_study'] = [
          'Mudanças no Capital de Giro',
        ]),
        (o.exports['COGS to revenue ratio_study'] = ['Relação CPV/receita']),
        (o.exports['Common dividends paid_study'] = ['Dividendos pagos']),
        (o.exports['Common equity, total_study'] = ['Capital social, total']),
        (o.exports['Common stock par/Carrying value_study'] = [
          'Valor nominal de ações ordinárias/Valor contábil',
        ]),
        (o.exports['Cost of goods_study'] = ['Custo dos Produtos']),
        (o.exports['Cost of goods sold_study'] = [
          'Custo das mercadorias vendidas',
        ]),
        (o.exports['Current portion of LT debt and capital leases_study'] = [
          'Parcela atual da dívida LP e leasing de capital',
        ]),
        (o.exports['Current ratio_study'] = ['Razão de liquidez corrente']),
        (o.exports['Days inventory_study'] = ['Dias em estoque']),
        (o.exports['Days payable_study'] = ['Dias a pagar']),
        (o.exports['Days sales outstanding_study'] = [
          'Dias de vendas em aberto',
        ]),
        (o.exports['Debt to assets ratio_study'] = ['Relação dívida/ativo']),
        (o.exports['Debt to EBITDA ratio_study'] = ['Relação dívida/EBITDA']),
        (o.exports['Debt to equity ratio_study'] = [
          'Relação dívida/capital próprio',
        ]),
        (o.exports['Debt to revenue ratio_study'] = ['Relação dívida/receita']),
        (o.exports['Deferred income, current_study'] = [
          'Receita diferida, atual',
        ]),
        (o.exports['Deferred income, non-current_study'] = [
          'Renda diferida, não corrente',
        ]),
        (o.exports['Deferred tax assets_study'] = ['Ativos fiscais diferidos']),
        (o.exports['Deferred taxes (cash flow)_study'] = [
          'Impostos diferidos (fluxo de caixa)',
        ]),
        (o.exports['Deferred tax liabilities_study'] = [
          'Passivos fiscais diferidos',
        ]),
        (o.exports.Depreciation_study = ['Depreciação']),
        (o.exports['Deprecation and amortization_study'] = [
          'Depreciação e amortização',
        ]),
        (o.exports['Depreciation & amortization (cash flow)_study'] = [
          'Depreciação e amortização (fluxo de caixa)',
        ]),
        (o.exports['Depreciation/depletion_study'] = [
          'Depreciação/esgotamento',
        ]),
        (o.exports['Diluted EPS_study'] = ['EPS Diluído']),
        (o.exports['Diluted earnings per share (Diluted EPS)_study'] = [
          'Lucro diluído por ação (EPS Diluído)',
        ]),
        (o.exports[
          'Diluted net income available to common stockholders_study'
        ] = ['Lucro líquido diluído disponível para acionistas ordinários']),
        (o.exports['Diluted shares outstanding_study'] = [
          'Ações diluídas em circulação',
        ]),
        (o.exports['Dilution adjustment_study'] = ['Ajuste de diluição']),
        (o.exports['Discontinued operations_study'] = [
          'Operações descontinuadas',
        ]),
        (o.exports['Dividend payout ratio %_study'] = [
          'Índice de distribuição de dividendos %',
        ]),
        (o.exports['Dividends payable_study'] = ['Dividendo em dinheiro']),
        (o.exports['Dividends per share - common stock primary issue_study'] = [
          'Dividendos por ação - emissão primária de ações ordinárias',
        ]),
        (o.exports['Dividend yield %_study'] = ['Rendimento do dividendo %']),
        (o.exports['Earnings yield_study'] = ['Retorno de lucro']),
        (o.exports.EBIT_study = 'EBIT'),
        (o.exports.EBITDA_study = 'EBITDA'),
        (o.exports['EBITDA margin %_study'] = ['Margem EBITDA %']),
        (o.exports['Effective interest rate on debt %_study'] = [
          'Taxa de juros efetiva sobre a dívida em %',
        ]),
        (o.exports['Enterprise value_study'] = ['Valor da Empresa']),
        (o.exports['Enterprise value to EBITDA ratio_study'] = [
          'Valor da empresa em relação ao EBITDA',
        ]),
        (o.exports['Enterprise value to EBIT ratio_study'] = [
          'Valor da empresa em relação ao EBIT',
        ]),
        (o.exports['Enterprise value to revenue ratio_study'] = [
          'Valor da empresa em relação à receita',
        ]),
        (o.exports['EPS basic one year growth_study'] = [
          'EPS básico para crescimento de um ano',
        ]),
        (o.exports['EPS diluted one year growth_study'] = [
          'EPS diluído por ano de crescimento',
        ]),
        (o.exports['EPS estimates_study'] = ['Estimativa de EPS']),
        (o.exports['Equity in earnings_study'] = ['Equity em resultados']),
        (o.exports['Equity to assets ratio_study'] = [
          'Índice de patrimônio líquido',
        ]),
        (o.exports['Financing activities – other sources_study'] = [
          'Atividades de financiamento - outras fontes',
        ]),
        (o.exports['Financing activities – other uses_study'] = [
          'Atividades de financiamento - outros usos',
        ]),
        (o.exports['Float shares outstanding_study'] = [
          'Ações flutuantes em circulação',
        ]),
        (o.exports['Free cash flow_study'] = ['Fluxo de caixa livre']),
        (o.exports['Free cash flow margin %_study'] = [
          'Margem de fluxo de caixa livre %',
        ]),
        (o.exports['Fulmer H factor_study'] = ['Fator Fulmer H']),
        (o.exports['Funds from operations_study'] = ['Recursos de Operações']),
        (o.exports['Goodwill, net_study'] = ['Patrimônio da marca, líquido']),
        (o.exports['Goodwill to assets ratio_study'] = [
          'Patrimônio da marca em relação aos ativos',
        ]),
        (o.exports["Graham's number_study"] = ['Número de Graham']),
        (o.exports['Gross margin %_study'] = ['Margem bruta %']),
        (o.exports['Gross profit_study'] = ['Lucro Bruto']),
        (o.exports['Gross profit to assets ratio_study'] = [
          'Lucro bruto em relação aos ativos',
        ]),
        (o.exports['Gross property/plant/equipment_study'] = [
          'Terrenos/edifícios/equipamento bruto',
        ]),
        (o.exports.Impairments_study = 'Impairments'),
        (o.exports['Income Tax Credits_study'] = [
          'Créditos de Imposto de Renda',
        ]),
        (o.exports['Income tax, current_study'] = [
          'Imposto de renda, vigente',
        ]),
        (o.exports['Income tax, current - domestic_study'] = [
          'Imposto de renda, vigente - nacional',
        ]),
        (o.exports['Income Tax, current - foreign_study'] = [
          'Imposto de renda, vigente - estrangeiro',
        ]),
        (o.exports['Income tax, deferred_study'] = [
          'Imposto de renda, diferido',
        ]),
        (o.exports['Income tax, deferred - domestic_study'] = [
          'Imposto de renda, diferido - nacional',
        ]),
        (o.exports['Income tax, deferred - foreign_study'] = [
          'Imposto de renda, diferido - estrangeiro',
        ]),
        (o.exports['Income tax payable_study'] = ['Imposto de renda a pagar']),
        (o.exports['Interest capitalized_study'] = ['Juros capitalizados']),
        (o.exports['Interest coverage_study'] = ['Cobertura de juros']),
        (o.exports['Interest expense, net of interest capitalized_study'] = [
          'Despesas com juros, juros líquidos capitalizados',
        ]),
        (o.exports['Interest expense on debt_study'] = [
          'Despesas com juros sobre dívida',
        ]),
        (o.exports['Inventories - finished goods_study'] = [
          'Estoques - produtos acabados',
        ]),
        (o.exports['Inventories - progress payments & other_study'] = [
          'Estoques - pagamentos em progresso e outros',
        ]),
        (o.exports['Inventories - raw materials_study'] = [
          'Estoques - matérias-primas',
        ]),
        (o.exports['Inventories - work in progress_study'] = [
          'Estoques - produto em andamento',
        ]),
        (o.exports['Inventory to revenue ratio_study'] = [
          'Estoque em relação à receita',
        ]),
        (o.exports['Inventory turnover_study'] = ['Rotatividade de estoque']),
        (o.exports['Investing activities – other sources_study'] = [
          'Atividades de investimento - outras fontes',
        ]),
        (o.exports['Investing activities – other uses_study'] = [
          'Atividades de investimento – outros usos',
        ]),
        (o.exports['Investments in unconsolidated subsidiaries_study'] = [
          'Investimentos em subsidiárias não consolidadas',
        ]),
        (o.exports['Issuance of long term debt_study'] = [
          'Emissão de dívida de longo prazo',
        ]),
        (o.exports['Issuance/retirement of debt, net_study'] = [
          'Emissão/amortização da dívida, líquida',
        ]),
        (o.exports['Issuance/retirement of long term debt_study'] = [
          'Emissão/amortização da dívida de longo prazo',
        ]),
        (o.exports['Issuance/retirement of other debt_study'] = [
          'Emissão/amortização de outras dívidas',
        ]),
        (o.exports['Issuance/retirement of short term debt_study'] = [
          'Emissão/amortização da dívida de curto prazo',
        ]),
        (o.exports['Issuance/retirement of stock, net_study'] = [
          'Emissão/retirada de ações, líquido',
        ]),
        (o.exports['KZ index_study'] = ['Índice KZ']),
        (o.exports['Legal claim expense_study'] = ['Despesas legais']),
        (o.exports['Long term debt_study'] = ['Dívida de Longo Prazo']),
        (o.exports['Long term debt excl. lease liabilities_study'] = [
          'Dívidas de longo prazo excluindo-se obrigações de leasing',
        ]),
        (o.exports['Long term debt to total assets ratio_study'] = [
          'Razão Dívidas de longo prazo/total de ativos',
        ]),
        (o.exports['Long term investments_study'] = [
          'Investimentos de longo prazo',
        ]),
        (o.exports['Market capitalization_study'] = ['Valor de Mercado']),
        (o.exports['Minority interest_study'] = ['Participações Minoritárias']),
        (o.exports['Miscellaneous non-operating expense_study'] = [
          'Despesas não operacionais diversas',
        ]),
        (o.exports['Net current asset value per share_study'] = [
          'Valor líquido do ativo por ação',
        ]),
        (o.exports['Net debt_study'] = ['Dívida líquida']),
        (o.exports['Net income_study'] = ['Lucro Líquido']),
        (o.exports['Net income before discontinued operations_study'] = [
          'Lucro líquido antes das operações descontinuadas',
        ]),
        (o.exports['Net income (cash flow)_study'] = [
          'Lucro líquido (fluxo de caixa)',
        ]),
        (o.exports['Net income per employee_study'] = [
          'Lucro líquido por funcionário',
        ]),
        (o.exports['Net intangible assets_study'] = [
          'Ativos intangíveis líquidos',
        ]),
        (o.exports['Net margin %_study'] = ['Margem líquida %']),
        (o.exports['Net property/plant/equipment_study'] = [
          'Terrenos/edifícios/equipamento líquido',
        ]),
        (o.exports['Non-cash items_study'] = ['Itens não monetários']),
        (o.exports['Non-controlling/minority interest_study'] = [
          'Participação de não-controladores/minoritários',
        ]),
        (o.exports['Non-operating income, excl. interest expenses_study'] = [
          'Receita não-operacional, excluindo despesas com juros',
        ]),
        (o.exports['Non-operating income, total_study'] = [
          'Receita não operacional, total',
        ]),
        (o.exports['Non-operating interest income_study'] = [
          'Receita de juros não operacionais',
        ]),
        (o.exports['Note receivable - long term_study'] = [
          'Nota a receber - longo prazo',
        ]),
        (o.exports['Notes payable_study'] = ['Notas a pagar']),
        (o.exports['Number of employees_study'] = ['Número de empregados']),
        (o.exports['Number of shareholders_study'] = ['Número de acionistas']),
        (o.exports['Operating earnings yield %_study'] = [
          'Rendimento operacional %',
        ]),
        (o.exports['Operating expenses (excl. COGS)_study'] = [
          'Despesas operacionais (excl. CPV)',
        ]),
        (o.exports['Operating income_study'] = ['Resultado Operacional']),
        (o.exports['Operating lease liabilities_study'] = [
          'Obrigações de leasing operacional',
        ]),
        (o.exports['Operating margin %_study'] = ['Margem operacional %']),
        (o.exports['Other COGS_study'] = ['Outros CPV']),
        (o.exports['Other common equity_study'] = ['Outras ações ordinárias']),
        (o.exports['Other current assets, total_study'] = [
          'Outros ativos circulantes, total',
        ]),
        (o.exports['Other current liabilities_study'] = [
          'Outros passivos circulantes',
        ]),
        (o.exports['Other cost of goods sold_study'] = [
          'Outros custos de produtos vendidos',
        ]),
        (o.exports['Other exceptional charges_study'] = [
          'Outras despesas excepcionais',
        ]),
        (o.exports['Other financing cash flow items, total_study'] = [
          'Outros Itens de Fluxo de Caixa de Financiamento, Total',
        ]),
        (o.exports['Other intangibles, net_study'] = [
          'Outros intangíveis, líquidos',
        ]),
        (o.exports['Other investing cash flow items, total_study'] = [
          'Outros Itens de Fluxo de Caixa das Aplicações, Total',
        ]),
        (o.exports['Other investments_study'] = ['Outros investimentos']),
        (o.exports['Other liabilities, total_study'] = [
          'Outros passivos, total',
        ]),
        (o.exports['Other long term assets, total_study'] = [
          'Outros ativos de longo prazo, total',
        ]),
        (o.exports['Other non-current liabilities, total_study'] = [
          'Outros passivos não circulantes, total',
        ]),
        (o.exports['Other operating expenses, total_study'] = [
          'Outras despesas operacionais, total',
        ]),
        (o.exports['Other receivables_study'] = ['Outras contas a receber']),
        (o.exports['Other short term debt_study'] = [
          'Outras dívidas de curto prazo',
        ]),
        (o.exports['Paid in capital_study'] = ['Pago em dinheiro']),
        (o.exports['PEG ratio_study'] = ['Índice PEG']),
        (o.exports['Piotroski F-score_study'] = 'Piotroski F-score'),
        (o.exports['Preferred dividends_study'] = ['Dividendos preferenciais']),
        (o.exports['Preferred dividends paid_study'] = [
          'Dividendos preferenciais pagos',
        ]),
        (o.exports['Preferred stock, carrying value_study'] = [
          'Ações preferenciais, valor contábil',
        ]),
        (o.exports['Prepaid expenses_study'] = ['Despesas pré-pagas']),
        (o.exports['Pretax equity in earnings_study'] = [
          'Equity em resultados antes dos impostos',
        ]),
        (o.exports['Pretax income_study'] = ['Receita antes de impostos']),
        (o.exports['Price earnings ratio forward_study'] = [
          'Relação Preço/Lucro em exercício futuro',
        ]),
        (o.exports['Price sales ratio forward_study'] = [
          'Razão de preços sobre vendas projetado',
        ]),
        (o.exports['Price to book ratio_study'] = [
          'Relação Preço sobre Valor Patrimonial',
        ]),
        (o.exports['Price to cash flow ratio_study'] = [
          'Relação Preço/Fluxo de Caixa',
        ]),
        (o.exports['Price to earnings ratio_study'] = ['Relação Preço/Lucros']),
        (o.exports['Price to free cash flow ratio_study'] = [
          'Relação preço/fluxo de caixa livre',
        ]),
        (o.exports['Price to sales ratio_study'] = [
          'Razão de preços sobre vendas',
        ]),
        (o.exports['Price to tangible book ratio_study'] = [
          'Relação Preço/Valor Contábil Tangível',
        ]),
        (o.exports['Provision for risks & charge_study'] = [
          'Provisão para riscos & encargos',
        ]),
        (o.exports['Purchase/acquisition of business_study'] = [
          'Compra/aquisição de negócios',
        ]),
        (o.exports['Purchase of investments_study'] = [
          'Compra de investimentos',
        ]),
        (o.exports['Purchase/sale of business, net_study'] = [
          'Compra/Venda de Negócios, Líquida',
        ]),
        (o.exports['Purchase/sale of investments, net_study'] = [
          'Compra/Venda de Investimentos, Líquida',
        ]),
        (o.exports['Quality ratio_study'] = ['Relação de qualidade']),
        (o.exports['Quick ratio_study'] = ['Razão rápida']),
        (o.exports['Reduction of long term debt_study'] = [
          'Redução da dívida de longo prazo',
        ]),
        (o.exports['Repurchase of common & preferred stock_study'] = [
          'Recompra de ações ordinárias e preferenciais',
        ]),
        (o.exports['Research & development_study'] = [
          'Pesquisa & Desenvolvimento',
        ]),
        (o.exports['Research & development to revenue ratio_study'] = [
          'Pesquisa e desenvolvimento em relação à receita',
        ]),
        (o.exports['Restructuring charge_study'] = ['Taxa de reestruturação']),
        (o.exports['Retained earnings_study'] = ['Lucros acumulados']),
        (o.exports['Return on assets %_study'] = ['Retorno sobre ativos %']),
        (o.exports['Return on equity %_study'] = [
          'Retorno sobre o patrimônio líquido %',
        ]),
        (o.exports['Return on equity adjusted to book value %_study'] = [
          'Retorno sobre o patrimônio líquido ajustado ao valor contábil %',
        ]),
        (o.exports['Return on invested capital %_study'] = [
          'Retorno sobre o capital investido %',
        ]),
        (o.exports['Return on tangible assets %_study'] = [
          'Retorno sobre ativos tangíveis %',
        ]),
        (o.exports['Return on tangible equity %_study'] = [
          'Retorno sobre o patrimônio tangível %',
        ]),
        (o.exports['Revenue estimates_study'] = ['Estimativas de receita']),
        (o.exports['Revenue one year growth_study'] = [
          'Crescimento de receita em um ano',
        ]),
        (o.exports['Revenue per employee_study'] = ['Receita por funcionário']),
        (o.exports['Sale/maturity of investments_study'] = [
          'Venda/maturidade de investimentos',
        ]),
        (o.exports['Sale of common & preferred stock_study'] = [
          'Venda de ações ordinárias & preferenciais',
        ]),
        (o.exports['Sale of fixed assets & businesses_study'] = [
          'Venda de ativos fixos & negócios',
        ]),
        (o.exports['Selling/general/admin expenses, other_study'] = [
          'Despesas Gerais/de Vendas/Administrativas, Outras',
        ]),
        (o.exports['Selling/general/admin expenses, total_study'] = [
          'Despesas com vendas/Gerais/Administração, Total',
        ]),
        (o.exports["Shareholders' equity_study"] = ['Patrimônio líquido']),
        (o.exports['Shares buyback ratio %_study'] = [
          'Taxa de recompra de ações %',
        ]),
        (o.exports["Elder's Force Index_study"] = ['Índice de Força de Elder']),
        (o.exports['Short term debt_study'] = ['Dívida de curto prazo']),
        (o.exports['Short term debt excl. current portion of LT debt_study'] = [
          'Dívida de curto prazo, excluindo a parcela atual da dívida LP',
        ]),
        (o.exports['Short term investments_study'] = [
          'Investimentos de curto prazo',
        ]),
        (o.exports['Sloan ratio %_study'] = ['Razão de Sloan %']),
        (o.exports['Springate score_study'] = ['Pontuação Springate']),
        (o.exports['Sustainable growth rate_study'] = [
          'Taxa de crescimento sustentável',
        ]),
        (o.exports['Tangible book value per share_study'] = [
          'Valor contábil tangível por ação',
        ]),
        (o.exports['Tangible common equity ratio_study'] = [
          'Índice de patrimônio comum tangível',
        ]),
        (o.exports.Taxes_study = ['Impostos']),
        (o.exports["Tobin's Q (approximate)_study"] = [
          "Tobin's Q (aproximado)",
        ]),
        (o.exports['Total assets_study'] = ['Ativos Totais']),
        (o.exports['Total cash dividends paid_study'] = [
          'Total de Dividendos Pagos',
        ]),
        (o.exports['Total common shares outstanding_study'] = [
          'Total de Ações Ordinárias em Circulação',
        ]),
        (o.exports['Total current assets_study'] = [
          'Ativos Totais Circulantes',
        ]),
        (o.exports['Total current liabilities_study'] = [
          'Passivos Totais Circulantes',
        ]),
        (o.exports['Total debt_study'] = ['Dívida Total']),
        (o.exports['Total equity_study'] = ['Patrimônio Total']),
        (o.exports['Total inventory_study'] = ['Inventário total']),
        (o.exports['Total liabilities_study'] = ['Total de Passivos']),
        (o.exports["Total liabilities & shareholders' equities_study"] = [
          'Total de passivos e patrimônio líquido',
        ]),
        (o.exports['Total non-current assets_study'] = [
          'Total de Ativos Não Circulantes',
        ]),
        (o.exports['Total non-current liabilities_study'] = [
          'Total do Passivo Não Circulante',
        ]),
        (o.exports['Total operating expenses_study'] = [
          'Total de custos operacionais',
        ]),
        (o.exports['Total receivables, net_study'] = [
          'Total de recebíveis, líquido',
        ]),
        (o.exports['Total revenue_study'] = ['Receita Total']),
        (o.exports['Treasury stock - common_study'] = [
          'Ações em tesouraria - ordinárias',
        ]),
        (o.exports['Unrealized gain/loss_study'] = [
          'Lucros/Prejuízos não realizados',
        ]),
        (o.exports['Unusual income/expense_study'] = [
          'Receita/despesa incomum',
        ]),
        (o.exports['Zmijewski score_study'] = ['Pontuação de Zmijewski']),
        (o.exports['Valuation ratios_study'] = ['Razão Valuation']),
        (o.exports['Profitability ratios_study'] = ['Índice de Rentabilidade']),
        (o.exports['Liquidity ratios_study'] = ['Índice de Liquidez']),
        (o.exports['Solvency ratios_study'] = ['Índice de Solvência']),
        (o.exports['Accumulation/Distribution_study'] = [
          'Acumulação/Distribuição',
        ]),
        (o.exports['Accumulative Swing Index_study'] = [
          'Índice Acumulativo de Swing',
        ]),
        (o.exports['Advance/Decline_study'] = ['Avanço/Declínio']),
        (o.exports['Arnaud Legoux Moving Average_study'] = [
          'Média móvel de Arnaud Legoux',
        ]),
        (o.exports.Aroon_study = 'Aroon'),
        (o.exports.ASI_study = ['IAS (ASI)']),
        (o.exports['Average Directional Index_study'] = [
          'Índice Direcional Médio',
        ]),
        (o.exports['Average True Range_study'] = [
          'Média de Amplitude de Variação (ATR)',
        ]),
        (o.exports['Awesome Oscillator_study'] = ['Oscilador Awesome']),
        (o.exports['Balance of Power_study'] = ['Equilíbrio de poder']),
        (o.exports['Bollinger Bands %B_study'] = ['Bandas de Bollinger %B']),
        (o.exports['Bollinger Bands Width_study'] = [
          'Largura das Bandas de Bollinger',
        ]),
        (o.exports['Bollinger Bands_study'] = ['Bandas de Bollinger']),
        (o.exports['Chaikin Money Flow_study'] = [
          'Fluxo de Dinheiro de Chaikin',
        ]),
        (o.exports['Chaikin Oscillator_study'] = ['Oscilador Chaikin']),
        (o.exports['Chande Kroll Stop_study'] = ['Parada de Chande Kroll']),
        (o.exports['Chande Momentum Oscillator_study'] = [
          'Oscilador de Momento de Chande',
        ]),
        (o.exports['Chop Zone_study'] = ['Zona Chop']),
        (o.exports['Choppiness Index_study'] = ['Índice Choppiness']),
        (o.exports['Commodity Channel Index_study'] = [
          'Índice de Canal de Commodities (CCI)',
        ]),
        (o.exports['Connors RSI_study'] = ['IFR de Connors']),
        (o.exports['Coppock Curve_study'] = ['Curva Coppock']),
        (o.exports['Correlation Coefficient_study'] = [
          'Coeficiente de correlação',
        ]),
        (o.exports.CRSI_study = 'CRSI'),
        (o.exports['Detrended Price Oscillator_study'] = [
          'Oscilador de Preço Destendenciado',
        ]),
        (o.exports['Directional Movement_study'] = ['Movimento Direcional']),
        (o.exports['Donchian Channels_study'] = ['Canais Donchian']),
        (o.exports['Double EMA_study'] = [
          'Média Móvel Exponencial Dupla (Double EMA)',
        ]),
        (o.exports['Ease Of Movement_study'] = 'Ease Of Movement'),
        (o.exports['Elder Force Index_study'] = ['Índice de Força de Elder']),
        (o.exports['EMA Cross_study'] = ['Cruzamento de MME']),
        (o.exports.Envelopes_study = 'Envelopes'),
        (o.exports['Fisher Transform_study'] = ['Transformação de Fisher']),
        (o.exports['Fixed Range_study'] = ['Range Fixo']),
        (o.exports['Fixed Range Volume Profile_study'] = [
          'Perfil de Volume Range Fixo',
        ]),
        (o.exports['Guppy Multiple Moving Average_study'] = [
          'Média Móvel Múltipla Guppy',
        ]),
        (o.exports['Historical Volatility_study'] = ['Volatilidade Histórica']),
        (o.exports['Hull Moving Average_study'] = ['Média Móvel de Hull']),
        (o.exports['Keltner Channels_study'] = ['Canais de Keltner']),
        (o.exports['Klinger Oscillator_study'] = ['Oscilador de Klinger']),
        (o.exports['Know Sure Thing_study'] = ['Indicador de Certeza (KST)']),
        (o.exports['Least Squares Moving Average_study'] = [
          'Média Móvel de Mínimos Quadrados',
        ]),
        (o.exports['Linear Regression Curve_study'] = [
          'Curva de Regressão Linear',
        ]),
        (o.exports['MA Cross_study'] = ['Cruzamento de MM']),
        (o.exports['MA with EMA Cross_study'] = [
          'Cruzamento de Média Móvel Simples e Exponencial',
        ]),
        (o.exports['MA/EMA Cross_study'] = ['Cruzamento MM/MME']),
        (o.exports.MACD_study = 'MACD'),
        (o.exports['Mass Index_study'] = ['Índice de Massa']),
        (o.exports['McGinley Dynamic_study'] = ['McGinley Dinâmico']),
        (o.exports.Median_study = ['Mediana']),
        (o.exports.Momentum_study = 'Momentum'),
        (o.exports['Money Flow_study'] = ['Fluxo Monetário']),
        (o.exports['Moving Average Channel_study'] = ['Canal de Média Móvel']),
        (o.exports['Moving Average Exponential_study'] = [
          'Média Móvel Exponencial',
        ]),
        (o.exports['Moving Average Weighted_study'] = [
          'Média Móvel Ponderada',
        ]),
        (o.exports['Moving Average_study'] = ['Média Móvel']),
        (o.exports['Net Volume_study'] = ['Volume Líquido']),
        (o.exports['On Balance Volume_study'] = 'On Balance Volume'),
        (o.exports['Parabolic SAR_study'] = ['SAR Parabólico']),
        (o.exports['Pivot Points Standard_study'] = ['Pontos de Pivô Padrão']),
        (o.exports['Periodic Volume Profile_study'] = [
          'Perfil de Volume Periódico',
        ]),
        (o.exports['Price Channel_study'] = ['Canal de Preço']),
        (o.exports['Price Oscillator_study'] = ['Oscilador de Preço']),
        (o.exports['Price Volume Trend_study'] = [
          'Tendência de Preço Volume (PVT)',
        ]),
        (o.exports['Rate Of Change_study'] = ['Taxa de Variação (ROC)']),
        (o.exports['Relative Strength Index_study'] = [
          'Indice de Força Relativa',
        ]),
        (o.exports['Relative Vigor Index_study'] = [
          'Índice de Vigor Relativo (RVI)',
        ]),
        (o.exports['Relative Volatility Index_study'] = [
          'Índice de Volatilidade Relativa',
        ]),
        (o.exports['Session Volume_study'] = ['Volume da Sessão']),
        (o.exports['Session Volume HD_study'] = ['Volume da Sessão HD']),
        (o.exports['Session Volume Profile_study'] = [
          'Perfil de Volume da Sessão',
        ]),
        (o.exports['Session Volume Profile HD_study'] = [
          'Perfil de Volume da Sessão HD',
        ]),
        (o.exports['SMI Ergodic Indicator/Oscillator_study'] = [
          'Indicador/Oscilador SMI Ergodic',
        ]),
        (o.exports['Smoothed Moving Average_study'] = [
          'Média Móvel Suavizada',
        ]),
        (o.exports.Stoch_study = 'Stoch'),
        (o.exports['Stochastic RSI_study'] = ['RSI Estocástico']),
        (o.exports.Stochastic_study = ['Estocástico']),
        (o.exports['Triple EMA_study'] = ['Média Móvel Exponencial Tripla']),
        (o.exports.TRIX_study = ['Média Móvel Tripla (TRIX)']),
        (o.exports['True Strength Indicator_study'] = [
          'Indicador de Força Real',
        ]),
        (o.exports['Ultimate Oscillator_study'] = 'Ultimate Oscillator'),
        (o.exports['Visible Range_study'] = ['Range Visível']),
        (o.exports['Visible Range Volume Profile_study'] = [
          'Perfil de Volume de Range Visível',
        ]),
        (o.exports['Volume Oscillator_study'] = ['Oscilador de Volume']),
        (o.exports.Volume_study = 'Volume'),
        (o.exports.Vol_study = 'Vol'),
        (o.exports['Vortex Indicator_study'] = ['Indicador Vortex']),
        (o.exports.VWAP_study = 'VWAP'),
        (o.exports.VWMA_study = 'VWMA'),
        (o.exports['Williams %R_study'] = 'Williams %R'),
        (o.exports['Williams Alligator_study'] = [
          'Indicador Alligator de Williams',
        ]),
        (o.exports['Williams Fractal_study'] = [
          'Indicador Fractal de Williams',
        ]),
        (o.exports['Zig Zag_study'] = ['Indicador Zig Zag']),
        (o.exports['24-hour Volume_study'] = ['Volume 24-horas']),
        (o.exports['Ease of Movement_study'] = ['Facilidade de Movimento']),
        (o.exports['Elders Force Index_study'] = ['Índice de Força de Elder']),
        (o.exports.Envelope_study = 'Envelope'),
        (o.exports.Gaps_study = 'Gaps'),
        (o.exports['Linear Regression Channel_study'] = [
          'Canal de Regressão Linear',
        ]),
        (o.exports['Moving Average Ribbon_study'] = ['Faixa de Média Móvel']),
        (o.exports['Multi-Time Period Charts_study'] = [
          'Gráfico Multi-Períodos',
        ]),
        (o.exports['Open Interest_study'] = ['Contrato não exercido']),
        (o.exports['Rob Booker - Intraday Pivot Points_study'] = [
          'Rob Booker - Pontos Pivôs Intradiários',
        ]),
        (o.exports['Rob Booker - Knoxville Divergence_study'] = [
          'Rob Booker - Divergência de Knoxville',
        ]),
        (o.exports['Rob Booker - Missed Pivot Points_study'] = [
          'Rob Booker - Pontos Pivôs Ausentes',
        ]),
        (o.exports['Rob Booker - Reversal_study'] = ['Rob Booker - Reversão']),
        (o.exports['Rob Booker - Ziv Ghost Pivots_study'] = [
          'Rob Booker - Pivôs Fantasmas de Ziv',
        ]),
        (o.exports.Supertrend_study = 'Supertrend'),
        (o.exports['Technical Ratings_study'] = 'Technical Ratings'),
        (o.exports['True Strength Index_study'] = [
          'Índice de Força Verdadeira',
        ]),
        (o.exports['Up/Down Volume_study'] = ['Volume Up/Down']),
        (o.exports['Visible Average Price_study'] = ['Preço Médio Visível']),
        (o.exports['Williams Fractals_study'] = ['Fractais de Williams']),
        (o.exports['Keltner Channels Strategy_study'] = [
          'Estratégia dos Canais de Keltner',
        ]),
        (o.exports['Rob Booker - ADX Breakout_study'] =
          'Rob Booker - ADX Breakout'),
        (o.exports['Supertrend Strategy_study'] = ['Estratégia Supertrend']),
        (o.exports['Technical Ratings Strategy_study'] = [
          'Estratégia Technical Ratings',
        ]),
        (o.exports['Auto Anchored Volume Profile_study'] = [
          'Perfil de Volume AutoAncorado',
        ]),
        (o.exports['Auto Fib Extension_study'] = ['Auto Extensão de Fib']),
        (o.exports['Auto Fib Retracement_study'] = ['Retração Fib Automática']),
        (o.exports['Auto Pitchfork_study'] = ['Auto Garfo']),
        (o.exports['Bearish Flag Chart Pattern_study'] = [
          'Padrão de Gráfico de Bandeira de Baixa',
        ]),
        (o.exports['Bullish Pennant Chart Pattern_study'] = [
          'Padrão de Gráfico de Flâmula Baixa',
        ]),
        (o.exports['Double Bottom Chart Pattern_study'] = [
          'Padrão de Gráfico de Fundo Duplo',
        ]),
        (o.exports['Double Top Chart Pattern_study'] = [
          'Padrão de Gráfico de Topo Duplo',
        ]),
        (o.exports['Elliott Wave Chart Pattern_study'] = [
          'Padrão de Gráfico de Onda de Elliott',
        ]),
        (o.exports['Falling Wedge Chart Pattern_study'] = [
          'Padrão de Gráfico de Cunha Descendente',
        ]),
        (o.exports['Head And Shoulders Chart Pattern_study'] = [
          'Padrão de Gráfico Ombro Cabeça Ombro',
        ]),
        (o.exports['Inverse Head And Shoulders Chart Pattern_study'] = [
          'Padrão de Gráfico Ombro Cabeça Ombro Invertido',
        ]),
        (o.exports['Rectangle Chart Pattern_study'] = [
          'Padrão de Gráfico de Retângulo',
        ]),
        (o.exports['Rising Wedge Chart Pattern_study'] = [
          'Padrão de Gráfico de Cunha Ascendente',
        ]),
        (o.exports['Triangle Chart Pattern_study'] = [
          'Padrão de Gráfico de Triângulo',
        ]),
        (o.exports['Triple Bottom Chart Pattern_study'] = [
          'Padrão de Gráfico de Fundo Triplo',
        ]),
        (o.exports['Triple Top Chart Pattern_study'] = [
          'Padrão de Gráfico de Topo Triplo',
        ]),
        (o.exports['VWAP Auto Anchored_study'] = ['VWAP Autoancorado']),
        (o.exports['*All Candlestick Patterns*_study'] = [
          '*Todos os Padrões de Velas*',
        ]),
        (o.exports['Abandoned Baby - Bearish_study'] = [
          'Abandoned Baby - De Baixa',
        ]),
        (o.exports['Abandoned Baby - Bullish_study'] = [
          'Abandoned Baby - De Alta',
        ]),
        (o.exports['Dark Cloud Cover - Bearish_study'] = [
          'Dark Cloud Cover - De Baixa',
        ]),
        (o.exports['Doji Star - Bearish_study'] = ['Doji Star - De Baixa']),
        (o.exports['Doji Star - Bullish_study'] = ['Doji Star - De Alta']),
        (o.exports['Downside Tasuki Gap - Bearish_study'] = [
          'Downside Tasuki Gap - De Baixa',
        ]),
        (o.exports['Dragonfly Doji - Bullish_study'] = [
          'Dragonfly Doji - De Alta',
        ]),
        (o.exports['Engulfing - Bearish_study'] = ['Engolfo - De Baixa']),
        (o.exports['Engulfing - Bullish_study'] = ['Engolfo - De Alta']),
        (o.exports['Evening Doji Star - Bearish_study'] = [
          'Evening Doji Star - De Baixa',
        ]),
        (o.exports['Evening Star - Bearish_study'] = [
          'Evening Star - De Baixa',
        ]),
        (o.exports['Falling Three Methods - Bearish_study'] = [
          'Falling Three Methods - De Baixa',
        ]),
        (o.exports['Falling Window - Bearish_study'] = [
          'Falling Window - De Baixa',
        ]),
        (o.exports['Gravestone Doji - Bearish_study'] = [
          'Gravestone Doji - De Baixa',
        ]),
        (o.exports['Hammer - Bullish_study'] = ['Martelo - De Alta']),
        (o.exports['Hanging Man - Bearish_study'] = ['Hanging Man - De Baixa']),
        (o.exports['Harami - Bearish_study'] = ['Harami - De Baixa']),
        (o.exports['Harami - Bullish_study'] = ['Harami - De Alta']),
        (o.exports['Inverted Hammer - Bullish_study'] = [
          'Martelo Invertido - De Alta',
        ]),
        (o.exports['Kicking - Bearish_study'] = ['Kicking - De Baixa']),
        (o.exports['Kicking - Bullish_study'] = ['Kicking - De Alta']),
        (o.exports['Long Lower Shadow - Bullish_study'] = [
          'Long Lower Shadow - De Alta',
        ]),
        (o.exports['Long Upper Shadow - Bearish_study'] = [
          'Long Lower Shadow - De Baixa',
        ]),
        (o.exports['Marubozu Black - Bearish_study'] = [
          'Marubozu Black - De Baixa',
        ]),
        (o.exports['Marubozu White - Bullish_study'] = [
          'Marubozu White - De Alta',
        ]),
        (o.exports['Morning Doji Star - Bullish_study'] = [
          'Morning Doji Star - De Alta',
        ]),
        (o.exports['Morning Star - Bullish_study'] = [
          'Morning Star - De Alta',
        ]),
        (o.exports['On Neck - Bearish_study'] = ['On Neck - De Baixa']),
        (o.exports['Piercing - Bullish_study'] = ['Piercing - De Alta']),
        (o.exports['Rising Three Methods - Bullish_study'] = [
          'Rising Three Methods - De Alta',
        ]),
        (o.exports['Rising Window - Bullish_study'] = [
          'Rising Window - De Alta',
        ]),
        (o.exports['Shooting Star - Bearish_study'] = [
          'Shooting Star - De Baixa',
        ]),
        (o.exports['Three Black Crows - Bearish_study'] = [
          'Three Black Crows - De Baixa',
        ]),
        (o.exports['Three White Soldiers - Bullish_study'] = [
          'Three White Soldiers - De Alta',
        ]),
        (o.exports['Tri-Star - Bearish_study'] = ['Tri-Star - De Baixa']),
        (o.exports['Tri-Star - Bullish_study'] = ['Tri-Star - De Alta']),
        (o.exports['Tweezer Top - Bearish_study'] = ['Tweezer Top - De Baixa']),
        (o.exports['Upside Tasuki Gap - Bullish_study'] = [
          'Upside Tasuki Gap - De Alta',
        ]),
        (o.exports.SuperTrend_study = 'SuperTrend'),
        (o.exports['Average Price_study'] = ['Preço Médio']),
        (o.exports['Typical Price_study'] = ['Preço Típico']),
        (o.exports['Median Price_study'] = ['Preço Meniano']),
        (o.exports['Money Flow Index_study'] = ['Índice de Fluxo e Caixa']),
        (o.exports['Moving Average Double_study'] = ['Média Móvel Dupla']),
        (o.exports['Moving Average Triple_study'] = ['Média Móvel Tripla']),
        (o.exports['Moving Average Adaptive_study'] = [
          'Média Móvel Adaptativa',
        ]),
        (o.exports['Moving Average Hamming_study'] = ['Média Móvel Hamming']),
        (o.exports['Moving Average Modified_study'] = [
          'Média Móvel Modificada',
        ]),
        (o.exports['Moving Average Multiple_study'] = ['Média Móvel Múltipla']),
        (o.exports['Linear Regression Slope_study'] = [
          'Curva de Regressão Linear',
        ]),
        (o.exports['Standard Error_study'] = ['Erro Padrão']),
        (o.exports['Standard Error Bands_study'] = ['Bandas de Erro Padrão']),
        (o.exports['Correlation - Log_study'] = ['Correlação - Log']),
        (o.exports['Standard Deviation_study'] = ['Desvio Padrão']),
        (o.exports['Chaikin Volatility_study'] = ['Volatilidade Chaikin']),
        (o.exports['Volatility Close-to-Close_study'] = [
          'Volatilidade Aproximada',
        ]),
        (o.exports['Volatility Zero Trend Close-to-Close_study'] = [
          'Volatilidade de Tendência Zero Aproximada',
        ]),
        (o.exports['Volatility O-H-L-C_study'] = ['Volatilidade O-H-L-C']),
        (o.exports['Volatility Index_study'] = ['Índice de Volatilidade']),
        (o.exports['Trend Strength Index_study'] = [
          'Índice de Força da Tendência',
        ]),
        (o.exports['Majority Rule_study'] = ['Regra da Maioria']),
        (o.exports['Advance Decline Line_study'] = [
          'Linha de Avanço e Declínio',
        ]),
        (o.exports['Advance Decline Ratio_study'] = [
          'Razão de avanço e declínio',
        ]),
        (o.exports['Advance/Decline Ratio (Bars)_study'] = [
          'Avanço/Declínio (barras)',
        ]),
        (o.exports['BarUpDn Strategy_study'] = ['Estratégia BarUpDn']),
        (o.exports['Bollinger Bands Strategy directed_study'] = [
          'Estratégia direcionada por Bandas de Bollinger',
        ]),
        (o.exports['Bollinger Bands Strategy_study'] = [
          'Estratégia de Bandas de Bollinger',
        ]),
        (o.exports.ChannelBreakOutStrategy_study = ['EstratégiaQuebraDeCanal']),
        (o.exports.Compare_study = ['Comparar']),
        (o.exports['Conditional Expressions_study'] = [
          'Expressões condicionais',
        ]),
        (o.exports.ConnorsRSI_study = 'ConnorsRSI'),
        (o.exports['Consecutive Up/Down Strategy_study'] = [
          'Estratégia Consecutiva de Acima/Abaixo',
        ]),
        (o.exports['Cumulative Volume Index_study'] = [
          'Índice de volume cumulativo',
        ]),
        (o.exports['Divergence Indicator_study'] = [
          'Indicador de Divergência',
        ]),
        (o.exports['Greedy Strategy_study'] = ['Estratégia da Avareza']),
        (o.exports['InSide Bar Strategy_study'] = ['Estratégia InSide Bar']),
        (o.exports['Keltner Channel Strategy_study'] = [
          'Estratégia Canal de Keltner',
        ]),
        (o.exports['Linear Regression_study'] = ['Regressão Linear']),
        (o.exports['MACD Strategy_study'] = ['Estratégia MACD']),
        (o.exports['Momentum Strategy_study'] = ['Estratégia de Momentum']),
        (o.exports['Moon Phases_study'] = ['Fases da Lua']),
        (o.exports['Moving Average Convergence/Divergence_study'] = [
          'Convergência / Divergência Média Móvel',
        ]),
        (o.exports['MovingAvg Cross_study'] = ['Cruzamento de Média Móvel']),
        (o.exports['MovingAvg2Line Cross_study'] = [
          'Cruzamento de Duas Médias Móveis',
        ]),
        (o.exports['OutSide Bar Strategy_study'] = ['Estratégia OutSide Bar']),
        (o.exports.Overlay_study = ['Sobreposição']),
        (o.exports['Parabolic SAR Strategy_study'] = [
          'Estratégia SAR Parabólico',
        ]),
        (o.exports['Pivot Extension Strategy_study'] = [
          'Estratégia de Extensão do Pivô',
        ]),
        (o.exports['Pivot Points High Low_study'] = [
          'Pontos de Pivô de Alta e de Baixa',
        ]),
        (o.exports['Pivot Reversal Strategy_study'] = [
          'Estratégia de Reversão do Pivô',
        ]),
        (o.exports['Price Channel Strategy_study'] = [
          'Estratégia de Canal de Preço',
        ]),
        (o.exports['RSI Strategy_study'] = ['Estratégia do RSI (IFR)']),
        (o.exports['SMI Ergodic Indicator_study'] = [
          'Indicador de Índice de Momento Estocástico (SMI) Ergódico',
        ]),
        (o.exports['SMI Ergodic Oscillator_study'] = [
          'Oscilador de Índice de Momento Estocástico (SMI) Ergódico',
        ]),
        (o.exports['Stochastic Slow Strategy_study'] = [
          'Estratégia do Estocástico Lento',
        ]),
        (o.exports['Volatility Stop_study'] = ['Stop de Volatilidade']),
        (o.exports['Volty Expan Close Strategy_study'] = [
          'Estratégia Volty Expan Close',
        ]),
        (o.exports['Woodies CCI_study'] = ['CCI Woodies']);
    },
    40434: (o) => {
      o.exports = ['Perfil de Volume Range Fixo'];
    },
    32819: (o) => {
      o.exports = 'Vol';
    },
    66051: (o) => {
      o.exports = ['Menor'];
    },
    86054: (o) => {
      o.exports = ['Minuto'];
    },
    20936: (o) => {
      o.exports = ['Texto'];
    },
    98478: (o) => {
      o.exports = ['Não foi possível copiar'];
    },
    34004: (o) => {
      o.exports = ['Não foi possível cortar'];
    },
    96260: (o) => {
      o.exports = ['Não foi possível colar'];
    },
    94370: (o) => {
      o.exports = ['Contagem regressiva para fechamento'];
    },
    15168: (o) => {
      o.exports = 'Colombo';
    },
    36018: (o) => {
      o.exports = ['Colunas'];
    },
    19372: (o) => {
      o.exports = ['Comentário'];
    },
    20229: (o) => {
      o.exports = ['Comparar ou Adicionar Símbolo'];
    },
    46689: (o) => {
      o.exports = ['Confirmar entradas'];
    },
    43432: (o) => {
      o.exports = ['Copenhague'];
    },
    35216: (o) => {
      o.exports = ['Copiar'];
    },
    87898: (o) => {
      o.exports = ['Copiar o layout do gráfico'];
    },
    28851: (o) => {
      o.exports = ['Copiar preço'];
    },
    94099: (o) => {
      o.exports = 'Cairo';
    },
    64149: (o) => {
      o.exports = ['Comentário'];
    },
    63528: (o) => {
      o.exports = ['Velas'];
    },
    46837: (o) => {
      o.exports = 'Caracas';
    },
    49329: (o) => {
      o.exports = ['Variação'];
    },
    28089: (o) => {
      o.exports = ['Mudar Símbolo'];
    },
    99374: (o) => {
      o.exports = ['Alterar Intervalo'];
    },
    14412: (o) => {
      o.exports = ['Propriedades do Gráfico'];
    },
    26619: (o) => {
      o.exports = ['Gráfico por TradingView'];
    },
    12011: (o) => {
      o.exports = [
        'Imagem gráfica copiada para a área de transferência {emoji}',
      ];
    },
    59884: (o) => {
      o.exports = ['Ilhas Chatham'];
    },
    28244: (o) => {
      o.exports = 'Chicago';
    },
    49648: (o) => {
      o.exports = 'Chongqing';
    },
    90068: (o) => {
      o.exports = ['Círculo'];
    },
    32234: (o) => {
      o.exports = ['Clique para marcar um ponto'];
    },
    52977: (o) => {
      o.exports = ['Duplicar'];
    },
    31691: (o) => {
      o.exports = ['Fechamento'];
    },
    50493: (o) => {
      o.exports = ['Criar uma Ordem'];
    },
    52302: (o) => {
      o.exports = ['Criar Ordem Limite'];
    },
    29908: (o) => {
      o.exports = ['Cruz'];
    },
    60997: (o) => {
      o.exports = ['Linha Cruzada'];
    },
    81520: (o) => {
      o.exports = ['Moedas'];
    },
    98486: (o) => {
      o.exports = ['Intervalo atual e superior'];
    },
    73106: (o) => {
      o.exports = ['Intervalo atual e inferior'];
    },
    85964: (o) => {
      o.exports = ['Somente os intervalos atuais'];
    },
    17206: (o) => {
      o.exports = ['Curva'];
    },
    95176: (o) => {
      o.exports = ['Ciclo'];
    },
    87761: (o) => {
      o.exports = ['Linhas Cíclicas'];
    },
    27891: (o) => {
      o.exports = ['Padrão Cypher'];
    },
    56996: (o) => {
      o.exports = ['Já existe um layout com esse nome.'];
    },
    30192: (o) => {
      o.exports = [
        'Já existe um layout com esse nome. Você quer substituí-lo?',
      ];
    },
    32852: (o) => {
      o.exports = ['Padrão ABCD'];
    },
    88010: (o) => {
      o.exports = 'Amsterdam';
    },
    37422: (o) => {
      o.exports = ['Analisar configuração de negociação'];
    },
    66828: (o) => {
      o.exports = ['Nota Ancorada'];
    },
    94782: (o) => {
      o.exports = ['Texto Ancorado'];
    },
    61704: (o) => {
      o.exports = ['VWAP Ancorado'];
    },
    63597: (o) => {
      o.exports = ['Adicionar uma Linha Horizontal'];
    },
    45743: (o) => {
      o.exports = ['Adicionar Símbolo'];
    },
    8700: (o) => {
      o.exports = ['Adicionar alerta'];
    },
    64885: (o) => {
      o.exports = ['Adicionar Alerta em {drawing}'];
    },
    90830: (o) => {
      o.exports = ['Adicionar Alerta em {series}'];
    },
    45986: (o) => {
      o.exports = ['Adicionar Alerta Para {title}'];
    },
    3612: (o) => {
      o.exports = ['Adicionar métrica financeira para {instrumentName}'];
    },
    92206: (o) => {
      o.exports = ['Adicionar Indicador/Estratégia em {studyTitle}'];
    },
    34810: (o) => {
      o.exports = ['Adicionar nota de texto para {symbol}'];
    },
    75669: (o) => {
      o.exports = ['Adicionar esta métrica financeira para todos os layouts'];
    },
    64288: (o) => {
      o.exports = ['Adicionar este Indicador para todos os layouts'];
    },
    77920: (o) => {
      o.exports = ['Adicionar esta estratégia para todo o layout'];
    },
    34059: (o) => {
      o.exports = ['Adicionar este Símbolo para todos os layouts'];
    },
    17365: (o) => {
      o.exports = 'Adelaide';
    },
    9408: (o) => {
      o.exports = ['Sempre invisível'];
    },
    71997: (o) => {
      o.exports = ['Sempre visível'];
    },
    97305: (o) => {
      o.exports = ['Todos os indicadores e ferramentas gráficas'];
    },
    59192: (o) => {
      o.exports = ['Todos os intervalos'];
    },
    14452: (o) => {
      o.exports = 'Almaty';
    },
    5716: (o) => {
      o.exports = ['Aplicar Onda de Elliot'];
    },
    19263: (o) => {
      o.exports = ['Aplicar Onda de Elliott Maior'];
    },
    15818: (o) => {
      o.exports = ['Aplicar Onda de Elliott Menor'];
    },
    50352: (o) => {
      o.exports = ['Aplicar Onda de Elliot Intermediária'];
    },
    66631: (o) => {
      o.exports = ['Aplicar ponto de decisão manual'];
    },
    15682: (o) => {
      o.exports = ['Aplicar risco/retorno manual'];
    },
    15644: (o) => {
      o.exports = ['Aplicar onda WPT de baixa'];
    },
    5897: (o) => {
      o.exports = ['Aplicar onda WPT de alta'];
    },
    13345: (o) => {
      o.exports = ['Aplicar Padrão'];
    },
    95910: (o) => {
      o.exports = ['Aplique esses indicadores para todos os layouts'];
    },
    42762: (o) => {
      o.exports = ['Аbr'];
    },
    45104: (o) => {
      o.exports = ['Arco'];
    },
    42097: (o) => {
      o.exports = ['Área'];
    },
    96237: (o) => {
      o.exports = ['Seta'];
    },
    48732: (o) => {
      o.exports = ['Seta para Baixo'];
    },
    82473: (o) => {
      o.exports = ['Marcador Seta'];
    },
    8738: (o) => {
      o.exports = ['Seta Para Baixo'];
    },
    35062: (o) => {
      o.exports = ['Seta Para Esquerda'];
    },
    92163: (o) => {
      o.exports = ['Seta Para Direita'];
    },
    33196: (o) => {
      o.exports = ['Seta Para Cima'];
    },
    10650: (o) => {
      o.exports = ['Seta para Cima'];
    },
    59340: (o) => {
      o.exports = ['Ashkhabad'];
    },
    13468: (o) => {
      o.exports = ['No fechamento'];
    },
    21983: (o) => {
      o.exports = ['Аtenas'];
    },
    86951: (o) => {
      o.exports = 'Auto';
    },
    50834: (o) => {
      o.exports = ['Auto (Adapta os Dados à Tela)'];
    },
    38465: (o) => {
      o.exports = ['Аgo'];
    },
    8975: (o) => {
      o.exports = ['Legenda de preço médio de fechamento'];
    },
    87899: (o) => {
      o.exports = ['Linha do preço médio de fechamento'];
    },
    22554: (o) => {
      o.exports = ['Med'];
    },
    54173: (o) => {
      o.exports = ['Bogotá'];
    },
    53260: (o) => {
      o.exports = ['Barein'];
    },
    40664: (o) => {
      o.exports = ['Balão'];
    },
    32376: (o) => {
      o.exports = 'Bangkok';
    },
    19149: (o) => {
      o.exports = [
        'O Replay de Barras não está disponível para este tipo de gráfico. Você quer sair do Replay de Barras?',
      ];
    },
    16812: (o) => {
      o.exports = ['Barras'];
    },
    98838: (o) => {
      o.exports = ['Padrão de Barras'];
    },
    17712: (o) => {
      o.exports = ['Linha Base'];
    },
    54861: (o) => {
      o.exports = ['Belgrado'];
    },
    26825: (o) => {
      o.exports = ['Bеrlim'];
    },
    30251: (o) => {
      o.exports = ['Pincel'];
    },
    90204: (o) => {
      o.exports = ['Bruxelas'];
    },
    5262: (o) => {
      o.exports = 'Bratislava';
    },
    59901: (o) => {
      o.exports = ['Trazer Para Frente'];
    },
    26354: (o) => {
      o.exports = ['Trazer Para o Topo'];
    },
    11741: (o) => {
      o.exports = 'Brisbane';
    },
    37728: (o) => {
      o.exports = ['Bucareste'];
    },
    87143: (o) => {
      o.exports = 'Budapest';
    },
    82446: (o) => {
      o.exports = 'Buenos Aires';
    },
    82128: (o) => {
      o.exports = ['Pelo TradingView'];
    },
    75190: (o) => {
      o.exports = ['Ir para data'];
    },
    38342: (o) => {
      o.exports = ['Ir para {lineToolName}'];
    },
    75139: (o) => {
      o.exports = ['Entendi'];
    },
    81180: (o) => {
      o.exports = ['Caixa de Gann'];
    },
    68102: (o) => {
      o.exports = ['Leque de Gann'];
    },
    66321: (o) => {
      o.exports = ['Quadrado de Gann'];
    },
    87107: (o) => {
      o.exports = ['Quadrado de Gann Fixo'];
    },
    34805: (o) => {
      o.exports = 'Get more connections';
    },
    7914: (o) => {
      o.exports = ['Barras Fantasma'];
    },
    18367: (o) => {
      o.exports = ['Grand Supercycle'];
    },
    97065: (o) => {
      o.exports = ["Você quer realmente deletar o modelo de estudo '{name}'?"];
    },
    59368: (o) => {
      o.exports = ['Curva Dupla'];
    },
    35273: (o) => {
      o.exports = [
        'Clique duas vezes em qualquer borda para redefinir o layout da tela',
      ];
    },
    5828: (o) => {
      o.exports = ['Duplo Clique para finalizar a Sequência'];
    },
    63898: (o) => {
      o.exports = ['Duplo Clique para finalizar a Poligonal'];
    },
    42660: (o) => {
      o.exports = ['Onda de baixa 1 ou A'];
    },
    44788: (o) => {
      o.exports = ['Onda de baixa 2 ou B'];
    },
    71263: (o) => {
      o.exports = ['Onda de baixa 3'];
    },
    70573: (o) => {
      o.exports = ['Onda de baixa 4'];
    },
    59560: (o) => {
      o.exports = ['Onda de baixa 5'];
    },
    70437: (o) => {
      o.exports = ['Onda de baixa C'];
    },
    93345: (o) => {
      o.exports = ['Dados fornecidos por'];
    },
    76912: (o) => {
      o.exports = ['Data'];
    },
    60222: (o) => {
      o.exports = ['Range de Data'];
    },
    79859: (o) => {
      o.exports = ['Variação de Data e Preço'];
    },
    92203: (o) => {
      o.exports = ['Dez'];
    },
    69479: (o) => {
      o.exports = ['Grau'];
    },
    57701: (o) => {
      o.exports = 'Denver';
    },
    73720: (o) => {
      o.exports = ['Diamante'];
    },
    3556: (o) => {
      o.exports = ['Canal Separado'];
    },
    62764: (o) => {
      o.exports = ['Deslocamento'];
    },
    22903: (o) => {
      o.exports = ['Barra de ferramentas de desenho'];
    },
    21442: (o) => {
      o.exports = ['Desenhar Linha Horizontal sobre'];
    },
    22429: (o) => {
      o.exports = 'Dubai';
    },
    9497: (o) => {
      o.exports = 'Dublin';
    },
    85223: (o) => {
      o.exports = 'Emoji';
    },
    24435: (o) => {
      o.exports = ['Digite um novo nome de layout gráfico'];
    },
    93512: (o) => {
      o.exports = ['Editar alerta {title}'];
    },
    91215: (o) => {
      o.exports = ['Onda de Elliot Correção (ABC)'];
    },
    80983: (o) => {
      o.exports = ['Onda de Elliot Combo Dupla (WXY)'];
    },
    74118: (o) => {
      o.exports = ['Onda de Elliot Impulso (12345)'];
    },
    95840: (o) => {
      o.exports = ['Onda de Elliot Triangular (ABCDE)'];
    },
    66637: (o) => {
      o.exports = ['Onda de Elliot Combo Tripla (WXYXZ)'];
    },
    69418: (o) => {
      o.exports = ['Elipse'];
    },
    27558: (o) => {
      o.exports = ['Estender Linhas de Alerta'];
    },
    2578: (o) => {
      o.exports = ['Linha Estendida'];
    },
    77295: (o) => {
      o.exports = ['Bolsa'];
    },
    2899: (o) => {
      o.exports = ['Painel Existente Acima'];
    },
    53387: (o) => {
      o.exports = ['Painel Existente Abaixo'];
    },
    36972: (o) => {
      o.exports = ['Previsão'];
    },
    17994: (o) => {
      o.exports = ['Falha ao salvar a biblioteca'];
    },
    87375: (o) => {
      o.exports = ['Falha ao salvar o script'];
    },
    35050: (o) => {
      o.exports = ['Fev'];
    },
    82719: (o) => {
      o.exports = ['Canal de Fibonacci'];
    },
    64192: (o) => {
      o.exports = ['Círculos de Fibonacci'];
    },
    63835: (o) => {
      o.exports = ['Retração de Fibonacci'];
    },
    18072: (o) => {
      o.exports = ['Arcos de Resistência e Velocidade em Fibonacci'];
    },
    20877: (o) => {
      o.exports = ['Leque de Resistência e Velocidade em Fibonacci'];
    },
    76783: (o) => {
      o.exports = ['Espiral de Fibonacci'];
    },
    89037: (o) => {
      o.exports = ['Zona Temporal em Fibonacci'];
    },
    72489: (o) => {
      o.exports = ['Cunha de Fibonacci'];
    },
    21524: (o) => {
      o.exports = ['Bandeira'];
    },
    55678: (o) => {
      o.exports = ['Bandeira'];
    },
    29230: (o) => {
      o.exports = ['Topo/Fundo Plano'];
    },
    92754: (o) => {
      o.exports = ['Virado'];
    },
    42015: (o) => {
      o.exports = ['Fração inválida.'];
    },
    47542: (o) => {
      o.exports = [
        'Os estudos dos fundamentos não estão mais disponíveis no gráfico',
      ];
    },
    16245: (o) => {
      o.exports = ['Calcuta'];
    },
    3155: (o) => {
      o.exports = ['Catmandu'];
    },
    92901: (o) => {
      o.exports = 'Kagi';
    },
    2693: (o) => {
      o.exports = 'Karachi';
    },
    72374: (o) => {
      o.exports = 'Kuwait';
    },
    87338: (o) => {
      o.exports = 'Ho Chi Minh';
    },
    61582: (o) => {
      o.exports = ['Candles vazios'];
    },
    32918: (o) => {
      o.exports = 'Hong Kong';
    },
    61351: (o) => {
      o.exports = 'Honolulu';
    },
    60049: (o) => {
      o.exports = ['Linha Horizontal'];
    },
    76604: (o) => {
      o.exports = ['Raio Horizontal'];
    },
    42616: (o) => {
      o.exports = ['Cabeça e Ombros'];
    },
    40530: (o) => {
      o.exports = ['Heiken Ashi'];
    },
    99820: (o) => {
      o.exports = 'Helsinki';
    },
    31971: (o) => {
      o.exports = ['Ocultar'];
    },
    33911: (o) => {
      o.exports = ['Ocultar tudo'];
    },
    95551: (o) => {
      o.exports = ['Ocultar todas as ferramentas de desenho'];
    },
    44312: (o) => {
      o.exports = ['Esconder todos os desenhos e indicadores'];
    },
    67927: (o) => {
      o.exports = ['Ocultar todos os desenhos, indicadores, posições & ordens'];
    },
    86306: (o) => {
      o.exports = ['Esconder todos os indicadores'];
    },
    70803: (o) => {
      o.exports = ['Ocultar todas as posições & ordens'];
    },
    13277: (o) => {
      o.exports = ['Esconder desenhos'];
    },
    8251: (o) => {
      o.exports = ['Ocultar eventos no gráfico'];
    },
    44177: (o) => {
      o.exports = ['Esconder indicadores'];
    },
    2441: (o) => {
      o.exports = ['Ocultar marcas nas barras'];
    },
    90540: (o) => {
      o.exports = ['Ocultar posições & ordens'];
    },
    30777: (o) => {
      o.exports = ['Мáx'];
    },
    31994: (o) => {
      o.exports = ['Máxima-Mínima'];
    },
    60259: (o) => {
      o.exports = ['Legenda de preços máximo e mínimo'];
    },
    21803: (o) => {
      o.exports = ['Linha de preços máximo e mínimo'];
    },
    31895: (o) => {
      o.exports = ['Destaques'];
    },
    69085: (o) => {
      o.exports = [
        'O Histograma é muito grande, por favor aumente a entrada "Tamanho da linha".',
      ];
    },
    8122: (o) => {
      o.exports = [
        'O Histograma é muito grande, por favor reduza a entrada "Tamanho da linha".',
      ];
    },
    22712: (o) => {
      o.exports = "I'm good thanks";
    },
    23450: (o) => {
      o.exports = ['Imagem'];
    },
    71778: (o) => {
      o.exports = ['Intermediária'];
    },
    14177: (o) => {
      o.exports = ['Símbolo inválido'];
    },
    32619: (o) => {
      o.exports = ['Símbolo Inválido'];
    },
    53239: (o) => {
      o.exports = ['Inverter Escala'];
    },
    20062: (o) => {
      o.exports = ['Indexada em 100'];
    },
    81584: (o) => {
      o.exports = ['Rótulos de valor dos indicadores'];
    },
    31485: (o) => {
      o.exports = ['Rótulo dos nomes do indicadores'];
    },
    27677: (o) => {
      o.exports = ['Linha com Informações'];
    },
    98767: (o) => {
      o.exports = ['Inserir indicador'];
    },
    9114: (o) => {
      o.exports = ['Interior'];
    },
    12354: (o) => {
      o.exports = ['Garfo Interno'];
    },
    26579: (o) => {
      o.exports = ['Ícone'];
    },
    37885: (o) => {
      o.exports = ['Istambul'];
    },
    87469: (o) => {
      o.exports = ['Joanesburgo'];
    },
    52707: (o) => {
      o.exports = ['Jacarta'];
    },
    95425: (o) => {
      o.exports = 'Jan';
    },
    42890: (o) => {
      o.exports = ['Jerusalém'];
    },
    6215: (o) => {
      o.exports = 'Jul';
    },
    15224: (o) => {
      o.exports = 'Jun';
    },
    36253: (o) => {
      o.exports = 'Juneau';
    },
    15241: (o) => {
      o.exports = ['Na Esquerda'];
    },
    29404: (o) => {
      o.exports = ['Na Direita'];
    },
    850: (o) => {
      o.exports = ['Ops!'];
    },
    675: (o) => {
      o.exports = ['Lista de Objetos'];
    },
    73546: (o) => {
      o.exports = ['Оut'];
    },
    39280: (o) => {
      o.exports = ['Abertura'];
    },
    25595: (o) => {
      o.exports = 'Original';
    },
    82906: (o) => {
      o.exports = 'Oslo';
    },
    8136: (o) => {
      o.exports = ['Мín'];
    },
    42284: (o) => {
      o.exports = ['Bloquear'];
    },
    1441: (o) => {
      o.exports = ['Bloquear/Desbloquear'];
    },
    82232: (o) => {
      o.exports = ['Bloquear linha de cursor vertical pelo tempo'];
    },
    18219: (o) => {
      o.exports = ['Fixar Razão Preço por Barra'];
    },
    12285: (o) => {
      o.exports = ['Logarítmica'];
    },
    50286: (o) => {
      o.exports = ['Londres'];
    },
    44604: (o) => {
      o.exports = ['Posição Comprada'];
    },
    87604: (o) => {
      o.exports = 'Los Angeles';
    },
    18528: (o) => {
      o.exports = ['Legenda para Baixo'];
    },
    13046: (o) => {
      o.exports = ['Legenda para Cima'];
    },
    94420: (o) => {
      o.exports = ['Legendas'];
    },
    89155: (o) => {
      o.exports = 'Lagos';
    },
    25846: (o) => {
      o.exports = 'Lima';
    },
    1277: (o) => {
      o.exports = ['Linha'];
    },
    63492: (o) => {
      o.exports = ['Quebra de linha'];
    },
    83182: (o) => {
      o.exports = ['Linhas'];
    },
    78104: (o) => {
      o.exports = [
        'Link para a imagem gráfica copiada para a área de transferência {emoji}',
      ];
    },
    50091: (o) => {
      o.exports = ['Lisboa'];
    },
    64352: (o) => {
      o.exports = ['Luxemburgo'];
    },
    11156: (o) => {
      o.exports = 'MTPredictor';
    },
    67861: (o) => {
      o.exports = [
        'Mova o ponto para posicionar a âncora e toque para colocar',
      ];
    },
    45828: (o) => {
      o.exports = ['Mover para'];
    },
    44302: (o) => {
      o.exports = ['Mover a Escala Para Esquerda'];
    },
    94338: (o) => {
      o.exports = ['Mover a Escala Para Direita'];
    },
    66276: (o) => {
      o.exports = ['Schiff modificado'];
    },
    18559: (o) => {
      o.exports = ['Garfo de Schiff Modificado'];
    },
    18665: (o) => {
      o.exports = ['Моscou'];
    },
    58038: (o) => {
      o.exports = ['Маdrid'];
    },
    34190: (o) => {
      o.exports = 'Malta';
    },
    90271: (o) => {
      o.exports = 'Manila';
    },
    51369: (o) => {
      o.exports = ['Маr'];
    },
    85095: (o) => {
      o.exports = ['Cidade do México'];
    },
    75633: (o) => {
      o.exports = ['Juntar Todas as Escalas Em Uma'];
    },
    95093: (o) => {
      o.exports = ['Misturado'];
    },
    10931: (o) => {
      o.exports = 'Micro';
    },
    58397: (o) => {
      o.exports = ['Milênio'];
    },
    85884: (o) => {
      o.exports = ['Minueto'];
    },
    9632: (o) => {
      o.exports = ['Minúsculo'];
    },
    63158: (o) => {
      o.exports = ['Refletido'];
    },
    42769: (o) => {
      o.exports = 'Muscat';
    },
    43088: (o) => {
      o.exports = 'N/A';
    },
    95222: (o) => {
      o.exports = ['Sem dados aqui'];
    },
    3485: (o) => {
      o.exports = ['Sem Escala (Tela Cheia)'];
    },
    8886: (o) => {
      o.exports = ['Não sincronizar'];
    },
    16971: (o) => {
      o.exports = ['Sem dados de volume'];
    },
    75549: (o) => {
      o.exports = ['Nota'];
    },
    71230: (o) => {
      o.exports = 'Nov';
    },
    99203: (o) => {
      o.exports = ['Ilha Norfolk'];
    },
    79023: (o) => {
      o.exports = ['Nairóbi'];
    },
    91203: (o) => {
      o.exports = ['Nova York'];
    },
    24143: (o) => {
      o.exports = ['Nova Zelândia'];
    },
    40887: (o) => {
      o.exports = ['Novo painel acima'];
    },
    96712: (o) => {
      o.exports = ['Novo painel abaixo'];
    },
    33566: (o) => {
      o.exports = 'Nicosia';
    },
    64968: (o) => {
      o.exports = ['Algo deu errado. Por favor, tente novamente mais tarde.'];
    },
    10520: (o) => {
      o.exports = ['Salvar novo layout de gráfico'];
    },
    9908: (o) => {
      o.exports = ['Salvar Como'];
    },
    68553: (o) => {
      o.exports = ['São Salvador'];
    },
    65412: (o) => {
      o.exports = 'Santiago';
    },
    13538: (o) => {
      o.exports = ['São Paulo'];
    },
    37207: (o) => {
      o.exports = ['Apenas o gráfico de escala de preços'];
    },
    51464: (o) => {
      o.exports = 'Schiff';
    },
    98114: (o) => {
      o.exports = ['Garfo de Schiff'];
    },
    1535: (o) => {
      o.exports = ['O script pode não ser atualizado se você sair da página.'];
    },
    89517: (o) => {
      o.exports = ['Configurações'];
    },
    43247: (o) => {
      o.exports = ['A segunda parte da fração não é válida.'];
    },
    19796: (o) => {
      o.exports = ['Enviar Para o Fundo'];
    },
    23221: (o) => {
      o.exports = ['Enviar Para Trás'];
    },
    5961: (o) => {
      o.exports = ['Seul'];
    },
    57902: (o) => {
      o.exports = ['Set'];
    },
    25866: (o) => {
      o.exports = ['Sessão'];
    },
    59827: (o) => {
      o.exports = ['Intervalos de Sessão'];
    },
    69240: (o) => {
      o.exports = ['Shangai'];
    },
    37819: (o) => {
      o.exports = ['Posição Vendida'];
    },
    81428: (o) => {
      o.exports = ['Visualizar'];
    },
    98116: (o) => {
      o.exports = ['Mostrar todos os desenhos'];
    },
    39046: (o) => {
      o.exports = ['Mostrar todos os desenhos e indicadores'];
    },
    38293: (o) => {
      o.exports = ['Mostrar todos os desenhos, indicadores, posições & ordens'];
    },
    49982: (o) => {
      o.exports = ['Mostrar todos os indicadores'];
    },
    48284: (o) => {
      o.exports = ['Mostrar Todas as Ideias'];
    },
    62632: (o) => {
      o.exports = ['Mostrar todas as posições & ordens'];
    },
    24620: (o) => {
      o.exports = ['Exibir mudança do contrato contínuo'];
    },
    84813: (o) => {
      o.exports = ['Mostrar vencimento do contrato'];
    },
    66263: (o) => {
      o.exports = ['Mostrar os dividendos'];
    },
    46771: (o) => {
      o.exports = ['Mostrar os ganhos'];
    },
    87933: (o) => {
      o.exports = ['Mostrar Ideias de Usuários Seguidos'];
    },
    30709: (o) => {
      o.exports = ['Mostrar últimas notícias'];
    },
    58669: (o) => {
      o.exports = ['Mostrar Minhas Ideias Apenas'];
    },
    30816: (o) => {
      o.exports = ['Mostrar splits'];
    },
    68161: (o) => {
      o.exports = ['Sinalizar'];
    },
    56683: (o) => {
      o.exports = ['Singapura'];
    },
    69502: (o) => {
      o.exports = ['Senóide'];
    },
    44904: (o) => {
      o.exports = ['Quadrado'];
    },
    70213: (o) => {
      o.exports = [
        'O limite de estudos excedeu: {number} estudos por layout. \nRemova alguns estudos.',
      ];
    },
    32733: (o) => {
      o.exports = ['Estilo'];
    },
    65323: (o) => {
      o.exports = ['Empilhar à esquerda'];
    },
    14113: (o) => {
      o.exports = ['Empilhar Na Direita'];
    },
    93161: (o) => {
      o.exports = ['Manter Em Modo Desenho'];
    },
    48767: (o) => {
      o.exports = ['Estocolmo'];
    },
    29662: (o) => {
      o.exports = 'Submicro';
    },
    9753: (o) => {
      o.exports = ['Sub-milênio'];
    },
    71722: (o) => {
      o.exports = ['Sub-minueto'];
    },
    91889: (o) => {
      o.exports = ['Superciclo'];
    },
    33820: (o) => {
      o.exports = ['Supermilênio'];
    },
    11020: (o) => {
      o.exports = ['Sidney'];
    },
    89659: (o) => {
      o.exports = ['Erro no Símbolo'];
    },
    90932: (o) => {
      o.exports = ['Nome do Símbolo do Indicador'];
    },
    65986: (o) => {
      o.exports = ['Informações do símbolo'];
    },
    52054: (o) => {
      o.exports = ['Legenda de último preço do símbolo'];
    },
    33606: (o) => {
      o.exports = ['Sincronizar globalmente'];
    },
    18008: (o) => {
      o.exports = ['Sincronizar no layout'];
    },
    99969: (o) => {
      o.exports = ['Ponto & Figura'];
    },
    53047: (o) => {
      o.exports = ['Linha Segmentada'];
    },
    34402: (o) => {
      o.exports = ['Sequência'];
    },
    70394: (o) => {
      o.exports = ['Canal Paralelo'];
    },
    95995: (o) => {
      o.exports = 'Paris';
    },
    29682: (o) => {
      o.exports = ['Colar'];
    },
    51102: (o) => {
      o.exports = ['Percentual'];
    },
    35590: (o) => {
      o.exports = 'Perth';
    },
    19093: (o) => {
      o.exports = 'Phoenix';
    },
    22293: (o) => {
      o.exports = ['Leque de Linhas'];
    },
    43852: (o) => {
      o.exports = ['Garfo'];
    },
    37680: (o) => {
      o.exports = ['Fixar na Nova Escala à Esquerda'];
    },
    43707: (o) => {
      o.exports = ['Fixar na Nova Escala à Direita'];
    },
    91130: (o) => {
      o.exports = ['Fixar na Escala à Esquerda'];
    },
    61201: (o) => {
      o.exports = ['Fixar na Escala à Esquerda (Oculto)'];
    },
    764: (o) => {
      o.exports = ['Fixar na escala à direita'];
    },
    20207: (o) => {
      o.exports = ['Fixar Na Escala à Direita (Oculto)'];
    },
    66156: (o) => {
      o.exports = ['Fixar na Escala (Agora à Esquerda)'];
    },
    54727: (o) => {
      o.exports = ['Fixar na Escala (Agora Sem Escala)'];
    },
    76598: (o) => {
      o.exports = ['Fixar na Escala (Agora à Direita)'];
    },
    39065: (o) => {
      o.exports = ['Fixar na Escala (Agora {label})'];
    },
    97324: (o) => {
      o.exports = ['Fixar na Escala {label}'];
    },
    56948: (o) => {
      o.exports = ['Fixar Na Escala {label} (Oculto)'];
    },
    32156: (o) => {
      o.exports = ['Fixado na Escala à Esquerda'];
    },
    8128: (o) => {
      o.exports = ['Fixado na Escala à Esquerda (Oculto)'];
    },
    3822: (o) => {
      o.exports = ['Fixado na Escala à Direita'];
    },
    44538: (o) => {
      o.exports = ['Fixar Na Escala à Direita (Oculto)'];
    },
    65810: (o) => {
      o.exports = ['Fixado à Escala {label}'];
    },
    14125: (o) => {
      o.exports = ['Escala Fixada {label} (Oculto)'];
    },
    97378: (o) => {
      o.exports = ['Botão + na escala'];
    },
    46669: (o) => {
      o.exports = [
        'Por favor, conceda uma permissão de escrita em seu navegador ou pressione {keystroke}.',
      ];
    },
    35963: (o) => {
      o.exports = [
        'Pressione e segure {key} enquanto faz zoom para manter a posição do gráfico',
      ];
    },
    95921: (o) => {
      o.exports = ['Legenda de Preços'];
    },
    28625: (o) => {
      o.exports = ['Nota de Preço'];
    },
    2032: (o) => {
      o.exports = ['Intervalo de Preços'];
    },
    32061: (o) => {
      o.exports = ['O formato do preço não é válido.'];
    },
    91492: (o) => {
      o.exports = ['Linha de preços'];
    },
    48404: (o) => {
      o.exports = ['Primária'];
    },
    87086: (o) => {
      o.exports = ['Projeção'];
    },
    10160: (o) => {
      o.exports = ['Publicado em {customer}, {date}'];
    },
    19056: (o) => {
      o.exports = ['Catar'];
    },
    9998: (o) => {
      o.exports = ['Retângulo Giravel'];
    },
    74214: (o) => {
      o.exports = ['Roma'];
    },
    50470: (o) => {
      o.exports = ['Raio'];
    },
    90357: (o) => {
      o.exports = 'Range';
    },
    26833: (o) => {
      o.exports = 'Reykjavik';
    },
    328: (o) => {
      o.exports = ['Retângulo'];
    },
    41615: (o) => {
      o.exports = ['Refazer'];
    },
    35001: (o) => {
      o.exports = ['Tendência de Regressão'];
    },
    34596: (o) => {
      o.exports = ['Remover'];
    },
    1434: (o) => {
      o.exports = ['Remover Desenhos'];
    },
    13951: (o) => {
      o.exports = ['Remover Indicadores'];
    },
    4142: (o) => {
      o.exports = ['Renomear gráfico'];
    },
    20801: (o) => {
      o.exports = 'Renko';
    },
    34301: (o) => {
      o.exports = ['Redefinir vista do gráfico'];
    },
    17258: (o) => {
      o.exports = ['Reiniciar Escala de Preços'];
    },
    25333: (o) => {
      o.exports = ['Reiniciar Escala de Tempo.'];
    },
    52588: (o) => {
      o.exports = 'Riyadh';
    },
    5871: (o) => {
      o.exports = 'Riga';
    },
    33603: (o) => {
      o.exports = ['Aviso'];
    },
    48474: (o) => {
      o.exports = ['Varsóvia'];
    },
    20466: (o) => {
      o.exports = 'Tokelau';
    },
    94284: (o) => {
      o.exports = ['Тóquio'];
    },
    83836: (o) => {
      o.exports = ['Тоronto'];
    },
    38788: (o) => {
      o.exports = ['Тaipé'];
    },
    39108: (o) => {
      o.exports = 'Tallinn';
    },
    37229: (o) => {
      o.exports = ['Texto'];
    },
    16267: (o) => {
      o.exports = ['Teerã'];
    },
    19611: (o) => {
      o.exports = ['Modelo'];
    },
    29198: (o) => {
      o.exports = [
        'O fornecedor de dados não disponibiliza dados de volume para este símbolo.',
      ];
    },
    8162: (o) => {
      o.exports = [
        'A pré-visualização da publicação não pôde ser carregada. Desative as extensões do seu navegador e tente novamente.',
      ];
    },
    65943: (o) => {
      o.exports = ['Este indicador não pode ser aplicado a outro indicador'];
    },
    74986: (o) => {
      o.exports = [
        'Este script é apenas por convite. Para solicitar acesso, favor entrar em contato com seu autor.',
      ];
    },
    98538: (o) => {
      o.exports = ['Padrão dos Três Avanços'];
    },
    30973: (o) => {
      o.exports = 'Ticks';
    },
    31976: (o) => {
      o.exports = ['Hora'];
    },
    64375: (o) => {
      o.exports = ['Fuso Horário'];
    },
    95005: (o) => {
      o.exports = ['Ciclos Temporais'];
    },
    87085: (o) => {
      o.exports = 'Trade';
    },
    94770: (o) => {
      o.exports = ['Ângulo de Tendência'];
    },
    23104: (o) => {
      o.exports = ['Linha de Tendência'];
    },
    15501: (o) => {
      o.exports = ['Extensão de Fibonacci Baseado em Tendências'];
    },
    31196: (o) => {
      o.exports = ['Tempo de Fibonacci Baseado em Tendências'];
    },
    29245: (o) => {
      o.exports = ['Triângulo'];
    },
    83356: (o) => {
      o.exports = ['Triângulo de Baixa'];
    },
    12390: (o) => {
      o.exports = ['Padrão Triangular'];
    },
    28340: (o) => {
      o.exports = ['Triângulo de Alta'];
    },
    93855: (o) => {
      o.exports = ['Tunísia'];
    },
    50406: (o) => {
      o.exports = ['Horário Universal (UTC)'];
    },
    38587: (o) => {
      o.exports = 'Understood';
    },
    81320: (o) => {
      o.exports = ['Desfazer'];
    },
    25933: (o) => {
      o.exports = ['Unidades'];
    },
    15101: (o) => {
      o.exports = ['Liberar'];
    },
    34150: (o) => {
      o.exports = ['Onda de alta 4'];
    },
    83927: (o) => {
      o.exports = ['Onda de alta 5'];
    },
    58976: (o) => {
      o.exports = ['Onda de alta 1 ou A'];
    },
    11661: (o) => {
      o.exports = ['Onda de alta 2 ou B'];
    },
    53958: (o) => {
      o.exports = ['Onda de alta 3'];
    },
    66560: (o) => {
      o.exports = ['Onda de alta C'];
    },
    18426: (o) => {
      o.exports = ['Perfil de Volume de Range Fixo'];
    },
    61022: (o) => {
      o.exports = [
        'Indicador de Perfil de Volume disponível apenas em nossos planos pagos.',
      ];
    },
    15771: (o) => {
      o.exports = 'Vancouver';
    },
    56211: (o) => {
      o.exports = ['Linha Vertical'];
    },
    75354: (o) => {
      o.exports = 'Vilnius';
    },
    21852: (o) => {
      o.exports = ['Visibilidade'];
    },
    27557: (o) => {
      o.exports = ['Visibilidade dos intervalos'];
    },
    89960: (o) => {
      o.exports = ['Visível com o mouse por cima'];
    },
    22198: (o) => {
      o.exports = ['Ordem Visual'];
    },
    7050: (o) => {
      o.exports = ['X Cruz'];
    },
    66527: (o) => {
      o.exports = ['Padrão XABCD'];
    },
    17126: (o) => {
      o.exports = ['Você não pode ver esse período de tempo nessa resolução'];
    },
    69293: (o) => {
      o.exports = 'Yangon';
    },
    84301: (o) => {
      o.exports = ['Zurique'];
    },
    76020: (o) => {
      o.exports = ['alterar o grau de Elliott'];
    },
    83935: (o) => {
      o.exports = ['alterar as legendas sem sobreposição'];
    },
    39402: (o) => {
      o.exports = [
        'mudar visibilidade da legenda de preço médio de fechamento',
      ];
    },
    98866: (o) => {
      o.exports = [
        'alterar visibilidade da linha de preço médio de fechamento',
      ];
    },
    5100: (o) => {
      o.exports = ['alterar visibilidade da legenda de compra e venda'];
    },
    32311: (o) => {
      o.exports = ['alterar visibilidade da linha de compra e venda'];
    },
    22641: (o) => {
      o.exports = ['alterar moeda'];
    },
    30501: (o) => {
      o.exports = ['alterar o layout do gráfico para {title}'];
    },
    7017: (o) => {
      o.exports = ['alterar visibilidade da mudança do contrato contínuo'];
    },
    58108: (o) => {
      o.exports = [
        'mudar a visibilidade da contagem regressiva para próxima barra',
      ];
    },
    7151: (o) => {
      o.exports = ['alterar range de datas'];
    },
    84944: (o) => {
      o.exports = ['alterar a visibilidade dos dividendos'];
    },
    79574: (o) => {
      o.exports = ['alterar a visibilidade dos eventos no gráfico'];
    },
    88217: (o) => {
      o.exports = ['alterar a visibilidade dos resultados'];
    },
    28288: (o) => {
      o.exports = [
        'alterar a visibilidade do vencimento dos contratos futuros',
      ];
    },
    66805: (o) => {
      o.exports = [
        'alterar a visibilidade das legendas de preço máximo e mínimo',
      ];
    },
    92556: (o) => {
      o.exports = [
        'alterar a visibilidade das linhas de preço máximo e mínimo',
      ];
    },
    87027: (o) => {
      o.exports = ['alterar a visibilidade do rótulo do nome do indicador'];
    },
    14922: (o) => {
      o.exports = [
        'alterar a visibilidade dos rótulos dos valores de indicadores',
      ];
    },
    77578: (o) => {
      o.exports = ['alterar a visibilidade das últimas notícias'];
    },
    87510: (o) => {
      o.exports = ['alterar a altura do painel'];
    },
    50190: (o) => {
      o.exports = ['mudar visibilidade do botão + na escala'];
    },
    49889: (o) => {
      o.exports = [
        'alterar a visibilidade da legenda de preços pré/pós-mercado',
      ];
    },
    16750: (o) => {
      o.exports = ['alterar a visibilidade da linha de preços pré/pós-mercado'];
    },
    59883: (o) => {
      o.exports = [
        'alterar visibilidade do valor de fechamento anterior da linha',
      ];
    },
    67761: (o) => {
      o.exports = ['Mudar Linha de Preço'];
    },
    69510: (o) => {
      o.exports = ['alterar razão preço por barra'];
    },
    32303: (o) => {
      o.exports = ['Mudar Resolução'];
    },
    526: (o) => {
      o.exports = ['Mudar símbolo'];
    },
    9402: (o) => {
      o.exports = ['alterar a visibilidade das legendas dos símbolos'];
    },
    53150: (o) => {
      o.exports = ['alterar a visibilidade do último valor do símbolo'];
    },
    12707: (o) => {
      o.exports = [
        'alterar visibilidade do valor de fechamento anterior do símbolo',
      ];
    },
    65303: (o) => {
      o.exports = ['alterar sessão'];
    },
    15403: (o) => {
      o.exports = ['alterar a visibilidade dos intervalos de sessão'];
    },
    53438: (o) => {
      o.exports = ['alterar o estilo da série'];
    },
    74488: (o) => {
      o.exports = ['alterar a visibilidade dos desdobramentos'];
    },
    20505: (o) => {
      o.exports = ['alterar o fuso horário'];
    },
    39028: (o) => {
      o.exports = ['alterar unidade'];
    },
    21511: (o) => {
      o.exports = ['Mudar Visibilidade'];
    },
    16698: (o) => {
      o.exports = ['alterar visibilidade do intervalo atual'];
    },
    78422: (o) => {
      o.exports = ['alterar visibilidade no intervalo atual e abaixo'];
    },
    49529: (o) => {
      o.exports = ['alterar visibilidade do intervalo atual e abaixo'];
    },
    66927: (o) => {
      o.exports = ['alterar visibilidade de todos os intervalos'];
    },
    74428: (o) => {
      o.exports = ['alterar estilo de {title}'];
    },
    72032: (o) => {
      o.exports = ['alterar ponto {pointIndex}'];
    },
    65911: (o) => {
      o.exports = ['gráficos por TradingView'];
    },
    5179: (o) => {
      o.exports = ['Clonar ferramentas de linha'];
    },
    3195: (o) => {
      o.exports = ['Criar grupo de ferramentas de linha'];
    },
    92659: (o) => {
      o.exports = ['Criar grupo de ferramentas de linha a partir da seleção'];
    },
    81791: (o) => {
      o.exports = ['criar {tool}'];
    },
    63649: (o) => {
      o.exports = ['recortar fontes'];
    },
    78755: (o) => {
      o.exports = ['recortar {title}'];
    },
    99113: (o) => {
      o.exports = ['Adicione a ferramenta de linha {lineTool} ao grupo {name}'];
    },
    40242: (o) => {
      o.exports = ['adicionar ferramenta(s) de linha para agrupar {group}'];
    },
    22856: (o) => {
      o.exports = ['adicionar esta métrica financeira para todos os layouts'];
    },
    82388: (o) => {
      o.exports = ['adicionar este indicador a todos os layouts'];
    },
    94292: (o) => {
      o.exports = ['adicionar esta estratégia para todos os layouts'];
    },
    27982: (o) => {
      o.exports = ['adicionar este Símbolo para todos os layouts'];
    },
    66568: (o) => {
      o.exports = ['aplicar o tema do gráfico'];
    },
    64034: (o) => {
      o.exports = ['aplicar todas as propriedades do gráfico'];
    },
    49037: (o) => {
      o.exports = ['Aplicar Modelo de Desenho'];
    },
    96996: (o) => {
      o.exports = ['aplicar os padrões de fábrica às fontes selecionadas'];
    },
    44547: (o) => {
      o.exports = ['aplicar indicadores no layout inteiro'];
    },
    26065: (o) => {
      o.exports = ['Aplicar o modelo de estudo {template}'];
    },
    58570: (o) => {
      o.exports = ['aplicar o tema das barras de ferramentas'];
    },
    27195: (o) => {
      o.exports = ['trazer o grupo {title} para frente'];
    },
    78246: (o) => {
      o.exports = ['trazer {title} para frente'];
    },
    56763: (o) => {
      o.exports = ['Traga {title} para frente'];
    },
    5607: (o) => {
      o.exports = ['do TradingView'];
    },
    90621: (o) => {
      o.exports = ['range de datas bloqueado'];
    },
    12962: (o) => {
      o.exports = ['apagar a linha de nível'];
    },
    63391: (o) => {
      o.exports = ['Excluir ferramentas de linha do grupo {group}'];
    },
    59942: (o) => {
      o.exports = ['inverter padrão de barras'];
    },
    70301: (o) => {
      o.exports = ['ocultar {title}'];
    },
    91842: (o) => {
      o.exports = ['Ocultar legenda das linhas de alerta'];
    },
    54781: (o) => {
      o.exports = ['Ocultar todas as ferramentas de desenho'];
    },
    44974: (o) => {
      o.exports = ['Ocultar marcas nas barras'];
    },
    28916: (o) => {
      o.exports = ['bloquear intervalo'];
    },
    94245: (o) => {
      o.exports = ['Inverter Escala'];
    },
    90743: (o) => {
      o.exports = ['inserir {title}'];
    },
    53146: (o) => {
      o.exports = ['inserir {title} depois de {targetTitle}'];
    },
    74055: (o) => {
      o.exports = ['inserir {title} depois de {target}'];
    },
    11231: (o) => {
      o.exports = ['inserir {title} antes de {target}'];
    },
    67176: (o) => {
      o.exports = ['inserir {title} antes de {targetTitle}'];
    },
    54597: (o) => {
      o.exports = ['carregar template de desenho padrão'];
    },
    30295: (o) => {
      o.exports = ['carregando...'];
    },
    50193: (o) => {
      o.exports = ['Travar {title}'];
    },
    4963: (o) => {
      o.exports = ['bloquear grupo {group}'];
    },
    68163: (o) => {
      o.exports = ['objetos bloqueados'];
    },
    47107: (o) => {
      o.exports = ['mover'];
    },
    11303: (o) => {
      o.exports = ['mover {title} para a nova escala à esquerda'];
    },
    45544: (o) => {
      o.exports = ['mudar {title} para uma nova escala à direita'];
    },
    81898: (o) => {
      o.exports = ['Mover Todas as Escalas Para Esquerda'];
    },
    22863: (o) => {
      o.exports = ['mover todas as escalas para direita'];
    },
    45356: (o) => {
      o.exports = ['Mover Desenho(s)'];
    },
    15086: (o) => {
      o.exports = ['deslocar para esquerda'];
    },
    61711: (o) => {
      o.exports = ['deslocar para direita'];
    },
    4184: (o) => {
      o.exports = ['Mover escala'];
    },
    74642: (o) => {
      o.exports = ['Fazer {title} Sem Escala (Tela Cheia)'];
    },
    45223: (o) => {
      o.exports = ['Tornar o grupo {group} invisível'];
    },
    87927: (o) => {
      o.exports = ['Tornar o grupo {group} visível'];
    },
    62153: (o) => {
      o.exports = ['mesclar para baixo'];
    },
    70746: (o) => {
      o.exports = ['mesclar ao painel'];
    },
    66143: (o) => {
      o.exports = ['mesclar para cima'];
    },
    81870: (o) => {
      o.exports = ['espelhar padrão de barras'];
    },
    16542: (o) => {
      o.exports = 'n/a';
    },
    47222: (o) => {
      o.exports = ['escala de preço'];
    },
    99042: (o) => {
      o.exports = ['Apenas o gráfico de escala de preços'];
    },
    35962: (o) => {
      o.exports = ['escala de tempo'];
    },
    68193: (o) => {
      o.exports = ['rolar'];
    },
    70009: (o) => {
      o.exports = ['tempo de rolagem'];
    },
    69485: (o) => {
      o.exports = [
        'definir a estratégia de seleção da escala de preços para {title}',
      ];
    },
    16259: (o) => {
      o.exports = ['Envie {title} para trás'];
    },
    66781: (o) => {
      o.exports = ['enviar {title} para trás'];
    },
    4998: (o) => {
      o.exports = ['enviar o grupo {title} para trás'];
    },
    64704: (o) => {
      o.exports = ['ferramentas de linha compartilhada globalmente'];
    },
    77554: (o) => {
      o.exports = ['ferramentas de linha compartilhada em layout'];
    },
    16237: (o) => {
      o.exports = ['mostrar legenda das linhas de alerta'];
    },
    13622: (o) => {
      o.exports = ['mostrar todas as ideias'];
    },
    26267: (o) => {
      o.exports = ['exibir as ideias dos usuários seguidos'];
    },
    40061: (o) => {
      o.exports = ['exibir apenas minhas ideias'];
    },
    52010: (o) => {
      o.exports = ['permanecer no modo desenho'];
    },
    98784: (o) => {
      o.exports = ['parar de sincronizar desenhos'];
    },
    57011: (o) => {
      o.exports = ['parar de sincronizar a(s) ferramenta(s) de linha(s)'];
    },
    92831: (o) => {
      o.exports = ['bloquear simbolo'];
    },
    60635: (o) => {
      o.exports = ['tempo de sincronização'];
    },
    99769: (o) => {
      o.exports = ['desenvolvido por'];
    },
    68111: (o) => {
      o.exports = ['patrocinado por TradingView'];
    },
    96916: (o) => {
      o.exports = ['colar desenhos'];
    },
    80611: (o) => {
      o.exports = ['colar indicadores'];
    },
    41601: (o) => {
      o.exports = ['colar {title}'];
    },
    84018: (o) => {
      o.exports = ['fixar na escala à esquerda'];
    },
    22615: (o) => {
      o.exports = ['Fixar na Escala à Direita'];
    },
    56015: (o) => {
      o.exports = ['fixar na escala {label}'];
    },
    33348: (o) => {
      o.exports = ['reorganizar os painéis'];
    },
    15516: (o) => {
      o.exports = ['Remova todos os estudos'];
    },
    80171: (o) => {
      o.exports = ['Remova todos os estudos e as ferramentas de desenho'];
    },
    59211: (o) => {
      o.exports = ['remover as ferramentas de linha vazia desmarcadas'];
    },
    44656: (o) => {
      o.exports = ['Remover Desenhos'];
    },
    70653: (o) => {
      o.exports = ['remover os grupos de desenhos'];
    },
    66414: (o) => {
      o.exports = ['remover fontes de dados da linha'];
    },
    47637: (o) => {
      o.exports = ['remover painel'];
    },
    39859: (o) => {
      o.exports = ['remover {title}'];
    },
    78811: (o) => {
      o.exports = ['remover as ferramentas de linha do grupo {name}'];
    },
    16338: (o) => {
      o.exports = ['Renomear o grupo {group} para {newName}'];
    },
    30910: (o) => {
      o.exports = ['restaurar o tamanho do layout'];
    },
    21948: (o) => {
      o.exports = ['reiniciar as escalas'];
    },
    55064: (o) => {
      o.exports = ['Reiniciar Escala de Tempo.'];
    },
    13034: (o) => {
      o.exports = ['redimensionar layout'];
    },
    9608: (o) => {
      o.exports = ['restaurar padrões'];
    },
    63060: (o) => {
      o.exports = ['alternar para escala automática'];
    },
    98860: (o) => {
      o.exports = ['habilitar escala indexada a 100'];
    },
    21203: (o) => {
      o.exports = ['bloquear escala'];
    },
    60166: (o) => {
      o.exports = ['alternar para escala logarítmica'];
    },
    68642: (o) => {
      o.exports = ['Alterar escala percentual'];
    },
    33714: (o) => {
      o.exports = ['habilitar escala regular'];
    },
    47122: (o) => {
      o.exports = ['monitorar tempo'];
    },
    28068: (o) => {
      o.exports = ['desligar o compartilhamento de ferramentas de linha'];
    },
    66824: (o) => {
      o.exports = ['objetos desbloqueado'];
    },
    51114: (o) => {
      o.exports = ['Destravar o grupo {group}'];
    },
    92421: (o) => {
      o.exports = ['desbloquear {title}'];
    },
    20057: (o) => {
      o.exports = ['desfazer mesclagem para o novo painel inferior'];
    },
    52540: (o) => {
      o.exports = ['desfazer mesclagem para cima'];
    },
    86949: (o) => {
      o.exports = ['desfazer mesclagem para baixo'];
    },
    50728: (o) => {
      o.exports = ['Atualizar {title} Script'];
    },
    33355: (o) => {
      o.exports = ['barras: {count}'];
    },
    88841: (o) => {
      o.exports = ['Finanças {symbol} por TradingView'];
    },
    38641: (o) => {
      o.exports = ['{userName} publicou em {customer}, {date}'];
    },
    59833: (o) => {
      o.exports = 'zoom';
    },
    19813: (o) => {
      o.exports = ['aumentar zoom'];
    },
    9645: (o) => {
      o.exports = ['diminuir zoom'];
    },
    30572: (o) => {
      o.exports = ['dia', 'dias'];
    },
    52254: (o) => {
      o.exports = ['hora', 'horas'];
    },
    99062: (o) => {
      o.exports = 'month';
    },
    69143: (o) => {
      o.exports = ['minuto', 'minutos'];
    },
    71787: (o) => {
      o.exports = ['segundo', 'segundos'];
    },
    82797: (o) => {
      o.exports = 'range';
    },
    47966: (o) => {
      o.exports = ['semana', 'semanas'];
    },
    99136: (o) => {
      o.exports = 'tick';
    },
    18562: (o) => {
      (o.exports = Object.create(null)),
        (o.exports['#AAPL-symbol-description'] = 'Apple Inc'),
        (o.exports['#AUDCAD-symbol-description'] = [
          'Dólar Australiano/Dólar Canadense',
        ]),
        (o.exports['#AUDCHF-symbol-description'] = [
          'Dólar Australiano/Franco Suíço',
        ]),
        (o.exports['#AUDJPY-symbol-description'] = [
          'Dólar Australiano/Iene Japonês',
        ]),
        (o.exports['#AUDNZD-symbol-description'] = [
          'Dólar Australiano/Dólar Neo-Zelandês',
        ]),
        (o.exports['#AUDRUB-symbol-description'] = [
          'DÓLAR AUSTRALIANO/RUBLO RUSSO',
        ]),
        (o.exports['#AUDUSD-symbol-description'] = [
          'Dólar Australiano/Dólar Americano',
        ]),
        (o.exports['#BRLJPY-symbol-description'] = [
          'Real Brasileiro/Iene Japonês',
        ]),
        (o.exports['#BTCCAD-symbol-description'] = [
          'Bitcoin / Dólar Canadense',
        ]),
        (o.exports['#BTCCNY-symbol-description'] = ['Bitcoin / Yuan Chinês']),
        (o.exports['#BTCEUR-symbol-description'] = 'Bitcoin / Euro'),
        (o.exports['#BTCKRW-symbol-description'] = ['Bitcoin/Won Sul-Coreano']),
        (o.exports['#BTCRUR-symbol-description'] = ['Bitcoin/Rublo Russo']),
        (o.exports['#BTCUSD-symbol-description'] = ['Bitcoin/Dólar Americano']),
        (o.exports['#BVSP-symbol-description'] = ['Índice Bovespa']),
        (o.exports['#CADJPY-symbol-description'] = [
          'Dólar Canadense/Iene Japonês',
        ]),
        (o.exports['#CB1!-symbol-description'] = ['Petróleo Bruto Brent']),
        (o.exports['#CHFJPY-symbol-description'] = [
          'Franco Suíço/Iene Japonês',
        ]),
        (o.exports['#COPPER-symbol-description'] = ['CFDs de cobre']),
        (o.exports['#ES1-symbol-description'] = ['S&P 500 E-Mini Futuros']),
        (o.exports['#ESP35-symbol-description'] = ['Índice IBEX 35']),
        (o.exports['#EUBUND-symbol-description'] = ['Eurobond']),
        (o.exports['#EURAUD-symbol-description'] = [
          'Euro / Dólar Australiano',
        ]),
        (o.exports['#EURBRL-symbol-description'] = ['Euro/Real Brasileiro']),
        (o.exports['#EURCAD-symbol-description'] = ['Euro / Dólar Canadense']),
        (o.exports['#EURCHF-symbol-description'] = ['Euro / Franco Suíço']),
        (o.exports['#EURGBP-symbol-description'] = ['Euro / Libra Esterlina']),
        (o.exports['#EURJPY-symbol-description'] = ['Euro / Iene Japonês']),
        (o.exports['#EURNZD-symbol-description'] = [
          'Euro / Dólar Neo-zelandês',
        ]),
        (o.exports['#EURRUB-symbol-description'] = ['EURO/RUBLO RUSSO']),
        (o.exports['#EURRUB_TOM-symbol-description'] = ['EUR/RUB TOM']),
        (o.exports['#EURSEK-symbol-description'] = ['Euro FX/Coroa Sueca']),
        (o.exports['#EURTRY-symbol-description'] = ['Euro / Nova Lira Turca']),
        (o.exports['#EURUSD-symbol-description'] = ['Euro / Dólar Americano']),
        (o.exports['#EUSTX50-symbol-description'] = ['Índice Euro Stoxx 50']),
        (o.exports['#FRA40-symbol-description'] = ['Índice CAC 40']),
        (o.exports['#GB10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Britânico',
        ]),
        (o.exports['#GBPAUD-symbol-description'] = [
          'Libra Esterlina/Dólar Australiano',
        ]),
        (o.exports['#GBPCAD-symbol-description'] = [
          'Libra Esterlina/Dólar Canadense',
        ]),
        (o.exports['#GBPCHF-symbol-description'] = [
          'Libra Esterlina/Franco Suíço',
        ]),
        (o.exports['#GBPEUR-symbol-description'] = ['Libra Esterlina / Euro']),
        (o.exports['#GBPJPY-symbol-description'] = [
          'Libra Esterlina/Iene Japonês',
        ]),
        (o.exports['#GBPNZD-symbol-description'] = [
          'Libra Esterlina/Dólar Neo-Zelandês',
        ]),
        (o.exports['#GBPRUB-symbol-description'] = [
          'Libra Esterlina/Rublo Russo',
        ]),
        (o.exports['#GBPUSD-symbol-description'] = [
          'Libra Esterlina/Dólar Americano',
        ]),
        (o.exports['#GER30-symbol-description'] = [
          'Índice DAX das ações alemãs negociadas em bolsa',
        ]),
        (o.exports['#GOOGL-symbol-description'] = ['Google Inc. classe A']),
        (o.exports['#ITA40-symbol-description'] = ['Índice FTSE MIB']),
        (o.exports['#JPN225-symbol-description'] = ['Índice Nikkei 225']),
        (o.exports['#JPYKRW-symbol-description'] = ['IENE/WON']),
        (o.exports['#JPYRUB-symbol-description'] = ['IENE/RUBLO RUSSO']),
        (o.exports['#KA1-symbol-description'] = ['Futuros #11 de Açúcar']),
        (o.exports['#KG1-symbol-description'] = ['Futuros de Algodão']),
        (o.exports['#KT1-symbol-description'] = ['Key Tronic Corp.']),
        (o.exports['#LKOH-symbol-description'] = 'LUKOIL'),
        (o.exports['#LTCBTC-symbol-description'] = ['Litecoin/Bitcoin']),
        (o.exports['#MGNT-symbol-description'] = 'Magnit'),
        (o.exports['#MICEX-symbol-description'] = ['ÍNDICE MICEX']),
        (o.exports['#MNOD_ME.EQRP-symbol-description'] =
          'ADR GMK NORILSKIYNIKEL ORD SHS [REPO]'),
        (o.exports['#MSFT-symbol-description'] = 'Microsoft Corp.'),
        (o.exports['#NAS100-symbol-description'] = 'US 100 Cash CFD'),
        (o.exports['#NGAS-symbol-description'] = ['Gás natural (Henry Hub)']),
        (o.exports['#NKY-symbol-description'] = ['Índice Nikkei 225']),
        (o.exports['#NZDJPY-symbol-description'] = [
          'Dólar Neo-Zelandês/Iene Japonês',
        ]),
        (o.exports['#NZDUSD-symbol-description'] = [
          'Dólar Neo-Zelandês/Dólar Americano',
        ]),
        (o.exports['#RB1-symbol-description'] = ['Futuros de Gasolina RBOB']),
        (o.exports['#RTS-symbol-description'] = ['Índice RTS russo']),
        (o.exports['#SBER-symbol-description'] = 'SBERBANK'),
        (o.exports['#SPX500-symbol-description'] = ['Índice S&P 500']),
        (o.exports['#TWTR-symbol-description'] = ['TWITTER INC']),
        (o.exports['#UK100-symbol-description'] = [
          'Índice FTSE das 100 principais ações negociadas em bolsa do Reino Unido',
        ]),
        (o.exports['#USDBRL-symbol-description'] = [
          'Dolar Americano / Real Brasileiro',
        ]),
        (o.exports['#USDCAD-symbol-description'] = [
          'Dólar Americano/Dólar Canadense',
        ]),
        (o.exports['#USDCHF-symbol-description'] = [
          'Dólar Americano/Franco Suíço',
        ]),
        (o.exports['#USDCNY-symbol-description'] = [
          'Dólar Americano/Yuan Renminbi',
        ]),
        (o.exports['#USDDKK-symbol-description'] = [
          'DÓLAR AMERICANO/COROA DINAMARQUESA',
        ]),
        (o.exports['#USDHKD-symbol-description'] = [
          'Dólar Americano/Dólar de Hong Kong',
        ]),
        (o.exports['#USDIDR-symbol-description'] = ['Dólar Americano / Rupia']),
        (o.exports['#USDINR-symbol-description'] = [
          'Dólar Americano/Rupia Indiana',
        ]),
        (o.exports['#USDJPY-symbol-description'] = [
          'Dólar Americano/Iene Japonês',
        ]),
        (o.exports['#USDKRW-symbol-description'] = ['DÓLAR AMERICANO/WON']),
        (o.exports['#USDMXN-symbol-description'] = [
          'Dólar Americano / Peso Mexicano',
        ]),
        (o.exports['#USDPHP-symbol-description'] = [
          'Dólar Americano / Peso Filipino',
        ]),
        (o.exports['#USDRUB-symbol-description'] = [
          'DÓLAR AMERICANO/RUBLO RUSSO',
        ]),
        (o.exports['#USDRUB_TOM-symbol-description'] = ['USD/RUB TOM']),
        (o.exports['#USDSEK-symbol-description'] = [
          'Dólar Americano/Coroa Sueca',
        ]),
        (o.exports['#USDSGD-symbol-description'] = [
          'DÓLAR AMERICANO/DÓLAR DE SINGAPURA',
        ]),
        (o.exports['#USDTRY-symbol-description'] = [
          'Dólar Americano/Nova Lira Turca',
        ]),
        (o.exports['#VTBR-symbol-description'] = 'VTB'),
        (o.exports['#XAGUSD-symbol-description'] = ['Prata/Dólar Americano']),
        (o.exports['#XAUUSD-symbol-description'] = ['Ouro/Dólar Americano']),
        (o.exports['#XPDUSD-symbol-description'] = ['CFDs de paládio']),
        (o.exports['#XPTUSD-symbol-description'] = ['Platina/Dólar Americano']),
        (o.exports['#ZS1-symbol-description'] = ['Futuros de Soja - ECBT']),
        (o.exports['#ZW1-symbol-description'] = ['Futuros de Trigo - ECBT']),
        (o.exports['#BTCGBP-symbol-description'] = [
          'Bitcoin / Libra Esterlina',
        ]),
        (o.exports['#MICEXINDEXCF-symbol-description'] = [
          'Índice MOEX Rússia',
        ]),
        (o.exports['#BTCAUD-symbol-description'] = [
          'Bitcoin / Dólar Australiano',
        ]),
        (o.exports['#BTCJPY-symbol-description'] = ['Bitcoin / Iene Japonês']),
        (o.exports['#BTCBRL-symbol-description'] = [
          'Bitcoin / Real Brasileiro',
        ]),
        (o.exports['#PT10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Português',
        ]),
        (o.exports['#TXSX-symbol-description'] = ['Índice TSX 60']),
        (o.exports['#VIXC-symbol-description'] = ['Índice TSX 60 VIX']),
        (o.exports['#USDPLN-symbol-description'] = ['USD/PLN']),
        (o.exports['#EURPLN-symbol-description'] = ['Euro / Zloti Polonês']),
        (o.exports['#BTCPLN-symbol-description'] = ['Bitcoin / Zloti Polonês']),
        (o.exports['#CAC40-symbol-description'] = ['Índice CAC 40']),
        (o.exports['#XBTCAD-symbol-description'] = ['Bitcoin/Dólar Canadense']),
        (o.exports['#ITI2!-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIF2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIF2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIF2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIG2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIG2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIG2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIH2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIH2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIH2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIJ2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIJ2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIJ2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIK2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIK2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIK2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIM2017-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIM2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIM2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIM2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIN2017-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIN2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIN2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIN2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIQ2017-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIQ2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIQ2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIQ2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIU2017-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIU2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIU2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIU2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIV2017-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIV2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIV2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIV2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIX2017-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIX2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIX2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIX2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIZ2017-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIZ2018-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIZ2019-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#ITIZ2020-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#AMEX:GXF-symbol-description'] = [
          'ETF Global x FTSE região nórdica',
        ]),
        (o.exports['#ASX:XAF-symbol-description'] = [
          'Índice S&P/ASX All Australian 50',
        ]),
        (o.exports['#ASX:XAT-symbol-description'] = [
          'Índice S&P/ASX All Australian 200',
        ]),
        (o.exports['#BIST:XU100-symbol-description'] = ['Índice BIST 100']),
        (o.exports['#GPW:WIG20-symbol-description'] = ['Índice WIG20']),
        (o.exports['#INDEX:JKSE-symbol-description'] = [
          'Índice Composto de Jarkarta',
        ]),
        (o.exports['#INDEX:KLSE-symbol-description'] = [
          'Índice KLCI da Bolsa da Málasia',
        ]),
        (o.exports['#INDEX:NZD-symbol-description'] = ['Índice NZX 50']),
        (o.exports['#INDEX:STI-symbol-description'] = ['Índice STI']),
        (o.exports['#INDEX:XLY0-symbol-description'] = [
          'Índice Composto de Xangai',
        ]),
        (o.exports['#MOEX:MICEXINDEXCF-symbol-description'] = [
          'Índice MOEX da Rússia',
        ]),
        (o.exports['#NYMEX:KT1!-symbol-description'] = ['Futuros de café']),
        (o.exports['#OANDA:NATGASUSD-symbol-description'] = [
          'CFD de gás natural',
        ]),
        (o.exports['#OANDA:USDPLN-symbol-description'] = [
          'Dólar dos EUA/Zloti Polonês',
        ]),
        (o.exports['#TSX:TX60-symbol-description'] = ['Índice S&P/TSX 60']),
        (o.exports['#TSX:VBU-symbol-description'] = [
          'Índice Vanguard US Agregado Bond ETF (CAD-hedged) UN',
        ]),
        (o.exports['#TSX:VIXC-symbol-description'] = ['Índice S&P/TSX 60 VIX']),
        (o.exports['#TVC:CAC40-symbol-description'] = ['Índice CAC 40']),
        (o.exports['#TVC:ES10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Espanhol',
        ]),
        (o.exports['#TVC:EUBUND-symbol-description'] = 'Euro Bund'),
        (o.exports['#TVC:GB02-symbol-description'] = [
          'Títulos de Dois Anos do Governo Britânico',
        ]),
        (o.exports['#TVC:GB10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Britânico',
        ]),
        (o.exports['#TVC:GOLD-symbol-description'] = [
          'CFDs em Ouro (US$ / OZ)',
        ]),
        (o.exports['#TVC:ID03-symbol-description'] = [
          'Títulos de Três Anos do Governo Indonésio',
        ]),
        (o.exports['#TVC:ID10-symbol-description'] = [
          'Títulos de Dez Anos do Governo da Indonésia',
        ]),
        (o.exports['#TVC:PALLADIUM-symbol-description'] = [
          'CFDs em Paládio (US$ / OZ)',
        ]),
        (o.exports['#TVC:PT10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Português',
        ]),
        (o.exports['#TVC:SILVER-symbol-description'] = [
          'CFDs em Prata (US$ / OZ)',
        ]),
        (o.exports['#TVC:RUT-symbol-description'] = ['Índice Russell 2000']),
        (o.exports['#TSX:TSX-symbol-description'] = [
          'Índice Composto S&P/TSX',
        ]),
        (o.exports['#OANDA:CH20CHF-symbol-description'] = ['Índice Swiss 20']),
        (o.exports['#TVC:SHCOMP-symbol-description'] = [
          'Índice Shanghai Composite',
        ]),
        (o.exports['#NZX:ALLC-symbol-description'] = [
          'Índice Geral S&P/NZX (Índice de Capital)',
        ]),
        (o.exports['#AMEX:SHYG-symbol-description'] = [
          'Cotas 0-5 YEAR High Yield Corporate Bond ETF',
        ]),
        (o.exports['#TVC:AU10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Australiano',
        ]),
        (o.exports['#TVC:CN10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Chinês',
        ]),
        (o.exports['#TVC:KR10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Coreano',
        ]),
        (o.exports['#NYMEX:RB1!-symbol-description'] = [
          'Futuros de gasolina RBOB',
        ]),
        (o.exports['#NYMEX:HO1!-symbol-description'] = [
          'Futuros de NY Harbor ULSD',
        ]),
        (o.exports['#NYMEX:AEZ1!-symbol-description'] = [
          'Futuros de etanol NY',
        ]),
        (o.exports['#OANDA:XCUUSD-symbol-description'] = [
          'CFD de cobre (US$/libra)',
        ]),
        (o.exports['#COMEX:ZA1!-symbol-description'] = ['Futuros de zinco']),
        (o.exports['#CBOT:ZW1!-symbol-description'] = ['Futuros de trigo']),
        (o.exports['#NYMEX:KA1!-symbol-description'] = [
          'Futuros de açúcar nº 11',
        ]),
        (o.exports['#CBOT:QBC1!-symbol-description'] = ['Futuros de milho']),
        (o.exports['#CME:E61!-symbol-description'] = ['Futuros de Euro']),
        (o.exports['#CME:B61!-symbol-description'] = [
          'Futuros de Libra Esterlina',
        ]),
        (o.exports['#CME:QJY1!-symbol-description'] = [
          'Futuros de Iene Japonês',
        ]),
        (o.exports['#CME:A61!-symbol-description'] = [
          'Futuros de Dólar Australiano',
        ]),
        (o.exports['#CME:D61!-symbol-description'] = [
          'Futuros de Dólar Canadense',
        ]),
        (o.exports['#CME:SP1!-symbol-description'] = ['Futuros S&P 500']),
        (o.exports['#CME_MINI:NQ1!-symbol-description'] = [
          'Futuros NASDAQ 100 E-mini',
        ]),
        (o.exports['#CBOT_MINI:YM1!-symbol-description'] = [
          'Futuros Dow Jones E-mini',
        ]),
        (o.exports['#CME:NY1!-symbol-description'] = ['Futuros NIKKEI 225']),
        (o.exports['#EUREX:DY1!-symbol-description'] = ['Índice DAX']),
        (o.exports['#CME:IF1!-symbol-description'] = [
          'Índice de Futuros IBOVESPA - US$',
        ]),
        (o.exports['#CBOT:TY1!-symbol-description'] = [
          'Futuros de Notas do Tesouro de dez anos',
        ]),
        (o.exports['#CBOT:FV1!-symbol-description'] = [
          'Futuros de Notas do Tesouro de cinco anos',
        ]),
        (o.exports['#CBOT:ZE1!-symbol-description'] = [
          'Títulos do Tesouro - Futuros de três anos',
        ]),
        (o.exports['#CBOT:TU1!-symbol-description'] = [
          'Futuros de Notas do Tesouro de dios anos',
        ]),
        (o.exports['#CBOT:FF1!-symbol-description'] = [
          'Futuros de taxas de juros de Fundos FED de 30 dias',
        ]),
        (o.exports['#CBOT:US1!-symbol-description'] = ['Futuros de T-Bond']),
        (o.exports['#TVC:EXY-symbol-description'] = ['Índice Moeda Euro']),
        (o.exports['#TVC:JXY-symbol-description'] = ['Índice Iene Japonês']),
        (o.exports['#TVC:BXY-symbol-description'] = ['Índice Libra Esterlina']),
        (o.exports['#TVC:AXY-symbol-description'] = [
          'Índice Dólar Australiano',
        ]),
        (o.exports['#TVC:CXY-symbol-description'] = ['Índice Dólar Canadense']),
        (o.exports['#FRED:GDP-symbol-description'] = [
          'Produto Interno Bruto, primeiro decimal',
        ]),
        (o.exports['#FRED:UNRATE-symbol-description'] = [
          'Taxa de desemprego civil',
        ]),
        (o.exports['#FRED:POP-symbol-description'] = [
          'População total: todas as idades, incluindo as forças armadas no exterior',
        ]),
        (o.exports['#ETHUSD-symbol-description'] = ['Ethereum / Dólar']),
        (o.exports['#BMFBOVESPA:IBOV-symbol-description'] = [
          'Índice IBovespa',
        ]),
        (o.exports['#BMFBOVESPA:IBRA-symbol-description'] = ['Índice IBrasil']),
        (o.exports['#BMFBOVESPA:IBXL-symbol-description'] = ['Índice IBRX 50']),
        (o.exports['#COMEX:HG1!-symbol-description'] = ['Futuros de cobre']),
        (o.exports['#INDEX:HSCE-symbol-description'] = [
          'Índice Hang Seng de empresas da China',
        ]),
        (o.exports['#NYMEX:CL1!-symbol-description'] = [
          'Futuros de petróleo leve bruto',
        ]),
        (o.exports['#OTC:IHRMF-symbol-description'] = [
          'Ishares MSCI SHS Japão',
        ]),
        (o.exports['#TVC:DAX-symbol-description'] = [
          'Índice das 30 Maiores Companhias Alemãs',
        ]),
        (o.exports['#TVC:DE10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Alemão',
        ]),
        (o.exports['#TVC:DJI-symbol-description'] = [
          'Índice Industrial Médio Dow Jones',
        ]),
        (o.exports['#TVC:DXY-symbol-description'] = [
          'Índice de Moeda Dólar Americano',
        ]),
        (o.exports['#TVC:FR10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Francês',
        ]),
        (o.exports['#TVC:HSI-symbol-description'] = ['Índice Hang Seng']),
        (o.exports['#TVC:IBEX35-symbol-description'] = ['Índice IBEX 35']),
        (o.exports['#FX:AUS200-symbol-description'] = ['Índice S&P/ASX']),
        (o.exports['#AMEX:SHY-symbol-description'] = [
          'Títulos do Tesouro Americano de 1-3 anos ETF Ishares',
        ]),
        (o.exports['#ASX:XJO-symbol-description'] = ['Índice S&P/ASX 200']),
        (o.exports['#BSE:SENSEX-symbol-description'] = [
          'Índice S&P BSE Sensex',
        ]),
        (o.exports['#INDEX:MIB-symbol-description'] = ['Índice MIB']),
        (o.exports['#INDEX:MOY0-symbol-description'] = [
          'Índice Euro Stoxx 50',
        ]),
        (o.exports['#MOEX:RTSI-symbol-description'] = ['Índice RTS']),
        (o.exports['#NSE:NIFTY-symbol-description'] = ['Índice Nifty 50']),
        (o.exports['#NYMEX:NG1!-symbol-description'] = [
          'Futuros de Gás Natural',
        ]),
        (o.exports['#NYMEX:ZC1!-symbol-description'] = ['Futuros Milho']),
        (o.exports['#TVC:IN10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Indiano',
        ]),
        (o.exports['#TVC:IT10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Italiano',
        ]),
        (o.exports['#TVC:JP10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Japonês',
        ]),
        (o.exports['#TVC:NDX-symbol-description'] = ['Índice US 100']),
        (o.exports['#TVC:NI225-symbol-description'] = ['NIKKEI 225']),
        (o.exports['#TVC:SPX-symbol-description'] = ['S&P 500']),
        (o.exports['#TVC:SX5E-symbol-description'] = ['Índice Euro Stoxx 50']),
        (o.exports['#TVC:TR10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Turco',
        ]),
        (o.exports['#TVC:UKOIL-symbol-description'] = [
          'CFDs em Petróleo Bruto Brent',
        ]),
        (o.exports['#TVC:UKX-symbol-description'] = ['Índice UK 100']),
        (o.exports['#TVC:US02-symbol-description'] = [
          'Títulos de Dois Anos do Governo Americano',
        ]),
        (o.exports['#TVC:US05-symbol-description'] = [
          'Títulos de Cinco Anos do Governo Americano',
        ]),
        (o.exports['#TVC:US10-symbol-description'] = [
          'Títulos de Dez Anos do Governo Americano',
        ]),
        (o.exports['#TVC:USOIL-symbol-description'] = [
          'CFDs em Petróleo Bruto WTI',
        ]),
        (o.exports['#NYMEX:ITI1!-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#NASDAQ:SHY-symbol-description'] = [
          'Títulos do Tesouro Americano de 1-3 anos ETF Ishares',
        ]),
        (o.exports['#AMEX:ALD-symbol-description'] = [
          'ETF WisdomTree Ásia Dívida Local',
        ]),
        (o.exports['#NASDAQ:AMD-symbol-description'] =
          'Advanced Micro Devices Inc'),
        (o.exports['#NYSE:BABA-symbol-description'] = [
          'Alibaba Group Holding Ltd.',
        ]),
        (o.exports['#ICEEUR:CB-symbol-description'] = ['Petróleo Bruto Brent']),
        (o.exports['#ICEEUR:CB1!-symbol-description'] = [
          'Petróleo Bruto Brent',
        ]),
        (o.exports['#ICEUSA:CC-symbol-description'] = ['Cacau']),
        (o.exports['#NYMEX:CL-symbol-description'] = ['Petróleo Bruto WTI']),
        (o.exports['#ICEUSA:CT-symbol-description'] = ['Algodão nº 2']),
        (o.exports['#NASDAQ:CTRV-symbol-description'] =
          'ContraVir Pharmaceuticals Inc'),
        (o.exports['#CME:DL-symbol-description'] = ['Leite classe III']),
        (o.exports['#NYSE:F-symbol-description'] = 'FORD MTR CO DEL'),
        (o.exports['#MOEX:GAZP-symbol-description'] = 'GAZPROM'),
        (o.exports['#COMEX:GC-symbol-description'] = ['Ouro']),
        (o.exports['#CME:GF-symbol-description'] = ['Gado para engorda']),
        (o.exports['#CME:HE-symbol-description'] = ['Porcos magros']),
        (o.exports['#NASDAQ:IEF-symbol-description'] = [
          'Ishares ETF de títulos do Tesouro Americano de 7-10 anos',
        ]),
        (o.exports['#NASDAQ:IEI-symbol-description'] = [
          'Ishares ETF de títulos do Tesouro Americano de 3-7 anos',
        ]),
        (o.exports['#NYMEX:KA1-symbol-description'] = [
          'Futuros de Açúcar nº 11',
        ]),
        (o.exports['#ICEUSA:KC-symbol-description'] = ['Café']),
        (o.exports['#NYMEX:KG1-symbol-description'] = ['Futuros de Algodão']),
        (o.exports['#FWB:KT1-symbol-description'] = ['Key Tronic Corporation']),
        (o.exports['#CME:LE-symbol-description'] = ['Gado Gordo']),
        (o.exports['#ICEEUR:LO-symbol-description'] = [
          'Óleo de calefação ICE',
        ]),
        (o.exports['#CME:LS-symbol-description'] = ['Madeira']),
        (o.exports['#MOEX:MGNT-symbol-description'] = 'MAGNIT'),
        (o.exports['#LSIN:MNOD-symbol-description'] =
          'ADR GMK NORILSKIYNIKEL ORD SHS [REPO]'),
        (o.exports['#NYMEX:NG-symbol-description'] = ['Gás natural']),
        (o.exports['#ICEUSA:OJ-symbol-description'] = ['Suco de laranja']),
        (o.exports['#NYMEX:PA-symbol-description'] = ['Paládio']),
        (o.exports['#NYSE:PBR-symbol-description'] = [
          'PETRÓLEO BRASILEIRO S.A. - PETROBRAS',
        ]),
        (o.exports['#NYMEX:PL-symbol-description'] = ['Platina']),
        (o.exports['#COMEX_MINI:QC-symbol-description'] = ['Cobre E-Mini']),
        (o.exports['#NYMEX:RB-symbol-description'] = ['Gasolina RBOB']),
        (o.exports['#NYMEX:RB1-symbol-description'] = [
          'Futuros de Gasolina RBOB',
        ]),
        (o.exports['#MOEX:SBER-symbol-description'] = 'SBERBANK'),
        (o.exports['#AMEX:SCHO-symbol-description'] = [
          'ETF Schwab Curto Prazo Títulos Americanos',
        ]),
        (o.exports['#COMEX:SI-symbol-description'] = ['Prata']),
        (o.exports['#NASDAQ:TLT-symbol-description'] = [
          'Ishares ETF de títulos do Tesouro Americano de 20 anos',
        ]),
        (o.exports['#TVC:VIX-symbol-description'] = [
          'Índice de Volatilidade S&P 500',
        ]),
        (o.exports['#MOEX:VTBR-symbol-description'] = 'VTB'),
        (o.exports['#COMEX:ZA-symbol-description'] = ['Zinco']),
        (o.exports['#CBOT:ZC-symbol-description'] = ['Milho']),
        (o.exports['#CBOT:ZK-symbol-description'] = ['Futuros de etanol']),
        (o.exports['#CBOT:ZL-symbol-description'] = ['Óleo de soja']),
        (o.exports['#CBOT:ZO-symbol-description'] = ['Aveia']),
        (o.exports['#CBOT:ZR-symbol-description'] = ['Arroz não processado']),
        (o.exports['#CBOT:ZS-symbol-description'] = ['Soja']),
        (o.exports['#CBOT:ZS1-symbol-description'] = ['Futuros de Soja']),
        (o.exports['#CBOT:ZW-symbol-description'] = ['Trigo']),
        (o.exports['#CBOT:ZW1-symbol-description'] = [
          'Futuros de Trigo - ECBT',
        ]),
        (o.exports['#NASDAQ:ITI-symbol-description'] = 'Iteris Inc'),
        (o.exports['#NYMEX:ITI2!-symbol-description'] = [
          'Futuros de Minério de Ferro',
        ]),
        (o.exports['#CADUSD-symbol-description'] = [
          'Dólar Canadense/Dólar EUA',
        ]),
        (o.exports['#CHFUSD-symbol-description'] = [
          'Franco Suíço/Dólar Americano',
        ]),
        (o.exports['#GPW:ACG-symbol-description'] = 'Acautogaz'),
        (o.exports['#JPYUSD-symbol-description'] = ['Iene/Dólar EUA']),
        (o.exports['#USDAUD-symbol-description'] = [
          'Dólar EUA/Dólar Australiano',
        ]),
        (o.exports['#USDEUR-symbol-description'] = ['Dólar/Euro']),
        (o.exports['#USDGBP-symbol-description'] = ['Dólar/Libra Esterlina']),
        (o.exports['#USDNZD-symbol-description'] = [
          'Dólar EUA/Dólar Neozelandês',
        ]),
        (o.exports['#UKOIL-symbol-description'] = [
          'CFDs em Petróleo Bruto (Brent)',
        ]),
        (o.exports['#USOIL-symbol-description'] = [
          'CFDs em Petróleo Bruto (WTI)',
        ]),
        (o.exports['#US30-symbol-description'] = [
          'Índice Dow Jones Industrial Average',
        ]),
        (o.exports['#BCHUSD-symbol-description'] = ['Bitcoin Cash/Dólar EUA']),
        (o.exports['#ETCUSD-symbol-description'] = [
          'Ethereum Classic/Dólar EUA',
        ]),
        (o.exports['#GOOG-symbol-description'] = [
          'Alphabet Inc (Google) Classe C',
        ]),
        (o.exports['#LTCUSD-symbol-description'] = ['Litecoin/Dólar EUA']),
        (o.exports['#XRPUSD-symbol-description'] = ['XRP/Dólar EUA']),
        (o.exports['#SP:SPX-symbol-description'] = ['Índice S&P 500']),
        (o.exports['#ETCBTC-symbol-description'] = [
          'Ethereum Classic/Bitcoin',
        ]),
        (o.exports['#ETHBTC-symbol-description'] = ['Ethereum/Bitcoin']),
        (o.exports['#XRPBTC-symbol-description'] = ['XRP/Bitcoin']),
        (o.exports['#TVC:US30-symbol-description'] = [
          'Títulos de 30 Anos do Governo Americano',
        ]),
        (o.exports['#COMEX:SI1!-symbol-description'] = ['Futuros de Prata']),
        (o.exports['#BTGUSD-symbol-description'] = ['Bitcoin Gold/Dólar EUA']),
        (o.exports['#IOTUSD-symbol-description'] = ['IOTA/Dólar EUA']),
        (o.exports['#CME:BTC1!-symbol-description'] = [
          'Futuros de Bitcoin CME',
        ]),
        (o.exports['#COMEX:GC1!-symbol-description'] = ['Futuros de ouro']),
        (o.exports['#CORNUSD-symbol-description'] = ['CFDs de milho']),
        (o.exports['#COTUSD-symbol-description'] = ['CFDs de algodão']),
        (o.exports['#DJ:DJA-symbol-description'] = [
          'Índice composto médio Dow Jones',
        ]),
        (o.exports['#DJ:DJI-symbol-description'] = [
          'Índice industrial médio Dow Jones',
        ]),
        (o.exports['#ETHEUR-symbol-description'] = ['Ethereum/Euro']),
        (o.exports['#ETHGBP-symbol-description'] = [
          'Ethereum/Libra Esterlina',
        ]),
        (o.exports['#ETHJPY-symbol-description'] = ['Ethereum/Iene Japonês']),
        (o.exports['#EURNOK-symbol-description'] = ['Euro/Coroa Norueguesa']),
        (o.exports['#GBPPLN-symbol-description'] = [
          'Libra Esterlina/Zloti Polonês',
        ]),
        (o.exports['#MOEX:BR1!-symbol-description'] = [
          'Futuros de petróleo Brent',
        ]),
        (o.exports['#NYMEX:KG1!-symbol-description'] = ['Futuros de algodão']),
        (o.exports['#NYMEX:PL1!-symbol-description'] = ['Futuros de platina']),
        (o.exports['#SOYBNUSD-symbol-description'] = ['CFDs de soja']),
        (o.exports['#SUGARUSD-symbol-description'] = ['CFDs de açúcar']),
        (o.exports['#TVC:IXIC-symbol-description'] = [
          'Índices Amplos dos EUA',
        ]),
        (o.exports['#TVC:RU-symbol-description'] = ['Índice Russell 1000']),
        (o.exports['#USDZAR-symbol-description'] = [
          'Dólar EUA/Rand Sul-africano',
        ]),
        (o.exports['#WHEATUSD-symbol-description'] = ['CFDs de trigo']),
        (o.exports['#XRPEUR-symbol-description'] = ['XRP/Euro']),
        (o.exports['#CBOT:S1!-symbol-description'] = ['Futuros de soja']),
        (o.exports['#SP:MID-symbol-description'] = ['Índice S&P 400']),
        (o.exports['#TSX:XCUUSD-symbol-description'] = ['CFDs de cobre']),
        (o.exports['#TVC:NYA-symbol-description'] = ['Índice NYSE Composite']),
        (o.exports['#TVC:PLATINUM-symbol-description'] = [
          'CFDs de platina (US$/onça)',
        ]),
        (o.exports['#TVC:SSMI-symbol-description'] = ['Índice Mercado Suíço']),
        (o.exports['#TVC:SXY-symbol-description'] = ['Índice Franco Suíço']),
        (o.exports['#TVC:RUI-symbol-description'] = ['Índice Russell 1000']),
        (o.exports['#MOEX:RI1!-symbol-description'] = [
          'Futuros do índice RTS',
        ]),
        (o.exports['#MOEX:MX1!-symbol-description'] = [
          'Futuros do índice MICEX',
        ]),
        (o.exports['#CBOE:BG1!-symbol-description'] = [
          'Futuros de Bitcoin CBOE',
        ]),
        (o.exports['#TVC:MY10-symbol-description'] = [
          'Títulos de Dez Anos do Governo da Malásia',
        ]),
        (o.exports['#CME:S61!-symbol-description'] = [
          'Futuros de Franco Suíço',
        ]),
        (o.exports['#TVC:DEU30-symbol-description'] = ['Índice DAX']),
        (o.exports['#BCHEUR-symbol-description'] = ['Bitcoin dinheiro/euros']),
        (o.exports['#TVC:ZXY-symbol-description'] = [
          'Índice Dólar da Nova Zelândia',
        ]),
        (o.exports['#MIL:FTSEMIB-symbol-description'] = ['Índice FTSE MIB']),
        (o.exports['#XETR:DAX-symbol-description'] = ['Índice DAX']),
        (o.exports['#MOEX:IMOEX-symbol-description'] = ['Índice MOEX Rússia']),
        (o.exports['#FX:US30-symbol-description'] = [
          'Índice Industrial Médio Dow Jones',
        ]),
        (o.exports['#MOEX:RUAL-symbol-description'] =
          'United Company RUSAL PLC'),
        (o.exports['#MOEX:MX2!-symbol-description'] = [
          'Índice de futuros MICEX',
        ]),
        (o.exports['#NEOUSD-symbol-description'] = ['NEO/Dólar dos EUA']),
        (o.exports['#XMRUSD-symbol-description'] = ['Monero/Dólar dos EUA']),
        (o.exports['#ZECUSD-symbol-description'] = ['Zcash/Dólar dos EUA']),
        (o.exports['#TVC:CAC-symbol-description'] = ['Índice CAC 40']),
        (o.exports['#NASDAQ:ZS-symbol-description'] = 'Zscaler Inc'),
        (o.exports['#TVC:GB10Y-symbol-description'] = [
          'Títulos do Governo Britânico 10a (Yields)',
        ]),
        (o.exports['#TVC:AU10Y-symbol-description'] = [
          'Títulos do Governo Australiano 10a (Yield)',
        ]),
        (o.exports['#TVC:CN10Y-symbol-description'] = [
          'Títulos do Governo Chinês 10a (Yield)',
        ]),
        (o.exports['#TVC:DE10Y-symbol-description'] = [
          'Títulos do Governo Alemão 10a (Yield)',
        ]),
        (o.exports['#TVC:ES10Y-symbol-description'] = [
          'Títulos do Governo Espanhol 10a (Yield)',
        ]),
        (o.exports['#TVC:FR10Y-symbol-description'] = [
          'Títulos do Governo Frances 10a (Yield)',
        ]),
        (o.exports['#TVC:IN10Y-symbol-description'] = [
          'Títulos do Governo Indiano 10a (Yield)',
        ]),
        (o.exports['#TVC:IT10Y-symbol-description'] = [
          'Títulos do Governo Italiano 10a (Yield)',
        ]),
        (o.exports['#TVC:JP10Y-symbol-description'] = [
          'Títulos do Governo Japonês 10a (Yield)',
        ]),
        (o.exports['#TVC:KR10Y-symbol-description'] = [
          'Títulos do Governo Coeano 10a (Yield)',
        ]),
        (o.exports['#TVC:MY10Y-symbol-description'] = [
          'Títulos do Governo Malásio 10a (Yield)',
        ]),
        (o.exports['#TVC:PT10Y-symbol-description'] = [
          'Títulos do Governo Português 10a (Yield)',
        ]),
        (o.exports['#TVC:TR10Y-symbol-description'] = [
          'Títulos do Governo Turco 10a (Yield)',
        ]),
        (o.exports['#TVC:US02Y-symbol-description'] = [
          'Títulos do Governo Norte Americano 2a (Yield)',
        ]),
        (o.exports['#TVC:US05Y-symbol-description'] = [
          'Títulos do Governo Norte Americano 5a (Yield)',
        ]),
        (o.exports['#TVC:US10Y-symbol-description'] = [
          'Títulos do Governo Norte Americano 10a (Yield)',
        ]),
        (o.exports['#INDEX:TWII-symbol-description'] = [
          'Índice Ponderado de Taiwan',
        ]),
        (o.exports['#CME:J61!-symbol-description'] = [
          'Futuros em ienes japoneses',
        ]),
        (o.exports['#CME_MINI:J71!-symbol-description'] = [
          'Futuros E-mini em ienes japoneses',
        ]),
        (o.exports['#CME_MINI:WM1!-symbol-description'] = [
          'Futuros E-micro em ienes japoneses/dólares americanos',
        ]),
        (o.exports['#CME:M61!-symbol-description'] = [
          'Futuros em pesos mexicanos',
        ]),
        (o.exports['#CME:T61!-symbol-description'] = [
          'Futuros em randes sul-africanos',
        ]),
        (o.exports['#CME:SK1!-symbol-description'] = [
          'Futuros em coroas suecas',
        ]),
        (o.exports['#CME:QT1!-symbol-description'] = [
          'Futuros em renminbis chineses/dólares americanos',
        ]),
        (o.exports['#COMEX:AUP1!-symbol-description'] = [
          'Futuros de alumínio MW U.S. Transaction Premium Platts (25MT)',
        ]),
        (o.exports['#CME:L61!-symbol-description'] = [
          'Futuros em reais brasileiros',
        ]),
        (o.exports['#CME:WP1!-symbol-description'] = [
          'Futuros em zlotis poloneses',
        ]),
        (o.exports['#CME:N61!-symbol-description'] = [
          'Futuros em dólares neozelandeses',
        ]),
        (o.exports['#CME_MINI:MG1!-symbol-description'] = [
          'Futuros E-micro em dólares australianos/dólares americanos',
        ]),
        (o.exports['#CME_MINI:WN1!-symbol-description'] = [
          'Futuros E-micro em francos suíços/dólares americanos',
        ]),
        (o.exports['#CME_MINI:MF1!-symbol-description'] = [
          'Futuros E-micro em euros/dólares americanos',
        ]),
        (o.exports['#CME_MINI:E71!-symbol-description'] = [
          'Futuros E-mini em euros',
        ]),
        (o.exports['#CBOT:ZK1!-symbol-description'] = [
          'Futuros de Combustível Etanol Desnaturado',
        ]),
        (o.exports['#CME_MINI:MB1!-symbol-description'] = [
          'Futuros E-micro em libras esterlinas/dólares americanos',
        ]),
        (o.exports['#NYMEX_MINI:QU1!-symbol-description'] = [
          'Futuros E-mini de Gasolina',
        ]),
        (o.exports['#NYMEX_MINI:QX1!-symbol-description'] = [
          'Futuros E-mini de Óleo de Calefação',
        ]),
        (o.exports['#COMEX_MINI:QC1!-symbol-description'] = [
          'Futuros E-mini de Cobre',
        ]),
        (o.exports['#NYMEX_MINI:QG1!-symbol-description'] = [
          'Futuros E-mini de Gás Natural',
        ]),
        (o.exports['#CME:E41!-symbol-description'] = [
          'Futuros em dólares americanos/liras turcas',
        ]),
        (o.exports['#COMEX_MINI:QI1!-symbol-description'] = [
          'Futuros (Mini) de Prata',
        ]),
        (o.exports['#CME:DL1!-symbol-description'] = [
          'Futuros de Leite Classe III',
        ]),
        (o.exports['#NYMEX:UX1!-symbol-description'] = ['Futuros de Urânio']),
        (o.exports['#CBOT:BO1!-symbol-description'] = [
          'Futuros de Óleo de Soja',
        ]),
        (o.exports['#CME:HE1!-symbol-description'] = [
          'Futuros de Suínos Magros',
        ]),
        (o.exports['#NYMEX:IAC1!-symbol-description'] = [
          'Futuros de Carvão Newcastle',
        ]),
        (o.exports['#NYMEX_MINI:QM1!-symbol-description'] = [
          'Futuros E-mini de Petróleo Bruto Leve',
        ]),
        (o.exports['#NYMEX:JMJ1!-symbol-description'] = [
          'Mini Futuros de Brent',
        ]),
        (o.exports['#COMEX:AEP1!-symbol-description'] = [
          'Futuros Europeus Premium de Alumínio',
        ]),
        (o.exports['#CBOT:ZQ1!-symbol-description'] = [
          'Futuros de Taxas de Juros de Fundos Federais a 30 dias',
        ]),
        (o.exports['#CME:LE1!-symbol-description'] = ['Futuros de Gado em Pé']),
        (o.exports['#CME:UP1!-symbol-description'] = [
          'Futuros de francos suíços/ienes japoneses',
        ]),
        (o.exports['#CBOT:ZN1!-symbol-description'] = [
          'Futuros de Notas Y de 10 anos',
        ]),
        (o.exports['#CBOT:ZB1!-symbol-description'] = [
          'Futuros de Obrigações do Tesouro',
        ]),
        (o.exports['#CME:GF1!-symbol-description'] = [
          'Futuros de Gado de Engorda',
        ]),
        (o.exports['#CBOT:UD1!-symbol-description'] = [
          'Futuros de Obrigações do Tesouro Ultra',
        ]),
        (o.exports['#CME:I91!-symbol-description'] = [
          'Futuro de Imoveis CME - Washington DC',
        ]),
        (o.exports['#CBOT:ZO1!-symbol-description'] = ['Futuros de Aveia']),
        (o.exports['#CBOT:ZM1!-symbol-description'] = [
          'Futuros de Farelo de Soja',
        ]),
        (o.exports['#CBOT_MINI:XN1!-symbol-description'] = [
          'Futuros Mini de Milho',
        ]),
        (o.exports['#CBOT:ZC1!-symbol-description'] = ['Futuros de Milho']),
        (o.exports['#CME:LS1!-symbol-description'] = ['Futuros de Madeira']),
        (o.exports['#CBOT_MINI:XW1!-symbol-description'] = [
          'Futuros Mini de Trigo',
        ]),
        (o.exports['#CBOT_MINI:XK1!-symbol-description'] = [
          'Futuros Mini de Soja',
        ]),
        (o.exports['#CBOT:ZS1!-symbol-description'] = ['Futuros de Soja']),
        (o.exports['#NYMEX:PA1!-symbol-description'] = ['Futuros de Paládio']),
        (o.exports['#CME:FTU1!-symbol-description'] = [
          'Futuros E-mini do Índice FTSE100 em dólares americanos',
        ]),
        (o.exports['#CBOT:ZR1!-symbol-description'] = ['Futuros de Arroz']),
        (o.exports['#COMEX_MINI:GR1!-symbol-description'] = [
          'Futuros (E-micro) de Ouro',
        ]),
        (o.exports['#COMEX_MINI:QO1!-symbol-description'] = [
          'Futuros (Mini) de Ouro',
        ]),
        (o.exports['#CME_MINI:RL1!-symbol-description'] = [
          'Futuros E-mini Russel 1000',
        ]),
        (o.exports['#CME_MINI:EW1!-symbol-description'] = [
          'Futuros E-mini S&P400 Midcap',
        ]),
        (o.exports['#COMEX:LD1!-symbol-description'] = ['Futuros de Chumbo']),
        (o.exports['#CME_MINI:ES1!-symbol-description'] = [
          'Futuros E-mini S&P500',
        ]),
        (o.exports['#TVC:SA40-symbol-description'] = [
          'Índice Top 40 da Africa do Sul',
        ]),
        (o.exports['#BMV:ME-symbol-description'] = ['Índice IPC do México']),
        (o.exports['#BCBA:IMV-symbol-description'] = ['Índice MERVAL']),
        (o.exports['#HSI:HSI-symbol-description'] = ['Índice Hang Seng']),
        (o.exports['#BVL:SPBLPGPT-symbol-description'] = [
          'Índice Geral S&P/BVL do Peru (PEN)',
        ]),
        (o.exports['#EGX:EGX30-symbol-description'] = [
          'Índice EGX30 Price Return',
        ]),
        (o.exports['#BVC:IGBC-symbol-description'] = [
          'Índice Geral da Bolsa de Valores da Colômbia',
        ]),
        (o.exports['#TWSE:TAIEX-symbol-description'] = [
          'Índice de Ações Ponderadas por Capitalização de Taiwan',
        ]),
        (o.exports['#QSE:GNRI-symbol-description'] = ['Índice QE']),
        (o.exports['#BME:IBC-symbol-description'] = ['Índice IBEX35']),
        (o.exports['#NZX:NZ50G-symbol-description'] = [
          'Índice Bruto S&P/NZX50',
        ]),
        (o.exports['#SIX:SMI-symbol-description'] = [
          'Índice do Mercado Suíço',
        ]),
        (o.exports['#SZSE:399001-symbol-description'] = [
          'Índice SZSE Componente',
        ]),
        (o.exports['#TADAWUL:TASI-symbol-description'] = [
          'Índice Tadawul Todas as Ações',
        ]),
        (o.exports['#IDX:COMPOSITE-symbol-description'] = [
          'Índice IDX Composto',
        ]),
        (o.exports['#EURONEXT:PX1-symbol-description'] = ['Índice CAC40']),
        (o.exports['#OMXHEX:OMXH25-symbol-description'] = [
          'Índice EMX Helsinque 25',
        ]),
        (o.exports['#EURONEXT:BEL20-symbol-description'] = ['Índice BEL 20']),
        (o.exports['#TVC:STI-symbol-description'] = ['Índice Straits Times']),
        (o.exports['#DFM:DFMGI-symbol-description'] = ['Índice DFM']),
        (o.exports['#TVC:KOSPI-symbol-description'] = [
          'índice de Preços de Ações da Coreia Composto',
        ]),
        (o.exports['#FTSEMYX:FBMKLCI-symbol-description'] = [
          'Índice FTSE da Bolsa da Malásia KLCI',
        ]),
        (o.exports['#TASE:TA35-symbol-description'] = ['Índice TA-35']),
        (o.exports['#OMXSTO:OMXS30-symbol-description'] = [
          'Índice OMX Estocolmo 30',
        ]),
        (o.exports['#OMXICE:OMXI8-symbol-description'] = [
          'Índice OMX Islândia 8',
        ]),
        (o.exports['#NSENG:NSE30-symbol-description'] = ['Índice NSE 30']),
        (o.exports['#BAHRAIN:BSEX-symbol-description'] = [
          'Índice Bahrain Todas as Ações',
        ]),
        (o.exports['#OMXTSE:OMXTGI-symbol-description'] = ['OMX Talin GI']),
        (o.exports['#OMXCOP:OMXC25-symbol-description'] = [
          'Índice OMX Copenhague 25',
        ]),
        (o.exports['#OMXRSE:OMXRGI-symbol-description'] = 'OMX Riga GI'),
        (o.exports['#BELEX:BELEX15-symbol-description'] = ['Índice BELEX 15']),
        (o.exports['#OMXVSE:OMXVGI-symbol-description'] = ['OMX Vilna GI']),
        (o.exports['#EURONEXT:AEX-symbol-description'] = ['Índice AEX']),
        (o.exports['#CBOE:VIX-symbol-description'] = [
          'Índice de Volatilidade S&P500',
        ]),
        (o.exports['#NASDAQ:XAU-symbol-description'] = [
          'Índice PHLX Setor Ouro e Prata',
        ]),
        (o.exports['#DJ:DJUSCL-symbol-description'] = [
          'Índice Carvão Dow Jones EUA',
        ]),
        (o.exports['#DJ:DJCIKC-symbol-description'] = [
          'Índice de Commodities de Café Dow Jones',
        ]),
        (o.exports['#DJ:DJCIEN-symbol-description'] = [
          'Índice de Commodities de Energia Dow Jones',
        ]),
        (o.exports['#NASDAQ:OSX-symbol-description'] = [
          'Índice PHLX do Setor de Serviços de Petróleo',
        ]),
        (o.exports['#DJ:DJCISB-symbol-description'] = [
          'Índice de Commodities de Açúcar Dow Jones',
        ]),
        (o.exports['#DJ:DJCICC-symbol-description'] = [
          'Índice de Commodities de Cacau Dow Jones',
        ]),
        (o.exports['#DJ:DJCIGR-symbol-description'] = [
          'Índice de Commodities de Grãos Dow Jones',
        ]),
        (o.exports['#DJ:DJCIAGC-symbol-description'] = [
          'Índice de Commodities com Componente Agrícola Dow Jones',
        ]),
        (o.exports['#DJ:DJCISI-symbol-description'] = [
          'Índice de Commodities de Prata Dow Jones',
        ]),
        (o.exports['#DJ:DJCIIK-symbol-description'] = [
          'Índice de Commodities de Níquel Dow Jones',
        ]),
        (o.exports['#NASDAQ:HGX-symbol-description'] = [
          'Índice PHLX do Setor de Imoveis',
        ]),
        (o.exports['#DJ:DJCIGC-symbol-description'] = [
          'Índice de Commodities de Ouro Dow Jones',
        ]),
        (o.exports['#SP:SPGSCI-symbol-description'] = [
          'Índice de Commodities S&P Goldman Sachs',
        ]),
        (o.exports['#NASDAQ:UTY-symbol-description'] = [
          'Índice PHLX do Setor de Serviços Públicos',
        ]),
        (o.exports['#DJ:DJU-symbol-description'] = [
          'Índice Médio de Serviços Públicos Dow Jones',
        ]),
        (o.exports['#SP:SVX-symbol-description'] = ['Índice Valor S&P500']),
        (o.exports['#SP:OEX-symbol-description'] = ['Índice S&P100']),
        (o.exports['#CBOE:OEX-symbol-description'] = ['Índice S&P100']),
        (o.exports['#NASDAQ:SOX-symbol-description'] = [
          'Índice de Semicondutores de Filadélfia',
        ]),
        (o.exports['#RUSSELL:RUI-symbol-description'] = ['Índice Russell1000']),
        (o.exports['#RUSSELL:RUA-symbol-description'] = [
          'Índice Russell 3000',
        ]),
        (o.exports['#RUSSELL:RUT-symbol-description'] = [
          'Índice Russell 2000',
        ]),
        (o.exports['#NYSE:XMI-symbol-description'] = [
          'Índice NYSE ARCA Maiores Mercados',
        ]),
        (o.exports['#NYSE:XAX-symbol-description'] = ['Índice AMEX Composto']),
        (o.exports['#NASDAQ:NDX-symbol-description'] = ['Índice Nasdaq 100']),
        (o.exports['#NASDAQ:IXIC-symbol-description'] = [
          'Índice Nasdaq Composite',
        ]),
        (o.exports['#DJ:DJT-symbol-description'] = [
          'Índice Médio de Transportes Dow Jones',
        ]),
        (o.exports['#NYSE:NYA-symbol-description'] = ['Índice NYSE Composto']),
        (o.exports['#NYMEX:CJ1!-symbol-description'] = ['Futuros de Cacau']),
        (o.exports['#USDILS-symbol-description'] = [
          'Dólar dos EUA/Shekel israelense',
        ]),
        (o.exports['#TSXV:F-symbol-description'] = 'Fiore Gold Inc'),
        (o.exports['#SIX:F-symbol-description'] = 'Ford Motor Company'),
        (o.exports['#BMV:F-symbol-description'] = 'Ford Motor Company'),
        (o.exports['#TWII-symbol-description'] = [
          'Índice Ponderado de Taiwan',
        ]),
        (o.exports['#TVC:PL10Y-symbol-description'] = [
          'Obrigações de Dez Anos do Governo Polonês',
        ]),
        (o.exports['#TVC:PL05Y-symbol-description'] = [
          'Obrigações de Cinco Anos do Governo Polonês',
        ]),
        (o.exports['#SET:GC-symbol-description'] = [
          'Global Connection Public Company',
        ]),
        (o.exports['#TSX:GC-symbol-description'] =
          'Great Canadian Gaming Corporation'),
        (o.exports['#TVC:FTMIB-symbol-description'] =
          'Milano Italia Borsa Index'),
        (o.exports['#OANDA:SPX500USD-symbol-description'] = ['Índice S&P 500']),
        (o.exports['#BMV:CT-symbol-description'] = 'China SX20 RT'),
        (o.exports['#TSXV:CT-symbol-description'] =
          'Centenera Mining Corporation'),
        (o.exports['#BYBIT:ETHUSD-symbol-description'] = [
          'ETHUSD Contrato Perpétuo',
        ]),
        (o.exports['#BYBIT:XRPUSD-symbol-description'] = [
          'XRPUSD Contrato Perpétuo',
        ]),
        (o.exports['#BYBIT:BTCUSD-symbol-description'] = [
          'BTCUSD Contrato Perpétuo',
        ]),
        (o.exports['#BITMEX:ETHUSD-symbol-description'] = [
          'ETHUSD Contrato Futuro Perpétuo',
        ]),
        (o.exports['#DERIBIT:BTCUSD-symbol-description'] = [
          'BTCUSD Contrato Futuro Perpétuo',
        ]),
        (o.exports['#DERIBIT:ETHUSD-symbol-description'] = [
          'ETHUSD Contrato Futuro Perpétuo',
        ]),
        (o.exports['#USDHUF-symbol-description'] = [
          'Dólar EUA / Florim Húngaro',
        ]),
        (o.exports['#USDTHB-symbol-description'] = [
          'Dólar EUA / Baht Tailandês',
        ]),
        (o.exports['#FOREXCOM:US2000-symbol-description'] =
          'US Small Cap 2000'),
        (o.exports['#TSXV:PBR-symbol-description'] = 'Para Resources Inc'),
        (o.exports['#NYSE:SI-symbol-description'] =
          'Silvergate Capital Corporation'),
        (o.exports['#NASDAQ:LE-symbol-description'] = "Lands' End Inc"),
        (o.exports['#CME:CB1!-symbol-description'] = [
          'Futuros de Manteiga (Contínuo: Contrato atual à frente)',
        ]),
        (o.exports['#LSE:SCHO-symbol-description'] = [
          'Grupo Scholium Plc Ord 1P',
        ]),
        (o.exports['#NEO:HE-symbol-description'] =
          'Hanwei Energy Services Corp.'),
        (o.exports['#NYSE:HE-symbol-description'] =
          'Hawaiian Electric Industries'),
        (o.exports['#OMXCOP:SCHO-symbol-description'] = 'Schouw & Co A/S'),
        (o.exports['#TSX:HE-symbol-description'] =
          'Hanwei Energy Services Corp.'),
        (o.exports['#BSE:ITI-symbol-description'] = 'ITI Ltd'),
        (o.exports['#NSE:ITI-symbol-description'] =
          'Indian Telephone Industries Limited'),
        (o.exports['#TSX:LS-symbol-description'] =
          'Middlefield Healthcare & Life Sciences Dividend Fund'),
        (o.exports['#BITMEX:XBT-symbol-description'] = [
          'Índice Bitcoin/Dólar US',
        ]),
        (o.exports['#CME_MINI:RTY1!-symbol-description'] = [
          'Índice Futuro E-Mini Russell 2000',
        ]),
        (o.exports['#CRYPTOCAP:TOTAL-symbol-description'] = [
          'Valor Total do Mercado de Criptomoedas, $',
        ]),
        (o.exports['#ICEUS:DX1!-symbol-description'] = [
          'Índice Futuro Dólar US',
        ]),
        (o.exports['#NYMEX:TT1!-symbol-description'] = ['Futuros de Algodão']),
        (o.exports['#PHEMEX:BTCUSD-symbol-description'] = [
          'Contrato Futuro Perpétuo BTC',
        ]),
        (o.exports['#PHEMEX:ETHUSD-symbol-description'] = [
          'Contrato Futuro Perpétuo ETH',
        ]),
        (o.exports['#PHEMEX:XRPUSD-symbol-description'] = [
          'Contrato Futuro Perpétuo XRP',
        ]),
        (o.exports['#PHEMEX:LTCUSD-symbol-description'] = [
          'Contrato Futuro Perpétuo LTC',
        ]),
        (o.exports['#BITCOKE:BCHUSD-symbol-description'] = ['Quanto Swap BCH']),
        (o.exports['#BITCOKE:BTCUSD-symbol-description'] = ['Quanto Swap BTC']),
        (o.exports['#BITCOKE:ETHUSD-symbol-description'] = ['Quanto Swap ETH']),
        (o.exports['#BITCOKE:LTCUSD-symbol-description'] = ['Quanto Swap LTC']),
        (o.exports['#TVC:CA10-symbol-description'] = [
          'Títulos do Governo Canadense, 10 anos',
        ]),
        (o.exports['#TVC:CA10Y-symbol-description'] = [
          'Títulos do Governo Canadense Rendimento 10 anos',
        ]),
        (o.exports['#TVC:ID10Y-symbol-description'] = [
          'Títulos do Governo Indonésio Rendimento 10 anos',
        ]),
        (o.exports['#TVC:NL10-symbol-description'] = [
          'Títulos do Governo Holandês, 10 anos',
        ]),
        (o.exports['#TVC:NL10Y-symbol-description'] = [
          'Títulos do Governo Holandês Rendimento 10 anos',
        ]),
        (o.exports['#TVC:NZ10-symbol-description'] = [
          'Títulos do Governo Nova Zelândia, 10 anos',
        ]),
        (o.exports['#TVC:NZ10Y-symbol-description'] = [
          'Títulos do Governo Nova Zenlândia Rendimentos 10 anos',
        ]),
        (o.exports['#SOLUSD-symbol-description'] = 'Solana / U.S. Dollar'),
        (o.exports['#LUNAUSD-symbol-description'] = 'Luna / U.S. Dollar'),
        (o.exports['#UNIUSD-symbol-description'] = 'Uniswap / U.S. Dollar'),
        (o.exports['#LTCBRL-symbol-description'] = [
          'Litecoin / Real Brasileiro',
        ]),
        (o.exports['#ETCEUR-symbol-description'] = 'Ethereum Classic / Euro'),
        (o.exports['#ETHKRW-symbol-description'] = ['Ethereum / Won Coreano']),
        (o.exports['#BTCRUB-symbol-description'] = ['Bitcoin / Rublo Russo']),
        (o.exports['#BTCTHB-symbol-description'] = [
          'Bitcoin / Baht Tailandês',
        ]),
        (o.exports['#ETHTHB-symbol-description'] = [
          'Ethereum / Baht tailandês',
        ]),
        (o.exports['#TVC:EU10YY-symbol-description'] = [
          'Títulos do Governo Europeu 10 A Yield',
        ]);
    },
  },
]);
