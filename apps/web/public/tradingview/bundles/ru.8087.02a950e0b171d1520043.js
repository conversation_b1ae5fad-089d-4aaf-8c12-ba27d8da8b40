(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (t) => {
      t.exports = ['Добавить'];
    },
    53585: (t) => {
      t.exports = ['Добавить свой цвет'];
    },
    81865: (t) => {
      t.exports = ['Прозрачность'];
    },
    73755: (t) => {
      t.exports = ['Другой символ'];
    },
    16936: (t) => {
      t.exports = ['Назад'];
    },
    88046: (t) => {
      t.exports = ['Главный символ графика'];
    },
    9898: (t) => {
      t.exports = ['Право на акцию'];
    },
    20036: (t) => {
      t.exports = ['Отмена'];
    },
    23398: (t) => {
      t.exports = ['Сменить инструмент'];
    },
    94551: (t) => {
      t.exports = ['График'];
    },
    64498: (t) => {
      t.exports = ['Все источники'];
    },
    73226: (t) => {
      t.exports = ['Применить'];
    },
    79852: (t) => {
      t.exports = ['Облигации'];
    },
    56095: (t) => {
      t.exports = ['Уменьшить'];
    },
    29601: (t) => {
      t.exports = ['Описание'];
    },
    46812: (t) => {
      t.exports = ['Увеличить'];
    },
    89298: (t) => {
      t.exports = ['Смещение'];
    },
    68988: (t) => {
      t.exports = ['Ок'];
    },
    29673: (t) => {
      t.exports = ['Нет подходящих бирж'];
    },
    41379: (t) => {
      t.exports = ['Нет подходящих символов'];
    },
    35563: (t) => {
      t.exports = ['Неверный формат числа.'];
    },
    19724: (t) => {
      t.exports = ['Источники'];
    },
    59877: (t) => {
      t.exports = [
        'Установить время и цену {inputInline} для {studyShortDescription}',
      ];
    },
    18571: (t) => {
      t.exports = ['Установить время {inputTitle} для {studyShortDescription}'];
    },
    58552: (t) => {
      t.exports = ['Установить цену {inputTitle} для {studyShortDescription}'];
    },
    80481: (t) => {
      t.exports = ['Задать время и цену для "{studyShortDescription}"'];
    },
    42917: (t) => {
      t.exports = ['Задать время для "{studyShortDescription}"'];
    },
    6083: (t) => {
      t.exports = ['Задать цену для "{studyShortDescription}"'];
    },
    52298: (t) => {
      t.exports = ['Поиск'];
    },
    13269: (t) => {
      t.exports = ['Выбрать источник'];
    },
    2607: (t) => {
      t.exports = [
        'Указанное значение больше допустимого максимума для {max}.',
      ];
    },
    53669: (t) => {
      t.exports = ['Указанное значение меньше допустимого минимума для {min}.'];
    },
    89053: (t) => {
      t.exports = ['Инструмент'];
    },
    48490: (t) => {
      t.exports = ['Инструмент и описание'];
    },
    99983: (t) => {
      t.exports = ['Поиск инструментов'];
    },
    54336: (t) => {
      t.exports = ['Удалить цвет'];
    },
    60142: (t) => {
      t.exports = ['Толщина'];
    },
    87592: (t) => {
      t.exports = 'cfd';
    },
    17023: (t) => {
      t.exports = ['изменение прозрачности'];
    },
    13066: (t) => {
      t.exports = ['изменение цвета'];
    },
    95657: (t) => {
      t.exports = ['изменение толщины'];
    },
    18567: (t) => {
      t.exports = ['изменение свойств {propertyName}'];
    },
    36962: (t) => {
      t.exports = ['закр'];
    },
    8448: (t) => {
      t.exports = ['криптовалюты'];
    },
    1328: (t) => {
      t.exports = ['Депоз. расписки'];
    },
    88720: (t) => {
      t.exports = ['экономические данные'];
    },
    39512: (t) => {
      t.exports = ['форекс'];
    },
    81859: (t) => {
      t.exports = ['фьючерсы'];
    },
    39337: (t) => {
      t.exports = ['макс.'];
    },
    91815: (t) => {
      t.exports = ['МаксМин2'];
    },
    40771: (t) => {
      t.exports = ['МаксМинЗакр3'];
    },
    9523: (t) => {
      t.exports = ['МаксМинЗакрЗакр4'];
    },
    12754: (t) => {
      t.exports = ['индекс'];
    },
    38071: (t) => {
      t.exports = ['индексы'];
    },
    12504: (t) => {
      t.exports = ['ОткрМаксМинЗакр4'];
    },
    38466: (t) => {
      t.exports = ['откр.'];
    },
    3919: (t) => {
      t.exports = ['мин.'];
    },
    36931: (t) => {
      t.exports = ['акция'];
    },
  },
]);
