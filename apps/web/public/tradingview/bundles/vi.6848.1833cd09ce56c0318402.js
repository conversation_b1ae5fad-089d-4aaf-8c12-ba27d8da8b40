(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (t) => {
      t.exports = ['Thêm'];
    },
    53585: (t) => {
      t.exports = ['Thêm màu tùy chỉnh'];
    },
    81865: (t) => {
      t.exports = ['Độ mờ'];
    },
    60558: (t) => {
      t.exports = ['động vật và thiên nhiên'];
    },
    14232: (t) => {
      t.exports = ['hoạt động'];
    },
    35305: (t) => {
      t.exports = ['đồ ăn & đồ uống'];
    },
    49546: (t) => {
      t.exports = ['gắn cờ'];
    },
    72302: (t) => {
      t.exports = ['các đối tượng'];
    },
    96330: (t) => {
      t.exports = ['nụ cười và mọi người'];
    },
    6878: (t) => {
      t.exports = ['mã giao dịch'];
    },
    15426: (t) => {
      t.exports = ['Sử dụng gần đây'];
    },
    15395: (t) => {
      t.exports = ['du lịch & địa điểm'];
    },
    73755: (t) => {
      t.exports = ['Mã giao dịch khác'];
    },
    16936: (t) => {
      t.exports = ['Quay lại'];
    },
    88046: (t) => {
      t.exports = ['Mã giao dịch biểu đồ chính'];
    },
    9898: (t) => {
      t.exports = ['Phải'];
    },
    20036: (t) => {
      t.exports = ['Hủy bỏ'];
    },
    72171: (t) => {
      t.exports = ['Trung tâm'];
    },
    23398: (t) => {
      t.exports = ['Thay đổi mã giao dịch'];
    },
    94551: (t) => {
      t.exports = ['Biểu đồ'];
    },
    64498: (t) => {
      t.exports = ['Tất cả các nguồn'];
    },
    91757: (t) => {
      t.exports = ['Đáy'];
    },
    79852: (t) => {
      t.exports = ['Trái phiếu'];
    },
    16079: (t) => {
      t.exports = 'Gradient';
    },
    42973: (t) => {
      t.exports = ['Đường chấm chấm'];
    },
    59317: (t) => {
      t.exports = ['Đường Đứt nét'];
    },
    56095: (t) => {
      t.exports = ['Giảm'];
    },
    29601: (t) => {
      t.exports = ['Mô tả'];
    },
    77405: (t) => {
      t.exports = ['Ngang'];
    },
    46812: (t) => {
      t.exports = ['Tăng'];
    },
    89298: (t) => {
      t.exports = ['Bù đắp'];
    },
    68988: (t) => {
      t.exports = 'Ok';
    },
    19286: (t) => {
      t.exports = ['Bên trái'];
    },
    76476: (t) => {
      t.exports = ['Giữa'];
    },
    29673: (t) => {
      t.exports = ['Không có sàn giao dịch nào khớp với yêu cầu của bạn'];
    },
    41379: (t) => {
      t.exports = ['Không có mã giao dịch nào khớp với tiêu chí của bạn'];
    },
    55362: (t) => {
      t.exports = ['Bình thường'];
    },
    35563: (t) => {
      t.exports = ['Định dạng số không hợp lệ.'];
    },
    19724: (t) => {
      t.exports = ['Nguồn'];
    },
    35637: (t) => {
      t.exports = 'Solid';
    },
    52298: (t) => {
      t.exports = ['Tìm kiếm'];
    },
    13269: (t) => {
      t.exports = ['Chọn nguồn'];
    },
    2607: (t) => {
      t.exports = [
        'Giá trị đã chỉ định lớn hơn giá trị tối đa của công cụ là {max}.',
      ];
    },
    53669: (t) => {
      t.exports = [
        'Giá trị đã chỉ định nhỏ hơn giá trị tối thiểu của công cụ là {min}.',
      ];
    },
    89053: (t) => {
      t.exports = ['Mã'];
    },
    48490: (t) => {
      t.exports = ['Mã giao dịch & mô tả'];
    },
    99983: (t) => {
      t.exports = ['Tìm kiếm Mã giao dịch'];
    },
    54336: (t) => {
      t.exports = ['Bỏ màu'];
    },
    21141: (t) => {
      t.exports = ['Phải'];
    },
    65994: (t) => {
      t.exports = ['Trên đầu'];
    },
    92960: (t) => {
      t.exports = ['Căn chỉnh chữ'];
    },
    90581: (t) => {
      t.exports = ['Chiều của chữ'];
    },
    60142: (t) => {
      t.exports = ['Độ dày'];
    },
    78019: (t) => {
      t.exports = [
        'Sử dụng các dấu hiệu toán học đặc biệt để thay thế các bản vẽ đã chọn: +, -, /, * cho giá và +, - cho chỉ số thanh.',
      ];
    },
    44085: (t) => {
      t.exports = ['Dọc'];
    },
    87592: (t) => {
      t.exports = 'cfd';
    },
    17023: (t) => {
      t.exports = ['Thay đổi Độ mờ'];
    },
    13066: (t) => {
      t.exports = ['Thay đổi Màu sắc'];
    },
    95657: (t) => {
      t.exports = ['Thay đổi Độ dày'];
    },
    18567: (t) => {
      t.exports = ['thay đổi thuộc tính {propertyName}'];
    },
    36962: (t) => {
      t.exports = ['đóng cửa'];
    },
    8448: (t) => {
      t.exports = ['tiền điện tử'];
    },
    1328: (t) => {
      t.exports = 'dr';
    },
    76080: (t) => {
      t.exports = 'e.g. +1';
    },
    95166: (t) => {
      t.exports = 'e.g. /2';
    },
    88720: (t) => {
      t.exports = ['kinh tế'];
    },
    39512: (t) => {
      t.exports = 'forex';
    },
    81859: (t) => {
      t.exports = ['hợp đồng tương lai'];
    },
    39337: (t) => {
      t.exports = ['cao'];
    },
    91815: (t) => {
      t.exports = 'hl2';
    },
    40771: (t) => {
      t.exports = 'hlc3';
    },
    9523: (t) => {
      t.exports = 'hlcc4';
    },
    12754: (t) => {
      t.exports = ['chỉ số'];
    },
    38071: (t) => {
      t.exports = ['các chỉ báo'];
    },
    12504: (t) => {
      t.exports = 'ohlc4';
    },
    38466: (t) => {
      t.exports = ['mở cửa'];
    },
    3919: (t) => {
      t.exports = ['thấp'];
    },
    36931: (t) => {
      t.exports = ['cổ phiếu'];
    },
  },
]);
