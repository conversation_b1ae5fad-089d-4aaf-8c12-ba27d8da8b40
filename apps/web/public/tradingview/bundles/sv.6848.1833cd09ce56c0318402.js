(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (r) => {
      r.exports = ['Lägg till'];
    },
    53585: (r) => {
      r.exports = ['Lägg till en anpassad färg'];
    },
    81865: (r) => {
      r.exports = ['Opacitet'];
    },
    60558: (r) => {
      r.exports = ['djur & natur'];
    },
    14232: (r) => {
      r.exports = ['aktiviteter'];
    },
    35305: (r) => {
      r.exports = ['mat & dryck'];
    },
    49546: (r) => {
      r.exports = ['flaggor'];
    },
    72302: (r) => {
      r.exports = ['objekt'];
    },
    96330: (r) => {
      r.exports = ['leenden & människor'];
    },
    6878: (r) => {
      r.exports = ['symboler'];
    },
    15426: (r) => {
      r.exports = ['nyligen använd'];
    },
    15395: (r) => {
      r.exports = ['resor & platser'];
    },
    73755: (r) => {
      r.exports = ['Annan symbol'];
    },
    16936: (r) => {
      r.exports = ['Tillbaka'];
    },
    88046: (r) => {
      r.exports = ['Huvuddiagramsymbol'];
    },
    9898: (r) => {
      r.exports = ['Höger'];
    },
    20036: (r) => {
      r.exports = ['Avbryt'];
    },
    72171: (r) => {
      r.exports = ['Centrera'];
    },
    23398: (r) => {
      r.exports = ['Ändra tickersymbol'];
    },
    94551: (r) => {
      r.exports = ['Diagram'];
    },
    64498: (r) => {
      r.exports = ['Samtliga källor'];
    },
    91757: (r) => {
      r.exports = ['Botten'];
    },
    79852: (r) => {
      r.exports = ['Obligation'];
    },
    16079: (r) => {
      r.exports = ['Lutning'];
    },
    42973: (r) => {
      r.exports = ['Prickad linje'];
    },
    59317: (r) => {
      r.exports = ['Streckad linje'];
    },
    56095: (r) => {
      r.exports = ['Minska'];
    },
    29601: (r) => {
      r.exports = ['Beskrivning'];
    },
    77405: (r) => {
      r.exports = ['Vågrät'];
    },
    46812: (r) => {
      r.exports = ['Öka'];
    },
    89298: (r) => {
      r.exports = ['Kompensation'];
    },
    68988: (r) => {
      r.exports = ['OK'];
    },
    19286: (r) => {
      r.exports = ['Vänster'];
    },
    76476: (r) => {
      r.exports = ['Mitten'];
    },
    29673: (r) => {
      r.exports = ['Inga börser matchar dina kriterier'];
    },
    41379: (r) => {
      r.exports = ['Inga symboler matchar dina kriterier'];
    },
    55362: (r) => {
      r.exports = 'Normal';
    },
    35563: (r) => {
      r.exports = ['Nummerformatet är felaktigt.'];
    },
    19724: (r) => {
      r.exports = ['Källor'];
    },
    35637: (r) => {
      r.exports = 'Solid';
    },
    52298: (r) => {
      r.exports = ['Sök'];
    },
    13269: (r) => {
      r.exports = ['Välj källa'];
    },
    2607: (r) => {
      r.exports = [
        'Det angivna värdet är större än instrumentets minimum av {max}.',
      ];
    },
    53669: (r) => {
      r.exports = [
        'Det angivna värdet är mindre än instrumentets minimum av {min}.',
      ];
    },
    89053: (r) => {
      r.exports = 'Symbol';
    },
    48490: (r) => {
      r.exports = ['Symbol & beskrivning'];
    },
    99983: (r) => {
      r.exports = ['Symbolsök'];
    },
    54336: (r) => {
      r.exports = ['Ta bort färg'];
    },
    21141: (r) => {
      r.exports = ['Höger'];
    },
    65994: (r) => {
      r.exports = ['Topp'];
    },
    92960: (r) => {
      r.exports = ['Textjustering'];
    },
    90581: (r) => {
      r.exports = ['Textriktning'];
    },
    60142: (r) => {
      r.exports = ['Tjocklek'];
    },
    78019: (r) => {
      r.exports = [
        'Använd särskilda matematiska tecken för att förflytta valda ritningar: +,-,/,* för pris och +,- för stapelindex.',
      ];
    },
    44085: (r) => {
      r.exports = ['Lodrät'];
    },
    87592: (r) => {
      r.exports = 'cfd';
    },
    17023: (r) => {
      r.exports = ['Ändra opacitet'];
    },
    13066: (r) => {
      r.exports = ['Ändra färg'];
    },
    95657: (r) => {
      r.exports = ['Ändra tjocklek'];
    },
    18567: (r) => {
      r.exports = ['ändra egenskapen {propertyName}'];
    },
    36962: (r) => {
      r.exports = ['stängning'];
    },
    8448: (r) => {
      r.exports = ['krypto'];
    },
    1328: (r) => {
      r.exports = 'dr';
    },
    76080: (r) => {
      r.exports = ['t. ex. +1'];
    },
    95166: (r) => {
      r.exports = ['t. ex. /2'];
    },
    88720: (r) => {
      r.exports = ['ekonomi'];
    },
    39512: (r) => {
      r.exports = ['valutor'];
    },
    81859: (r) => {
      r.exports = ['terminer'];
    },
    39337: (r) => {
      r.exports = ['högsta'];
    },
    91815: (r) => {
      r.exports = 'hl2';
    },
    40771: (r) => {
      r.exports = 'hlc3';
    },
    9523: (r) => {
      r.exports = 'hlcc4';
    },
    12754: (r) => {
      r.exports = 'index';
    },
    38071: (r) => {
      r.exports = ['index'];
    },
    12504: (r) => {
      r.exports = 'ohlc4';
    },
    38466: (r) => {
      r.exports = ['öppning'];
    },
    3919: (r) => {
      r.exports = ['lägsta'];
    },
    36931: (r) => {
      r.exports = ['aktier'];
    },
  },
]);
