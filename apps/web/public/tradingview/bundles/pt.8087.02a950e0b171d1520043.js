(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (o) => {
      o.exports = ['Adicionar'];
    },
    53585: (o) => {
      o.exports = ['Adicionar cor customizada'];
    },
    81865: (o) => {
      o.exports = ['Opacidade'];
    },
    73755: (o) => {
      o.exports = ['Outro Símbolo'];
    },
    16936: (o) => {
      o.exports = ['Voltar'];
    },
    88046: (o) => {
      o.exports = ['Símbolo do gráfico principal'];
    },
    9898: (o) => {
      o.exports = ['Direita'];
    },
    20036: (o) => {
      o.exports = ['Cancelar'];
    },
    23398: (o) => {
      o.exports = ['Mudar símbolo'];
    },
    94551: (o) => {
      o.exports = ['Gráfico'];
    },
    64498: (o) => {
      o.exports = ['Todas as fontes'];
    },
    73226: (o) => {
      o.exports = ['Aplicar'];
    },
    79852: (o) => {
      o.exports = ['Título'];
    },
    56095: (o) => {
      o.exports = ['Diminuir'];
    },
    29601: (o) => {
      o.exports = ['Descrição'];
    },
    46812: (o) => {
      o.exports = ['Aumentar'];
    },
    89298: (o) => {
      o.exports = ['Desvio'];
    },
    68988: (o) => {
      o.exports = 'Ok';
    },
    29673: (o) => {
      o.exports = ['Nenhuma bolsa corresponde ao seu critério'];
    },
    41379: (o) => {
      o.exports = ['Nenhum símbolo compatível com seu critério'];
    },
    35563: (o) => {
      o.exports = ['O formato numérico é inválido.'];
    },
    19724: (o) => {
      o.exports = ['Fontes'];
    },
    59877: (o) => {
      o.exports = [
        'Definir o preço e o tempo de "{inputInline}" para "{studyShortDescription}"',
      ];
    },
    18571: (o) => {
      o.exports = [
        'Definir o tempo de "{inputTitle}" para "{studyShortDescription}"',
      ];
    },
    58552: (o) => {
      o.exports = [
        'Definir o preço de "{inputTitle}" para "{studyShortDescription}"',
      ];
    },
    80481: (o) => {
      o.exports = ['Definir o tempo e preço para "{studyShortDescription}"'];
    },
    42917: (o) => {
      o.exports = ['Definir o tempo para "{studyShortDescription}"'];
    },
    6083: (o) => {
      o.exports = ['Definir o tempo para "{studyShortDescription}"'];
    },
    52298: (o) => {
      o.exports = ['Pesquisar'];
    },
    13269: (o) => {
      o.exports = ['Selecionar fonte'];
    },
    2607: (o) => {
      o.exports = [
        'O valor especificado é maior que o instrumento máximo de {max}.',
      ];
    },
    53669: (o) => {
      o.exports = [
        'O valor especificado é maior que o instrumento máximo de {min}.',
      ];
    },
    89053: (o) => {
      o.exports = ['Símbolo'];
    },
    48490: (o) => {
      o.exports = ['Símbolo & descrição'];
    },
    99983: (o) => {
      o.exports = ['Pesquisa de Símbolo'];
    },
    54336: (o) => {
      o.exports = ['Remover cor'];
    },
    60142: (o) => {
      o.exports = ['Espessura'];
    },
    87592: (o) => {
      o.exports = 'cfd';
    },
    17023: (o) => {
      o.exports = ['Mudar Opacidade'];
    },
    13066: (o) => {
      o.exports = ['Mudar Cor'];
    },
    95657: (o) => {
      o.exports = ['Mudar Espessura'];
    },
    18567: (o) => {
      o.exports = ['alterar propriedade {propertyName}'];
    },
    36962: (o) => {
      o.exports = ['fch'];
    },
    8448: (o) => {
      o.exports = ['Cripto'];
    },
    1328: (o) => {
      o.exports = 'dr';
    },
    88720: (o) => {
      o.exports = ['economia'];
    },
    39512: (o) => {
      o.exports = 'forex';
    },
    81859: (o) => {
      o.exports = ['futuros'];
    },
    39337: (o) => {
      o.exports = ['máx'];
    },
    91815: (o) => {
      o.exports = 'hl2';
    },
    40771: (o) => {
      o.exports = 'hlc3';
    },
    9523: (o) => {
      o.exports = 'hlcc4';
    },
    12754: (o) => {
      o.exports = ['índice'];
    },
    38071: (o) => {
      o.exports = ['índices'];
    },
    12504: (o) => {
      o.exports = 'ohlc4';
    },
    38466: (o) => {
      o.exports = ['abertura'];
    },
    3919: (o) => {
      o.exports = ['mín'];
    },
    36931: (o) => {
      o.exports = ['ação'];
    },
  },
]);
