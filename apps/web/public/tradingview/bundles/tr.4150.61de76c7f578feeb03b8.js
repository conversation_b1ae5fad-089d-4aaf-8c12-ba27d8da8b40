(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4150],
  {
    74274: (e) => {
      e.exports = ['Artı'];
    },
    41596: (e) => {
      e.exports = ['Fiyat ölçeğindeki etiketler'];
    },
    23545: (e) => {
      e.exports = ['Durum satırındaki değerler'];
    },
    39495: (e) => {
      e.exports = ['Daireler'];
    },
    41389: (e) => {
      e.exports = ['Bar Üstü'];
    },
    29520: (e) => {
      e.exports = ['Mutlak'];
    },
    58102: (e) => {
      e.exports = ['Varsayılanları Uygula'];
    },
    65262: (e) => {
      e.exports = ['Kesmeli alan'];
    },
    83760: (e) => {
      e.exports = ['Temel hat'];
    },
    48848: (e) => {
      e.exports = ['Kenar'];
    },
    27331: (e) => {
      e.exports = ['Arkaplan'];
    },
    78626: (e) => {
      e.exports = ['Bar altı'];
    },
    41361: (e) => {
      e.exports = ['Alt'];
    },
    22192: (e) => {
      e.exports = ['Gün'];
    },
    31577: (e) => {
      e.exports = ['Gelişen TA(Takas Aralığı)'];
    },
    4329: (e) => {
      e.exports = ['Varsayılan'];
    },
    98938: (e) => {
      e.exports = ['Varsayılanlar'];
    },
    63099: (e) => {
      e.exports = ['Saat'];
    },
    11091: (e) => {
      e.exports = 'Histogram';
    },
    66304: (e) => {
      e.exports = ['Girdiler'];
    },
    40297: (e) => {
      e.exports = ['Çıktı'];
    },
    36993: (e) => {
      e.exports = ['Fiyatın Min Adımı'];
    },
    64606: (e) => {
      e.exports = ['Etiket Yazı Tipi'];
    },
    54934: (e) => {
      e.exports = ['Kesme çizgi'];
    },
    95543: (e) => {
      e.exports = ['Aylar'];
    },
    28134: (e) => {
      e.exports = ['Dakika'];
    },
    18229: (e) => {
      e.exports = ['Varsayılan olarak sakla'];
    },
    71129: (e) => {
      e.exports = ['Saniye'];
    },
    86520: (e) => {
      e.exports = ['Sinyal etiketleri'];
    },
    79511: (e) => {
      e.exports = ['Adım çizgisi'];
    },
    64108: (e) => {
      e.exports = 'Step line with breaks';
    },
    67767: (e) => {
      e.exports = ['Eşkenar Dörtgenlerle Adım Çizgisi'];
    },
    21861: (e) => {
      e.exports = ['Yerleştirme'];
    },
    73947: (e) => {
      e.exports = ['Hassasiyet'];
    },
    66596: (e) => {
      e.exports = ['Miktar'];
    },
    86672: (e) => {
      e.exports = ['Aralıklar'];
    },
    79782: (e) => {
      e.exports = ['Ayarları Sıfırla'];
    },
    21594: (e) => {
      e.exports = ['Haftalar'];
    },
    26458: (e) => {
      e.exports = ['Fitil'];
    },
    95247: (e) => {
      e.exports = ['Genişlik (Kutudan %)'];
    },
    19221: (e) => {
      e.exports = ['Metin rengi'];
    },
    7138: (e) => {
      e.exports = ['Grafik üzeri işlemler'];
    },
    98802: (e) => {
      e.exports = ['Üst'];
    },
    14414: (e) => {
      e.exports = ['Hacim Profili'];
    },
    91322: (e) => {
      e.exports = ['Değerler'];
    },
    20834: (e) => {
      e.exports = ['en düşük işareti değiştir'];
    },
    98491: (e) => {
      e.exports = ['karakter değiştir'];
    },
    7378: (e) => {
      e.exports = ['yazı büyüklüğünü değiştir'];
    },
    28691: (e) => {
      e.exports = ['çizgi stilini değiştir'];
    },
    38361: (e) => {
      e.exports = ['konum değiştir'];
    },
    51081: (e) => {
      e.exports = ['yüzdelik dilimi değiştir'];
    },
    47634: (e) => {
      e.exports = ['konum değiştir'];
    },
    15683: (e) => {
      e.exports = ['Çizim tipi değiştir'];
    },
    164: (e) => {
      e.exports = ['Hassasiyet değiştir'];
    },
    86888: (e) => {
      e.exports = ['şekil değiştir'];
    },
    50463: (e) => {
      e.exports = ['Değer Değiştir'];
    },
    12628: (e) => {
      e.exports = ['değerlerin görünürlüğünü değiştir'];
    },
    13355: (e) => {
      e.exports = ['{title} gününü dğş'];
    },
    41377: (e) => {
      e.exports = ['{title} gününü dğş'];
    },
    35388: (e) => {
      e.exports = ['{title} saati sonrası dğş'];
    },
    78586: (e) => {
      e.exports = ['{title} saatini dğş'];
    },
    59635: (e) => {
      e.exports = ['{title} ayını değiştir'];
    },
    74266: (e) => {
      e.exports = ['{title} ayını şu şekilde değiştir:'];
    },
    91633: (e) => {
      e.exports = ['{title} dakikayı dğş'];
    },
    15106: (e) => {
      e.exports = ['{title} dkyı itibaren dğş:'];
    },
    66161: (e) => {
      e.exports = ['{title} saniyeyi dğş:'];
    },
    2822: (e) => {
      e.exports = ['{title} saniyesini değiştir'];
    },
    21339: (e) => {
      e.exports = ['{title} haftayı değiştir'];
    },
    68643: (e) => {
      e.exports = ['{title} haftayı dğş'];
    },
    30810: (e) => {
      e.exports = ['sembolde {title} görünümü değiştir'];
    },
    24941: (e) => {
      e.exports = ['{title} görünürlüğünü haftalarda dğş'];
    },
    29088: (e) => {
      e.exports = ['{title} görünümünü günlerde değiştir'];
    },
    68971: (e) => {
      e.exports = ['{title} görünürlüğünü saatte dğş'];
    },
    64370: (e) => {
      e.exports = ['{title} görünümünü dakikada değiştir'];
    },
    6659: (e) => {
      e.exports = ['aylarda {title} görünürlüğünü değiştir'];
    },
    29091: (e) => {
      e.exports = ['aralıklarda {title} görünürlüğünü değiştir'];
    },
    46948: (e) => {
      e.exports = ['{title} görünümünü saniye içinde dğş'];
    },
    82211: (e) => {
      e.exports = ['Gün'];
    },
    33486: (e) => {
      e.exports = ['gün'];
    },
    14077: (e) => {
      e.exports = ['günden itibaren'];
    },
    3143: (e) => {
      e.exports = ['Saat'];
    },
    84775: (e) => {
      e.exports = ['saat'];
    },
    11255: (e) => {
      e.exports = ['saat'];
    },
    58964: (e) => {
      e.exports = ['Aylar'];
    },
    71770: (e) => {
      e.exports = ['aydan itibaren'];
    },
    37179: (e) => {
      e.exports = ['ay'];
    },
    16465: (e) => {
      e.exports = ['dakika'];
    },
    72317: (e) => {
      e.exports = ['dakika'];
    },
    25586: (e) => {
      e.exports = ['dakikadan itibaren'];
    },
    32925: (e) => {
      e.exports = ['saniye'];
    },
    39017: (e) => {
      e.exports = ['saniye'];
    },
    6049: (e) => {
      e.exports = ['saniyeden itibaren'];
    },
    13604: (e) => {
      e.exports = ['Aralıklar'];
    },
    93016: (e) => {
      e.exports = ['hafta'];
    },
    32002: (e) => {
      e.exports = ['haftadan'];
    },
    28091: (e) => {
      e.exports = ['hafta'];
    },
    59523: (e) => {
      e.exports = ['kademeler'];
    },
  },
]);
