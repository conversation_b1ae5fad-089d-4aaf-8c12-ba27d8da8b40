(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6265],
  {
    82335: (e) => {
      e.exports = {
        switcher: 'switcher-Wv0rGnT8',
        'thumb-wrapper': 'thumb-wrapper-Wv0rGnT8',
        'size-small': 'size-small-Wv0rGnT8',
        'size-medium': 'size-medium-Wv0rGnT8',
        'size-large': 'size-large-Wv0rGnT8',
        input: 'input-Wv0rGnT8',
        'intent-default': 'intent-default-Wv0rGnT8',
        'disable-active-state-styles': 'disable-active-state-styles-Wv0rGnT8',
        'intent-select': 'intent-select-Wv0rGnT8',
        track: 'track-Wv0rGnT8',
        thumb: 'thumb-Wv0rGnT8',
      };
    },
    13549: (e) => {
      e.exports = { scrollable: 'scrollable-qk6xG5kt' };
    },
    24959: (e) => {
      e.exports = {
        defaultsButtonText: 'defaultsButtonText-CXKzQOaV',
        defaultsButtonItem: 'defaultsButtonItem-CXKzQOaV',
        defaultsButtonIcon: 'defaultsButtonIcon-CXKzQOaV',
      };
    },
    10986: (e) => {
      e.exports = {
        themesButtonText: 'themesButtonText-ugYgmubN',
        themesButtonIcon: 'themesButtonIcon-ugYgmubN',
        defaultsButtonText: 'defaultsButtonText-ugYgmubN',
        defaultsButtonItem: 'defaultsButtonItem-ugYgmubN',
      };
    },
    15185: (e) => {
      e.exports = { slider: 'slider-n4RgAWzv', inner: 'inner-n4RgAWzv' };
    },
    57248: (e) => {
      e.exports = {
        scrollWrap: 'scrollWrap-Rf5MOAG5',
        tabsWrap: 'tabsWrap-Rf5MOAG5',
        tabs: 'tabs-Rf5MOAG5',
        withoutBorder: 'withoutBorder-Rf5MOAG5',
        tab: 'tab-Rf5MOAG5',
        withHover: 'withHover-Rf5MOAG5',
        headerBottomSeparator: 'headerBottomSeparator-Rf5MOAG5',
        fadeWithoutSlider: 'fadeWithoutSlider-Rf5MOAG5',
        withBadge: 'withBadge-Rf5MOAG5',
      };
    },
    39416: (e) => {
      e.exports = {
        wrap: 'wrap-qKQlcmkd',
        wrapWithArrowsOuting: 'wrapWithArrowsOuting-qKQlcmkd',
        wrapOverflow: 'wrapOverflow-qKQlcmkd',
        scrollWrap: 'scrollWrap-qKQlcmkd',
        noScrollBar: 'noScrollBar-qKQlcmkd',
        icon: 'icon-qKQlcmkd',
        scrollLeft: 'scrollLeft-qKQlcmkd',
        scrollRight: 'scrollRight-qKQlcmkd',
        isVisible: 'isVisible-qKQlcmkd',
        iconWrap: 'iconWrap-qKQlcmkd',
        fadeLeft: 'fadeLeft-qKQlcmkd',
        fadeRight: 'fadeRight-qKQlcmkd',
      };
    },
    45829: (e) => {
      e.exports = {
        separator: 'separator-w5iW5vBm',
        small: 'small-w5iW5vBm',
        normal: 'normal-w5iW5vBm',
        large: 'large-w5iW5vBm',
      };
    },
    86355: (e) => {
      e.exports = {
        tabs: 'tabs-g47ZTMzc',
        tab: 'tab-g47ZTMzc',
        noBorder: 'noBorder-g47ZTMzc',
        disabled: 'disabled-g47ZTMzc',
        active: 'active-g47ZTMzc',
        defaultCursor: 'defaultCursor-g47ZTMzc',
        slider: 'slider-g47ZTMzc',
        content: 'content-g47ZTMzc',
      };
    },
    17946: (e, t, l) => {
      'use strict';
      l.d(t, { CustomBehaviourContext: () => r });
      const r = (0, l(50959).createContext)({ enableActiveStateStyles: !0 });
      r.displayName = 'CustomBehaviourContext';
    },
    45601: (e, t, l) => {
      'use strict';
      l.d(t, { Measure: () => n });
      var r = l(19566);
      function n(e) {
        const { children: t, onResize: l } = e;
        return t((0, r.useResizeObserver)(l || (() => {}), [null === l]));
      }
    },
    4060: (e, t, l) => {
      'use strict';
      l.r(t), l.d(t, { EditObjectDialogRenderer: () => pl });
      var r = l(962),
        n = l(50959),
        o = l(50151),
        i = l(44352),
        s = l(47539),
        a = l(11536),
        c = l(56840),
        p = l(2484),
        d = l(76422),
        h = l(51768),
        u = l(31807),
        m = l(34290),
        v = l(50182),
        y = l(59064),
        g = l(86656),
        b = l(89215),
        w = l(37289),
        f = l(6250),
        C = l(48531),
        S = l(13549);
      class P extends n.PureComponent {
        constructor(e) {
          super(e),
            (this._handleClose = (e) => {
              ((null == e ? void 0 : e.target) &&
                e.target.closest('[data-dialog-name="gopro"]')) ||
                this.props.onClose();
            }),
            (this._renderFooterLeft = (e) => {
              const { source: t, model: l } = this.props;
              if ((0, f.isLineTool)(t))
                return n.createElement(C.FooterMenu, {
                  sources: [t],
                  chartUndoModel: l,
                });
              if ((0, b.isStudy)(t))
                return n.createElement(m.StudyDefaultsManager, {
                  model: l,
                  source: t,
                  mode: e ? 'compact' : 'normal',
                });
              throw new TypeError('Unsupported source type.');
            }),
            (this._handleSelect = (e) => {
              this.setState({ activeTabId: e }, () => {
                this._requestResize && this._requestResize();
              }),
                this.props.onActiveTabChanged &&
                  this.props.onActiveTabChanged(e);
            }),
            (this._handleScroll = () => {
              y.globalCloseDelegate.fire();
            }),
            (this._handleSubmit = () => {
              this.props.onSubmit(), this.props.onClose();
            });
          const { pages: t, initialActiveTab: l } = this.props;
          this.state = { activeTabId: t.allIds.includes(l) ? l : t.allIds[0] };
        }
        render() {
          const { title: e, onCancel: t, onClose: l } = this.props,
            { activeTabId: r } = this.state;
          return n.createElement(v.AdaptiveConfirmDialog, {
            dataName: 'indicator-properties-dialog',
            title: e,
            isOpened: !0,
            onSubmit: this._handleSubmit,
            onCancel: t,
            onClickOutside: this._handleClose,
            onClose: l,
            footerLeftRenderer: this._renderFooterLeft,
            render: this._renderChildren(r),
            submitOnEnterKey: !1,
          });
        }
        _renderChildren(e) {
          return ({ requestResize: t }) => {
            this._requestResize = t;
            const { pages: l, source: r, model: o } = this.props,
              i = l.byId[e],
              s = 'Component' in i ? void 0 : i.page;
            return n.createElement(
              n.Fragment,
              null,
              n.createElement(u.DialogTabs, {
                activeTabId: e,
                onSelect: this._handleSelect,
                tabs: l,
              }),
              n.createElement(
                g.TouchScrollContainer,
                { className: S.scrollable, onScroll: this._handleScroll },
                'Component' in i
                  ? n.createElement(i.Component, { source: r, model: o })
                  : n.createElement(w.PropertiesEditorTab, {
                      page: s,
                      tableKey: e,
                    }),
              ),
            );
          };
        }
      }
      var T = l(8069),
        E = l(46069);
      class _ extends n.PureComponent {
        constructor(e) {
          super(e),
            (this._properties = this.props.source.properties()),
            (this._inputs = new E.MetaInfoHelper(
              this.props.source.metaInfo(),
            ).getUserEditableInputs());
        }
        render() {
          return n.createElement(T.InputsTabContent, {
            property: this._properties,
            model: this.props.model,
            study: this.props.source,
            inputs: this._inputs,
          });
        }
      }
      var k = l(22064),
        x = l(83421),
        I = l(58403),
        L = l(56857),
        R = l(72126),
        V = l(37350);
      const B = new s.TranslatedString(
        'change visibility',
        i.t(null, void 0, l(21511)),
      );
      class M extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onChange = (e) => {
              const { setValue: t } = this.context,
                { visible: l } = this.props;
              l && (0, V.setPropertyValue)(l, (l) => t(l, e, B));
            });
        }
        render() {
          const { id: e, title: t, visible: r, disabled: o } = this.props,
            s = (0, a.clean)(i.t(t, { context: 'input' }, l(88601)), !0);
          return n.createElement(R.BoolInputComponent, {
            label: s,
            disabled: o,
            input: { id: e, type: 'bool', defval: !0, name: 'visible' },
            value: !r || (0, V.getPropertyValue)(r),
            onChange: this._onChange,
          });
        }
      }
      M.contextType = L.StylePropertyContext;
      var D = l(98111),
        N = l(51613),
        W = l(51107),
        A = l(94697),
        z = l(94152),
        G = l(18819),
        H = l(14643),
        O = l(46464),
        F = l(96298),
        U = l(18621),
        q = l(98450),
        K = l(91512),
        Q = l(93976),
        Z = l(72914),
        j = l(21579);
      const X = {
          [x.LineStudyPlotStyle.Line]: {
            type: x.LineStudyPlotStyle.Line,
            order: 0,
            icon: z,
            label: i.t(null, void 0, l(1277)),
          },
          [x.LineStudyPlotStyle.LineWithBreaks]: {
            type: x.LineStudyPlotStyle.LineWithBreaks,
            order: 1,
            icon: G,
            label: i.t(null, void 0, l(54934)),
          },
          [x.LineStudyPlotStyle.StepLine]: {
            type: x.LineStudyPlotStyle.StepLine,
            order: 2,
            icon: H,
            label: i.t(null, void 0, l(79511)),
          },
          [x.LineStudyPlotStyle.StepLineWithBreaks]: {
            type: x.LineStudyPlotStyle.StepLineWithBreaks,
            order: 3,
            icon: O,
            label: i.t(null, void 0, l(64108)),
          },
          [x.LineStudyPlotStyle.StepLineWithDiamonds]: {
            type: x.LineStudyPlotStyle.StepLineWithDiamonds,
            order: 4,
            icon: F,
            label: i.t(null, void 0, l(67767)),
          },
          [x.LineStudyPlotStyle.Histogram]: {
            type: x.LineStudyPlotStyle.Histogram,
            order: 5,
            icon: U,
            label: i.t(null, void 0, l(11091)),
          },
          [x.LineStudyPlotStyle.Cross]: {
            type: x.LineStudyPlotStyle.Cross,
            order: 6,
            icon: q,
            label: i.t(null, { context: 'chart_type' }, l(74274)),
          },
          [x.LineStudyPlotStyle.Area]: {
            type: x.LineStudyPlotStyle.Area,
            order: 7,
            icon: K,
            label: i.t(null, void 0, l(42097)),
          },
          [x.LineStudyPlotStyle.AreaWithBreaks]: {
            type: x.LineStudyPlotStyle.AreaWithBreaks,
            order: 8,
            icon: Q,
            label: i.t(null, void 0, l(65262)),
          },
          [x.LineStudyPlotStyle.Columns]: {
            type: x.LineStudyPlotStyle.Columns,
            order: 9,
            icon: Z,
            label: i.t(null, void 0, l(36018)),
          },
          [x.LineStudyPlotStyle.Circles]: {
            type: x.LineStudyPlotStyle.Circles,
            order: 10,
            icon: j,
            label: i.t(null, void 0, l(39495)),
          },
        },
        Y = Object.values(X)
          .sort((e, t) => e.order - t.order)
          .map((e) => ({
            value: e.type,
            selectedContent: n.createElement(A.DisplayItem, { icon: e.icon }),
            content: n.createElement(A.DropItem, {
              icon: e.icon,
              label: e.label,
            }),
          })),
        $ = i.t(null, void 0, l(91492));
      class J extends n.PureComponent {
        render() {
          const {
            id: e,
            plotType: t,
            className: l,
            priceLine: r,
            plotTypeChange: o,
            priceLineChange: i,
            disabled: s,
          } = this.props;
          if (!(t in X)) return null;
          const a = {
            readonly: !0,
            content: n.createElement(
              n.Fragment,
              null,
              n.createElement(W.MenuItemSwitcher, {
                id: 'PlotTypePriceLineSwitch',
                checked: r,
                label: $,
                preventLabelHighlight: !0,
                value: 'priceLineSwitcher',
                onChange: i,
              }),
              n.createElement(N.PopupMenuSeparator, null),
            ),
          };
          return n.createElement(A.IconDropdown, {
            id: e,
            disabled: s,
            className: l,
            hideArrowButton: !0,
            items: [a, ...Y],
            value: t,
            onChange: o,
          });
        }
      }
      var ee = l(14448),
        te = l(42430);
      const le = new s.TranslatedString(
          'change plot type',
          i.t(null, void 0, l(15683)),
        ),
        re = new s.TranslatedString(
          'change price line visibility',
          i.t(null, void 0, l(67761)),
        );
      class ne extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onPlotTypeChange = (e) => {
              const { setValue: t } = this.context,
                {
                  styleProp: { plottype: l },
                } = this.props;
              l && t(l, e, le);
            }),
            (this._onPriceLineChange = (e) => {
              const { setValue: t } = this.context,
                {
                  styleProp: { trackPrice: l },
                } = this.props;
              l && t(l, e, re);
            });
        }
        render() {
          const {
              id: e,
              paletteColor: t,
              paletteColorProps: r,
              styleProp: o,
              isLine: s,
              hasPlotTypeSelect: a,
              grouped: c,
              offset: p,
            } = this.props,
            d = r.childs();
          return n.createElement(
            D.InputRow,
            {
              grouped: c,
              label: n.createElement(
                'div',
                { className: te.childRowContainer },
                i.t(t.name, { context: 'input' }, l(88601)),
              ),
              offset: p,
            },
            n.createElement(ee.ColorWithThicknessSelect, {
              disabled: !o.visible.value(),
              color: d.color,
              transparency: o.transparency,
              thickness: s ? d.width : void 0,
              isPaletteColor: !0,
            }),
            s && a && o.plottype && o.trackPrice
              ? n.createElement(J, {
                  id: (0, k.createDomId)(e, 'plot-type-select'),
                  disabled: !o.visible.value(),
                  className: te.smallStyleControl,
                  plotType: o.plottype.value(),
                  priceLine: o.trackPrice.value(),
                  plotTypeChange: this._onPlotTypeChange,
                  priceLineChange: this._onPriceLineChange,
                })
              : null,
          );
        }
      }
      ne.contextType = L.StylePropertyContext;
      var oe = l(40296);
      function ie(e, t, l, r, i, s, a) {
        const c = t.colors,
          p = l.colors;
        return Object.keys(c).map((t, l) =>
          n.createElement(ne, {
            key: a ? `${t}-secondary` : t,
            id: e,
            grouped: !0,
            paletteColor: (0, o.ensureDefined)(c[t]),
            paletteColorProps: (0, o.ensureDefined)(p[t]),
            styleProp: r,
            isLine: i,
            hasPlotTypeSelect: 0 === l,
            offset: s,
          }),
        );
      }
      class se extends n.PureComponent {
        render() {
          const {
              plot: e,
              area: t,
              palette: l,
              paletteProps: r,
              hideVisibilitySwitch: i,
              styleProp: s,
              showOnlyTitle: a,
              showSeparator: c = !0,
              offset: p,
              secondaryPalette: d,
              secondaryPaletteProps: h,
            } = this.props,
            u = e ? e.id : (0, o.ensureDefined)(t).id,
            m = !u.startsWith('fill') && e && (0, x.isLinePlot)(e);
          return n.createElement(
            n.Fragment,
            null,
            !i &&
              n.createElement(
                oe.PropertyTable.Row,
                null,
                n.createElement(
                  oe.PropertyTable.Cell,
                  { placement: 'first', colSpan: 2, offset: p },
                  a
                    ? n.createElement(
                        'div',
                        null,
                        t ? t.title : s.title.value(),
                      )
                    : n.createElement(M, {
                        id: u,
                        title: t ? t.title : s.title.value(),
                        visible: s.visible,
                      }),
                ),
              ),
            ie(u, l, r, s, m, p),
            d && h && ie(u, d, h, s, m, p, !0),
            c && n.createElement(oe.PropertyTable.GroupSeparator, null),
          );
        }
      }
      se.contextType = L.StylePropertyContext;
      var ae = l(23460);
      class ce extends n.PureComponent {
        constructor(e) {
          super(e),
            (this._visible = new ae.StudyPlotVisibleProperty(
              e.styleProp.display,
            ));
        }
        render() {
          const {
            plot: e,
            area: t,
            palette: l,
            paletteProps: r,
            hideVisibilitySwitch: o,
            styleProp: i,
            showOnlyTitle: s,
            showSeparator: a = !0,
            offset: c,
          } = this.props;
          return n.createElement(se, {
            plot: e,
            area: t,
            palette: l,
            paletteProps: r,
            styleProp: { ...i, visible: this._visible },
            showSeparator: a,
            hideVisibilitySwitch: o,
            showOnlyTitle: s,
            offset: c,
          });
        }
        componentWillUnmount() {
          this._visible.destroy();
        }
      }
      ce.contextType = L.StylePropertyContext;
      class pe extends n.PureComponent {
        constructor(e) {
          super(e),
            (this._visible = new ae.StudyPlotVisibleProperty(e.display));
        }
        render() {
          const { id: e, title: t, disabled: l } = this.props;
          return n.createElement(M, {
            id: e,
            title: t,
            disabled: l,
            visible: this._visible,
          });
        }
        componentWillUnmount() {
          this._visible.destroy();
        }
      }
      pe.contextType = L.StylePropertyContext;
      var de = l(50890);
      const he = new s.TranslatedString(
          'change plot type',
          i.t(null, void 0, l(15683)),
        ),
        ue = new s.TranslatedString(
          'change price line visibility',
          i.t(null, void 0, l(67761)),
        );
      class me extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onPlotTypeChange = (e) => {
              const { setValue: t } = this.context,
                {
                  property: { plottype: l },
                } = this.props;
              l && t(l, e, he);
            }),
            (this._onPriceLineChange = (e) => {
              const { setValue: t } = this.context,
                {
                  property: { trackPrice: l },
                } = this.props;
              l && t(l, e, ue);
            });
        }
        render() {
          const {
            id: e,
            isRGB: t,
            isFundamental: l,
            property: {
              title: r,
              color: o,
              plottype: i,
              linewidth: s,
              transparency: a,
              trackPrice: c,
              display: p,
            },
          } = this.props;
          return n.createElement(
            D.InputRow,
            {
              label: n.createElement(pe, {
                id: e,
                title: r.value(),
                display: p,
              }),
            },
            t && !l
              ? this._getInputForRgb()
              : n.createElement(ee.ColorWithThicknessSelect, {
                  disabled: 0 === p.value(),
                  color: o,
                  transparency: a,
                  thickness: s,
                }),
            n.createElement(J, {
              id: (0, k.createDomId)(e, 'plot-type-select'),
              disabled: 0 === p.value(),
              className: te.smallStyleControl,
              plotType: i.value(),
              priceLine: c.value(),
              plotTypeChange: this._onPlotTypeChange,
              priceLineChange: this._onPriceLineChange,
            }),
          );
        }
        _getInputForRgb() {
          const { id: e, showLineWidth: t, property: l } = this.props,
            { linewidth: r, display: o } = l;
          return r && t
            ? n.createElement(de.LineWidthSelect, {
                id: (0, k.createDomId)(e, 'line-width-select'),
                property: r,
                disabled: 0 === o.value(),
              })
            : null;
        }
      }
      me.contextType = L.StylePropertyContext;
      const ve = n.createContext(null);
      class ye extends n.PureComponent {
        render() {
          const {
            id: e,
            isRGB: t,
            title: l,
            visible: r,
            color: o,
            transparency: i,
            thickness: s,
            children: a,
            switchable: c = !0,
            offset: p,
            grouped: d,
          } = this.props;
          return n.createElement(
            D.InputRow,
            {
              label: c
                ? n.createElement(M, { id: e, title: l, visible: r })
                : l,
              offset: p,
              grouped: d,
            },
            t
              ? null
              : n.createElement(ee.ColorWithThicknessSelect, {
                  disabled: r && !(Array.isArray(r) ? r[0].value() : r.value()),
                  color: o,
                  transparency: i,
                  thickness: s,
                }),
            a,
          );
        }
      }
      ye.contextType = L.StylePropertyContext;
      class ge extends n.PureComponent {
        constructor(e) {
          super(e),
            (this._visible = new ae.StudyPlotVisibleProperty(e.display));
        }
        render() {
          const {
            id: e,
            isRGB: t,
            title: l,
            color: r,
            transparency: o,
            thickness: i,
            children: s,
            switchable: a = !0,
            offset: c,
            grouped: p,
          } = this.props;
          return n.createElement(ye, {
            id: e,
            isRGB: t,
            title: l,
            color: r,
            transparency: o,
            thickness: i,
            children: s,
            switchable: a,
            offset: c,
            grouped: p,
            visible: this._visible,
          });
        }
        componentWillUnmount() {
          this._visible.destroy();
        }
      }
      ge.contextType = L.StylePropertyContext;
      class be extends n.PureComponent {
        render() {
          const {
            id: e,
            isRGB: t,
            property: { colorup: l, colordown: r, transparency: i, display: s },
          } = this.props;
          return n.createElement(ve.Consumer, null, (a) =>
            n.createElement(
              n.Fragment,
              null,
              n.createElement(
                oe.PropertyTable.Row,
                null,
                n.createElement(
                  oe.PropertyTable.Cell,
                  { placement: 'first', colSpan: 2, grouped: !0 },
                  n.createElement(pe, {
                    id: e,
                    title: je((0, o.ensureNotNull)(a), e),
                    display: s,
                  }),
                ),
              ),
              !t &&
                n.createElement(
                  n.Fragment,
                  null,
                  n.createElement(ge, {
                    id: e,
                    title: Fe,
                    color: l,
                    transparency: i,
                    display: s,
                    switchable: !1,
                    offset: !0,
                    grouped: !0,
                  }),
                  n.createElement(ge, {
                    id: e,
                    title: Ue,
                    color: r,
                    transparency: i,
                    display: s,
                    switchable: !1,
                    offset: !0,
                    grouped: !0,
                  }),
                ),
              n.createElement(oe.PropertyTable.GroupSeparator, null),
            ),
          );
        }
      }
      be.contextType = L.StylePropertyContext;
      var we = l(87795),
        fe = l.n(we),
        Ce = l(97754),
        Se = l.n(Ce),
        Pe = l(31261),
        Te = l(75690),
        Ee = l(89);
      const _e = {
          [Ee.MarkLocation.AboveBar]: {
            value: Ee.MarkLocation.AboveBar,
            content: i.t(null, void 0, l(41389)),
            order: 0,
          },
          [Ee.MarkLocation.BelowBar]: {
            value: Ee.MarkLocation.BelowBar,
            content: i.t(null, void 0, l(78626)),
            order: 1,
          },
          [Ee.MarkLocation.Top]: {
            value: Ee.MarkLocation.Top,
            content: i.t(null, void 0, l(65994)),
            order: 2,
          },
          [Ee.MarkLocation.Bottom]: {
            value: Ee.MarkLocation.Bottom,
            content: i.t(null, void 0, l(91757)),
            order: 3,
          },
          [Ee.MarkLocation.Absolute]: {
            value: Ee.MarkLocation.Absolute,
            content: i.t(null, void 0, l(29520)),
            order: 4,
          },
        },
        ke = Object.values(_e).sort((e, t) => e.order - t.order);
      class xe extends n.PureComponent {
        render() {
          const {
            id: e,
            shapeLocation: t,
            className: l,
            menuItemClassName: r,
            shapeLocationChange: o,
            disabled: i,
          } = this.props;
          return n.createElement(Te.Select, {
            id: e,
            disabled: i,
            className: l,
            menuItemClassName: r,
            items: ke,
            value: t,
            onChange: o,
          });
        }
      }
      const Ie = new s.TranslatedString(
          'change char',
          i.t(null, void 0, l(98491)),
        ),
        Le = new s.TranslatedString(
          'change location',
          i.t(null, void 0, l(38361)),
        );
      class Re extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onCharChange = (e) => {
              const { setValue: t } = this.context,
                l = e.currentTarget.value.trim(),
                r = fe()(l),
                n = 0 === r.length ? '' : r[r.length - 1],
                {
                  property: { char: o },
                } = this.props;
              t(o, n, Ie);
            }),
            (this._onLocationChange = (e) => {
              const { setValue: t } = this.context,
                {
                  property: { location: l },
                } = this.props;
              t(l, e, Le);
            });
        }
        render() {
          const {
            id: e,
            isRGB: t,
            property: {
              title: l,
              color: r,
              transparency: o,
              char: i,
              location: s,
              display: a,
            },
            hasPalette: c,
          } = this.props;
          return n.createElement(
            D.InputRow,
            {
              grouped: c,
              label: n.createElement(pe, {
                id: e,
                title: l.value(),
                display: a,
              }),
            },
            !c &&
              !t &&
              n.createElement(ee.ColorWithThicknessSelect, {
                disabled: 0 === a.value(),
                color: r,
                transparency: o,
              }),
            n.createElement(Pe.InputControl, {
              disabled: 0 === a.value(),
              className: te.smallStyleControl,
              value: i.value(),
              onChange: this._onCharChange,
            }),
            n.createElement(xe, {
              id: (0, k.createDomId)(e, 'shape-style-select'),
              disabled: 0 === a.value(),
              className: Ce(te.defaultSelect, te.additionalSelect),
              menuItemClassName: te.defaultSelectItem,
              shapeLocation: s.value(),
              shapeLocationChange: this._onLocationChange,
            }),
          );
        }
      }
      Re.contextType = L.StylePropertyContext;
      var Ve = l(23387);
      const Be = {
        arrow_down: l(69151),
        arrow_up: l(67211),
        circle: l(83786),
        cross: l(50858),
        diamond: l(13201),
        flag: l(59058),
        label_down: l(8537),
        label_up: l(2309),
        square: l(78240),
        triangle_down: l(41683),
        triangle_up: l(6570),
        x_cross: l(23223),
      };
      function Me(e) {
        return Be[e];
      }
      const De = [];
      Object.keys(Ve.plotShapesData).forEach((e) => {
        const t = Ve.plotShapesData[e];
        De.push({
          id: t.id,
          value: t.id,
          selectedContent: n.createElement(A.DisplayItem, { icon: Me(t.icon) }),
          content: n.createElement(A.DropItem, {
            icon: Me(t.icon),
            label: t.guiName,
          }),
        });
      });
      class Ne extends n.PureComponent {
        render() {
          const {
            id: e,
            shapeStyleId: t,
            className: l,
            shapeStyleChange: r,
            disabled: o,
          } = this.props;
          return n.createElement(A.IconDropdown, {
            id: e,
            disabled: o,
            className: l,
            hideArrowButton: !0,
            items: De,
            value: t,
            onChange: r,
          });
        }
      }
      const We = new s.TranslatedString(
          'change shape',
          i.t(null, void 0, l(86888)),
        ),
        Ae = new s.TranslatedString(
          'change location',
          i.t(null, void 0, l(38361)),
        );
      class ze extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onPlotTypeChange = (e) => {
              const { setValue: t } = this.context,
                {
                  property: { plottype: l },
                } = this.props;
              t(l, e, We);
            }),
            (this._onLocationChange = (e) => {
              const { setValue: t } = this.context,
                {
                  property: { location: l },
                } = this.props;
              t(l, e, Ae);
            });
        }
        render() {
          const {
            id: e,
            isRGB: t,
            hasPalette: l,
            property: {
              title: r,
              color: o,
              transparency: i,
              plottype: s,
              location: a,
              display: c,
            },
          } = this.props;
          return n.createElement(
            D.InputRow,
            {
              grouped: l,
              label: n.createElement(pe, {
                id: e,
                title: r.value(),
                display: c,
              }),
            },
            !l &&
              !t &&
              n.createElement(ee.ColorWithThicknessSelect, {
                disabled: 0 === c.value(),
                color: o,
                transparency: i,
              }),
            n.createElement(Ne, {
              id: (0, k.createDomId)(e, 'shape-style-select'),
              disabled: 0 === c.value(),
              className: te.smallStyleControl,
              shapeStyleId: s.value(),
              shapeStyleChange: this._onPlotTypeChange,
            }),
            n.createElement(xe, {
              id: (0, k.createDomId)(e, 'shape-location-select'),
              disabled: 0 === c.value(),
              className: Ce(te.defaultSelect, te.additionalSelect),
              menuItemClassName: te.defaultSelectItem,
              shapeLocation: a.value(),
              shapeLocationChange: this._onLocationChange,
            }),
          );
        }
      }
      ze.contextType = L.StylePropertyContext;
      var Ge = l(98351),
        He = l(60923);
      const Oe = (0, Ge.getLogger)('Chart.Study.PropertyPage'),
        Fe = i.t(null, void 0, l(98802)),
        Ue = i.t(null, void 0, l(41361)),
        qe = i.t(null, void 0, l(83760)),
        Ke = i.t(null, void 0, l(26458)),
        Qe = i.t(null, void 0, l(48848));
      class Ze extends n.PureComponent {
        render() {
          const { plot: e, palettes: t, study: l } = this.props,
            r = e.id,
            o = l.properties().styles[r],
            i = e.type,
            s = t.main,
            a = !!l.metaInfo().isRGB;
          if ('line' === i || 'bar_colorer' === i || 'bg_colorer' === i)
            return s && s.palette && s.paletteProps
              ? n.createElement(ce, {
                  plot: e,
                  palette: s.palette,
                  paletteProps: s.paletteProps,
                  styleProp: o,
                })
              : n.createElement(me, {
                  id: r,
                  property: o,
                  isRGB: a,
                  isFundamental: false,
                  showLineWidth: 'line' === i,
                });
          if ('arrows' === i) {
            const i = this._getPlotSwitch(r, je(l, r), o.display);
            if (a) return i;
            const s = t.up,
              c = t.down;
            return s || c
              ? n.createElement(
                  n.Fragment,
                  null,
                  i,
                  s && s.palette && s.paletteProps
                    ? n.createElement(ce, {
                        plot: e,
                        palette: s.palette,
                        paletteProps: s.paletteProps,
                        styleProp: {
                          ...o,
                          title: (0, He.createPrimitiveProperty)(Fe),
                        },
                        showSeparator: !1,
                        showOnlyTitle: !0,
                        offset: !0,
                      })
                    : n.createElement(ge, {
                        id: r,
                        isRGB: a,
                        title: Fe,
                        color: o.colorup,
                        display: o.display,
                        transparency: o.transparency,
                        switchable: !1,
                        grouped: !0,
                        offset: !0,
                      }),
                  c && c.palette && c.paletteProps
                    ? n.createElement(ce, {
                        plot: e,
                        palette: c.palette,
                        paletteProps: c.paletteProps,
                        styleProp: {
                          ...o,
                          title: (0, He.createPrimitiveProperty)(Ue),
                        },
                        showSeparator: !1,
                        showOnlyTitle: !0,
                        offset: !0,
                      })
                    : n.createElement(ge, {
                        id: r,
                        isRGB: a,
                        title: Ue,
                        color: o.colordown,
                        display: o.display,
                        transparency: o.transparency,
                        switchable: !1,
                        grouped: !0,
                        offset: !0,
                      }),
                  n.createElement(oe.PropertyTable.GroupSeparator, null),
                )
              : n.createElement(be, {
                  id: r,
                  property: o,
                  isRGB: a,
                  plot: e,
                  palettes: t,
                  styleProp: o,
                });
          }
          if ('chars' === i || 'shapes' === i)
            return n.createElement(
              n.Fragment,
              null,
              'chars' === i
                ? n.createElement(Re, {
                    id: r,
                    property: o,
                    hasPalette: Boolean(s && s.palette),
                    isRGB: a,
                  })
                : n.createElement(ze, {
                    id: r,
                    property: o,
                    hasPalette: Boolean(s && s.palette),
                    isRGB: a,
                  }),
              s &&
                s.palette &&
                s.paletteProps &&
                n.createElement(ce, {
                  plot: e,
                  palette: s.palette,
                  paletteProps: s.paletteProps,
                  hideVisibilitySwitch: !0,
                  styleProp: o,
                }),
            );
          if ((0, x.isOhlcPlot)(e)) {
            const o = e.target,
              i = l.properties().ohlcPlots[o],
              c = this._getPlotSwitch(r, i.title.value(), i.display);
            if (a) return c;
            const p = t.wick && t.wick.palette && t.wick.paletteProps,
              d = t.border && t.border.palette && t.border.paletteProps;
            return n.createElement(
              n.Fragment,
              null,
              c,
              s && s.palette && s.paletteProps
                ? n.createElement(ce, {
                    plot: e,
                    palette: s.palette,
                    paletteProps: s.paletteProps,
                    styleProp: {
                      ...i,
                      title: (0, He.createPrimitiveProperty)(qe),
                    },
                    showSeparator: !1,
                    showOnlyTitle: !0,
                    offset: !0,
                  })
                : n.createElement(ge, {
                    id: r,
                    isRGB: a,
                    title: qe,
                    display: i.display,
                    color: i.color,
                    transparency: i.transparency,
                    switchable: !1,
                    grouped: !0,
                    offset: !0,
                  }),
              t.wick &&
                t.wick.palette &&
                t.wick.paletteProps &&
                n.createElement(ce, {
                  plot: e,
                  palette: t.wick.palette,
                  paletteProps: t.wick.paletteProps,
                  styleProp: {
                    ...i,
                    title: (0, He.createPrimitiveProperty)(Ke),
                  },
                  showSeparator: !1,
                  showOnlyTitle: !0,
                  offset: !0,
                }),
              Boolean(!p && i.wickColor) &&
                n.createElement(ge, {
                  id: r,
                  isRGB: a,
                  title: Ke,
                  display: i.display,
                  color: i.wickColor,
                  transparency: i.transparency,
                  switchable: !1,
                  grouped: !0,
                  offset: !0,
                }),
              t.border &&
                t.border.palette &&
                t.border.paletteProps &&
                n.createElement(ce, {
                  plot: e,
                  palette: t.border.palette,
                  paletteProps: t.border.paletteProps,
                  styleProp: {
                    ...i,
                    title: (0, He.createPrimitiveProperty)(Qe),
                  },
                  showSeparator: !1,
                  showOnlyTitle: !0,
                  offset: !0,
                }),
              Boolean(!d && i.borderColor) &&
                n.createElement(ge, {
                  id: r,
                  isRGB: a,
                  title: Qe,
                  display: i.display,
                  color: i.borderColor,
                  transparency: i.transparency,
                  switchable: !1,
                  grouped: !0,
                  offset: !0,
                }),
              n.createElement(oe.PropertyTable.GroupSeparator, null),
            );
          }
          return Oe.logError('Unknown plot type: ' + i), null;
        }
        _getPlotSwitch(e, t, l) {
          return n.createElement(
            oe.PropertyTable.Row,
            null,
            n.createElement(
              oe.PropertyTable.Cell,
              { placement: 'first', colSpan: 2 },
              n.createElement(pe, { id: e, title: t, display: l }),
            ),
          );
        }
      }
      function je(e, t) {
        const l = (0, o.ensureDefined)(e.metaInfo().styles),
          { title: r } = (0, o.ensureDefined)(l[t]);
        return (0, o.ensureDefined)(r);
      }
      var Xe = l(50866),
        Ye = l(53598);
      const $e = new s.TranslatedString(
        'change line style',
        i.t(null, void 0, l(28691)),
      );
      class Je extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onLineStyleChange = (e) => {
              const { setValue: t } = this.context,
                { lineStyle: l } = this.props;
              (0, V.setPropertyValue)(l, (l) => t(l, e, $e));
            });
        }
        render() {
          const { lineStyle: e, ...t } = this.props;
          return n.createElement(Ye.LineStyleSelect, {
            ...t,
            lineStyle: (0, V.getPropertyValue)(e),
            lineStyleChange: this._onLineStyleChange,
          });
        }
      }
      Je.contextType = L.StylePropertyContext;
      const et = new s.TranslatedString(
        'change value',
        i.t(null, void 0, l(50463)),
      );
      class tt extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onValueChange = (e) => {
              const { setValue: t } = this.context,
                { value: l } = this.props.property;
              t(l, e, et);
            });
        }
        render() {
          const {
            id: e,
            property: {
              name: t,
              color: l,
              linestyle: r,
              linewidth: o,
              transparency: i,
              value: s,
              visible: a,
            },
          } = this.props;
          return n.createElement(
            D.InputRow,
            {
              labelAlign: 'adaptive',
              label: n.createElement(M, {
                id: e,
                title: t.value(),
                visible: a,
              }),
            },
            n.createElement(
              'div',
              { className: te.block },
              n.createElement(
                'div',
                { className: te.group },
                n.createElement(ee.ColorWithThicknessSelect, {
                  disabled: !a.value(),
                  color: l,
                  transparency: i,
                  thickness: o,
                }),
                n.createElement(Je, {
                  id: (0, k.createDomId)(e, 'line-style-select'),
                  disabled: !a.value(),
                  className: te.smallStyleControl,
                  lineStyle: r,
                }),
              ),
              n.createElement(
                'div',
                {
                  className: Ce(
                    te.wrapGroup,
                    te.defaultSelect,
                    te.additionalSelect,
                  ),
                },
                n.createElement(Xe.FloatInputComponent, {
                  input: { id: '', name: '', type: 'float', defval: 0 },
                  value: s.value(),
                  disabled: !a.value(),
                  onChange: this._onValueChange,
                }),
              ),
            ),
          );
        }
      }
      tt.contextType = L.StylePropertyContext;
      class lt extends n.PureComponent {
        render() {
          const {
            orders: { visible: e, showLabels: t, showQty: r },
          } = this.props;
          return n.createElement(
            n.Fragment,
            null,
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first', colSpan: 2 },
                n.createElement(M, {
                  id: 'chart-orders-switch',
                  title: i.t(null, void 0, l(7138)),
                  visible: e,
                }),
              ),
            ),
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first', colSpan: 2 },
                n.createElement(M, {
                  id: 'chart-orders-labels-switch',
                  title: i.t(null, void 0, l(86520)),
                  visible: t,
                }),
              ),
            ),
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first', colSpan: 2 },
                n.createElement(M, {
                  id: 'chart-orders-qty-switch',
                  title: i.t(null, void 0, l(66596)),
                  visible: r,
                }),
              ),
            ),
          );
        }
      }
      lt.contextType = L.StylePropertyContext;
      var rt = l(9822),
        nt = l(3927);
      const ot = new s.TranslatedString(
          'change percent width',
          i.t(null, void 0, l(51081)),
        ),
        it = new s.TranslatedString(
          'change placement',
          i.t(null, void 0, l(47634)),
        ),
        st = new s.TranslatedString(
          'change values visibility',
          i.t(null, void 0, l(12628)),
        ),
        at = [
          {
            value: rt.HHistDirection.LeftToRight,
            content: i.t(null, void 0, l(19286)),
          },
          {
            value: rt.HHistDirection.RightToLeft,
            content: i.t(null, void 0, l(21141)),
          },
        ],
        ct = i.t(null, void 0, l(95247)),
        pt = i.t(null, void 0, l(21861)),
        dt = i.t(null, void 0, l(91322)),
        ht = i.t(null, void 0, l(19221));
      class ut extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onPercentWidthChange = (e) => {
              const { setValue: t } = this.context,
                { percentWidth: l } = this.props.property.childs();
              t(l, e, ot);
            }),
            (this._onPlacementChange = (e) => {
              const { setValue: t } = this.context,
                { direction: l } = this.props.property.childs();
              t(l, e, it);
            }),
            (this._onShowValuesChange = (e) => {
              const { setValue: t } = this.context,
                { showValues: l } = this.props.property.childs();
              t(l, e, st);
            });
        }
        render() {
          const {
            title: e,
            percentWidth: t,
            direction: l,
            showValues: r,
            valuesColor: o,
            visible: i,
          } = this.props.property.childs();
          return n.createElement(
            n.Fragment,
            null,
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first', colSpan: 2, grouped: !0 },
                n.createElement(M, {
                  id: e.value(),
                  title: e.value(),
                  visible: i,
                }),
              ),
            ),
            n.createElement(
              D.InputRow,
              {
                label: n.createElement(
                  'div',
                  { className: te.childRowContainer },
                  ct,
                ),
                grouped: !0,
              },
              n.createElement(nt.IntegerInputComponent, {
                input: { id: '', name: '', type: 'integer', defval: 0 },
                value: t.value(),
                disabled: !i.value(),
                onChange: this._onPercentWidthChange,
              }),
            ),
            n.createElement(
              D.InputRow,
              {
                label: n.createElement(
                  'div',
                  { className: te.childRowContainer },
                  pt,
                ),
                grouped: !0,
              },
              n.createElement(Te.Select, {
                id: 'hhist-graphic-placement-select',
                disabled: !i.value(),
                className: te.defaultSelect,
                menuItemClassName: te.defaultSelectItem,
                items: at,
                value: l.value(),
                onChange: this._onPlacementChange,
              }),
            ),
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                {
                  className: te.childRowContainer,
                  placement: 'first',
                  colSpan: 2,
                  grouped: !0,
                },
                n.createElement(R.BoolInputComponent, {
                  label: dt,
                  input: {
                    id: e.value() + '_showValues',
                    type: 'bool',
                    defval: !0,
                    name: 'visible',
                  },
                  value: !r || r.value(),
                  disabled: !i.value(),
                  onChange: this._onShowValuesChange,
                }),
              ),
            ),
            n.createElement(
              D.InputRow,
              {
                label: n.createElement(
                  'div',
                  { className: te.childRowContainer },
                  ht,
                ),
                grouped: !0,
              },
              n.createElement(ee.ColorWithThicknessSelect, {
                disabled: i && !i.value(),
                color: o,
              }),
            ),
            this._renderColors(),
            n.createElement(oe.PropertyTable.GroupSeparator, null),
          );
        }
        _renderColors() {
          const {
            colors: e,
            titles: t,
            transparencies: l,
            visible: r,
          } = this.props.property.childs();
          return e.childNames().map((o) =>
            n.createElement(
              D.InputRow,
              {
                key: o,
                grouped: !0,
                label: n.createElement(
                  'div',
                  { className: te.childRowContainer },
                  t.childs()[o].value(),
                ),
              },
              n.createElement(ee.ColorWithThicknessSelect, {
                disabled: !r.value(),
                color: e.childs()[o],
                transparency: l.childs()[o],
              }),
            ),
          );
        }
      }
      ut.contextType = L.StylePropertyContext;
      class mt extends n.PureComponent {
        render() {
          const { title: e } = this.props,
            {
              color: t,
              transparency: l,
              width: r,
              style: o,
              visible: i,
            } = this.props.property.childs();
          return n.createElement(
            D.InputRow,
            {
              label: n.createElement(M, {
                id: e.value(),
                title: e.value(),
                visible: i,
              }),
            },
            n.createElement(ee.ColorWithThicknessSelect, {
              disabled: !i.value(),
              color: t,
              transparency: l,
              thickness: r,
            }),
            n.createElement(Je, {
              id: (0, k.createDomId)(e.value(), 'line-style-select'),
              disabled: !i.value(),
              className: te.smallStyleControl,
              lineStyle: o,
            }),
          );
        }
      }
      mt.contextType = L.StylePropertyContext;
      class vt extends n.PureComponent {
        render() {
          const { graphicType: e, study: t } = this.props,
            l = t.metaInfo().graphics,
            r = t.properties().graphics,
            i = (0, o.ensureDefined)(l[e]);
          return Object.keys(i).map((t, l) => {
            const o = r[e][t];
            return 'horizlines' === e || 'vertlines' === e || 'lines' === e
              ? n.createElement(mt, {
                  key: t,
                  title: 'lines' === e ? o.title : o.name,
                  property: o,
                })
              : 'hhists' === e
                ? n.createElement(ut, { key: t, property: o })
                : null;
          });
        }
      }
      var yt = l(66045);
      const gt = new s.TranslatedString(
          'change font size',
          i.t(null, void 0, l(7378)),
        ),
        bt = [10, 11, 12, 14, 16, 20, 24, 28, 32, 40].map((e) => ({
          value: e,
          title: e.toString(),
        }));
      class wt extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onFontSizeChange = (e) => {
              const { setValue: t } = this.context,
                { fontSize: l } = this.props;
              t(l, e, gt);
            });
        }
        render() {
          const { fontSize: e, ...t } = this.props;
          return n.createElement(yt.FontSizeSelect, {
            ...t,
            fontSizes: bt,
            fontSize: e.value(),
            fontSizeChange: this._onFontSizeChange,
          });
        }
      }
      wt.contextType = L.StylePropertyContext;
      const ft = new s.TranslatedString(
          'change visibility',
          i.t(null, void 0, l(21511)),
        ),
        Ct = i.t(null, void 0, l(64606)),
        St = i.t(null, void 0, l(94420)),
        Pt = {
          Traditional: new Set([
            'S5/R5',
            'S4/R4',
            'S3/R3',
            'S2/R2',
            'S1/R1',
            'P',
          ]),
          Fibonacci: new Set(['S3/R3', 'S2/R2', 'S1/R1', 'P']),
          Woodie: new Set(['S4/R4', 'S3/R3', 'S2/R2', 'S1/R1', 'P']),
          Classic: new Set(['S4/R4', 'S3/R3', 'S2/R2', 'S1/R1', 'P']),
          DM: new Set(['S1/R1', 'P']),
          DeMark: new Set(['S1/R1', 'P']),
          Camarilla: new Set(['S4/R4', 'S3/R3', 'S2/R2', 'S1/R1', 'P']),
        };
      class Tt extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onChange = (e) => {
              const { setValue: t } = this.context,
                { levelsStyle: l } = this.props.property.childs(),
                { showLabels: r } = l.childs();
              t(r, e, ft);
            });
        }
        render() {
          const { fontsize: e, levelsStyle: t } = this.props.property.childs();
          return n.createElement(
            n.Fragment,
            null,
            n.createElement(
              D.InputRow,
              {
                labelAlign: 'adaptive',
                label: n.createElement('span', null, Ct),
              },
              n.createElement(
                'div',
                { className: te.block },
                n.createElement(
                  'div',
                  { className: Ce(te.wrapGroup, te.additionalSelect) },
                  n.createElement(wt, {
                    id: 'pivot-points-standard-font-size-select',
                    fontSize: e,
                  }),
                ),
              ),
            ),
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first', colSpan: 2 },
                n.createElement(R.BoolInputComponent, {
                  label: St,
                  input: {
                    id: 'ShowLabels',
                    type: 'bool',
                    defval: !0,
                    name: 'visible',
                  },
                  value: t.childs().showLabels.value(),
                  onChange: this._onChange,
                }),
              ),
            ),
            this._renderColors(),
          );
        }
        _renderColors() {
          const { levelsStyle: e, inputs: t } = this.props.property.childs(),
            { colors: l, widths: r, visibility: i } = e.childs(),
            { kind: s } = t.childs(),
            a = (0, o.ensureDefined)(Pt[s.value()]);
          return l
            .childNames()
            .filter((e) => a.has(e))
            .map((e) =>
              n.createElement(ye, {
                key: e,
                id: e,
                title: e,
                color: l.childs()[e],
                visible: i.childs()[e],
                thickness: r.childs()[e],
              }),
            );
        }
      }
      Tt.contextType = L.StylePropertyContext;
      const Et = new s.TranslatedString(
          'change visibility',
          i.t(null, void 0, l(21511)),
        ),
        _t = i.t(null, void 0, l(14414)),
        kt = i.t(null, void 0, l(91322)),
        xt = i.t(null, void 0, l(95247)),
        It = i.t(null, void 0, l(21861)),
        Lt = i.t(null, void 0, l(31577)),
        Rt = i.t(null, { context: 'input' }, l(23545)),
        Vt = i.t(null, { context: 'input' }, l(41596)),
        Bt = [
          {
            value: rt.HHistDirection.RightToLeft,
            content: i.t(null, void 0, l(21141)),
          },
          {
            value: rt.HHistDirection.LeftToRight,
            content: i.t(null, void 0, l(19286)),
          },
        ];
      class Mt extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onChange = (e) => {
              this._setHhistsProperty('visible', e);
            }),
            (this._onShowValuesChange = (e) => {
              this._setHhistsProperty('showValues', e);
            }),
            (this._onValueChange = (e) => {
              this._setHhistsProperty('percentWidth', e);
            }),
            (this._onDirectionChange = (e) => {
              this._setHhistsProperty('direction', e);
            });
        }
        render() {
          var e, t, r, s, a, c;
          const { metaInfo: p } = this.props,
            {
              graphics: d,
              styles: h,
              showLabelsOnPriceScale: u,
              showLegendValues: m,
            } = this.props.property.childs(),
            { hhists: v, horizlines: y, polygons: g } = d.childs(),
            b = (0, o.ensureDefined)(p.graphics.hhists),
            w = Object.keys(b),
            f = v.childs()[w[0]],
            C = f.childs().visible,
            S = w.map((e) => v.childs()[e].childs().showValues),
            P = f.childs().percentWidth,
            T = f.childs().direction,
            E = w.map((e) => v.childs()[e].childs().valuesColor),
            _ = null === (e = y.childs()) || void 0 === e ? void 0 : e.vahLines,
            k =
              null === (t = p.graphics.horizlines) || void 0 === t
                ? void 0
                : t.vahLines,
            x = null === (r = y.childs()) || void 0 === r ? void 0 : r.valLines,
            I =
              null === (s = p.graphics.horizlines) || void 0 === s
                ? void 0
                : s.valLines,
            L = y.childs().pocLines,
            V = (0, o.ensureDefined)(
              null === (a = p.graphics.horizlines) || void 0 === a
                ? void 0
                : a.pocLines,
            ),
            B = h.childs().developingPoc,
            N = new ae.StudyPlotVisibleProperty(B.childs().display),
            W = (0, o.ensureDefined)(
              null === (c = p.styles) || void 0 === c
                ? void 0
                : c.developingPoc,
            ),
            A = h.childs().developingVAHigh,
            z = new ae.StudyPlotVisibleProperty(A.childs().display),
            G = h.childs().developingVALow,
            H = new ae.StudyPlotVisibleProperty(G.childs().display),
            O = p.graphics.polygons && p.graphics.polygons.histBoxBg;
          return n.createElement(
            n.Fragment,
            null,
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first', colSpan: 2 },
                n.createElement(R.BoolInputComponent, {
                  label: _t,
                  input: {
                    id: 'VolumeProfile',
                    type: 'bool',
                    defval: !0,
                    name: 'visible',
                  },
                  value: C.value(),
                  onChange: this._onChange,
                }),
              ),
            ),
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first' },
                n.createElement(
                  'div',
                  { className: te.childRowContainer },
                  n.createElement(R.BoolInputComponent, {
                    disabled: !C.value(),
                    label: kt,
                    input: {
                      id: 'ShowValues',
                      type: 'bool',
                      defval: !0,
                      name: 'visible',
                    },
                    value: S[0].value(),
                    onChange: this._onShowValuesChange,
                  }),
                ),
              ),
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'last' },
                n.createElement(ee.ColorWithThicknessSelect, {
                  disabled: !C.value() || !S[0].value(),
                  color: E,
                }),
              ),
            ),
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first' },
                n.createElement('div', { className: te.childRowContainer }, xt),
              ),
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'last' },
                n.createElement(nt.IntegerInputComponent, {
                  disabled: !C.value(),
                  input: { id: '', name: '', type: 'integer', defval: 0 },
                  value: P.value(),
                  onChange: this._onValueChange,
                }),
              ),
            ),
            n.createElement(
              oe.PropertyTable.Row,
              null,
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'first' },
                n.createElement('div', { className: te.childRowContainer }, It),
              ),
              n.createElement(
                oe.PropertyTable.Cell,
                { placement: 'last' },
                n.createElement(Te.Select, {
                  id: 'hhist-direction-select',
                  disabled: !C.value(),
                  className: te.defaultSelect,
                  menuItemClassName: te.defaultSelectItem,
                  items: Bt,
                  value: T.value(),
                  onChange: this._onDirectionChange,
                }),
              ),
            ),
            w.map((e) =>
              n.createElement(
                n.Fragment,
                { key: e },
                v
                  .childs()
                  [e].childs()
                  .colors.childNames()
                  .map((t, r) => {
                    const o = b[e];
                    return n.createElement(
                      D.InputRow,
                      {
                        key: r,
                        label: n.createElement(
                          'div',
                          { className: te.childRowContainer },
                          (o &&
                            i.t(o.titles[r], { context: 'input' }, l(88601))) ||
                            '',
                        ),
                      },
                      n.createElement(ee.ColorWithThicknessSelect, {
                        disabled: !C.value(),
                        color: v.childs()[e].childs().colors.childs()[r],
                        transparency: v
                          .childs()
                          [e].childs()
                          .transparencies.childs()[r],
                      }),
                    );
                  }),
              ),
            ),
            k &&
              _ &&
              n.createElement(
                ye,
                {
                  id: 'vahLines',
                  title: k.name,
                  color: _.childs().color,
                  visible: _.childs().visible,
                  thickness: _.childs().width,
                },
                n.createElement(Je, {
                  id: 'vah-lines-line-style-select',
                  disabled: !_.childs().visible.value(),
                  className: te.smallStyleControl,
                  lineStyle: _.childs().style,
                }),
              ),
            I &&
              x &&
              n.createElement(
                ye,
                {
                  id: 'valLines',
                  title: I.name,
                  color: x.childs().color,
                  visible: x.childs().visible,
                  thickness: x.childs().width,
                },
                n.createElement(Je, {
                  id: 'val-lines-line-style-select',
                  disabled: !x.childs().visible.value(),
                  className: te.smallStyleControl,
                  lineStyle: x.childs().style,
                }),
              ),
            n.createElement(
              ye,
              {
                id: 'pocLines',
                title: V.name,
                color: L.childs().color,
                visible: L.childs().visible,
                thickness: L.childs().width,
              },
              n.createElement(Je, {
                id: 'poc-lines-line-style-select',
                disabled: !L.childs().visible.value(),
                className: te.smallStyleControl,
                lineStyle: L.childs().style,
              }),
            ),
            B &&
              n.createElement(
                ye,
                {
                  id: 'developingPoc',
                  title:
                    (W.title && i.t(W.title, { context: 'input' }, l(88601))) ||
                    '',
                  color: B.childs().color,
                  visible: N,
                  thickness: B.childs().linewidth,
                },
                n.createElement(Je, {
                  id: 'developing-poc-line-style-select',
                  disabled: !N.value(),
                  className: te.smallStyleControl,
                  lineStyle: B.childs().linestyle,
                }),
              ),
            A &&
              G &&
              n.createElement(
                ye,
                {
                  id: 'developingPoc',
                  title: Lt,
                  color: [A.childs().color, G.childs().color],
                  visible: [z, H],
                  thickness: [A.childs().linewidth, G.childs().linewidth],
                },
                n.createElement(Je, {
                  id: 'developing-VA-line-style-select',
                  disabled: !z.value() && !H.value(),
                  className: te.smallStyleControl,
                  lineStyle: [A.childs().linestyle, G.childs().linestyle],
                }),
              ),
            g &&
              n.createElement(
                D.InputRow,
                {
                  label: n.createElement(
                    'div',
                    null,
                    (O && i.t(O.name, { context: 'input' }, l(88601))) || '',
                  ),
                },
                n.createElement(ee.ColorWithThicknessSelect, {
                  color: g.childs().histBoxBg.childs().color,
                  transparency: g.childs().histBoxBg.childs().transparency,
                }),
              ),
            'VbPFixed' !== p.shortId &&
              n.createElement(
                n.Fragment,
                null,
                n.createElement(
                  oe.PropertyTable.Cell,
                  { placement: 'first', colSpan: 2 },
                  n.createElement(M, {
                    id: 'showLabelsOnPriceScale',
                    title: Vt,
                    visible: u,
                  }),
                ),
                n.createElement(
                  oe.PropertyTable.Cell,
                  { placement: 'first', colSpan: 2 },
                  n.createElement(M, {
                    id: 'showLegendValues',
                    title: Rt,
                    visible: m,
                  }),
                ),
              ),
          );
        }
        _setHhistsProperty(e, t) {
          const { setValue: l } = this.context,
            { metaInfo: r, property: n } = this.props,
            i = n.childs().graphics.childs().hhists,
            s = Object.keys((0, o.ensureDefined)(r.graphics.hhists));
          for (let r = 0; r < s.length; r++) {
            const n = i.childs()[s[r]].child(e);
            l((0, o.ensureDefined)(n), t, Et);
          }
        }
      }
      function Dt() {
        const e = (0, o.ensureNotNull)((0, n.useContext)(ve)),
          t = e.metaInfo(),
          l = e.properties();
        return n.createElement(Mt, { metaInfo: t, property: l });
      }
      Mt.contextType = L.StylePropertyContext;
      var Nt = l(19243);
      const Wt = {
        VbPFixed: Dt,
        PivotPointsStandard: function () {
          const e = (0, o.ensureNotNull)((0, n.useContext)(ve)).properties();
          return n.createElement(Tt, { property: e });
        },
        VbPVisible: Dt,
      };
      class At extends n.PureComponent {
        render() {
          const e = (0, o.ensureNotNull)(this.context);
          return n.createElement(ve.Consumer, null, (t) =>
            n.createElement(
              L.StylePropertyContainer,
              { property: (0, o.ensureNotNull)(t).properties(), model: e },
              n.createElement(
                oe.PropertyTable,
                null,
                this._renderCustomContent(
                  (0, o.ensureNotNull)(t).metaInfo().shortId,
                ),
              ),
            ),
          );
        }
        _renderCustomContent(e) {
          if (e in Wt) {
            const t = Wt[e];
            return n.createElement(t, null);
          }
          return null;
        }
      }
      At.contextType = Nt.ModelContext;
      var zt = l(50534);
      const Gt = new s.TranslatedString(
          'change precision',
          i.t(null, void 0, l(164)),
        ),
        Ht = i.t(null, void 0, l(4329)),
        Ot = i.t(null, void 0, l(73947)),
        Ft = [{ value: 'default', content: Ht }];
      for (let e = 0; e <= 8; e++) Ft.push({ value: e, content: e.toString() });
      class Ut extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onChange = (e) => {
              const { setValue: t } = this.context,
                { precision: l } = this.props;
              t(l, e, Gt);
            });
        }
        render() {
          const { id: e, precision: t } = this.props;
          return n.createElement(
            D.InputRow,
            { label: Ot },
            n.createElement(Te.Select, {
              id: e,
              className: te.defaultSelect,
              menuItemClassName: te.defaultSelectItem,
              items: Ft,
              value: t.value(),
              onChange: this._onChange,
            }),
          );
        }
      }
      Ut.contextType = L.StylePropertyContext;
      const qt = new s.TranslatedString(
          'change min tick',
          i.t(null, void 0, l(20834)),
        ),
        Kt = i.t(null, void 0, l(4329)),
        Qt = i.t(null, void 0, l(36993)),
        Zt = [
          { priceScale: 1, minMove: 1, frac: !1 },
          { priceScale: 10, minMove: 1, frac: !1 },
          { priceScale: 100, minMove: 1, frac: !1 },
          { priceScale: 1e3, minMove: 1, frac: !1 },
          { priceScale: 1e4, minMove: 1, frac: !1 },
          { priceScale: 1e5, minMove: 1, frac: !1 },
          { priceScale: 1e6, minMove: 1, frac: !1 },
          { priceScale: 1e7, minMove: 1, frac: !1 },
          { priceScale: 1e8, minMove: 1, frac: !1 },
          { priceScale: 2, minMove: 1, frac: !0 },
          { priceScale: 4, minMove: 1, frac: !0 },
          { priceScale: 8, minMove: 1, frac: !0 },
          { priceScale: 16, minMove: 1, frac: !0 },
          { priceScale: 32, minMove: 1, frac: !0 },
          { priceScale: 64, minMove: 1, frac: !0 },
          { priceScale: 128, minMove: 1, frac: !0 },
          { priceScale: 320, minMove: 1, frac: !0 },
        ],
        jt = [{ id: 'tick-default', value: 'default', content: Kt }];
      for (let e = 0; e < Zt.length; e++) {
        const t = Zt[e];
        jt.push({
          value: t.priceScale + ',' + t.minMove + ',' + t.frac,
          content: t.minMove + '/' + t.priceScale,
        });
      }
      class Xt extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._onChange = (e) => {
              const { setValue: t } = this.context,
                { minTick: l } = this.props;
              t(l, e, qt);
            });
        }
        render() {
          const { id: e, minTick: t } = this.props;
          return n.createElement(
            D.InputRow,
            { label: Qt },
            n.createElement(Te.Select, {
              id: e,
              className: te.defaultSelect,
              menuItemClassName: te.defaultSelectItem,
              items: jt,
              value: t.value(),
              onChange: this._onChange,
            }),
          );
        }
      }
      Xt.contextType = L.StylePropertyContext;
      var Yt = l(73146),
        $t = l(86067);
      class Jt extends n.PureComponent {
        render() {
          const {
            id: e,
            isRGB: t,
            title: l,
            visible: r,
            bottomColor: o,
            topColor: i,
            transparency: s,
            children: a,
            switchable: c = !0,
            offset: p,
            grouped: d,
          } = this.props;
          return n.createElement(
            D.InputRow,
            {
              label: c
                ? n.createElement(M, { id: e, title: l, visible: r })
                : l,
              offset: p,
              grouped: d,
            },
            t
              ? null
              : n.createElement(
                  n.Fragment,
                  null,
                  i &&
                    n.createElement(ee.ColorWithThicknessSelect, {
                      disabled:
                        r && !(Array.isArray(r) ? r[0].value() : r.value()),
                      color: i,
                      transparency: s,
                    }),
                  o &&
                    n.createElement(
                      'div',
                      { className: Se()(o && i && te.additionalSelect) },
                      n.createElement(ee.ColorWithThicknessSelect, {
                        disabled:
                          r && !(Array.isArray(r) ? r[0].value() : r.value()),
                        color: o,
                        transparency: s,
                      }),
                    ),
                ),
            a,
          );
        }
      }
      Jt.contextType = L.StylePropertyContext;
      const el = i.t(null, void 0, l(27331)),
        tl = i.t(null, { context: 'input' }, l(23545)),
        ll = i.t(null, { context: 'input' }, l(41596)),
        rl = i.t(null, void 0, l(40297));
      class nl extends n.PureComponent {
        constructor() {
          super(...arguments),
            (this._findPlotPalettes = (e) => {
              const { study: t } = this.props,
                l = t.metaInfo(),
                r = (0, o.ensureDefined)(l.palettes);
              return (0, x.isBarColorerPlot)(e) || (0, x.isBgColorerPlot)(e)
                ? {
                    main: {
                      palette: r[e.palette],
                      paletteProps: t.properties().palettes[e.palette],
                    },
                  }
                : this._findPalettesByTargetId(e.id);
            });
        }
        render() {
          const { study: e } = this.props,
            t = e.metaInfo();
          if ((0, zt.isCustomStudy)(t.shortId))
            return n.createElement(At, null);
          const l = e.properties(),
            {
              precision: r,
              strategy: o,
              minTick: i,
              showLabelsOnPriceScale: s,
              showLegendValues: a,
            } = l,
            c = t.plots.length > 0,
            p = t.plots.some((e) => !(0, x.isPlotWithTechnicalValues)(e)),
            d = c || t.inputs.some((e) => 'price' === e.type),
            h = (0, Yt.createAdapter)(e).canOverrideMinTick();
          return n.createElement(
            oe.PropertyTable,
            null,
            this._plotsElement(),
            this._bandsElement(),
            this._bandsBackgroundsElement(),
            this._areasBackgroundsElement(),
            this._filledAreasElement(),
            this._graphicsElement(),
            h &&
              n.createElement(Xt, {
                id: (0, k.createDomId)(t.id, 'min-tick-select'),
                minTick: i,
              }),
            I.StudyMetaInfo.isScriptStrategy(t) &&
              n.createElement(lt, { orders: o.orders }),
            (d || p) &&
              n.createElement(
                oe.PropertyTable.Row,
                null,
                n.createElement(oe.PropertyTable.GroupSeparator, { size: 1 }),
                n.createElement($t.GroupTitleSection, { title: rl, name: rl }),
                d &&
                  n.createElement(Ut, {
                    id: (0, k.createDomId)(t.id, 'precision-select'),
                    precision: r,
                  }),
                p &&
                  n.createElement(
                    n.Fragment,
                    null,
                    n.createElement(
                      oe.PropertyTable.Cell,
                      { placement: 'first', colSpan: 2 },
                      n.createElement(M, {
                        id: 'showLabelsOnPriceScale',
                        title: ll,
                        visible: s,
                      }),
                    ),
                    n.createElement(
                      oe.PropertyTable.Cell,
                      { placement: 'first', colSpan: 2 },
                      n.createElement(M, {
                        id: 'showLegendValues',
                        title: tl,
                        visible: a,
                      }),
                    ),
                  ),
              ),
          );
        }
        _plotsElement() {
          const { study: e } = this.props,
            t = e.metaInfo();
          return new E.MetaInfoHelper(t)
            .getUserEditablePlots()
            .filter(
              (e) =>
                !(
                  (0, x.isUpColorerPlot)(e) ||
                  (0, x.isDownColorerPlot)(e) ||
                  (0, x.isCandleBorderColorerPlot)(e) ||
                  (0, x.isCandleWickColorerPlot)(e)
                ),
            )
            .map((t) => {
              const l = (0, x.isOhlcPlot)(t) ? { ...t, id: t.target } : t,
                r = this._findPlotPalettes(l);
              return n.createElement(Ze, {
                key: t.id,
                plot: t,
                palettes: r,
                study: e,
              });
            });
        }
        _bandsElement() {
          const { study: e } = this.props,
            t = e.properties(),
            { bands: l } = t;
          return (
            l &&
            l.childNames().map((e, t) => {
              const r = l.child(e);
              if (!r.isHidden || !r.isHidden.value())
                return n.createElement(tt, {
                  key: t,
                  id: r.name.value(),
                  property: r,
                });
            })
          );
        }
        _bandsBackgroundsElement() {
          const { study: e } = this.props,
            t = e.properties(),
            { bandsBackground: l } = t;
          return (
            l &&
            n.createElement(ye, {
              id: 'bandsBackground',
              title: el,
              visible: l.fillBackground,
              color: l.backgroundColor,
              transparency: l.transparency,
            })
          );
        }
        _areasBackgroundsElement() {
          const { study: e } = this.props,
            t = e.metaInfo(),
            l = e.properties(),
            { areaBackground: r } = l;
          return t.isRGB
            ? null
            : r &&
                n.createElement(ye, {
                  id: 'areaBackground',
                  title: el,
                  visible: r.fillBackground,
                  color: r.backgroundColor,
                  transparency: r.transparency,
                });
        }
        _filledAreasElement() {
          const { study: e } = this.props,
            t = e.metaInfo(),
            l = t.filledAreas;
          return !l || t.isRGB
            ? []
            : l.map((t) => {
                if (t.isHidden) return null;
                const l = e.properties().filledAreasStyle[t.id],
                  r = t.title || el;
                if (
                  l.hasChild('fillType') &&
                  'gradient' === l.childs().fillType.value()
                ) {
                  if (l.topColor || l.bottomColor)
                    return n.createElement(Jt, {
                      key: t.id,
                      id: t.id,
                      title: r,
                      bottomColor: l.bottomColor,
                      topColor: l.topColor,
                      visible: l.visible,
                      transparency: l.transparency,
                    });
                  if (t.palette) {
                    const e = this._findPalettesByTargetId(t.id),
                      r = (0, o.ensureDefined)(e.main),
                      i = e.secondary;
                    return n.createElement(se, {
                      key: t.id,
                      area: t,
                      palette: (0, o.ensureDefined)(r.palette),
                      paletteProps: (0, o.ensureDefined)(r.paletteProps),
                      secondaryPalette: null == i ? void 0 : i.palette,
                      secondaryPaletteProps:
                        null == i ? void 0 : i.paletteProps,
                      styleProp: l,
                    });
                  }
                  return null;
                }
                if (t.palette) {
                  const e = this._findPalettesByTargetId(t.id),
                    r = (0, o.ensureDefined)(e.main);
                  return n.createElement(se, {
                    key: t.id,
                    area: t,
                    palette: (0, o.ensureDefined)(r.palette),
                    paletteProps: (0, o.ensureDefined)(r.paletteProps),
                    styleProp: l,
                  });
                }
                return n.createElement(ye, {
                  key: t.id,
                  id: t.id,
                  title: r,
                  color: l.color,
                  visible: l.visible,
                  transparency: l.transparency,
                });
              });
        }
        _graphicsElement() {
          const { study: e } = this.props,
            t = e.metaInfo().graphics;
          return (
            t &&
            Object.keys(t).map((t, l) =>
              n.createElement(vt, { key: t, graphicType: t, study: e }),
            )
          );
        }
        _findPalettesByTargetId(e) {
          const { study: t } = this.props,
            l = t.metaInfo(),
            r = l.plots,
            n = (0, o.ensureDefined)(l.palettes),
            i = {};
          for (const l of r) {
            if (
              ((0, x.isPaletteColorerPlot)(l) || (0, x.isOhlcColorerPlot)(l)) &&
              l.target === e
            ) {
              if (i.main) {
                i.secondary = {
                  palette: n[l.palette],
                  paletteProps: t.properties().palettes[l.palette],
                };
                continue;
              }
              i.main = {
                palette: n[l.palette],
                paletteProps: t.properties().palettes[l.palette],
              };
            }
            (0, x.isUpColorerPlot)(l) &&
              l.target === e &&
              (i.up = {
                palette: n[l.palette],
                paletteProps: t.properties().palettes[l.palette],
              }),
              (0, x.isDownColorerPlot)(l) &&
                l.target === e &&
                (i.down = {
                  palette: n[l.palette],
                  paletteProps: t.properties().palettes[l.palette],
                }),
              (0, x.isCandleWickColorerPlot)(l) &&
                l.target === e &&
                (i.wick = {
                  palette: n[l.palette],
                  paletteProps: t.properties().palettes[l.palette],
                }),
              (0, x.isCandleBorderColorerPlot)(l) &&
                l.target === e &&
                (i.border = {
                  palette: n[l.palette],
                  paletteProps: t.properties().palettes[l.palette],
                });
          }
          return i;
        }
      }
      function ol(e) {
        return (0, L.bindPropertyContext)(nl, {
          ...e,
          property: e.study.properties(),
        });
      }
      class il extends n.PureComponent {
        render() {
          return n.createElement(
            Nt.ModelContext.Provider,
            { value: this.props.model },
            n.createElement(
              ve.Provider,
              { value: this.props.source },
              n.createElement(ol, { study: this.props.source }),
            ),
          );
        }
      }
      var sl = l(82623),
        al = l(88279),
        cl = l(20196);
      class pl extends al.DialogRenderer {
        constructor(e, t, l, n) {
          super(),
            (this._timeout = null),
            (this._handleClose = () => {
              r.unmountComponentAtNode(this._container),
                this._setVisibility(!1),
                this._subscription.unsubscribe(
                  this,
                  this._handleCollectionChanged,
                );
            }),
            (this._handleCancel = () => {
              this._model.undoToCheckpoint(this._checkpoint);
            }),
            (this._handleSubmit = () => {}),
            (this._handleActiveTabChanged = (e) => {
              c.setValue(this._activeTabSettingsName(), e);
            }),
            (this._source = e),
            (this._model = t),
            (this._propertyPages = n),
            (this._checkpoint = this._ensureCheckpoint(l)),
            (this._subscription = this._model
              .model()
              .dataSourceCollectionChanged()),
            this._subscription.subscribe(this, this._handleCollectionChanged);
        }
        hide(e) {
          e ? this._handleCancel() : this._handleSubmit(), this._handleClose();
        }
        isVisible() {
          return this.visible().value();
        }
        show(e = {}) {
          if (!p.enabled('property_pages')) return;
          const t = this._source.metaInfo();
          if (
            ((0, f.isLineTool)(this._source) &&
              (0, h.trackEvent)(
                'GUI',
                'Drawing Properties',
                this._source.name(),
              ),
            (0, b.isStudy)(this._source))
          ) {
            const e =
              !this._source.isPine() || this._source.isStandardPine()
                ? t.description
                : 'Custom Pine';
            (0, h.trackEvent)('GUI', 'Study Properties', e);
          }
          let o = {
            byId: {
              inputs: { title: i.t(null, void 0, l(66304)), Component: _ },
              style: { title: i.t(null, void 0, l(32733)), Component: il },
            },
            allIds: [],
          };
          const s = new E.MetaInfoHelper(t);
          s.hasUserEditableInputs() && o.allIds.push('inputs'),
            s.hasUserEditableProperties(),
            s.hasUserEditableStyles() && o.allIds.push('style'),
            this._propertyPages ||
              ((o.byId.visibilities = {
                title: i.t(null, void 0, l(21852)),
                page: this._createVisibilitiesPropertyPage(),
              }),
              o.allIds.push('visibilities')),
            (o = this._getPagesForStudyLineTool(o));
          const u =
            e.initialTab ||
            c.getValue(this._activeTabSettingsName()) ||
            'inputs';
          let m = (0, a.clean)(t.shortDescription, !0);
          r.render(
            n.createElement(P, {
              title: m,
              model: this._model,
              source: this._source,
              initialActiveTab: o.allIds.includes(u) ? u : o.allIds[0],
              pages: o,
              onSubmit: this._handleSubmit,
              onCancel: this._handleCancel,
              onClose: this._handleClose,
              onActiveTabChanged: this._handleActiveTabChanged,
            }),
            this._container,
          ),
            this._setVisibility(!0),
            d.emit('edit_object_dialog', {
              objectType: 'study',
              scriptTitle: this._source.title(),
            });
        }
        _createVisibilitiesPropertyPage() {
          const e = this._source
            .properties()
            .childs()
            .intervalsVisibilities.childs();
          return (0, sl.createPropertyPage)(
            (0, cl.getIntervalsVisibilitiesPropertiesDefinitions)(
              this._model,
              e,
              new s.TranslatedString(
                this._source.name(!0),
                this._source.title(!0),
              ),
            ),
            'visibility',
            i.t(null, void 0, l(21852)),
          );
        }
        _activeTabSettingsName() {
          return 'properties_dialog.active_tab.study';
        }
        _ensureCheckpoint(e) {
          return void 0 === e && (e = this._model.createUndoCheckpoint()), e;
        }
        _getPagesForStudyLineTool(e) {
          if (this._propertyPages) {
            const t = this._propertyPages.filter(
                (e) => 'coordinates' === e.id || 'visibility' === e.id,
              ),
              l = {
                allIds: t.map((e) => e.id),
                byId: t.reduce(
                  (e, t) => ({ ...e, [t.id]: { title: t.title, page: t } }),
                  {},
                ),
              };
            return {
              allIds: [...e.allIds, ...l.allIds],
              byId: { ...e.byId, ...l.byId },
            };
          }
          return e;
        }
        _handleCollectionChanged() {
          null === this._timeout &&
            (this._timeout = setTimeout(() => {
              this._closeDialogIfSourceIsDeleted(), (this._timeout = null);
            }));
        }
        _closeDialogIfSourceIsDeleted() {
          null === this._model.model().dataSourceForId(this._source.id()) &&
            this._handleClose();
        }
      }
    },
    34290: (e, t, l) => {
      'use strict';
      l.d(t, { StudyDefaultsManager: () => u });
      var r = l(50959),
        n = l(97754),
        o = l.n(n),
        i = l(9745),
        s = l(44352),
        a = l(95276),
        c = l(16396),
        p = l(44996),
        d = l(24959);
      const h = {
        reset: s.t(null, void 0, l(79782)),
        saveAsDefault: s.t(null, void 0, l(18229)),
        defaults: s.t(null, void 0, l(98938)),
      };
      class u extends r.PureComponent {
        constructor() {
          super(...arguments),
            (this._handleResetToDefaults = () => {
              this.props.model.restorePropertiesForSource(this.props.source);
            }),
            (this._handleSaveAsDefaults = () => {
              this.props.source.properties().saveDefaults();
            });
        }
        render() {
          const { mode: e } = this.props;
          return r.createElement(
            a.ControlDisclosure,
            {
              id: 'study-defaults-manager',
              className: o()('normal' === e && d.defaultsButtonText),
              hideArrowButton: 'compact' === e,
              buttonChildren: this._getPlaceHolderItem('compact' === e),
            },
            r.createElement(c.PopupMenuItem, {
              className: d.defaultsButtonItem,
              isActive: !1,
              label: h.reset,
              onClick: this._handleResetToDefaults,
            }),
            r.createElement(c.PopupMenuItem, {
              className: d.defaultsButtonItem,
              isActive: !1,
              label: h.saveAsDefault,
              onClick: this._handleSaveAsDefaults,
            }),
          );
        }
        _getPlaceHolderItem(e) {
          return e
            ? r.createElement(i.Icon, {
                className: d.defaultsButtonIcon,
                icon: p,
              })
            : h.defaults;
        }
      }
    },
    48531: (e, t, l) => {
      'use strict';
      l.d(t, { FooterMenu: () => b });
      var r = l(50959),
        n = l(44352),
        o = l(9745),
        i = l(95276),
        s = l(90692),
        a = l(10986),
        c = l(44996);
      function p(e) {
        return e.isTabletWidth
          ? r.createElement(o.Icon, { className: a.themesButtonIcon, icon: c })
          : r.createElement(r.Fragment, null, n.t(null, void 0, l(19611)));
      }
      function d(e) {
        return r.createElement(
          s.MatchMedia,
          { rule: 'screen and (max-width: 768px)' },
          (t) =>
            r.createElement(
              i.ControlDisclosure,
              {
                className: !t && a.themesButtonText,
                hideArrowButton: t,
                buttonChildren: r.createElement(p, { isTabletWidth: t }),
              },
              e.children,
            ),
        );
      }
      var h = l(16396),
        u = l(96040),
        m = l(70412),
        v = l(32563);
      function y(e) {
        const { name: t, onRemove: l, onClick: n } = e,
          [o, i] = (0, m.useHover)(),
          s = r.useCallback(() => n(t), [n, t]),
          c = r.useCallback(() => {
            l && l(t);
          }, [l, t]);
        return r.createElement(
          'div',
          { ...i },
          r.createElement(h.PopupMenuItem, {
            className: a.defaultsButtonItem,
            isActive: !1,
            label: t,
            onClick: s,
            toolbox:
              l &&
              r.createElement(u.RemoveButton, {
                hidden: !v.mobiletouch && !o,
                onClick: c,
              }),
          }),
        );
      }
      function g(e) {
        return r.createElement(
          d,
          null,
          r.createElement(y, {
            onClick: function () {
              const { sources: t, chartUndoModel: l } = e;
              l.restoreLineToolsFactoryDefaults(t);
            },
            name: n.t(null, void 0, l(58102)),
          }),
        );
      }
      function b(e) {
        return r.createElement(g, { ...e });
      }
    },
    37289: (e, t, l) => {
      'use strict';
      l.d(t, { PropertiesEditorTab: () => c });
      var r = l(50959),
        n = l(66849);
      const o = {
          'Elliott Impulse Wave (12345)Degree': 'normal',
          'Elliott Triangle Wave (ABCDE)Degree': 'normal',
          'Elliott Triple Combo Wave (WXYXZ)Degree': 'normal',
          'Elliott Correction Wave (ABC)Degree': 'normal',
          'Elliott Double Combo Wave (WXY)Degree': 'normal',
          BarsPatternMode: 'normal',
          StudyInputSource: 'normal',
        },
        i = {
          TextText: 'big',
          AnchoredTextText: 'big',
          NoteText: 'big',
          AnchoredNoteText: 'big',
          CalloutText: 'big',
          BalloonText: 'big',
        };
      var s = l(40296),
        a = l(53942);
      function c(e) {
        const { page: t, pageRef: l, tableKey: c } = e;
        return r.createElement(
          n.ControlCustomHeightContext.Provider,
          { value: i },
          r.createElement(
            n.ControlCustomWidthContext.Provider,
            { value: o },
            t &&
              r.createElement(
                s.PropertyTable,
                { reference: l, key: c },
                t.definitions
                  .value()
                  .map((e) =>
                    r.createElement(a.Section, { key: e.id, definition: e }),
                  ),
              ),
          ),
        );
      }
    },
    31807: (e, t, l) => {
      'use strict';
      l.d(t, { DialogTabs: () => m });
      var r = l(50959),
        n = l(97754),
        o = l(64205),
        i = l(40173),
        s = l(15185);
      const a = (0, i.mergeThemes)(o.DEFAULT_SLIDER_THEME, s);
      var c = l(39440),
        p = l(32563),
        d = l(57248);
      const h = d,
        u = (0, o.factory)(function (e) {
          return r.createElement(
            'div',
            { className: a.slider, ref: e.reference },
            r.createElement('div', { className: a.inner }),
          );
        });
      class m extends r.PureComponent {
        constructor() {
          super(...arguments),
            (this._createClickHandler = (e) => () => {
              this.props.onSelect(e);
            });
        }
        render() {
          const {
              theme: e = h,
              hiddenBottomBorders: t,
              fadedSlider: l = !0,
              ScrollComponent: o = c.HorizontalScroll,
            } = this.props,
            i = this._generateDialogTabs();
          return r.createElement(
            'div',
            { className: n(e.scrollWrap) },
            !t &&
              r.createElement('div', { className: e.headerBottomSeparator }),
            r.createElement(
              o,
              {
                isVisibleFade: p.mobiletouch,
                isVisibleButtons: !p.mobiletouch,
                isVisibleScrollbar: !1,
                fadeClassName: n({ [e.fadeWithoutSlider]: !l }),
              },
              (l) =>
                r.createElement(
                  'div',
                  { className: e.tabsWrap, ref: l },
                  r.createElement(
                    u,
                    { className: n(e.tabs, t && e.withoutBorder) },
                    i,
                  ),
                ),
            ),
          );
        }
        _generateDialogTabs() {
          const { activeTabId: e, tabs: t, theme: l = h } = this.props;
          return t.allIds.map((i) => {
            const s = e === i,
              a = t.byId[i].withNotificationsBadge;
            return r.createElement(
              o.SliderItem,
              {
                key: i,
                value: i,
                className: n(l.tab, !s && l.withHover, a && d.withBadge),
                isActive: s,
                onClick: this._createClickHandler(i),
              },
              t.byId[i].title,
            );
          });
        }
      }
    },
    39440: (e, t, l) => {
      'use strict';
      l.d(t, { HorizontalScroll: () => w });
      var r = l(50959),
        n = l(97754),
        o = l(50151),
        i = l(9745),
        s = l(70439),
        a = l(41207),
        c = l(80142),
        p = l(45601),
        d = l(61380),
        h = l(39416);
      const u = {
        isVisibleScrollbar: !0,
        shouldMeasure: !0,
        hideButtonsFrom: 1,
      };
      function m(e) {
        return r.createElement('div', {
          className: n(h.fadeLeft, e.className, { [h.isVisible]: e.isVisible }),
        });
      }
      function v(e) {
        return r.createElement('div', {
          className: n(h.fadeRight, e.className, {
            [h.isVisible]: e.isVisible,
          }),
        });
      }
      function y(e) {
        return r.createElement(b, { ...e, className: h.scrollLeft });
      }
      function g(e) {
        return r.createElement(b, { ...e, className: h.scrollRight });
      }
      function b(e) {
        return r.createElement(
          'div',
          {
            className: n(e.className, { [h.isVisible]: e.isVisible }),
            onClick: e.onClick,
          },
          r.createElement(
            'div',
            { className: h.iconWrap },
            r.createElement(i.Icon, { icon: d, className: h.icon }),
          ),
        );
      }
      const w = (function (e = y, t = g, l = m, i = v) {
        var d;
        return (
          ((d = class extends r.PureComponent {
            constructor(e) {
              super(e),
                (this._scroll = r.createRef()),
                (this._handleScrollLeft = () => {
                  if (this.props.onScrollButtonClick)
                    return void this.props.onScrollButtonClick('left');
                  const e =
                    this.props.scrollStepSize || this.state.widthWrap - 50;
                  this.animateTo(Math.max(0, this.currentPosition() - e));
                }),
                (this._handleScrollRight = () => {
                  if (this.props.onScrollButtonClick)
                    return void this.props.onScrollButtonClick('right');
                  const e =
                    this.props.scrollStepSize || this.state.widthWrap - 50;
                  this.animateTo(
                    Math.min(
                      (this.state.widthContent || 0) -
                        (this.state.widthWrap || 0),
                      this.currentPosition() + e,
                    ),
                  );
                }),
                (this._handleResizeWrap = ([e]) => {
                  const t = e.target.getBoundingClientRect();
                  this.props.onMeasureWrap && this.props.onMeasureWrap(t),
                    this.setState({ widthWrap: t.width }),
                    this._checkButtonsVisibility();
                }),
                (this._handleResizeContent = ([e]) => {
                  const t = e.target.getBoundingClientRect();
                  this.props.onMeasureContent && this.props.onMeasureContent(t);
                  const {
                    shouldDecreaseWidthContent: l,
                    buttonsWidthIfDecreasedWidthContent: r,
                  } = this.props;
                  l && r
                    ? this.setState({ widthContent: t.width + 2 * r })
                    : this.setState({ widthContent: t.width });
                }),
                (this._handleScroll = () => {
                  const { onScroll: e } = this.props;
                  e &&
                    e(
                      this.currentPosition(),
                      this.isAtLeft(),
                      this.isAtRight(),
                    ),
                    this._checkButtonsVisibility();
                }),
                (this._checkButtonsVisibility = () => {
                  const { isVisibleLeftButton: e, isVisibleRightButton: t } =
                      this.state,
                    l = this.isAtLeft(),
                    r = this.isAtRight();
                  l || e
                    ? l && e && this.setState({ isVisibleLeftButton: !1 })
                    : this.setState({ isVisibleLeftButton: !0 }),
                    r || t
                      ? r && t && this.setState({ isVisibleRightButton: !1 })
                      : this.setState({ isVisibleRightButton: !0 });
                }),
                (this.state = {
                  widthContent: 0,
                  widthWrap: 0,
                  isVisibleRightButton: !1,
                  isVisibleLeftButton: !1,
                });
            }
            componentDidMount() {
              this._checkButtonsVisibility();
            }
            componentDidUpdate(e, t) {
              (t.widthWrap === this.state.widthWrap &&
                t.widthContent === this.state.widthContent) ||
                this._handleScroll();
            }
            currentPosition() {
              return this._scroll.current
                ? (0, c.isRtl)()
                  ? (0, c.getLTRScrollLeft)(this._scroll.current)
                  : this._scroll.current.scrollLeft
                : 0;
            }
            isAtLeft() {
              return (
                !this._isOverflowed() ||
                this.currentPosition() <=
                  (0, o.ensureDefined)(this.props.hideButtonsFrom)
              );
            }
            isAtRight() {
              return (
                !this._isOverflowed() ||
                this.currentPosition() + this.state.widthWrap >=
                  this.state.widthContent -
                    (0, o.ensureDefined)(this.props.hideButtonsFrom)
              );
            }
            animateTo(e, t = a.dur) {
              const l = this._scroll.current;
              l &&
                ((0, c.isRtl)() && (e = (0, c.getLTRScrollLeftOffset)(l, e)),
                t <= 0
                  ? (l.scrollLeft = Math.round(e))
                  : (0, s.doAnimate)({
                      onStep(e, t) {
                        l.scrollLeft = Math.round(t);
                      },
                      from: l.scrollLeft,
                      to: Math.round(e),
                      easing: a.easingFunc.easeInOutCubic,
                      duration: t,
                    }));
            }
            render() {
              const {
                  children: o,
                  isVisibleScrollbar: s,
                  isVisibleFade: a,
                  isVisibleButtons: c,
                  shouldMeasure: d,
                  shouldDecreaseWidthContent: u,
                  buttonsWidthIfDecreasedWidthContent: m,
                  onMouseOver: v,
                  onMouseOut: y,
                  scrollWrapClassName: g,
                  fadeClassName: b,
                } = this.props,
                { isVisibleRightButton: w, isVisibleLeftButton: f } =
                  this.state,
                C = u && m;
              return r.createElement(
                p.Measure,
                { onResize: d ? this._handleResizeWrap : null },
                (u) =>
                  r.createElement(
                    'div',
                    {
                      className: h.wrapOverflow,
                      onMouseOver: v,
                      onMouseOut: y,
                      ref: u,
                    },
                    r.createElement(
                      'div',
                      { className: n(h.wrap, C ? h.wrapWithArrowsOuting : '') },
                      r.createElement(
                        'div',
                        {
                          className: n(h.scrollWrap, g, {
                            [h.noScrollBar]: !s,
                          }),
                          onScroll: this._handleScroll,
                          ref: this._scroll,
                        },
                        r.createElement(
                          p.Measure,
                          { onResize: d ? this._handleResizeContent : null },
                          o,
                        ),
                      ),
                      a && r.createElement(l, { isVisible: f, className: b }),
                      a && r.createElement(i, { isVisible: w, className: b }),
                      c &&
                        r.createElement(e, {
                          onClick: this._handleScrollLeft,
                          isVisible: f,
                        }),
                      c &&
                        r.createElement(t, {
                          onClick: this._handleScrollRight,
                          isVisible: w,
                        }),
                    ),
                  ),
              );
            }
            _isOverflowed() {
              const { widthContent: e, widthWrap: t } = this.state;
              return e > t;
            }
          }).defaultProps = u),
          d
        );
      })(y, g, m, v);
    },
    51613: (e, t, l) => {
      'use strict';
      l.d(t, { PopupMenuSeparator: () => s });
      var r = l(50959),
        n = l(97754),
        o = l.n(n),
        i = l(45829);
      function s(e) {
        const { size: t = 'normal', className: l, ariaHidden: n = !1 } = e;
        return r.createElement('div', {
          className: o()(
            i.separator,
            'small' === t && i.small,
            'normal' === t && i.normal,
            'large' === t && i.large,
            l,
          ),
          role: 'separator',
          'aria-hidden': n,
        });
      }
    },
    64205: (e, t, l) => {
      'use strict';
      l.d(t, {
        DEFAULT_SLIDER_THEME: () => s,
        SliderItem: () => a,
        factory: () => c,
      });
      var r = l(50959),
        n = l(97754),
        o = l(50151),
        i = l(86355);
      const s = i;
      function a(e) {
        const t = n(e.className, i.tab, {
          [i.active]: e.isActive,
          [i.disabled]: e.isDisabled,
          [i.defaultCursor]: !!e.shouldUseDefaultCursor,
          [i.noBorder]: !!e.noBorder,
        });
        return r.createElement(
          'div',
          {
            className: t,
            onClick: e.onClick,
            ref: e.reference,
            'data-type': 'tab-item',
            'data-value': e.value,
            'data-name': 'tab-item-' + e.value.toString().toLowerCase(),
          },
          e.children,
        );
      }
      function c(e) {
        return class extends r.PureComponent {
          constructor() {
            super(...arguments), (this.activeTab = { current: null });
          }
          componentDidUpdate() {
            ((0, o.ensureNotNull)(this._slider).style.transition =
              'transform 350ms'),
              this._componentDidUpdate();
          }
          componentDidMount() {
            this._componentDidUpdate();
          }
          render() {
            const { className: t } = this.props,
              l = this._generateTabs();
            return r.createElement(
              'div',
              { className: n(t, i.tabs), 'data-name': this.props['data-name'] },
              l,
              r.createElement(e, {
                reference: (e) => {
                  this._slider = e;
                },
              }),
            );
          }
          _generateTabs() {
            return (
              (this.activeTab.current = null),
              r.Children.map(this.props.children, (e) => {
                const t = e,
                  l = Boolean(t.props.isActive),
                  n = {
                    reference: (e) => {
                      l && (this.activeTab.current = e),
                        t.props.reference && t.props.reference(e);
                    },
                  };
                return r.cloneElement(t, n);
              })
            );
          }
          _componentDidUpdate() {
            const e = (0, o.ensureNotNull)(this._slider).style;
            if (this.activeTab.current) {
              const t = this.activeTab.current.offsetWidth,
                l = this.activeTab.current.offsetLeft;
              (e.transform = `translateX(${l}px)`),
                (e.width = `${t}px`),
                (e.opacity = '1');
            } else e.opacity = '0';
          }
        };
      }
      c(function (e) {
        return r.createElement('div', {
          className: i.slider,
          ref: e.reference,
        });
      });
    },
    6001: (e) => {
      e.exports = {
        wrapper: 'wrapper-n_GqAsy6',
        hovered: 'hovered-n_GqAsy6',
        withIcon: 'withIcon-n_GqAsy6',
        labelRow: 'labelRow-n_GqAsy6',
        label: 'label-n_GqAsy6',
        switchWrap: 'switchWrap-n_GqAsy6',
        icon: 'icon-n_GqAsy6',
        labelHint: 'labelHint-n_GqAsy6',
        labelOn: 'labelOn-n_GqAsy6',
      };
    },
    42430: (e) => {
      e.exports = {
        smallStyleControl: 'smallStyleControl-dlnS6A6v',
        additionalSelect: 'additionalSelect-dlnS6A6v',
        childRowContainer: 'childRowContainer-dlnS6A6v',
        defaultSelect: 'defaultSelect-dlnS6A6v',
        defaultSelectItem: 'defaultSelectItem-dlnS6A6v',
        block: 'block-dlnS6A6v',
        group: 'group-dlnS6A6v',
        wrapGroup: 'wrapGroup-dlnS6A6v',
        textMarkGraphicBlock: 'textMarkGraphicBlock-dlnS6A6v',
        textMarkGraphicWrapGroup: 'textMarkGraphicWrapGroup-dlnS6A6v',
      };
    },
    51107: (e, t, l) => {
      'use strict';
      l.d(t, {
        DEFAULT_MENU_ITEM_SWITCHER_THEME: () => m,
        MenuItemSwitcher: () => v,
      });
      var r = l(50959),
        n = l(97754),
        o = l.n(n),
        i = l(17946),
        s = l(82335),
        a = l.n(s);
      function c(e) {
        const t = (0, r.useContext)(i.CustomBehaviourContext),
          {
            className: l,
            intent: o = 'default',
            size: s = 'small',
            enableActiveStateStyles: c = t.enableActiveStateStyles,
          } = e;
        return n(
          l,
          a().switcher,
          a()[`size-${s}`],
          a()[`intent-${o}`],
          !c && a()['disable-active-state-styles'],
        );
      }
      function p(e) {
        var t;
        const {
            reference: l,
            size: n,
            intent: o,
            role: i,
            'aria-checked': s,
            checked: p,
            defaultChecked: d,
            onKeyDown: h,
            ...u
          } = e,
          m = (0, r.useCallback)(
            (e) => {
              13 === e.keyCode && e.target.click(), h && h(e);
            },
            [h],
          );
        return r.createElement(
          'span',
          { className: c(e) },
          r.createElement('input', {
            ...u,
            type: 'checkbox',
            className: a().input,
            ref: l,
            role: null != i ? i : 'switch',
            'aria-checked':
              null !== (t = null != s ? s : p) && void 0 !== t ? t : d,
            checked: p,
            defaultChecked: d,
            onKeyDown: m,
          }),
          r.createElement(
            'span',
            { className: a()['thumb-wrapper'] },
            r.createElement('span', { className: a().track }),
            r.createElement('span', { className: a().thumb }),
          ),
        );
      }
      var d = l(9745),
        h = l(90186),
        u = l(6001);
      const m = u;
      function v(e) {
        const {
            className: t,
            checked: l,
            id: n,
            label: i,
            labelDescription: s,
            value: a,
            preventLabelHighlight: c,
            reference: m,
            switchReference: v,
            theme: y = u,
            disabled: g,
            icon: b,
          } = e,
          w = o()(y.label, l && !c && y.labelOn),
          f = o()(
            t,
            y.wrapper,
            l && y.wrapperWithOnLabel,
            s && y.wrapperWithDescription,
          );
        return r.createElement(
          'label',
          { className: o()(f, b && y.withIcon), htmlFor: n, ref: m },
          void 0 !== b &&
            r.createElement(d.Icon, { className: y.icon, icon: b }),
          r.createElement(
            'div',
            { className: y.labelRow },
            r.createElement('div', { className: w }, i),
            s && r.createElement('div', { className: y.labelHint }, s),
          ),
          r.createElement(
            'div',
            { className: u.switchWrap },
            r.createElement(p, {
              disabled: g,
              className: y.switch,
              reference: v,
              checked: l,
              onChange: function (t) {
                const l = t.target.checked;
                void 0 !== e.onChange && e.onChange(l);
              },
              value: a,
              tabIndex: -1,
              id: n,
              role: e.switchRole,
              ...(0, h.filterDataProps)(e),
            }),
          ),
        );
      }
    },
    82623: (e, t, l) => {
      'use strict';
      l.r(t), l.d(t, { createPropertyPage: () => o });
      var r = l(40549),
        n = l.n(r);
      function o(e, t, l, r = null) {
        var o;
        const i = {
          id: t,
          title: l,
          definitions: new (n())(e.definitions),
          visible:
            null !== (o = e.visible) && void 0 !== o
              ? o
              : new (n())(!0).readonly(),
        };
        return null !== r && (i.icon = r), i;
      }
    },
    20196: (e, t, l) => {
      'use strict';
      l.r(t),
        l.d(t, {
          getIntervalsVisibilitiesPropertiesDefinitions: () => ce,
          getSelectionIntervalsVisibilitiesPropertiesDefinition: () => pe,
        });
      var r = l(44352),
        n = l(47539),
        o = l(2484),
        i = l(65279),
        s = l(40549),
        a = l.n(s),
        c = l(92133),
        p = l(75862),
        d = l(85804);
      const h = new n.TranslatedString(
          'change {title} visibility on ticks',
          r.t(null, void 0, l(30810)),
        ),
        u = new n.TranslatedString(
          'change {title} visibility on seconds',
          r.t(null, void 0, l(46948)),
        ),
        m = new n.TranslatedString(
          'change {title} seconds from',
          r.t(null, void 0, l(2822)),
        ),
        v = new n.TranslatedString(
          'change {title} seconds to',
          r.t(null, void 0, l(66161)),
        ),
        y = new n.TranslatedString(
          'change {title} visibility on minutes',
          r.t(null, void 0, l(64370)),
        ),
        g = new n.TranslatedString(
          'change {title} minutes from',
          r.t(null, void 0, l(15106)),
        ),
        b = new n.TranslatedString(
          'change {title} minutes to',
          r.t(null, void 0, l(91633)),
        ),
        w = new n.TranslatedString(
          'change {title} visibility on hours',
          r.t(null, void 0, l(68971)),
        ),
        f = new n.TranslatedString(
          'change {title} hours from',
          r.t(null, void 0, l(35388)),
        ),
        C = new n.TranslatedString(
          'change {title} hours to',
          r.t(null, void 0, l(78586)),
        ),
        S = new n.TranslatedString(
          'change {title} visibility on days',
          r.t(null, void 0, l(29088)),
        ),
        P = new n.TranslatedString(
          'change {title} days from',
          r.t(null, void 0, l(41377)),
        ),
        T = new n.TranslatedString(
          'change {title} days to',
          r.t(null, void 0, l(13355)),
        ),
        E = new n.TranslatedString(
          'change {title} visibility on weeks',
          r.t(null, void 0, l(24941)),
        ),
        _ = new n.TranslatedString(
          'change {title} weeks from',
          r.t(null, void 0, l(21339)),
        ),
        k = new n.TranslatedString(
          'change {title} weeks to',
          r.t(null, void 0, l(68643)),
        ),
        x = new n.TranslatedString(
          'change {title} visibility on months',
          r.t(null, void 0, l(6659)),
        ),
        I = new n.TranslatedString(
          'change {title} months from',
          r.t(null, void 0, l(59635)),
        ),
        L = new n.TranslatedString(
          'change {title} months to',
          r.t(null, void 0, l(74266)),
        ),
        R =
          (new n.TranslatedString(
            'change {title} visibility on ranges',
            r.t(null, void 0, l(29091)),
          ),
          r.t(null, void 0, l(30973))),
        V = r.t(null, void 0, l(71129)),
        B = r.t(null, void 0, l(28134)),
        M = r.t(null, void 0, l(63099)),
        D = r.t(null, void 0, l(22192)),
        N = r.t(null, void 0, l(21594)),
        W = r.t(null, void 0, l(95543)),
        A =
          (r.t(null, void 0, l(86672)),
          new n.TranslatedString('ticks', r.t(null, void 0, l(59523)))),
        z = new n.TranslatedString('seconds', r.t(null, void 0, l(32925))),
        G = new n.TranslatedString('seconds from', r.t(null, void 0, l(6049))),
        H = new n.TranslatedString('seconds to', r.t(null, void 0, l(39017))),
        O = new n.TranslatedString('minutes', r.t(null, void 0, l(16465))),
        F = new n.TranslatedString('minutes from', r.t(null, void 0, l(25586))),
        U = new n.TranslatedString('minutes to', r.t(null, void 0, l(72317))),
        q = new n.TranslatedString('hours', r.t(null, void 0, l(3143))),
        K = new n.TranslatedString('hours from', r.t(null, void 0, l(84775))),
        Q = new n.TranslatedString('hours to', r.t(null, void 0, l(11255))),
        Z = new n.TranslatedString('days', r.t(null, void 0, l(82211))),
        j = new n.TranslatedString('days from', r.t(null, void 0, l(14077))),
        X = new n.TranslatedString('days to', r.t(null, void 0, l(33486))),
        Y = new n.TranslatedString('weeks', r.t(null, void 0, l(93016))),
        $ = new n.TranslatedString('weeks from', r.t(null, void 0, l(32002))),
        J = new n.TranslatedString('weeks to', r.t(null, void 0, l(28091))),
        ee = new n.TranslatedString('months', r.t(null, void 0, l(58964))),
        te = new n.TranslatedString('months from', r.t(null, void 0, l(71770))),
        le = new n.TranslatedString('months to', r.t(null, void 0, l(37179))),
        re =
          (new n.TranslatedString('ranges', r.t(null, void 0, l(13604))),
          [1, 59]),
        ne = [1, 59],
        oe = [1, 24],
        ie = [1, 366],
        se = [1, 52],
        ae = [1, 12];
      function ce(e, t, l) {
        const r = [];
        if (o.enabled('tick_resolution')) {
          const n = (0, i.createCheckablePropertyDefinition)(
            {
              checked: (0, i.convertToDefinitionProperty)(
                e,
                t.ticks,
                h.format({ title: l }),
              ),
            },
            { id: 'IntervalsVisibilitiesTicks', title: R },
          );
          r.push(n);
        }
        if ((0, c.isSecondsEnabled)()) {
          const n = (0, i.createRangePropertyDefinition)(
            {
              checked: (0, i.convertToDefinitionProperty)(
                e,
                t.seconds,
                u.format({ title: l }),
              ),
              from: (0, i.convertToDefinitionProperty)(
                e,
                t.secondsFrom,
                m.format({ title: l }),
              ),
              to: (0, i.convertToDefinitionProperty)(
                e,
                t.secondsTo,
                v.format({ title: l }),
              ),
            },
            {
              id: 'IntervalsVisibilitiesSecond',
              title: V,
              min: new (a())(re[0]),
              max: new (a())(re[1]),
            },
          );
          r.push(n);
        }
        const n = (0, i.createRangePropertyDefinition)(
            {
              checked: (0, i.convertToDefinitionProperty)(
                e,
                t.minutes,
                y.format({ title: l }),
              ),
              from: (0, i.convertToDefinitionProperty)(
                e,
                t.minutesFrom,
                g.format({ title: l }),
              ),
              to: (0, i.convertToDefinitionProperty)(
                e,
                t.minutesTo,
                b.format({ title: l }),
              ),
            },
            {
              id: 'IntervalsVisibilitiesMinutes',
              title: B,
              min: new (a())(ne[0]),
              max: new (a())(ne[1]),
            },
          ),
          s = (0, i.createRangePropertyDefinition)(
            {
              checked: (0, i.convertToDefinitionProperty)(
                e,
                t.hours,
                w.format({ title: l }),
              ),
              from: (0, i.convertToDefinitionProperty)(
                e,
                t.hoursFrom,
                f.format({ title: l }),
              ),
              to: (0, i.convertToDefinitionProperty)(
                e,
                t.hoursTo,
                C.format({ title: l }),
              ),
            },
            {
              id: 'IntervalsVisibilitiesHours',
              title: M,
              min: new (a())(oe[0]),
              max: new (a())(oe[1]),
            },
          ),
          p = (0, i.createRangePropertyDefinition)(
            {
              checked: (0, i.convertToDefinitionProperty)(
                e,
                t.days,
                S.format({ title: l }),
              ),
              from: (0, i.convertToDefinitionProperty)(
                e,
                t.daysFrom,
                P.format({ title: l }),
              ),
              to: (0, i.convertToDefinitionProperty)(
                e,
                t.daysTo,
                T.format({ title: l }),
              ),
            },
            {
              id: 'IntervalsVisibilitiesDays',
              title: D,
              min: new (a())(ie[0]),
              max: new (a())(ie[1]),
            },
          );
        r.push(n, s, p);
        const d = (0, i.createRangePropertyDefinition)(
            {
              checked: (0, i.convertToDefinitionProperty)(
                e,
                t.weeks,
                E.format({ title: l }),
              ),
              from: (0, i.convertToDefinitionProperty)(
                e,
                t.weeksFrom,
                _.format({ title: l }),
              ),
              to: (0, i.convertToDefinitionProperty)(
                e,
                t.weeksTo,
                k.format({ title: l }),
              ),
            },
            {
              id: 'IntervalsVisibilitiesWeeks',
              title: N,
              min: new (a())(se[0]),
              max: new (a())(se[1]),
            },
          ),
          A = (0, i.createRangePropertyDefinition)(
            {
              checked: (0, i.convertToDefinitionProperty)(
                e,
                t.months,
                x.format({ title: l }),
              ),
              from: (0, i.convertToDefinitionProperty)(
                e,
                t.monthsFrom,
                I.format({ title: l }),
              ),
              to: (0, i.convertToDefinitionProperty)(
                e,
                t.monthsTo,
                L.format({ title: l }),
              ),
            },
            {
              id: 'IntervalsVisibilitiesMonths',
              title: W,
              min: new (a())(ae[0]),
              max: new (a())(ae[1]),
            },
          );
        return r.push(d, A), { definitions: r };
      }
      function pe(e, t) {
        const l = [];
        if (o.enabled('tick_resolution')) {
          const r = (0, i.createCheckablePropertyDefinition)(
            {
              checked: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.ticks),
                A,
                t,
              ),
            },
            { id: 'IntervalsVisibilitiesTicks', title: R },
          );
          l.push(r);
        }
        if ((0, c.isSecondsEnabled)()) {
          const r = (0, i.createRangePropertyDefinition)(
            {
              checked: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.seconds),
                z,
                t,
              ),
              from: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.secondsFrom),
                G,
                t,
              ),
              to: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.secondsTo),
                H,
                t,
              ),
            },
            {
              id: 'IntervalsVisibilitiesSecond',
              title: V,
              min: new (a())(re[0]),
              max: new (a())(re[1]),
            },
          );
          l.push(r);
        }
        const r = (0, i.createRangePropertyDefinition)(
            {
              checked: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.minutes),
                O,
                t,
              ),
              from: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.minutesFrom),
                F,
                t,
              ),
              to: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.minutesTo),
                U,
                t,
              ),
            },
            {
              id: 'IntervalsVisibilitiesMinutes',
              title: B,
              min: new (a())(ne[0]),
              max: new (a())(ne[1]),
            },
          ),
          n = (0, i.createRangePropertyDefinition)(
            {
              checked: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.hours),
                q,
                t,
              ),
              from: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.hoursFrom),
                K,
                t,
              ),
              to: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.hoursTo),
                Q,
                t,
              ),
            },
            {
              id: 'IntervalsVisibilitiesHours',
              title: M,
              min: new (a())(oe[0]),
              max: new (a())(oe[1]),
            },
          ),
          s = (0, i.createRangePropertyDefinition)(
            {
              checked: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.days),
                Z,
                t,
              ),
              from: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.daysFrom),
                j,
                t,
              ),
              to: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.daysTo),
                X,
                t,
              ),
            },
            {
              id: 'IntervalsVisibilitiesDays',
              title: D,
              min: new (a())(ie[0]),
              max: new (a())(ie[1]),
            },
          );
        l.push(r, n, s);
        const h = (0, i.createRangePropertyDefinition)(
            {
              checked: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.weeks),
                Y,
                t,
              ),
              from: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.weeksFrom),
                $,
                t,
              ),
              to: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.weeksTo),
                J,
                t,
              ),
            },
            {
              id: 'IntervalsVisibilitiesWeeks',
              title: N,
              min: new (a())(se[0]),
              max: new (a())(se[1]),
            },
          ),
          u = (0, i.createRangePropertyDefinition)(
            {
              checked: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.months),
                ee,
                t,
              ),
              from: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.monthsFrom),
                te,
                t,
              ),
              to: new d.CollectiblePropertyUndoWrapper(
                new p.LineToolCollectedProperty(e.monthsTo),
                le,
                t,
              ),
            },
            {
              id: 'IntervalsVisibilitiesMonths',
              title: W,
              min: new (a())(ae[0]),
              max: new (a())(ae[1]),
            },
          );
        return l.push(h, u), { definitions: l };
      }
    },
    85804: (e, t, l) => {
      'use strict';
      l.d(t, { CollectiblePropertyUndoWrapper: () => c });
      var r = l(50151),
        n = l(44352),
        o = l(47539),
        i = l(26220),
        s = l.n(i);
      const a = new o.TranslatedString(
        'change {propertyName} property',
        n.t(null, void 0, l(18567)),
      );
      class c extends s() {
        constructor(e, t, l) {
          super(),
            (this._isProcess = !1),
            (this._listenersMappers = []),
            (this._valueApplier = {
              applyValue: (e, t) => {
                this._propertyApplier.setProperty(e, t, a);
              },
            }),
            (this._baseProperty = e),
            (this._propertyApplier = l),
            (this._propertyName = t);
        }
        destroy() {
          this._baseProperty.destroy();
        }
        value() {
          return this._baseProperty.value();
        }
        setValue(e, t) {
          this._propertyApplier.beginUndoMacro(
            a.format({ propertyName: this._propertyName }),
          ),
            (this._isProcess = !0),
            this._baseProperty.setValue(e, void 0, this._valueApplier),
            (this._isProcess = !1),
            this._propertyApplier.endUndoMacro(),
            this._listenersMappers.forEach((e) => {
              e.method.call(e.obj, this);
            });
        }
        subscribe(e, t) {
          const l = () => {
            this._isProcess || t.call(e, this);
          };
          this._listenersMappers.push({ obj: e, method: t, callback: l }),
            this._baseProperty.subscribe(e, l);
        }
        unsubscribe(e, t) {
          var l;
          const n = (0, r.ensureDefined)(
            null ===
              (l = this._listenersMappers.find(
                (l) => l.obj === e && l.method === t,
              )) || void 0 === l
              ? void 0
              : l.callback,
          );
          this._baseProperty.unsubscribe(e, n);
        }
        unsubscribeAll(e) {
          this._baseProperty.unsubscribeAll(e);
        }
      }
    },
    23460: (e, t, l) => {
      'use strict';
      l.d(t, { StudyPlotVisibleProperty: () => o });
      var r = l(26867),
        n = l.n(r);
      class o {
        constructor(e) {
          (this._subscribers = new (n())()),
            (this._displayProperty = e),
            this._displayProperty.subscribe(
              this,
              this._displayPropertyValueChanged,
            );
        }
        destroy() {
          this._displayProperty.unsubscribe(
            this,
            this._displayPropertyValueChanged,
          );
        }
        value() {
          return 0 !== this._displayProperty.value();
        }
        setValue(e, t) {
          this._displayProperty.setValue(e ? 15 : 0);
        }
        subscribe(e, t) {
          this._subscribers.subscribe(e, t, !1);
        }
        unsubscribe(e, t) {
          this._subscribers.unsubscribe(e, t);
        }
        unsubscribeAll(e) {
          this._subscribers.unsubscribeAll(e);
        }
        storeStateIfUndefined() {
          return !1;
        }
        _displayPropertyValueChanged() {
          this._subscribers.fire(this);
        }
      }
    },
    61380: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 10" width="20" height="10"><path fill="none" stroke="currentColor" stroke-width="1.5" d="M2 1l8 8 8-8"/></svg>';
    },
    69151: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 21l7.424-6.114a.5.5 0 0 0-.318-.886H18.5V7h-9v7H6.894a.5.5 0 0 0-.318.886L14 21z"/></svg>';
    },
    67211: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 7l7.424 6.114a.5.5 0 0 1-.318.886H18.5v7h-9v-7H6.894a.5.5 0 0 1-.318-.886L14 7z"/></svg>';
    },
    83786: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><circle stroke="currentColor" cx="14" cy="14" r="6.5"/></svg>';
    },
    50858: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 14.5h11M14.5 20V9"/></svg>';
    },
    13201: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14.354 6.646L14 6.293l-.354.353-7 7-.353.354.353.354 7 7 .354.353.354-.353 7-7 .353-.354-.353-.354-7-7z"/></svg>';
    },
    59058: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 22v-5.5m0 0v-8L12 7l4 2.5 3.5-1v8l-3.5 1-4-2.5-3.5 1.5z"/></svg>';
    },
    8537: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 8.5h-.5v9.707l.146.147 3 3 .354.353.354-.353 3-3 .146-.147V8.5H11z"/></svg>';
    },
    2309: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 18.5h-.5V8.793l.146-.147 3-3L14 5.293l.354.353 3 3 .146.147V18.5H11z"/></svg>';
    },
    78240: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 7.5h13v13h-13z"/></svg>';
    },
    41683: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 11.265l.478-.765H8.098l.478.765 5 8 .424.678.424-.678 5-8z"/></svg>';
    },
    6570: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 16.735l.478.765H8.098l.478-.765 5-8L14 8.057l.424.678 5 8z"/></svg>';
    },
    23223: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 9l11 11M9 20L20 9"/></svg>';
    },
    93976: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M13 11.5l-1.915-1.532a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82V18.5a1 1 0 0 0 1 1H13m3.5-7l4.293-4.293c.63-.63 1.707-.184 1.707.707V18.5a1 1 0 0 1-1 1H16"/><path fill="currentColor" d="M14 6h1v2h-1zM14 11h1v2h-1zM14 16h1v2h-1zM14 21h1v2h-1z"/></svg>';
    },
    91512: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 13.52v4.98a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1V8.914c0-.89-1.077-1.337-1.707-.707l-4.66 4.66a1 1 0 0 1-1.332.074l-3.716-2.973a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82z"/></svg>';
    },
    21579: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M10.5 13a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM16.5 19a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM22.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/></svg>';
    },
    72914: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 12.5v8h3v-8h-3zM12.5 7.5v13h3v-13h-3zM18.5 15.5v5h3v-5h-3z"/></svg>';
    },
    98450: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M17 8.5h7M20.5 12V5M10 19.5h7M13.5 23v-7M3 12.5h7M6.5 16V9"/></svg>';
    },
    18621: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4.5 20v-7m3 7V10m3 10V8m3 12V10m3 10v-8m3 8V10m3 10V8"/></svg>';
    },
    18819: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l5-5a1.414 1.414 0 0 1 2 0m11-1l-5 5a1.414 1.414 0 0 1-2 0"/><path fill="currentColor" d="M14 5h1v2h-1zM14 10h1v2h-1zM14 15h1v2h-1zM14 20h1v2h-1z"/></svg>';
    },
    94152: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>';
    },
    46464: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 3h1v2h-1V3Zm1 5h-1v2h1V8Zm-1 5h1v2h-1v-2Zm0 5h1v2h-1v-2Zm0 5h1v2h-1v-2ZM10 5h2V4H9v18H6v-5H5v6h5V5Zm11 16h1V7h-5v10h1V8h3v13Z"/></svg>';
    },
    96298: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M9.8 2.7l.7-.7.7.7 2.1 2.1.2.2H18v9.5l.2.2 2.1 2.1.2.2H24v1h-3.5l-.2.2-2.1 2.1-.7.7-.7-.7-2.1-2.1-.7-.7.7-.7 2.1-2.1.2-.2V6h-3.5l-.2.2-2.1 2.1-.2.2V24H5.5v-1H10V8.5l-.2-.2-2.1-2.1-.7-.7.7-.7 2.1-2.1zM8.4 5.5l2.09 2.09 2.09-2.09-2.09-2.09L8.41 5.5zm9.09 14.09l-2.09-2.09 2.09-2.09 2.09 2.09-2.09 2.09z"/></svg>';
    },
    14643: (e) => {
      e.exports =
        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 17v5.5h4v-18h4v12h4v-9h4V21"/></svg>';
    },
  },
]);
