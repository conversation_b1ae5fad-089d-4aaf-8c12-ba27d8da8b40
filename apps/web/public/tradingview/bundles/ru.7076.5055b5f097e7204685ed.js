(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [7076],
  {
    20747: (t) => {
      t.exports = ['Сим.'];
    },
    14642: (t) => {
      t.exports = ['Тёмная'];
    },
    69841: (t) => {
      t.exports = ['Светлая'];
    },
    673: (t) => {
      (t.exports = Object.create(null)),
        (t.exports.d_dates = ['д']),
        (t.exports.h_dates = ['ч']),
        (t.exports.m_dates = ['м']),
        (t.exports.s_dates = ['с']),
        (t.exports.in_dates = ['за']);
    },
    97840: (t) => {
      t.exports = ['д'];
    },
    64302: (t) => {
      t.exports = ['ч'];
    },
    79442: (t) => {
      t.exports = ['м'];
    },
    22448: (t) => {
      t.exports = ['с'];
    },
    16493: (t) => {
      t.exports = ['Копия {title}'];
    },
    13395: (t) => {
      t.exports = ['Д'];
    },
    37720: (t) => {
      t.exports = ['Мес'];
    },
    69838: (t) => {
      t.exports = 'R';
    },
    59231: (t) => {
      t.exports = ['Т'];
    },
    85521: (t) => {
      t.exports = ['Н'];
    },
    13994: (t) => {
      t.exports = ['Ч'];
    },
    6791: (t) => {
      t.exports = ['М'];
    },
    2949: (t) => {
      t.exports = ['С'];
    },
    77297: (t) => {
      t.exports = ['ЗАКР'];
    },
    56723: (t) => {
      t.exports = ['МАКС'];
    },
    5801: (t) => {
      t.exports = ['Макс-Мин-2'];
    },
    98865: (t) => {
      t.exports = ['Макс-Мин-Закр-3'];
    },
    42659: (t) => {
      t.exports = ['Откр-Макс-Мин-Закр-4'];
    },
    4292: (t) => {
      t.exports = ['МИН'];
    },
    78155: (t) => {
      t.exports = ['ОТКР'];
    },
    88601: (t) => {
      (t.exports = Object.create(null)),
        (t.exports.Close_input = ['Закрыть']),
        (t.exports.Back_input = ['Назад']),
        (t.exports['Hull MA_input'] = ['Скользящее среднее Хала']),
        (t.exports['{number} item_combobox_input'] = [
          '{number} критерий',
          '{number} критерия',
          '{number} критериев',
          '{number} критериев',
        ]),
        (t.exports.Length_input = ['Длина']),
        (t.exports.Plot_input = ['Граф.отображение']),
        (t.exports.Zero_input = ['Ноль']),
        (t.exports.Signal_input = ['Сигнал']),
        (t.exports.Long_input = ['Длинная']),
        (t.exports.Short_input = ['Короткая']),
        (t.exports.UpperLimit_input = ['Верхн.Лимит']),
        (t.exports.LowerLimit_input = ['Нижни.Лимит']),
        (t.exports.Offset_input = ['Отступ']),
        (t.exports.length_input = ['длина']),
        (t.exports.mult_input = ['умнож.']),
        (t.exports.short_input = ['короткая']),
        (t.exports.long_input = ['длинная']),
        (t.exports.Limit_input = ['Лимит']),
        (t.exports.Move_input = ['Шаг']),
        (t.exports.Value_input = ['Значение']),
        (t.exports.Method_input = ['Метод']),
        (t.exports['Values in status line_input'] = [
          'Значения в строке статуса',
        ]),
        (t.exports['Labels on price scale_input'] = ['Метки на ценовой шкале']),
        (t.exports['Accumulation/Distribution_input'] = [
          'Накопление/Распределение',
        ]),
        (t.exports.ADR_B_input = 'ADR_B'),
        (t.exports['Equality Line_input'] = ['Линия равенства']),
        (t.exports['Window Size_input'] = ['Размер окна']),
        (t.exports.Sigma_input = ['Сигма']),
        (t.exports['Aroon Up_input'] = ['Арун вверх']),
        (t.exports['Aroon Down_input'] = ['Арун вниз']),
        (t.exports.Upper_input = ['Верхн.']),
        (t.exports.Lower_input = ['Нижний']),
        (t.exports.Deviation_input = ['Отклонение']),
        (t.exports['Levels Format_input'] = ['Формат уровней']),
        (t.exports['Labels Position_input'] = ['Позиция меток']),
        (t.exports['0 Level Color_input'] = ['Цвет уровня 0']),
        (t.exports['0.236 Level Color_input'] = ['Цвет уровня 0.236']),
        (t.exports['0.382 Level Color_input'] = ['Цвет уровня 0.382']),
        (t.exports['0.5 Level Color_input'] = ['Цвет уровня 0.5']),
        (t.exports['0.618 Level Color_input'] = ['Цвет уровня 0.618']),
        (t.exports['0.65 Level Color_input'] = ['Цвет уровня 0.65']),
        (t.exports['0.786 Level Color_input'] = ['Цвет уровня 0.786']),
        (t.exports['1 Level Color_input'] = ['Цвет уровня 1']),
        (t.exports['1.272 Level Color_input'] = ['Цвет уровня 1.272']),
        (t.exports['1.414 Level Color_input'] = ['Цвет уровня 1.414']),
        (t.exports['1.618 Level Color_input'] = ['Цвет уровня 1.618']),
        (t.exports['1.65 Level Color_input'] = ['Цвет уровня 1.65']),
        (t.exports['2.618 Level Color_input'] = ['Цвет уровня 2.618']),
        (t.exports['2.65 Level Color_input'] = ['Цвет уровня 2.65']),
        (t.exports['3.618 Level Color_input'] = ['Цвет уровня 3.618']),
        (t.exports['3.65 Level Color_input'] = ['Цвет уровня 3.65']),
        (t.exports['4.236 Level Color_input'] = ['Цвет уровня 4.236']),
        (t.exports['-0.236 Level Color_input'] = ['Цвет уровня -0.236']),
        (t.exports['-0.382 Level Color_input'] = ['Цвет уровня -0.382']),
        (t.exports['-0.618 Level Color_input'] = ['Цвет уровня -0.618']),
        (t.exports['-0.65 Level Color_input'] = ['Цвет уровня -0.65']),
        (t.exports.ADX_input = 'ADX'),
        (t.exports['ADX Smoothing_input'] = ['Сглаживание ADX']),
        (t.exports['DI Length_input'] = ['DI Длина']),
        (t.exports.Smoothing_input = ['Сглаживание']),
        (t.exports.ATR_input = 'ATR'),
        (t.exports.Growing_input = ['Растущий']),
        (t.exports.Falling_input = ['Нисходящий']),
        (t.exports['Color 0_input'] = ['Цвет 0']),
        (t.exports['Color 1_input'] = ['Цвет 1']),
        (t.exports.Source_input = ['Данные']),
        (t.exports.StdDev_input = ['Станд. отклон.']),
        (t.exports.Basis_input = ['Базовая линия']),
        (t.exports.Median_input = ['Средняя линия']),
        (t.exports['Bollinger Bands %B_input'] = ['%B Полос Боллинджера']),
        (t.exports.Overbought_input = ['Перекуплены']),
        (t.exports.Oversold_input = ['Перепроданы']),
        (t.exports['Bollinger Bands Width_input'] = [
          'Ширина полос Боллинджера',
        ]),
        (t.exports['RSI Length_input'] = ['Длина RSI']),
        (t.exports['UpDown Length_input'] = ['Длина вверх/вниз']),
        (t.exports['ROC Length_input'] = ['Длина ROC']),
        (t.exports.MF_input = 'MF'),
        (t.exports.resolution_input = ['интервал']),
        (t.exports['Fast Length_input'] = ['Длина Fast']),
        (t.exports['Slow Length_input'] = ['Длина Slow']),
        (t.exports['Chaikin Oscillator_input'] = ['Осциллятор Чайкина']),
        (t.exports.P_input = 'P'),
        (t.exports.X_input = 'X'),
        (t.exports.Q_input = 'Q'),
        (t.exports.p_input = 'p'),
        (t.exports.x_input = 'x'),
        (t.exports.q_input = 'q'),
        (t.exports.Price_input = ['Цена']),
        (t.exports['Chande MO_input'] = ['Осциллятор темпа Чанде']),
        (t.exports['Zero Line_input'] = ['Нулевая линия']),
        (t.exports['Color 2_input'] = ['Цвет 2']),
        (t.exports['Color 3_input'] = ['Цвет 3']),
        (t.exports['Color 4_input'] = ['Цвет 4']),
        (t.exports['Color 5_input'] = ['Цвет 5']),
        (t.exports['Color 6_input'] = ['Цвет 6']),
        (t.exports['Color 7_input'] = ['Цвет 7']),
        (t.exports['Color 8_input'] = ['Цвет 8']),
        (t.exports.CHOP_input = 'CHOP'),
        (t.exports['Upper Band_input'] = ['Верхняя полоса']),
        (t.exports['Lower Band_input'] = ['Нижняя полоса']),
        (t.exports.CCI_input = 'CCI'),
        (t.exports['Smoothing Line_input'] = ['Линия сглаживания']),
        (t.exports['Smoothing Length_input'] = ['Длина сглаживания']),
        (t.exports['WMA Length_input'] = ['Длина WMA']),
        (t.exports['Long RoC Length_input'] = ['Длина Long RoC']),
        (t.exports['Short RoC Length_input'] = ['Длина Short RoC']),
        (t.exports.sym_input = 'sym'),
        (t.exports.Symbol_input = ['Инструмент']),
        (t.exports.Correlation_input = ['корреляция']),
        (t.exports.Period_input = ['Период']),
        (t.exports.Centered_input = ['Центральный']),
        (t.exports['Detrended Price Oscillator_input'] = [
          'Детрендовый ценовой осциллятор',
        ]),
        (t.exports.isCentered_input = ['По центру']),
        (t.exports.DPO_input = 'DPO'),
        (t.exports['ADX smoothing_input'] = ['Сглаживание ADX']),
        (t.exports['+DI_input'] = '+DI'),
        (t.exports['-DI_input'] = '-DI'),
        (t.exports.DEMA_input = 'DEMA'),
        (t.exports.Divisor_input = ['Разделитель']),
        (t.exports.EOM_input = 'EOM'),
        (t.exports["Elder's Force Index_input"] = ['Индекс силы Элдера']),
        (t.exports.Percent_input = ['Процент']),
        (t.exports.Exponential_input = ['Экспоненциальное']),
        (t.exports.Average_input = ['Среднее']),
        (t.exports['Upper Percentage_input'] = ['Верхний процент']),
        (t.exports['Lower Percentage_input'] = ['Нижний процент']),
        (t.exports.Fisher_input = 'Fisher'),
        (t.exports.Trigger_input = ['Условие срабатывания']),
        (t.exports.Level_input = ['Уровень']),
        (t.exports['Trader EMA 1 length_input'] = ['Длина EMA 1 трейдера']),
        (t.exports['Trader EMA 2 length_input'] = ['Длина EMA 2 трейдера']),
        (t.exports['Trader EMA 3 length_input'] = ['Длина EMA 3 трейдера']),
        (t.exports['Trader EMA 4 length_input'] = ['Длина EMA 4 трейдера']),
        (t.exports['Trader EMA 5 length_input'] = ['Длина EMA 5 трейдера']),
        (t.exports['Trader EMA 6 length_input'] = ['Длина EMA 6 трейдера']),
        (t.exports['Investor EMA 1 length_input'] = ['Длина EMA 1 инвестора']),
        (t.exports['Investor EMA 2 length_input'] = ['Длина EMA 2 инвестора']),
        (t.exports['Investor EMA 3 length_input'] = ['Длина EMA 3 инвестора']),
        (t.exports['Investor EMA 4 length_input'] = ['Длина EMA 4 инвестора']),
        (t.exports['Investor EMA 5 length_input'] = ['Длина EMA 5 инвестора']),
        (t.exports['Investor EMA 6 length_input'] = ['Длина EMA 6 инвестора']),
        (t.exports.HV_input = 'HV'),
        (t.exports['Conversion Line Periods_input'] = [
          'Периоды Линии переворота',
        ]),
        (t.exports['Base Line Periods_input'] = ['Периоды Линии стандарта']),
        (t.exports['Lagging Span_input'] = ['Опаздывающая линия']),
        (t.exports['Conversion Line_input'] = ['Линия переворота']),
        (t.exports['Base Line_input'] = ['Линия стандарта']),
        (t.exports['Leading Span A_input'] = ['Верховая линия 1']),
        (t.exports['Leading Span B_input'] = ['Опаздывающая линия 2 периода']),
        (t.exports['Plots Background_input'] = ['Заливка фона']),
        (t.exports['yay Color 0_input'] = ['yay Цвет 2']),
        (t.exports['yay Color 1_input'] = ['yay Цвет 1']),
        (t.exports.Multiplier_input = ['Множитель']),
        (t.exports['Bands style_input'] = ['Стиль полос']),
        (t.exports.Middle_input = ['Центральная линия']),
        (t.exports.useTrueRange_input = ['Исп. истинный диапазон']),
        (t.exports.ROCLen1_input = ['ROC Длин.1']),
        (t.exports.ROCLen2_input = ['ROC Длин.2']),
        (t.exports.ROCLen3_input = ['ROC Длин.3']),
        (t.exports.ROCLen4_input = ['ROC Длин.4']),
        (t.exports.SMALen1_input = ['SMA Длин.1']),
        (t.exports.SMALen2_input = ['SMA Длин.2']),
        (t.exports.SMALen3_input = ['SMA Длин.3']),
        (t.exports.SMALen4_input = ['SMA Длин.4']),
        (t.exports.SigLen_input = ['Дл.Сигн.']),
        (t.exports.KST_input = 'KST'),
        (t.exports.Sig_input = ['Сиг.']),
        (t.exports.roclen1_input = ['Roc длин.1']),
        (t.exports.roclen2_input = ['Roc длин.2']),
        (t.exports.roclen3_input = ['Roc длин.3']),
        (t.exports.roclen4_input = ['Roc длин.4']),
        (t.exports.smalen1_input = ['sma длин.1']),
        (t.exports.smalen2_input = ['sma длин.2']),
        (t.exports.smalen3_input = ['sma длин.3']),
        (t.exports.smalen4_input = ['sma длин.4']),
        (t.exports.siglen_input = ['Длин. сигн.']),
        (t.exports['Upper Deviation_input'] = ['Верхнее отклонение']),
        (t.exports['Lower Deviation_input'] = ['Нижнее отклонение']),
        (t.exports['Use Upper Deviation_input'] = [
          'Использовать верхнее отклонение',
        ]),
        (t.exports['Use Lower Deviation_input'] = [
          'Использовать нижнее отклонение',
        ]),
        (t.exports.Count_input = ['Количество']),
        (t.exports.Crosses_input = ['Пересечения']),
        (t.exports.MOM_input = 'MOM'),
        (t.exports.MA_input = 'MA'),
        (t.exports['Length EMA_input'] = ['Длина EMA']),
        (t.exports['Length MA_input'] = ['Длина MA']),
        (t.exports['Fast length_input'] = ['Длина Fast']),
        (t.exports['Slow length_input'] = ['Длина Slow']),
        (t.exports['Signal smoothing_input'] = ['Сглаживание сигнала']),
        (t.exports['Simple ma(oscillator)_input'] = [
          'Прост. скольз.сред. (осциллятор)',
        ]),
        (t.exports['Simple ma(signal line)_input'] = [
          'Прост. скольз.сред. (линия сигнала)',
        ]),
        (t.exports.Histogram_input = ['Гистограмма']),
        (t.exports.MACD_input = 'MACD'),
        (t.exports.fastLength_input = 'fastLength'),
        (t.exports.slowLength_input = 'slowLength'),
        (t.exports.signalLength_input = ['Длин. сигнала']),
        (t.exports.NV_input = 'NV'),
        (t.exports.OnBalanceVolume_input = ['Балансовый объём']),
        (t.exports.Start_input = ['Начать']),
        (t.exports.Increment_input = ['Шаг']),
        (t.exports['Max value_input'] = ['Макс. значение']),
        (t.exports.ParabolicSAR_input = ['Параболическая система SAR']),
        (t.exports.start_input = ['начало']),
        (t.exports.increment_input = ['шаг']),
        (t.exports.maximum_input = ['максимум']),
        (t.exports['Short length_input'] = ['Длина Short']),
        (t.exports['Long length_input'] = ['Длина Long']),
        (t.exports.OSC_input = 'OSC'),
        (t.exports.shortlen_input = 'shortlen'),
        (t.exports.longlen_input = 'longlen'),
        (t.exports.PVT_input = 'PVT'),
        (t.exports.ROC_input = 'ROC'),
        (t.exports.RSI_input = 'RSI'),
        (t.exports.RVGI_input = 'RVGI'),
        (t.exports.RVI_input = 'RVI'),
        (t.exports['Long period_input'] = ['Длинный период']),
        (t.exports['Short period_input'] = ['Короткий период']),
        (t.exports['Signal line period_input'] = ['Период линии сигнала']),
        (t.exports.SMI_input = 'SMI'),
        (t.exports['SMI Ergodic Oscillator_input'] = [
          'Осциллятор SMI Ergodic',
        ]),
        (t.exports.Indicator_input = ['Индикатор']),
        (t.exports.Oscillator_input = ['Осциллятор']),
        (t.exports.K_input = 'K'),
        (t.exports.D_input = 'D'),
        (t.exports.smoothK_input = 'smoothK'),
        (t.exports.smoothD_input = 'smoothD'),
        (t.exports['%K_input'] = '%K'),
        (t.exports['%D_input'] = '%D'),
        (t.exports['Stochastic Length_input'] = ['Длина Стохастик']),
        (t.exports['RSI Source_input'] = ['Данные для RSI']),
        (t.exports.lengthRSI_input = ['длина RSI']),
        (t.exports.lengthStoch_input = ['длина Стох.']),
        (t.exports.TRIX_input = 'TRIX'),
        (t.exports.TEMA_input = 'TEMA'),
        (t.exports['Long Length_input'] = ['Длина Long']),
        (t.exports['Short Length_input'] = ['Длина Short']),
        (t.exports['Signal Length_input'] = ['Длина сигнала']),
        (t.exports.Length1_input = ['Длина1']),
        (t.exports.Length2_input = ['Длина2']),
        (t.exports.Length3_input = ['Длина3']),
        (t.exports.length7_input = ['длина 7']),
        (t.exports.length14_input = ['длина 14']),
        (t.exports.length28_input = ['длина 28']),
        (t.exports.UO_input = 'UO'),
        (t.exports.VWMA_input = 'VWMA'),
        (t.exports.len_input = ['длина']),
        (t.exports['VI +_input'] = 'VI +'),
        (t.exports['VI -_input'] = 'VI -'),
        (t.exports['%R_input'] = '%R'),
        (t.exports['Jaw Length_input'] = ['Длина Jaw']),
        (t.exports['Teeth Length_input'] = ['Длина Teeth']),
        (t.exports['Lips Length_input'] = ['Длина Lips']),
        (t.exports.Jaw_input = 'Jaw'),
        (t.exports.Teeth_input = 'Teeth'),
        (t.exports.Lips_input = 'Lips'),
        (t.exports['Jaw Offset_input'] = ['Смещение Jaw']),
        (t.exports['Teeth Offset_input'] = ['Смещение Teeth']),
        (t.exports['Lips Offset_input'] = ['Смещение Lips']),
        (t.exports['Down fractals_input'] = ['Нижние фракталы']),
        (t.exports['Up fractals_input'] = ['Верхние фракталы']),
        (t.exports.Periods_input = ['Периоды']),
        (t.exports.Shapes_input = ['Фигуры']),
        (t.exports['show MA_input'] = ['показать MA']),
        (t.exports['MA Length_input'] = ['Длина MA']),
        (t.exports['Color based on previous close_input'] = [
          'Цвет зависит от цены предыдущего закрытия',
        ]),
        (t.exports['Rows Layout_input'] = ['Размещение строк']),
        (t.exports['Row Size_input'] = ['Размер строки']),
        (t.exports.Volume_input = ['Объём']),
        (t.exports['Value Area volume_input'] = ['Объём зоны стоимости']),
        (t.exports['Extend Right_input'] = ['Продолжить вправо']),
        (t.exports['Extend POC Right_input'] = ['Продолжить POC вправо']),
        (t.exports['Extend VAH Right_input'] = ['Продолжить VAH вправо']),
        (t.exports['Extend VAL Right_input'] = ['Продолжить VAL вправо']),
        (t.exports['Value Area Volume_input'] = ['Объём зоны стоимости']),
        (t.exports.Placement_input = ['Расположение']),
        (t.exports.POC_input = 'POC'),
        (t.exports['Developing Poc_input'] = [
          'Динамическая точка контроля (POC)',
        ]),
        (t.exports['Up Volume_input'] = ['Растущий объём']),
        (t.exports['Down Volume_input'] = ['Снижающийся объём']),
        (t.exports['Value Area_input'] = ['Зона стоимости']),
        (t.exports['Histogram Box_input'] = ['Прямоугольник гистограммы']),
        (t.exports['Value Area Up_input'] = ['Восходящ. зона стоимости']),
        (t.exports['Value Area Down_input'] = ['Нисходящ. зона стоимости']),
        (t.exports['Number Of Rows_input'] = ['Число строк']),
        (t.exports['Ticks Per Row_input'] = ['Тики в строке']),
        (t.exports['Up/Down_input'] = ['Вверх/Вниз']),
        (t.exports.Total_input = ['Итого']),
        (t.exports.Delta_input = ['Дельта']),
        (t.exports.Bar_input = ['Бар']),
        (t.exports.Day_input = ['День']),
        (t.exports['Deviation (%)_input'] = ['Отклонение (%)']),
        (t.exports.Depth_input = ['Глубина']),
        (t.exports['Extend to last bar_input'] = [
          'Продолжить до последнего бара',
        ]),
        (t.exports.Simple_input = ['Простое']),
        (t.exports.Weighted_input = ['Взвешенное']),
        (t.exports["Wilder's Smoothing_input"] = ['Сглаживание Уайлдера']),
        (t.exports['1st Period_input'] = ['1-й период']),
        (t.exports['2nd Period_input'] = ['2-й период']),
        (t.exports['3rd Period_input'] = ['3-й период']),
        (t.exports['4th Period_input'] = ['4-й период']),
        (t.exports['5th Period_input'] = ['5-й период']),
        (t.exports['6th Period_input'] = ['6-й период']),
        (t.exports['Rate of Change Lookback_input'] =
          'Rate of Change Lookback'),
        (t.exports['Instrument 1_input'] = ['Инструмент 1']),
        (t.exports['Instrument 2_input'] = ['Инструмент 2']),
        (t.exports['Rolling Period_input'] = 'Rolling Period'),
        (t.exports['Standard Errors_input'] = ['Стандартные ошибки']),
        (t.exports['Averaging Periods_input'] = ['Периоды усреднения']),
        (t.exports['Days Per Year_input'] = ['Дней в году']),
        (t.exports['Market Closed Percentage_input'] =
          'Market Closed Percentage'),
        (t.exports['ATR Mult_input'] = ['ATR Множ.']),
        (t.exports.VWAP_input = 'VWAP'),
        (t.exports['Anchor Period_input'] = ['Временной период']),
        (t.exports.Session_input = ['Сессия']),
        (t.exports.Week_input = ['Неделя']),
        (t.exports.Month_input = ['Месяц']),
        (t.exports.Year_input = ['Год']),
        (t.exports.Decade_input = ['Декада']),
        (t.exports.Century_input = ['Век']),
        (t.exports.Sessions_input = ['Сессии']),
        (t.exports['Each (pre-market, market, post-market)_input'] = [
          'Каждая (премаркет, основная, постмаркет)',
        ]),
        (t.exports['Pre-market only_input'] = ['Только премаркет']),
        (t.exports['Market only_input'] = ['Только торговая сессия']),
        (t.exports['Post-market only_input'] = ['Только постмаркет']),
        (t.exports['Main chart symbol_input'] = ['Главный символ графика']),
        (t.exports['Another symbol_input'] = ['Другой символ']),
        (t.exports.Line_input = ['Линия']),
        (t.exports['Nothing selected_combobox_input'] = ['Ничего не выбрано']),
        (t.exports['All items_combobox_input'] = ['Все критерии']),
        (t.exports.Cancel_input = ['Отменить']),
        (t.exports.Open_input = ['Открыть']);
    },
    54138: (t) => {
      t.exports = ['Инвертировать шкалу'];
    },
    47807: (t) => {
      t.exports = ['Индексированная на 100'];
    },
    34727: (t) => {
      t.exports = ['Логарифмическая'];
    },
    19238: (t) => {
      t.exports = ['Не перекрывать метки'];
    },
    70361: (t) => {
      t.exports = ['Процентная'];
    },
    72116: (t) => {
      t.exports = ['Обычная'];
    },
    33021: (t) => {
      t.exports = ['Расш.'];
    },
    75610: (t) => {
      t.exports = ['Электронная торговая сессия'];
    },
    97442: (t) => {
      t.exports = ['Торговые данные вне сессии'];
    },
    32929: (t) => {
      t.exports = ['пост'];
    },
    56137: (t) => {
      t.exports = ['пре'];
    },
    98801: (t) => {
      t.exports = ['Постмаркет'];
    },
    56935: (t) => {
      t.exports = ['Премаркет'];
    },
    63798: (t) => {
      t.exports = ['рег'];
    },
    24380: (t) => {
      t.exports = ['Регулярная торговая сессия'];
    },
    27991: (t) => {
      t.exports = ['Май'];
    },
    68716: (t) => {
      (t.exports = Object.create(null)),
        (t.exports['Amortization of Intangibles_study'] = [
          'Амортизация нематериальных активов',
        ]),
        (t.exports['Amortization of Deferred Charges_study'] = [
          'Амортизация расходов будущих периодов',
        ]),
        (t.exports['Average Day Range_study'] = ['Средний дневной диапазон']),
        (t.exports['Bull Bear Power_study'] = [
          '«Сила быков» и «Сила медведей»',
        ]),
        (t.exports['Directional Movement Index_study'] = [
          'Индекс направленного движения (DMI)',
        ]),
        (t.exports['Ichimoku Cloud_study'] = ['Облако Ишимоку']),
        (t.exports.Ichimoku_study = ['Ишимоку']),
        (t.exports['Moving Average Convergence Divergence_study'] = [
          'Схождение/расхождение скользящих средних',
        ]),
        (t.exports['Volume Weighted Average Price_study'] = [
          'Средневзвешенная цена объёма',
        ]),
        (t.exports['Volume Weighted Moving Average_study'] = [
          'Объёмно-взвешенное скользящее среднее',
        ]),
        (t.exports['Williams Percent Range_study'] = [
          'Процентный диапазон Вильямса',
        ]),
        (t.exports.Doji_study = ['Доджи']),
        (t.exports['Spinning Top Black_study'] = ['Чёрный волчок']),
        (t.exports['Spinning Top White_study'] = ['Белый волчок']),
        (t.exports.Technicals_study = ['Теханализ']),
        (t.exports['Accounts payable_study'] = ['Кредиторская задолженность']),
        (t.exports['Accounts receivables, gross_study'] = [
          'Дебиторская задолженность, валовая',
        ]),
        (t.exports['Accounts receivable - trade, net_study'] = [
          'Дебиторская задолженность - торговая, нетто',
        ]),
        (t.exports.Accruals_study = ['Начисления']),
        (t.exports['Accrued payroll_study'] = [
          'Задолженность по заработной плате',
        ]),
        (t.exports['Accumulated depreciation, total_study'] = [
          'Накопленная амортизация, итого',
        ]),
        (t.exports['Additional paid-in capital/Capital surplus_study'] = [
          'Добавочный капитал',
        ]),
        (t.exports['After tax other income/expense_study'] = [
          'Прочая прибыль (убыток) после налогообложения',
        ]),
        (t.exports['Altman Z-score_study'] = ['Коэффициент Альтмана']),
        (t.exports.Amortization_study = ['Амортизация']),
        (t.exports['Amortization of intangibles_study'] = [
          'Амортизация нематериальных активов',
        ]),
        (t.exports['Amortization of deferred charges_study'] = [
          'Амортизация расходов будущих периодов',
        ]),
        (t.exports['Asset turnover_study'] = ['Оборачиваемость активов']),
        (t.exports['Average basic shares outstanding_study'] = [
          'Среднее число акций в обращении',
        ]),
        (t.exports['Bad debt / Doubtful accounts_study'] = [
          'Безнадёжная задолженность / Сомнительная дебиторская задолженность',
        ]),
        (t.exports['Basic EPS_study'] = ['Базовая прибыль на акцию']),
        (t.exports['Basic earnings per share (Basic EPS)_study'] = [
          'Базовая прибыль на акцию',
        ]),
        (t.exports['Beneish M-score_study'] = ['Коэффициент Бениша']),
        (t.exports['Book value per share_study'] = [
          'Балансовая стоимость на акцию',
        ]),
        (t.exports['Buyback yield %_study'] = [
          'Доходность обратного выкупа акций, %',
        ]),
        (t.exports['Capital and operating lease obligations_study'] = [
          'Обязательства по финансовой и операционной аренде',
        ]),
        (t.exports['Capital expenditures_study'] = ['Капиталовложения']),
        (t.exports['Capital expenditures - fixed assets_study'] = [
          'Капиталовложения — основные средства',
        ]),
        (t.exports['Capital expenditures - other assets_study'] = [
          'Капиталовложения — прочие активы',
        ]),
        (t.exports['Capitalized lease obligations_study'] = [
          'Капитализированные арендные обязательства',
        ]),
        (t.exports['Cash and short term investments_study'] = [
          'Денежные средства и краткосрочные инвестиции',
        ]),
        (t.exports['Cash conversion cycle_study'] = [
          'Цикл обращения денежных средств',
        ]),
        (t.exports['Cash & equivalents_study'] = [
          'Денежные средства и их эквиваленты',
        ]),
        (t.exports['Cash from financing activities_study'] = [
          'Денежные средства от финансовой деятельности',
        ]),
        (t.exports['Cash from investing activities_study'] = [
          'Денежные средства от инвестиционной деятельности',
        ]),
        (t.exports['Cash from operating activities_study'] = [
          'Денежные средства от операционной деятельности',
        ]),
        (t.exports['Cash to debt ratio_study'] = [
          'Денежные средства/Задолженность',
        ]),
        (t.exports['Change in accounts payable_study'] = [
          'Изменения в кредиторской задолженности',
        ]),
        (t.exports['Change in accounts receivable_study'] = [
          'Изменение дебиторской задолженности',
        ]),
        (t.exports['Change in accrued expenses_study'] = [
          'Изменения в начисленных расходах',
        ]),
        (t.exports['Change in inventories_study'] = ['Изменения в запасах']),
        (t.exports['Change in other assets/liabilities_study'] = [
          'Изменения в других активах/обязательствах',
        ]),
        (t.exports['Change in taxes payable_study'] = [
          'Изменения в налогах к оплате',
        ]),
        (t.exports['Changes in working capital_study'] = [
          'Изменения оборотного капитала',
        ]),
        (t.exports['COGS to revenue ratio_study'] = [
          'Себестоимость реализованной продукции/Выручка',
        ]),
        (t.exports['Common dividends paid_study'] = [
          'Выплачено дивидендов по обыкновенным акциям',
        ]),
        (t.exports['Common equity, total_study'] = ['Основной капитал, итого']),
        (t.exports['Common stock par/Carrying value_study'] = [
          'Учётная стоимость обыкновенных акций',
        ]),
        (t.exports['Cost of goods_study'] = ['Себестоимость']),
        (t.exports['Cost of goods sold_study'] = [
          'Себестоимость реализованных товаров',
        ]),
        (t.exports['Current portion of LT debt and capital leases_study'] = [
          'Текущая часть долгосрочной зад-ти и обяз-в по аренде',
        ]),
        (t.exports['Current ratio_study'] = [
          'Коэффициент текущей ликвидности',
        ]),
        (t.exports['Days inventory_study'] = [
          'Период оборачиваемости запасов',
        ]),
        (t.exports['Days payable_study'] = [
          'Период погашения кредиторской задолженности',
        ]),
        (t.exports['Days sales outstanding_study'] = [
          'Период погашения дебиторской задолженности',
        ]),
        (t.exports['Debt to assets ratio_study'] = ['Задолженность/Активы']),
        (t.exports['Debt to EBITDA ratio_study'] = ['Задолженность/EBITDA']),
        (t.exports['Debt to equity ratio_study'] = ['Задолженность/Капитал']),
        (t.exports['Debt to revenue ratio_study'] = ['Задолженность/Выручка']),
        (t.exports['Deferred income, current_study'] = [
          'Доходы будущих периодов, оборотные',
        ]),
        (t.exports['Deferred income, non-current_study'] = [
          'Доходы будущих периодов, внеоборотные',
        ]),
        (t.exports['Deferred tax assets_study'] = [
          'Отложенные налоговые активы',
        ]),
        (t.exports['Deferred taxes (cash flow)_study'] = [
          'Отложенный налог (движение денежных средств)',
        ]),
        (t.exports['Deferred tax liabilities_study'] = [
          'Отложенные налоговые обязательства',
        ]),
        (t.exports.Depreciation_study = ['Амортизация']),
        (t.exports['Deprecation and amortization_study'] = [
          'Износ и амортизация',
        ]),
        (t.exports['Depreciation & amortization (cash flow)_study'] = [
          'Амортизация (движение денежных средств)',
        ]),
        (t.exports['Depreciation/depletion_study'] = ['Амортизация/износ']),
        (t.exports['Diluted EPS_study'] = ['Разводнённая прибыль на акцию']),
        (t.exports['Diluted earnings per share (Diluted EPS)_study'] = [
          'Разводнённая прибыль на акцию',
        ]),
        (t.exports[
          'Diluted net income available to common stockholders_study'
        ] = [
          'Разводн. чистая прибыль доступная владельцам обыкновенных акций',
        ]),
        (t.exports['Diluted shares outstanding_study'] = [
          'Разводнённые акции в обращении',
        ]),
        (t.exports['Dilution adjustment_study'] = [
          'Корректировка на разводнение',
        ]),
        (t.exports['Discontinued operations_study'] = [
          'Прекращённые операции',
        ]),
        (t.exports['Dividend payout ratio %_study'] = [
          'Коэффициент выплаты дивиденда %',
        ]),
        (t.exports['Dividends payable_study'] = ['Дивиденды к выплате']),
        (t.exports['Dividends per share - common stock primary issue_study'] = [
          'Дивиденды на акцию — первичный выпуск обыкновенных акций',
        ]),
        (t.exports['Dividend yield %_study'] = ['Дивидендная доходность, %']),
        (t.exports['Earnings yield_study'] = ['Доходность акции по прибыли']),
        (t.exports.EBIT_study = 'EBIT'),
        (t.exports.EBITDA_study = [
          'EBITDA (прибыль до вычета процентов, налогов и амортизации)',
        ]),
        (t.exports['EBITDA margin %_study'] = ['Рентабельность по EBITDA %']),
        (t.exports['Effective interest rate on debt %_study'] = [
          'Действующая процентная ставка по задолж-ти, %',
        ]),
        (t.exports['Enterprise value_study'] = ['Стоимость компании']),
        (t.exports['Enterprise value to EBITDA ratio_study'] = [
          'Стоимость компании/EBITDA',
        ]),
        (t.exports['Enterprise value to EBIT ratio_study'] = [
          'Стоимость компании/EBIT',
        ]),
        (t.exports['Enterprise value to revenue ratio_study'] = [
          'Стоимость компании/Выручка',
        ]),
        (t.exports['EPS basic one year growth_study'] = [
          'Базовая приб./акцию — годовой рост',
        ]),
        (t.exports['EPS diluted one year growth_study'] = [
          'Разводн. приб./акцию — годовой рост',
        ]),
        (t.exports['EPS estimates_study'] = ['Оценка приб./акцию']),
        (t.exports['Equity in earnings_study'] = [
          'Доходы от участия в других организациях',
        ]),
        (t.exports['Equity to assets ratio_study'] = ['Капитал/активы']),
        (t.exports['Financing activities – other sources_study'] = [
          'Финансовая деятельность — прочие источники поступлений',
        ]),
        (t.exports['Financing activities – other uses_study'] = [
          'Финансовая деятельность — прочие направления расходования',
        ]),
        (t.exports['Float shares outstanding_study'] = [
          'Количество акций доступных к купле-продаже',
        ]),
        (t.exports['Free cash flow_study'] = [
          'Движение свободных денежных средств',
        ]),
        (t.exports['Free cash flow margin %_study'] = [
          'Маржа свободных денежных средств %',
        ]),
        (t.exports['Fulmer H factor_study'] = ['Фактор Fulmer H']),
        (t.exports['Funds from operations_study'] = [
          'Средства от основной деятельности',
        ]),
        (t.exports['Goodwill, net_study'] = ['Деловая репутация, нетто']),
        (t.exports['Goodwill to assets ratio_study'] = ['Репутация/активы']),
        (t.exports["Graham's number_study"] = ['Число Грэма']),
        (t.exports['Gross margin %_study'] = ['Валовая рентабельность %']),
        (t.exports['Gross profit_study'] = ['Валовая прибыль']),
        (t.exports['Gross profit to assets ratio_study'] = [
          'Валовая прибыль/активы',
        ]),
        (t.exports['Gross property/plant/equipment_study'] = [
          'Основные средства, брутто',
        ]),
        (t.exports.Impairments_study = ['Обесценение активов']),
        (t.exports['Income Tax Credits_study'] = [
          'Вычет по налогу на прибыль',
        ]),
        (t.exports['Income tax, current_study'] = [
          'Налог на прибыль, текущий',
        ]),
        (t.exports['Income tax, current - domestic_study'] = [
          'Налог на прибыль, текущий — внутренний',
        ]),
        (t.exports['Income Tax, current - foreign_study'] = [
          'Налог на прибыль, текущий — иностранный',
        ]),
        (t.exports['Income tax, deferred_study'] = [
          'Налог на прибыль, отложенный',
        ]),
        (t.exports['Income tax, deferred - domestic_study'] = [
          'Налог на прибыль, отложенный — внутренний',
        ]),
        (t.exports['Income tax, deferred - foreign_study'] = [
          'Налог на прибыль, отложенный — иностранный',
        ]),
        (t.exports['Income tax payable_study'] = ['Налог на прибыль к уплате']),
        (t.exports['Interest capitalized_study'] = [
          'Капитализированные проценты',
        ]),
        (t.exports['Interest coverage_study'] = ['Процентное покрытие']),
        (t.exports['Interest expense, net of interest capitalized_study'] = [
          'Проценты к уплате за вычетом капитализированных процентов',
        ]),
        (t.exports['Interest expense on debt_study'] = ['Проценты к уплате']),
        (t.exports['Inventories - finished goods_study'] = [
          'Запасы — готовая продукция',
        ]),
        (t.exports['Inventories - progress payments & other_study'] = [
          'Запасы — постепенные платежи и другое',
        ]),
        (t.exports['Inventories - raw materials_study'] = [
          'Запасы — сырье и материалы',
        ]),
        (t.exports['Inventories - work in progress_study'] = [
          'Запасы — незавершенное производство',
        ]),
        (t.exports['Inventory to revenue ratio_study'] = ['Запасы/Выручка']),
        (t.exports['Inventory turnover_study'] = ['Оборачиваемость запасов']),
        (t.exports['Investing activities – other sources_study'] = [
          'Инвестиционная деятельность — прочие источники',
        ]),
        (t.exports['Investing activities – other uses_study'] = [
          'Инвестиционная деятельность — прочие направления',
        ]),
        (t.exports['Investments in unconsolidated subsidiaries_study'] = [
          'Инвестиции в неконсолидированные дочерние компании',
        ]),
        (t.exports['Issuance of long term debt_study'] = [
          'Начисление долгосрочной задолж-ти',
        ]),
        (t.exports['Issuance/retirement of debt, net_study'] = [
          'Начисление/погашение задолж-ти, нетто',
        ]),
        (t.exports['Issuance/retirement of long term debt_study'] = [
          'Начисление/погашение долгосрочной задолжен-ти',
        ]),
        (t.exports['Issuance/retirement of other debt_study'] = [
          'Начисление/погашение прочей задолжен-ти',
        ]),
        (t.exports['Issuance/retirement of short term debt_study'] = [
          'Начисление/погашение краткосрочной задолжен-ти',
        ]),
        (t.exports['Issuance/retirement of stock, net_study'] = [
          'Выпуск/выкуп и аннулирование акций, нетто',
        ]),
        (t.exports['KZ index_study'] = ['Индекс KZ']),
        (t.exports['Legal claim expense_study'] = [
          'Расходы по судебным разбирательствам',
        ]),
        (t.exports['Long term debt_study'] = ['Долгосрочная задолженность']),
        (t.exports['Long term debt excl. lease liabilities_study'] = [
          'Долгосрочная задолж-ть без обязательств по аренде',
        ]),
        (t.exports['Long term debt to total assets ratio_study'] = [
          'Долгосрочная задолж./совокупные активы',
        ]),
        (t.exports['Long term investments_study'] = [
          'Долгосрочные инвестиции',
        ]),
        (t.exports['Market capitalization_study'] = ['Рыночная капитализация']),
        (t.exports['Minority interest_study'] = ['Миноритарный пакет акций']),
        (t.exports['Miscellaneous non-operating expense_study'] = [
          'Прочие внеоперационные расходы',
        ]),
        (t.exports['Net current asset value per share_study'] = [
          'Чистая стоимость оборотных активов на акцию',
        ]),
        (t.exports['Net debt_study'] = ['Чистая задолженность']),
        (t.exports['Net income_study'] = ['Чистая прибыль (убыток)']),
        (t.exports['Net income before discontinued operations_study'] = [
          'Чистая прибыль (убыток) от прекращения деятельности',
        ]),
        (t.exports['Net income (cash flow)_study'] = [
          'Чистая прибыль (движение денежных средств)',
        ]),
        (t.exports['Net income per employee_study'] = [
          'Чистая прибыль на одного работника',
        ]),
        (t.exports['Net intangible assets_study'] = [
          'Чистые нематериальные активы',
        ]),
        (t.exports['Net margin %_study'] = [
          'Рентабельность по чистой прибыли, %',
        ]),
        (t.exports['Net property/plant/equipment_study'] = [
          'Основные средства, нетто',
        ]),
        (t.exports['Non-cash items_study'] = ['Неденежные статьи']),
        (t.exports['Non-controlling/minority interest_study'] = [
          'Неконтрольный/миноритарный пакет акций',
        ]),
        (t.exports['Non-operating income, excl. interest expenses_study'] = [
          'Внеоперационные доходы за вычетом процентов к уплате',
        ]),
        (t.exports['Non-operating income, total_study'] = [
          'Внеоперационные доходы, итого',
        ]),
        (t.exports['Non-operating interest income_study'] = [
          'Проценты к получению по внеоперационной деятельности',
        ]),
        (t.exports['Note receivable - long term_study'] = [
          'Вексель к получению — долгосрочный',
        ]),
        (t.exports['Notes payable_study'] = ['Векселя к оплате']),
        (t.exports['Number of employees_study'] = ['Количество сотрудников']),
        (t.exports['Number of shareholders_study'] = ['Количество акционеров']),
        (t.exports['Operating earnings yield %_study'] = [
          'Доходность акции по операционной прибыли, %',
        ]),
        (t.exports['Operating expenses (excl. COGS)_study'] = [
          'Операционные расходы (без себестоимости реализованной продукции)',
        ]),
        (t.exports['Operating income_study'] = ['Операционные доходы']),
        (t.exports['Operating lease liabilities_study'] = [
          'Обязательства по операционной аренде',
        ]),
        (t.exports['Operating margin %_study'] = [
          'Операционная рентабельность %',
        ]),
        (t.exports['Other COGS_study'] = [
          'Прочая себестоимость реализованной продукции',
        ]),
        (t.exports['Other common equity_study'] = ['Прочий основной капитал']),
        (t.exports['Other current assets, total_study'] = [
          'Прочие оборотные активы, итог',
        ]),
        (t.exports['Other current liabilities_study'] = [
          'Прочие текущие обязательства',
        ]),
        (t.exports['Other cost of goods sold_study'] = [
          'Прочая себестоимость реализованных товаров',
        ]),
        (t.exports['Other exceptional charges_study'] = [
          'Другие исключительные расходы',
        ]),
        (t.exports['Other financing cash flow items, total_study'] = [
          'Общее движение денежных средств от прочей финансовой деятельности',
        ]),
        (t.exports['Other intangibles, net_study'] = [
          'Прочие нематериальные активы, нетто',
        ]),
        (t.exports['Other investing cash flow items, total_study'] = [
          'Общее движение денежных средств от прочей инвестиционной деятельности',
        ]),
        (t.exports['Other investments_study'] = ['Прочие инвестиции']),
        (t.exports['Other liabilities, total_study'] = [
          'Прочие обязательства, итого',
        ]),
        (t.exports['Other long term assets, total_study'] = [
          'Другие долгосрочные активы, итого',
        ]),
        (t.exports['Other non-current liabilities, total_study'] = [
          'Прочие долгосрочные обязательства, итого',
        ]),
        (t.exports['Other operating expenses, total_study'] = [
          'Другие операционные расходы, итого',
        ]),
        (t.exports['Other receivables_study'] = [
          'Прочая дебиторская задолженность',
        ]),
        (t.exports['Other short term debt_study'] = [
          'Прочая краткосрочная задолж-ть',
        ]),
        (t.exports['Paid in capital_study'] = [
          'Оплаченная часть акционерного капитала',
        ]),
        (t.exports['PEG ratio_study'] = ['Коэффициент PEG']),
        (t.exports['Piotroski F-score_study'] = ['Коэффициент Пиотровски']),
        (t.exports['Preferred dividends_study'] = [
          'Дивиденды по привилегированным акциям',
        ]),
        (t.exports['Preferred dividends paid_study'] = [
          'Выплачено дивидендов по привилегированным акциям',
        ]),
        (t.exports['Preferred stock, carrying value_study'] = [
          'Привилегированные акции, учётная стоимость',
        ]),
        (t.exports['Prepaid expenses_study'] = ['Авансы полученные']),
        (t.exports['Pretax equity in earnings_study'] = [
          'Доходы от участия в других организациях до вычета налогов',
        ]),
        (t.exports['Pretax income_study'] = [
          'Прибыль (убыток) до налогообложения',
        ]),
        (t.exports['Price earnings ratio forward_study'] = [
          'Соотношение цена/прибыль за будущие периоды',
        ]),
        (t.exports['Price sales ratio forward_study'] = [
          'Соотношение цена/объем продаж за будущие периоды',
        ]),
        (t.exports['Price to book ratio_study'] = [
          'Цена/балансовая стоимость',
        ]),
        (t.exports['Price to cash flow ratio_study'] = [
          'Цена/Движение денежных средств',
        ]),
        (t.exports['Price to earnings ratio_study'] = ['Цена/Прибыль']),
        (t.exports['Price to free cash flow ratio_study'] = [
          'Цена/Движение свободных денежных средств',
        ]),
        (t.exports['Price to sales ratio_study'] = ['Цена/Продажи']),
        (t.exports['Price to tangible book ratio_study'] = [
          'Цена/материальная балансовая стоимость',
        ]),
        (t.exports['Provision for risks & charge_study'] = [
          'Резерв на покрытие рисков и расходов',
        ]),
        (t.exports['Purchase/acquisition of business_study'] = [
          'Покупка/приобретение бизнеса',
        ]),
        (t.exports['Purchase of investments_study'] = [
          'Приобретение финансовых вложений',
        ]),
        (t.exports['Purchase/sale of business, net_study'] = [
          'Покупка/продажа бизнеса, нетто',
        ]),
        (t.exports['Purchase/sale of investments, net_study'] = [
          'Приобретение/выбытие финансовых вложений, нетто',
        ]),
        (t.exports['Quality ratio_study'] = [
          'Коэфф. эффективности использования активов',
        ]),
        (t.exports['Quick ratio_study'] = ['Коэффициент быстрой ликвидности']),
        (t.exports['Reduction of long term debt_study'] = [
          'Сокращение долгосрочной задолж-ти',
        ]),
        (t.exports['Repurchase of common & preferred stock_study'] = [
          'Выкуп обыкновенных и привилегированных акций',
        ]),
        (t.exports['Research & development_study'] = [
          'Расходы на исследование и разработку',
        ]),
        (t.exports['Research & development to revenue ratio_study'] = [
          'Расходы на исследование и разработку/выручка',
        ]),
        (t.exports['Restructuring charge_study'] = [
          'Расходы на реструктуризацию',
        ]),
        (t.exports['Retained earnings_study'] = ['Нераспределённая прибыль']),
        (t.exports['Return on assets %_study'] = [
          'Коэффициент рентабельности активов, %',
        ]),
        (t.exports['Return on equity %_study'] = [
          'Коэфф. рентабельности собственного капитала, %',
        ]),
        (t.exports['Return on equity adjusted to book value %_study'] = [
          'Коэф. рентабельности собственного капитала с поправкой на баланс. стоимость, %',
        ]),
        (t.exports['Return on invested capital %_study'] = [
          'Доход на инвестированный капитал, %',
        ]),
        (t.exports['Return on tangible assets %_study'] = [
          'Прибыль на общ. сумму материальных активов, %',
        ]),
        (t.exports['Return on tangible equity %_study'] = [
          'Доход на материальный капитал, %',
        ]),
        (t.exports['Revenue estimates_study'] = ['Расчёт выручки']),
        (t.exports['Revenue one year growth_study'] = ['Годовой рост выручки']),
        (t.exports['Revenue per employee_study'] = [
          'Выручка на одного работника',
        ]),
        (t.exports['Sale/maturity of investments_study'] = [
          'Продажа/погашение финансовых вложений',
        ]),
        (t.exports['Sale of common & preferred stock_study'] = [
          'Продажа обыкновенных и привилегированных акций',
        ]),
        (t.exports['Sale of fixed assets & businesses_study'] = [
          'Продажа основных средств и бизнеса',
        ]),
        (t.exports['Selling/general/admin expenses, other_study'] = [
          'Коммерческие, общие и управленческие расходы, прочие',
        ]),
        (t.exports['Selling/general/admin expenses, total_study'] = [
          'Коммерческие, общие и управленческие расходы, итого',
        ]),
        (t.exports["Shareholders' equity_study"] = ['Акционерный капитал']),
        (t.exports['Shares buyback ratio %_study'] = [
          'Коэффициент выкупа акций %',
        ]),
        (t.exports["Elder's Force Index_study"] = ['Индекс силы Элдера']),
        (t.exports['Short term debt_study'] = ['Краткосрочная задолж-ть']),
        (t.exports['Short term debt excl. current portion of LT debt_study'] = [
          'Краткосрочная зад-ть без текущей части долгосрочной зад-ти',
        ]),
        (t.exports['Short term investments_study'] = [
          'Краткосрочные инвестиции',
        ]),
        (t.exports['Sloan ratio %_study'] = ['Коэффициент Слоуна']),
        (t.exports['Springate score_study'] = ['Показатель Спрингейта']),
        (t.exports['Sustainable growth rate_study'] = [
          'Темп устойчивого роста',
        ]),
        (t.exports['Tangible book value per share_study'] = [
          'Балансовая стоимость материальных активов на акцию',
        ]),
        (t.exports['Tangible common equity ratio_study'] = [
          'Капитал за вычетом нематериальных активов к общим материальным активам',
        ]),
        (t.exports.Taxes_study = ['Налоги']),
        (t.exports["Tobin's Q (approximate)_study"] = [
          'Коэффициент Тобина (приблизительно)',
        ]),
        (t.exports['Total assets_study'] = ['Итого активы']),
        (t.exports['Total cash dividends paid_study'] = [
          'Всего дивидендов, выплаченных наличными',
        ]),
        (t.exports['Total common shares outstanding_study'] = [
          'Всего выпущено обыкновенных акций',
        ]),
        (t.exports['Total current assets_study'] = ['Итого оборотные активы']),
        (t.exports['Total current liabilities_study'] = [
          'Общая сумма текущих обязательств',
        ]),
        (t.exports['Total debt_study'] = ['Итого задолженность']),
        (t.exports['Total equity_study'] = ['Итого собственный капитал']),
        (t.exports['Total inventory_study'] = [
          'Итого товарно-материальные запасы',
        ]),
        (t.exports['Total liabilities_study'] = ['Общая сумма обязательств']),
        (t.exports["Total liabilities & shareholders' equities_study"] = [
          'Общая сумма обязательств и акционерного капитала',
        ]),
        (t.exports['Total non-current assets_study'] = [
          'Итого внеоборотные активы',
        ]),
        (t.exports['Total non-current liabilities_study'] = [
          'Итого долгосрочные обязательства',
        ]),
        (t.exports['Total operating expenses_study'] = [
          'Итого операционные расходы',
        ]),
        (t.exports['Total receivables, net_study'] = [
          'Общая сумма дебиторской задолженности, нетто',
        ]),
        (t.exports['Total revenue_study'] = ['Общая выручка']),
        (t.exports['Treasury stock - common_study'] = [
          'Выкупленные собственные обыкновенные акции',
        ]),
        (t.exports['Unrealized gain/loss_study'] = [
          'Нереализованная прибыль/убыток',
        ]),
        (t.exports['Unusual income/expense_study'] = [
          'Нестандартные доходы/расходы',
        ]),
        (t.exports['Zmijewski score_study'] = ['Показатель Zmijewski']),
        (t.exports['Valuation ratios_study'] = [
          'Отношение рыночной цены акции к номиналу',
        ]),
        (t.exports['Profitability ratios_study'] = [
          'Коэффициенты рентабельности',
        ]),
        (t.exports['Liquidity ratios_study'] = ['Коэффициенты ликвидности']),
        (t.exports['Solvency ratios_study'] = [
          'Коэффициенты платежеспособности',
        ]),
        (t.exports['Accumulation/Distribution_study'] = [
          'Накопление/Распределение',
        ]),
        (t.exports['Accumulative Swing Index_study'] = [
          'Кумулятивный индекс колебаний',
        ]),
        (t.exports['Advance/Decline_study'] = ['Рост/падение']),
        (t.exports['Arnaud Legoux Moving Average_study'] = [
          'Скользящее среднее Арно Легу',
        ]),
        (t.exports.Aroon_study = ['Арун']),
        (t.exports.ASI_study = 'ASI'),
        (t.exports['Average Directional Index_study'] = [
          'Индикатор среднего направленного движения (ADX)',
        ]),
        (t.exports['Average True Range_study'] = ['Средний истинный диапазон']),
        (t.exports['Awesome Oscillator_study'] = [
          'Чудесный осциллятор Билла Вильямса',
        ]),
        (t.exports['Balance of Power_study'] = ['Баланс силы']),
        (t.exports['Bollinger Bands %B_study'] = ['%B Полос Боллинджера']),
        (t.exports['Bollinger Bands Width_study'] = [
          'Ширина полос Боллинджера',
        ]),
        (t.exports['Bollinger Bands_study'] = ['Полосы Боллинджера']),
        (t.exports['Chaikin Money Flow_study'] = ['Денежный поток Чайкина']),
        (t.exports['Chaikin Oscillator_study'] = ['Осциллятор Чайкина']),
        (t.exports['Chande Kroll Stop_study'] = 'Chande Kroll Stop'),
        (t.exports['Chande Momentum Oscillator_study'] = [
          'Моментум-осциллятор Чанде',
        ]),
        (t.exports['Chop Zone_study'] = ['Индикатор Chop Zone']),
        (t.exports['Choppiness Index_study'] = ['Индекс переменчивости']),
        (t.exports['Commodity Channel Index_study'] = [
          'Индекс товарного канала',
        ]),
        (t.exports['Connors RSI_study'] = ['RSI Коннора']),
        (t.exports['Coppock Curve_study'] = ['Кривая Коппока']),
        (t.exports['Correlation Coefficient_study'] = [
          'Коэффициент корреляции',
        ]),
        (t.exports.CRSI_study = 'CRSI'),
        (t.exports['Detrended Price Oscillator_study'] = [
          'Детрендовый ценовой осциллятор',
        ]),
        (t.exports['Directional Movement_study'] = [
          'Индекс направленного движения',
        ]),
        (t.exports['Donchian Channels_study'] = ['Канал Дончиана']),
        (t.exports['Double EMA_study'] = [
          'Двойное экспоненц. скользящ. средн.',
        ]),
        (t.exports['Ease Of Movement_study'] = ['Легкость движения']),
        (t.exports['Elder Force Index_study'] = ['Индекс силы Элдера']),
        (t.exports['EMA Cross_study'] = [
          'Пересечение экспоненц. скользящих средних',
        ]),
        (t.exports.Envelopes_study = ['Конверты']),
        (t.exports['Fisher Transform_study'] = ['Индикатор Fisher Transform']),
        (t.exports['Fixed Range_study'] = ['Фиксированный диапазон']),
        (t.exports['Fixed Range Volume Profile_study'] = [
          'Фиксированный профиль объема',
        ]),
        (t.exports['Guppy Multiple Moving Average_study'] = [
          'Множественное скользящее среднее Гуппи',
        ]),
        (t.exports['Historical Volatility_study'] = [
          'Историческая волатильность',
        ]),
        (t.exports['Hull Moving Average_study'] = ['Скользящее среднее Хала']),
        (t.exports['Keltner Channels_study'] = ['Канал Кельтнера']),
        (t.exports['Klinger Oscillator_study'] = ['Осциллятор Клингера']),
        (t.exports['Know Sure Thing_study'] = ['Знать наверняка']),
        (t.exports['Least Squares Moving Average_study'] = [
          'Скользящее среднее (наименьшие квадраты)',
        ]),
        (t.exports['Linear Regression Curve_study'] = [
          'Кривая линейной регрессии',
        ]),
        (t.exports['MA Cross_study'] = ['Пересечение скользящих средних']),
        (t.exports['MA with EMA Cross_study'] = ['MA с пересеч. EMA']),
        (t.exports['MA/EMA Cross_study'] = ['Пересечение MA/EMA']),
        (t.exports.MACD_study = 'MACD'),
        (t.exports['Mass Index_study'] = ['Индекс массы']),
        (t.exports['McGinley Dynamic_study'] = [
          'Динамический индикатор МакГинли',
        ]),
        (t.exports.Median_study = ['Средняя линия']),
        (t.exports.Momentum_study = ['Моментум (Momentum)']),
        (t.exports['Money Flow_study'] = ['Денежный поток']),
        (t.exports['Moving Average Channel_study'] = [
          'Канал скользящей средней',
        ]),
        (t.exports['Moving Average Exponential_study'] = [
          'Скользящее среднее (эксп.)',
        ]),
        (t.exports['Moving Average Weighted_study'] = [
          'Взвешенное скользящее среднее',
        ]),
        (t.exports['Moving Average_study'] = ['Скользящее среднее']),
        (t.exports['Net Volume_study'] = ['Чистый объём']),
        (t.exports['On Balance Volume_study'] = ['Балансовый объём']),
        (t.exports['Parabolic SAR_study'] = [
          'Параболическая система времени/цены',
        ]),
        (t.exports['Pivot Points Standard_study'] = [
          'Стандартные точки разворота',
        ]),
        (t.exports['Periodic Volume Profile_study'] = [
          'Профиль объёма за период',
        ]),
        (t.exports['Price Channel_study'] = ['Ценовой канал']),
        (t.exports['Price Oscillator_study'] = ['Осциллятор цены']),
        (t.exports['Price Volume Trend_study'] = ['Тренд цены и объёма']),
        (t.exports['Rate Of Change_study'] = ['Скорость изменения цены']),
        (t.exports['Relative Strength Index_study'] = [
          'Индекс относительной силы',
        ]),
        (t.exports['Relative Vigor Index_study'] = [
          'Индекс относительной бодрости',
        ]),
        (t.exports['Relative Volatility Index_study'] = [
          'Относительный индекс волатильности',
        ]),
        (t.exports['Session Volume_study'] = ['Объём за сессию']),
        (t.exports['Session Volume HD_study'] = ['Объём за сессию HD']),
        (t.exports['Session Volume Profile_study'] = [
          'Профиль объёма за сессию',
        ]),
        (t.exports['Session Volume Profile HD_study'] = [
          'Профиль объёма за сессию HD',
        ]),
        (t.exports['SMI Ergodic Indicator/Oscillator_study'] = [
          'Индикатор/Осциллятор SMI Ergodic',
        ]),
        (t.exports['Smoothed Moving Average_study'] = [
          'Сглаженное скользящее среднее',
        ]),
        (t.exports.Stoch_study = ['Стох.']),
        (t.exports['Stochastic RSI_study'] = [
          'Стохастический индекс относительной силы',
        ]),
        (t.exports.Stochastic_study = ['Стохастический осциллятор']),
        (t.exports['Triple EMA_study'] = [
          'Скользящее среднее (тройное эксп.)',
        ]),
        (t.exports.TRIX_study = [
          'Скользящее среднее (тройное эксп. сглаженное)',
        ]),
        (t.exports['True Strength Indicator_study'] = ['Индекс истинной силы']),
        (t.exports['Ultimate Oscillator_study'] = ['Окончательный осциллятор']),
        (t.exports['Visible Range_study'] = ['Видимая область']),
        (t.exports['Visible Range Volume Profile_study'] = [
          'Профиль объёма видимой области',
        ]),
        (t.exports['Volume Oscillator_study'] = ['Осциллятор объёма']),
        (t.exports.Volume_study = ['Объём']),
        (t.exports.Vol_study = ['Объём']),
        (t.exports['Vortex Indicator_study'] = ['Vortex индикатор']),
        (t.exports.VWAP_study = 'VWAP'),
        (t.exports.VWMA_study = ['Скользящее среднее, взвешенное по объёму']),
        (t.exports['Williams %R_study'] = [
          'Процентный диапазон Вильямса (%R)',
        ]),
        (t.exports['Williams Alligator_study'] = ['Аллигатор Билла Вильямса']),
        (t.exports['Williams Fractal_study'] = ['Фракталы Билла Вильямса']),
        (t.exports['Zig Zag_study'] = ['ЗигЗаг']),
        (t.exports['24-hour Volume_study'] = ['Объём за 24 часа']),
        (t.exports['Ease of Movement_study'] = ['Легкость движения']),
        (t.exports['Elders Force Index_study'] = ['Индекс Силы Элдера']),
        (t.exports.Envelope_study = ['Конверт']),
        (t.exports.Gaps_study = ['Гэпы']),
        (t.exports['Linear Regression Channel_study'] = [
          'Линейный регрессионный канал',
        ]),
        (t.exports['Moving Average Ribbon_study'] = [
          'Лента скользящего среднего',
        ]),
        (t.exports['Multi-Time Period Charts_study'] =
          'Multi-Time Period Charts'),
        (t.exports['Open Interest_study'] = ['Сумма открытых позиций']),
        (t.exports['Rob Booker - Intraday Pivot Points_study'] = [
          'Роб Букер — Внутридневные точки разворота',
        ]),
        (t.exports['Rob Booker - Knoxville Divergence_study'] = [
          'Роб Букер — Отклонение Ноксвилла',
        ]),
        (t.exports['Rob Booker - Missed Pivot Points_study'] = [
          'Роб Букер — Пропущенные точки разворота',
        ]),
        (t.exports['Rob Booker - Reversal_study'] = ['Роб Букер — Разворот']),
        (t.exports['Rob Booker - Ziv Ghost Pivots_study'] = [
          'Роб Букер — точки разворота Ziv Ghost',
        ]),
        (t.exports.Supertrend_study = ['Супертренд']),
        (t.exports['Technical Ratings_study'] = [
          'Tехнический индикатор рынка',
        ]),
        (t.exports['True Strength Index_study'] = ['Индекс истинной силы']),
        (t.exports['Up/Down Volume_study'] = [
          'Предельный объем (по верхней/нижней границе)',
        ]),
        (t.exports['Visible Average Price_study'] = ['Видимая средняя цена']),
        (t.exports['Williams Fractals_study'] = ['Фракталы Вильямса']),
        (t.exports['Keltner Channels Strategy_study'] = [
          'Канал Кельтнера — стратегия',
        ]),
        (t.exports['Rob Booker - ADX Breakout_study'] = [
          'Роб Букер — Прорыв ADX',
        ]),
        (t.exports['Supertrend Strategy_study'] = ['Супертренд — стратегия']),
        (t.exports['Technical Ratings Strategy_study'] = [
          'Tехнический индикатор рынка — стратегия',
        ]),
        (t.exports['Auto Anchored Volume Profile_study'] = [
          'Профиль объёма (Auto Anchored)',
        ]),
        (t.exports['Auto Fib Extension_study'] = [
          'Автокоррекция по Фибоначчи',
        ]),
        (t.exports['Auto Fib Retracement_study'] = [
          'Автокоррекция по Фибоначчи',
        ]),
        (t.exports['Auto Pitchfork_study'] = ['Вилы (Авто)']),
        (t.exports['Bearish Flag Chart Pattern_study'] = [
          'Паттерн Медвежий флаг',
        ]),
        (t.exports['Bullish Pennant Chart Pattern_study'] = [
          'Паттерн Бычий вымпел',
        ]),
        (t.exports['Double Bottom Chart Pattern_study'] = [
          'Паттерн Двойное дно',
        ]),
        (t.exports['Double Top Chart Pattern_study'] = [
          'Паттерн Двойная вершина',
        ]),
        (t.exports['Elliott Wave Chart Pattern_study'] = [
          'Паттерн Волны Эллиотта',
        ]),
        (t.exports['Falling Wedge Chart Pattern_study'] = [
          'Паттерн Нисходящий клин',
        ]),
        (t.exports['Head And Shoulders Chart Pattern_study'] = [
          'Паттерн Голова и плечи',
        ]),
        (t.exports['Inverse Head And Shoulders Chart Pattern_study'] = [
          'Паттерн Перевернутые голова и плечи',
        ]),
        (t.exports['Rectangle Chart Pattern_study'] = [
          'Паттерн Прямоугольник',
        ]),
        (t.exports['Rising Wedge Chart Pattern_study'] = [
          'Паттерн Восходящий клин',
        ]),
        (t.exports['Triangle Chart Pattern_study'] = ['Паттерн Треугольник']),
        (t.exports['Triple Bottom Chart Pattern_study'] = [
          'Паттерн Тройное дно',
        ]),
        (t.exports['Triple Top Chart Pattern_study'] = [
          'Паттерн Тройная вершина',
        ]),
        (t.exports['VWAP Auto Anchored_study'] = 'VWAP Auto Anchored'),
        (t.exports['*All Candlestick Patterns*_study'] = [
          '*Все паттерны японских свечей*',
        ]),
        (t.exports['Abandoned Baby - Bearish_study'] = [
          'Медвежий Брошенный младенец',
        ]),
        (t.exports['Abandoned Baby - Bullish_study'] = [
          'Бычий Брошенный младенец',
        ]),
        (t.exports['Dark Cloud Cover - Bearish_study'] = [
          'Завеса из тёмных облаков — медвежья',
        ]),
        (t.exports['Doji Star - Bearish_study'] = ['Медвежий Доджи']),
        (t.exports['Doji Star - Bullish_study'] = ['Бычий Доджи']),
        (t.exports['Downside Tasuki Gap - Bearish_study'] = [
          'Разрыв Тасуки вниз — медвежий',
        ]),
        (t.exports['Dragonfly Doji - Bullish_study'] = [
          'Бычья Доджи-стрекоза',
        ]),
        (t.exports['Engulfing - Bearish_study'] = ['Медвежье Поглощение']),
        (t.exports['Engulfing - Bullish_study'] = ['Бычье Поглощение']),
        (t.exports['Evening Doji Star - Bearish_study'] = [
          'Медвежья Вечерняя звезда Доджи',
        ]),
        (t.exports['Evening Star - Bearish_study'] = [
          'Медвежья вечерняя звезда',
        ]),
        (t.exports['Falling Three Methods - Bearish_study'] = [
          'Медвежий Метод трёх нисходящих',
        ]),
        (t.exports['Falling Window - Bearish_study'] = [
          'Медвежье нисходящее окно',
        ]),
        (t.exports['Gravestone Doji - Bearish_study'] = [
          'Могильный камень Доджи — медвежий',
        ]),
        (t.exports['Hammer - Bullish_study'] = ['Бычий Молот']),
        (t.exports['Hanging Man - Bearish_study'] = ['Медвежий Повешенный']),
        (t.exports['Harami - Bearish_study'] = ['Медвежий Харами']),
        (t.exports['Harami - Bullish_study'] = ['Бычий Харами']),
        (t.exports['Inverted Hammer - Bullish_study'] = [
          'Бычий Перевёрнутый молот',
        ]),
        (t.exports['Kicking - Bearish_study'] = ['Медвежий Кикер']),
        (t.exports['Kicking - Bullish_study'] = ['Бычий Кикер']),
        (t.exports['Long Lower Shadow - Bullish_study'] = [
          'Длинная тень снизу — бычья',
        ]),
        (t.exports['Long Upper Shadow - Bearish_study'] = [
          'Длинная тень сверху — медвежья',
        ]),
        (t.exports['Marubozu Black - Bearish_study'] = [
          'Медвежий Чёрный Марубозу',
        ]),
        (t.exports['Marubozu White - Bullish_study'] = [
          'Бычий Белый Марубозу',
        ]),
        (t.exports['Morning Doji Star - Bullish_study'] = [
          'Утренняя звезда Доджи — бычья',
        ]),
        (t.exports['Morning Star - Bullish_study'] = ['Бычья Утренняя звезда']),
        (t.exports['On Neck - Bearish_study'] = ['На шее — медвежий']),
        (t.exports['Piercing - Bullish_study'] = ['Бычий Просвет в облаках']),
        (t.exports['Rising Three Methods - Bullish_study'] = [
          'Метод трёх восходящих — бычий',
        ]),
        (t.exports['Rising Window - Bullish_study'] = [
          'Бычье Восходящее окно',
        ]),
        (t.exports['Shooting Star - Bearish_study'] = [
          'Медвежья падающая звезда',
        ]),
        (t.exports['Three Black Crows - Bearish_study'] = [
          'Три черные вороны — медвежьи',
        ]),
        (t.exports['Three White Soldiers - Bullish_study'] = [
          'Три белых солдата — бычьи',
        ]),
        (t.exports['Tri-Star - Bearish_study'] = ['Три звезды — медвежьи']),
        (t.exports['Tri-Star - Bullish_study'] = ['Три звезды — бычьи']),
        (t.exports['Tweezer Top - Bearish_study'] = [
          'Верхний Пинцет — медвежий',
        ]),
        (t.exports['Upside Tasuki Gap - Bullish_study'] = [
          'Верхний гэп Тасуки — бычий',
        ]),
        (t.exports.SuperTrend_study = 'SuperTrend'),
        (t.exports['Average Price_study'] = ['Средняя цена']),
        (t.exports['Typical Price_study'] = ['Типичная цена']),
        (t.exports['Median Price_study'] = ['Медианная цена']),
        (t.exports['Money Flow Index_study'] = ['Индекс денежного потока']),
        (t.exports['Moving Average Double_study'] = [
          'Двойное скользящее среднее',
        ]),
        (t.exports['Moving Average Triple_study'] = [
          'Тройное скользящее среднее',
        ]),
        (t.exports['Moving Average Adaptive_study'] = [
          'Адаптивное скользящее среднее',
        ]),
        (t.exports['Moving Average Hamming_study'] = [
          'Скользящее среднее Хэмминга',
        ]),
        (t.exports['Moving Average Modified_study'] = [
          'Модифицированное скользящее среднее',
        ]),
        (t.exports['Moving Average Multiple_study'] = [
          'Множественное скользящее среднее',
        ]),
        (t.exports['Linear Regression Slope_study'] = [
          'Наклон линейной регрессии',
        ]),
        (t.exports['Standard Error_study'] = ['Стандартная ошибка']),
        (t.exports['Standard Error Bands_study'] = [
          'Полосы стандартных ошибок',
        ]),
        (t.exports['Correlation - Log_study'] = ['Корреляция - Лог.']),
        (t.exports['Standard Deviation_study'] = ['Стандартное отклонение']),
        (t.exports['Chaikin Volatility_study'] = [
          'Индикатор волатильности Чайкина',
        ]),
        (t.exports['Volatility Close-to-Close_study'] = [
          'Волатильность Close-to-Close',
        ]),
        (t.exports['Volatility Zero Trend Close-to-Close_study'] =
          'Volatility Zero Trend Close-to-Close'),
        (t.exports['Volatility O-H-L-C_study'] = ['Волатильность O-H-L-C']),
        (t.exports['Volatility Index_study'] = ['Индекс волатильности']),
        (t.exports['Trend Strength Index_study'] = ['Индекс силы тренда']),
        (t.exports['Majority Rule_study'] = ['Правило большинства']),
        (t.exports['Advance Decline Line_study'] = ['Линия роста/падения']),
        (t.exports['Advance Decline Ratio_study'] = [
          'Коэффициент роста/падения',
        ]),
        (t.exports['Advance/Decline Ratio (Bars)_study'] = [
          'Коэффициент роста/падения (Бары)',
        ]),
        (t.exports['BarUpDn Strategy_study'] = ['Стратегия BarUpDn']),
        (t.exports['Bollinger Bands Strategy directed_study'] = [
          'Стратегия Полосы Боллинджера направленная',
        ]),
        (t.exports['Bollinger Bands Strategy_study'] = [
          'Стратегия Полосы Боллинджера',
        ]),
        (t.exports.ChannelBreakOutStrategy_study = [
          'Стратегия Channel BreakOut',
        ]),
        (t.exports.Compare_study = ['Сравнить']),
        (t.exports['Conditional Expressions_study'] = ['Условные выражения']),
        (t.exports.ConnorsRSI_study = ['RSI Коннора']),
        (t.exports['Consecutive Up/Down Strategy_study'] = [
          'Стратегия Consecutive Up/Down',
        ]),
        (t.exports['Cumulative Volume Index_study'] = [
          'Кумулятивный индекс объёма',
        ]),
        (t.exports['Divergence Indicator_study'] = ['Индикатор расхождения']),
        (t.exports['Greedy Strategy_study'] = ['Стратегия Greedy']),
        (t.exports['InSide Bar Strategy_study'] = ['Стратегия Inside Bar']),
        (t.exports['Keltner Channel Strategy_study'] = [
          'Стратегия Канал Кельтнера',
        ]),
        (t.exports['Linear Regression_study'] = ['Кривая линейной регрессии']),
        (t.exports['MACD Strategy_study'] = ['Стратегия MACD']),
        (t.exports['Momentum Strategy_study'] = [
          'Стратегия моментум (Momentum)',
        ]),
        (t.exports['Moon Phases_study'] = ['Фазы Луны']),
        (t.exports['Moving Average Convergence/Divergence_study'] = [
          'Схождение/расхождение скользящих средних',
        ]),
        (t.exports['MovingAvg Cross_study'] = [
          'Пересечение скользящих средних',
        ]),
        (t.exports['MovingAvg2Line Cross_study'] = [
          'Пересечение 2 линий скользящих средних',
        ]),
        (t.exports['OutSide Bar Strategy_study'] = ['Стратегия OutSide Bar']),
        (t.exports.Overlay_study = ['Поверх основной серии']),
        (t.exports['Parabolic SAR Strategy_study'] = [
          'Параболическая остановка и разворот (SAR)',
        ]),
        (t.exports['Pivot Extension Strategy_study'] = [
          'Стратегия Pivot Extension',
        ]),
        (t.exports['Pivot Points High Low_study'] = [
          'Контрольные точки разворота',
        ]),
        (t.exports['Pivot Reversal Strategy_study'] = [
          'Стратегия контрольной точки разворота',
        ]),
        (t.exports['Price Channel Strategy_study'] = [
          'Стратегия ценовых каналов',
        ]),
        (t.exports['RSI Strategy_study'] = ['Стратегия RSI']),
        (t.exports['SMI Ergodic Indicator_study'] = ['Индикатор SMI Ergodic']),
        (t.exports['SMI Ergodic Oscillator_study'] = [
          'Осциллятор SMI Ergodic',
        ]),
        (t.exports['Stochastic Slow Strategy_study'] = [
          'Стратегия медленный стохастик',
        ]),
        (t.exports['Volatility Stop_study'] = ['Стоп по волатильности']),
        (t.exports['Volty Expan Close Strategy_study'] = [
          'Стратегия Volty Expan Close',
        ]),
        (t.exports['Woodies CCI_study'] = ['Вуди CCI']);
    },
    40434: (t) => {
      t.exports = ['Фиксированный профиль объема'];
    },
    32819: (t) => {
      t.exports = ['Объём'];
    },
    66051: (t) => {
      t.exports = ['Второстепенная'];
    },
    86054: (t) => {
      t.exports = ['Минута'];
    },
    20936: (t) => {
      t.exports = ['Текст'];
    },
    98478: (t) => {
      t.exports = ['Не удалось скопировать'];
    },
    34004: (t) => {
      t.exports = ['Не удалось вырезать'];
    },
    96260: (t) => {
      t.exports = ['Не удалось вставить'];
    },
    94370: (t) => {
      t.exports = ['Обратный отсчёт до закрытия бара'];
    },
    15168: (t) => {
      t.exports = ['Коломбо'];
    },
    36018: (t) => {
      t.exports = ['Столбцы'];
    },
    19372: (t) => {
      t.exports = ['Комментарий'];
    },
    20229: (t) => {
      t.exports = ['Сравнить/Добавить'];
    },
    46689: (t) => {
      t.exports = ['Подтвердить аргументы'];
    },
    43432: (t) => {
      t.exports = ['Копенгаген'];
    },
    35216: (t) => {
      t.exports = ['Копировать'];
    },
    87898: (t) => {
      t.exports = ['Сохранить график как'];
    },
    28851: (t) => {
      t.exports = ['Копировать цену'];
    },
    94099: (t) => {
      t.exports = ['Каир'];
    },
    64149: (t) => {
      t.exports = ['Сноска'];
    },
    63528: (t) => {
      t.exports = ['Японские свечи'];
    },
    46837: (t) => {
      t.exports = ['Каракас'];
    },
    49329: (t) => {
      t.exports = ['Изменение'];
    },
    28089: (t) => {
      t.exports = ['Сменить инструмент'];
    },
    99374: (t) => {
      t.exports = ['Изменить интервал'];
    },
    14412: (t) => {
      t.exports = ['Свойства графика'];
    },
    26619: (t) => {
      t.exports = ['График от TradingView'];
    },
    12011: (t) => {
      t.exports = ['Изображение графика скопировано {emoji}'];
    },
    59884: (t) => {
      t.exports = ['Чатем'];
    },
    28244: (t) => {
      t.exports = ['Чикаго'];
    },
    49648: (t) => {
      t.exports = ['Чунцин'];
    },
    90068: (t) => {
      t.exports = ['Окружность'];
    },
    32234: (t) => {
      t.exports = ['Кликните, чтобы установить точку'];
    },
    52977: (t) => {
      t.exports = ['Клонировать'];
    },
    31691: (t) => {
      t.exports = ['Цена закр.'];
    },
    50493: (t) => {
      t.exports = ['Создать заявку'];
    },
    52302: (t) => {
      t.exports = ['Создать лимитную заявку'];
    },
    29908: (t) => {
      t.exports = ['Перекрестие'];
    },
    60997: (t) => {
      t.exports = ['Перекрещенные линии'];
    },
    81520: (t) => {
      t.exports = ['Валюты'];
    },
    98486: (t) => {
      t.exports = ['Текущий и выше'];
    },
    73106: (t) => {
      t.exports = ['Текущий и ниже'];
    },
    85964: (t) => {
      t.exports = ['Только текущий'];
    },
    17206: (t) => {
      t.exports = ['Кривая'];
    },
    95176: (t) => {
      t.exports = ['Цикл'];
    },
    87761: (t) => {
      t.exports = ['Разделение циклов'];
    },
    27891: (t) => {
      t.exports = ['Паттерн Cypher'];
    },
    56996: (t) => {
      t.exports = ['График с таким именем уже существует'];
    },
    30192: (t) => {
      t.exports = [
        'График с таким именем уже существует. Хотите переименовать?',
      ];
    },
    32852: (t) => {
      t.exports = ['Шаблон ABCD'];
    },
    88010: (t) => {
      t.exports = ['Амстердам'];
    },
    37422: (t) => {
      t.exports = 'Analyze Trade Setup';
    },
    66828: (t) => {
      t.exports = ['Заметка на экране'];
    },
    94782: (t) => {
      t.exports = ['Текст на экране'];
    },
    61704: (t) => {
      t.exports = 'Anchored VWAP';
    },
    63597: (t) => {
      t.exports = ['Добавить горизонтальную линию'];
    },
    45743: (t) => {
      t.exports = ['Добавить'];
    },
    8700: (t) => {
      t.exports = ['Добавить оповещение'];
    },
    64885: (t) => {
      t.exports = ['Добавить оповещение для {drawing}'];
    },
    90830: (t) => {
      t.exports = ['Добавить оповещение для {series}'];
    },
    45986: (t) => {
      t.exports = ['Добавить оповещение для {title}'];
    },
    3612: (t) => {
      t.exports = ['Добавить данные отчётности для {instrumentName}'];
    },
    92206: (t) => {
      t.exports = ['Добавить индикатор/стратегию на {studyTitle}'];
    },
    34810: (t) => {
      t.exports = ['Добавить текстовую заметку для {symbol}'];
    },
    75669: (t) => {
      t.exports = ['Добавить этот фин. показатель на все графики в окне'];
    },
    64288: (t) => {
      t.exports = ['Добавить этот индикатор на все графики в окне'];
    },
    77920: (t) => {
      t.exports = ['Добавить эту стратегию ко всем графикам в окне'];
    },
    34059: (t) => {
      t.exports = ['Добавить этот символ на все графики в окне'];
    },
    17365: (t) => {
      t.exports = ['Аделаида'];
    },
    9408: (t) => {
      t.exports = ['Никогда не отображать'];
    },
    71997: (t) => {
      t.exports = ['Отображать всегда'];
    },
    97305: (t) => {
      t.exports = ['Все индикаторы и инструменты рисования'];
    },
    59192: (t) => {
      t.exports = ['Все интервалы'];
    },
    14452: (t) => {
      t.exports = ['Алма-Ата'];
    },
    5716: (t) => {
      t.exports = ['Применить волну Эллиотта'];
    },
    19263: (t) => {
      t.exports = ['Применить основную волну Эллиотта'];
    },
    15818: (t) => {
      t.exports = ['Применить второстепенную волну Эллиотта'];
    },
    50352: (t) => {
      t.exports = ['Применить промежуточную волну Эллиотта'];
    },
    66631: (t) => {
      t.exports = ['Применить Manual Decision Point'];
    },
    15682: (t) => {
      t.exports = ['Применить ручную настройку риска/прибыли'];
    },
    15644: (t) => {
      t.exports = ['Применить WPT Down Wave'];
    },
    5897: (t) => {
      t.exports = ['Применить WPT Up Wave'];
    },
    13345: (t) => {
      t.exports = ['Сбросить изменения'];
    },
    95910: (t) => {
      t.exports = ['Применить эти индикаторы для всех графиков в окне'];
    },
    42762: (t) => {
      t.exports = ['Апр'];
    },
    45104: (t) => {
      t.exports = ['Дуга'];
    },
    42097: (t) => {
      t.exports = ['Область'];
    },
    96237: (t) => {
      t.exports = ['Стрелка'];
    },
    48732: (t) => {
      t.exports = ['Стрелка вниз'];
    },
    82473: (t) => {
      t.exports = ['Стрелка-указатель'];
    },
    8738: (t) => {
      t.exports = ['Стрелка вниз'];
    },
    35062: (t) => {
      t.exports = ['Стрелка влево'];
    },
    92163: (t) => {
      t.exports = ['Стрелка вправо'];
    },
    33196: (t) => {
      t.exports = ['Стрелка вверх'];
    },
    10650: (t) => {
      t.exports = ['Стрелка вверх'];
    },
    59340: (t) => {
      t.exports = ['Ашхабад'];
    },
    13468: (t) => {
      t.exports = ['Цена закрытия'];
    },
    21983: (t) => {
      t.exports = ['Афины'];
    },
    86951: (t) => {
      t.exports = ['Авто'];
    },
    50834: (t) => {
      t.exports = ['Авто (бары подстраиваются под экран)'];
    },
    38465: (t) => {
      t.exports = ['Авг'];
    },
    8975: (t) => {
      t.exports = ['Метка средней цены закрытия'];
    },
    87899: (t) => {
      t.exports = ['Линия средней цены закрытия'];
    },
    22554: (t) => {
      t.exports = ['Средн.'];
    },
    54173: (t) => {
      t.exports = ['Богота'];
    },
    53260: (t) => {
      t.exports = ['Бахрейн'];
    },
    40664: (t) => {
      t.exports = ['Всплывающий текст'];
    },
    32376: (t) => {
      t.exports = ['Бангкок'];
    },
    19149: (t) => {
      t.exports = [
        'Симуляция рынка недоступна для этого типа графика. Хотите выйти из режима симуляции?',
      ];
    },
    16812: (t) => {
      t.exports = ['Бары'];
    },
    98838: (t) => {
      t.exports = ['Шаблон баров'];
    },
    17712: (t) => {
      t.exports = ['Базовая линия'];
    },
    54861: (t) => {
      t.exports = ['Белград'];
    },
    26825: (t) => {
      t.exports = ['Берлин'];
    },
    30251: (t) => {
      t.exports = ['Кисть'];
    },
    90204: (t) => {
      t.exports = ['Брюссель'];
    },
    5262: (t) => {
      t.exports = ['Братислава'];
    },
    59901: (t) => {
      t.exports = ['На один слой вперед'];
    },
    26354: (t) => {
      t.exports = ['Перенести поверх'];
    },
    11741: (t) => {
      t.exports = ['Брисбен'];
    },
    37728: (t) => {
      t.exports = ['Бухарест'];
    },
    87143: (t) => {
      t.exports = ['Будапешт'];
    },
    82446: (t) => {
      t.exports = ['Буэнос-Айрес'];
    },
    82128: (t) => {
      t.exports = ['от TradingView'];
    },
    75190: (t) => {
      t.exports = ['Перейти к дате'];
    },
    38342: (t) => {
      t.exports = ['Перейти к {lineToolName}'];
    },
    75139: (t) => {
      t.exports = ['Хорошо'];
    },
    81180: (t) => {
      t.exports = ['Коробка Ганна'];
    },
    68102: (t) => {
      t.exports = ['Веер Ганна'];
    },
    66321: (t) => {
      t.exports = ['Квадрат Ганна'];
    },
    87107: (t) => {
      t.exports = ['Фиксированный Квадрат Ганна'];
    },
    34805: (t) => {
      t.exports = ['Больше подключений'];
    },
    7914: (t) => {
      t.exports = ['Проекция цены'];
    },
    18367: (t) => {
      t.exports = ['Гранд Суперцикл'];
    },
    97065: (t) => {
      t.exports = [
        'Вы действительно хотите удалить шаблон индикаторов "{name}"?',
      ];
    },
    59368: (t) => {
      t.exports = ['Двойная кривая'];
    },
    35273: (t) => {
      t.exports = [
        'Двойной клик по любому краю графика, чтобы сбросить настройки размеров',
      ];
    },
    5828: (t) => {
      t.exports = ['Двойной клик, чтобы завершить Траекторию'];
    },
    63898: (t) => {
      t.exports = ['Двойной клик, чтобы завершить Ломаную линию'];
    },
    42660: (t) => {
      t.exports = ['Нисходящая волна 1 или А'];
    },
    44788: (t) => {
      t.exports = ['Нисходящая волна 2 или B'];
    },
    71263: (t) => {
      t.exports = ['Нисходящая волна 3'];
    },
    70573: (t) => {
      t.exports = ['Нисходящая волна 4'];
    },
    59560: (t) => {
      t.exports = ['Нисходящая волна 5'];
    },
    70437: (t) => {
      t.exports = ['Нисходящая волна C'];
    },
    93345: (t) => {
      t.exports = ['Данные предоставлены'];
    },
    76912: (t) => {
      t.exports = ['Дата'];
    },
    60222: (t) => {
      t.exports = ['Диапазон дат'];
    },
    79859: (t) => {
      t.exports = ['Диапазон цены и времени'];
    },
    92203: (t) => {
      t.exports = ['Дек'];
    },
    69479: (t) => {
      t.exports = ['Степень'];
    },
    57701: (t) => {
      t.exports = ['Денвер'];
    },
    73720: (t) => {
      t.exports = 'Diamond';
    },
    3556: (t) => {
      t.exports = ['Расходящийся канал'];
    },
    62764: (t) => {
      t.exports = ['Перемещение'];
    },
    22903: (t) => {
      t.exports = ['Показывать панель инструментов'];
    },
    21442: (t) => {
      t.exports = ['Нарисовать горизонтальную линию на'];
    },
    22429: (t) => {
      t.exports = ['Дубай'];
    },
    9497: (t) => {
      t.exports = ['Дублин'];
    },
    85223: (t) => {
      t.exports = ['Эмодзи'];
    },
    24435: (t) => {
      t.exports = ['Укажите новое имя графика'];
    },
    93512: (t) => {
      t.exports = ['Редактировать {title} оповещение'];
    },
    91215: (t) => {
      t.exports = ['Коррекционная волна Эллиотта (ABC)'];
    },
    80983: (t) => {
      t.exports = ['Двойная комбинация Эллиотта (WXY)'];
    },
    74118: (t) => {
      t.exports = ['Импульсная волна Эллиотта (12345)'];
    },
    95840: (t) => {
      t.exports = ['ABCDE волна (треугольник)'];
    },
    66637: (t) => {
      t.exports = ['Тройная комбинация Эллиотта (WXYXZ)'];
    },
    69418: (t) => {
      t.exports = ['Эллипс'];
    },
    27558: (t) => {
      t.exports = ['Продолжить линии оповещений'];
    },
    2578: (t) => {
      t.exports = ['Удлинённая линия'];
    },
    77295: (t) => {
      t.exports = ['Биржа'];
    },
    2899: (t) => {
      t.exports = ['Выше'];
    },
    53387: (t) => {
      t.exports = ['Ниже'];
    },
    36972: (t) => {
      t.exports = ['Прогноз'];
    },
    17994: (t) => {
      t.exports = ['Не удалось сохранить библиотеку'];
    },
    87375: (t) => {
      t.exports = ['Не удалось сохранить скрипт'];
    },
    35050: (t) => {
      t.exports = ['Фев'];
    },
    82719: (t) => {
      t.exports = ['Каналы по Фибоначчи'];
    },
    64192: (t) => {
      t.exports = ['Окружности Фибоначчи'];
    },
    63835: (t) => {
      t.exports = ['Коррекция по Фибоначчи'];
    },
    18072: (t) => {
      t.exports = ['Дуги сопротивления по Фибоначчи'];
    },
    20877: (t) => {
      t.exports = ['Веерные линии сопротивления по Фибоначчи'];
    },
    76783: (t) => {
      t.exports = ['Спираль по Фибоначчи'];
    },
    89037: (t) => {
      t.exports = ['Временные периоды по Фибоначчи'];
    },
    72489: (t) => {
      t.exports = ['Клин по Фибоначчи'];
    },
    21524: (t) => {
      t.exports = ['Флаг'];
    },
    55678: (t) => {
      t.exports = ['Флаг'];
    },
    29230: (t) => {
      t.exports = ['Плоский верх/низ'];
    },
    92754: (t) => {
      t.exports = ['Отразить по горизонтали'];
    },
    42015: (t) => {
      t.exports = ['Дробная часть неверна.'];
    },
    47542: (t) => {
      t.exports = [
        'Индикаторы фундаментального анализа на графиках более недоступны',
      ];
    },
    16245: (t) => {
      t.exports = ['Калькутта'];
    },
    3155: (t) => {
      t.exports = ['Катманду'];
    },
    92901: (t) => {
      t.exports = ['Каги'];
    },
    2693: (t) => {
      t.exports = 'Karachi';
    },
    72374: (t) => {
      t.exports = ['Кувейт'];
    },
    87338: (t) => {
      t.exports = ['Хошимин'];
    },
    61582: (t) => {
      t.exports = ['Пустые свечи'];
    },
    32918: (t) => {
      t.exports = ['Гонконг'];
    },
    61351: (t) => {
      t.exports = ['Гонолулу'];
    },
    60049: (t) => {
      t.exports = ['Горизонтальная линия'];
    },
    76604: (t) => {
      t.exports = ['Горизонтальный луч'];
    },
    42616: (t) => {
      t.exports = ['Голова и плечи'];
    },
    40530: (t) => {
      t.exports = ['Хейкен Аши'];
    },
    99820: (t) => {
      t.exports = ['Хельсинки'];
    },
    31971: (t) => {
      t.exports = ['Скрыть'];
    },
    33911: (t) => {
      t.exports = ['Скрыть все'];
    },
    95551: (t) => {
      t.exports = ['Скрыть все объекты рисования'];
    },
    44312: (t) => {
      t.exports = ['Скрыть все объекты и индикаторы'];
    },
    67927: (t) => {
      t.exports = [
        'Скрыть все объекты рисования, индикаторы, позиции и заявки',
      ];
    },
    86306: (t) => {
      t.exports = ['Скрыть все индикаторы'];
    },
    70803: (t) => {
      t.exports = ['Скрыть все позиции и заявки'];
    },
    13277: (t) => {
      t.exports = ['Скрыть объекты рисования'];
    },
    8251: (t) => {
      t.exports = ['Скрыть события на графике'];
    },
    44177: (t) => {
      t.exports = ['Скрыть индикаторы'];
    },
    2441: (t) => {
      t.exports = ['Скрыть отметки на барах'];
    },
    90540: (t) => {
      t.exports = ['Скрыть позиции и заявки'];
    },
    30777: (t) => {
      t.exports = ['Макс.'];
    },
    31994: (t) => {
      t.exports = ['Мин-Макс'];
    },
    60259: (t) => {
      t.exports = ['Метки макс. и мин. цен'];
    },
    21803: (t) => {
      t.exports = ['Линии макс. и мин. цен'];
    },
    31895: (t) => {
      t.exports = ['Маркер'];
    },
    69085: (t) => {
      t.exports = [
        'Гистограмма слишком большая, увеличьте параметр "Размер строки".',
      ];
    },
    8122: (t) => {
      t.exports = [
        'Гистограмма слишком большая, уменьшите параметр "Размер строки".',
      ];
    },
    22712: (t) => {
      t.exports = ['Не нужно'];
    },
    23450: (t) => {
      t.exports = ['Изображение'];
    },
    71778: (t) => {
      t.exports = ['Промежуточная'];
    },
    14177: (t) => {
      t.exports = ['Неизвестный инструмент'];
    },
    32619: (t) => {
      t.exports = ['Неверный инструмент'];
    },
    53239: (t) => {
      t.exports = ['Инвертировать шкалу'];
    },
    20062: (t) => {
      t.exports = ['Индексированная на 100'];
    },
    81584: (t) => {
      t.exports = ['Метки значений индикаторов'];
    },
    31485: (t) => {
      t.exports = ['Метки названий индикаторов'];
    },
    27677: (t) => {
      t.exports = ['Линия данных'];
    },
    98767: (t) => {
      t.exports = ['Добавить индикатор'];
    },
    9114: (t) => {
      t.exports = ['Внутрь'];
    },
    12354: (t) => {
      t.exports = ['Вилы (внутрь)'];
    },
    26579: (t) => {
      t.exports = ['Значок'];
    },
    37885: (t) => {
      t.exports = ['Стамбул'];
    },
    87469: (t) => {
      t.exports = ['Йоханнесбург'];
    },
    52707: (t) => {
      t.exports = ['Джакарта'];
    },
    95425: (t) => {
      t.exports = ['Янв'];
    },
    42890: (t) => {
      t.exports = ['Иерусалим'];
    },
    6215: (t) => {
      t.exports = ['Июл'];
    },
    15224: (t) => {
      t.exports = ['Июн'];
    },
    36253: (t) => {
      t.exports = ['Джуно'];
    },
    15241: (t) => {
      t.exports = ['Влево'];
    },
    29404: (t) => {
      t.exports = ['Вправо'];
    },
    850: (t) => {
      t.exports = ['Упс!'];
    },
    675: (t) => {
      t.exports = ['Дерево объектов'];
    },
    73546: (t) => {
      t.exports = ['Окт'];
    },
    39280: (t) => {
      t.exports = ['Цена откр.'];
    },
    25595: (t) => {
      t.exports = ['Обычные'];
    },
    82906: (t) => {
      t.exports = ['Осло'];
    },
    8136: (t) => {
      t.exports = ['Мин.'];
    },
    42284: (t) => {
      t.exports = ['Заблокировать'];
    },
    1441: (t) => {
      t.exports = ['Блокировать/разблокировать'];
    },
    82232: (t) => {
      t.exports = ['Зафиксировать вертикальную линию курсора по времени'];
    },
    18219: (t) => {
      t.exports = ['Зафиксировать соотношение цена/бар'];
    },
    12285: (t) => {
      t.exports = ['Логарифмическая'];
    },
    50286: (t) => {
      t.exports = ['Лондон'];
    },
    44604: (t) => {
      t.exports = ['Длинная позиция'];
    },
    87604: (t) => {
      t.exports = ['Лос-Анджелес'];
    },
    18528: (t) => {
      t.exports = ['Метка вниз'];
    },
    13046: (t) => {
      t.exports = ['Метка вверх'];
    },
    94420: (t) => {
      t.exports = ['Метки'];
    },
    89155: (t) => {
      t.exports = ['Лагос'];
    },
    25846: (t) => {
      t.exports = ['Лима'];
    },
    1277: (t) => {
      t.exports = ['Линия'];
    },
    63492: (t) => {
      t.exports = ['Линейный прорыв'];
    },
    83182: (t) => {
      t.exports = ['Линии'];
    },
    78104: (t) => {
      t.exports = ['Ссылка на изображение графика скопирована {emoji}'];
    },
    50091: (t) => {
      t.exports = ['Лиссабон'];
    },
    64352: (t) => {
      t.exports = ['Люксембург'];
    },
    11156: (t) => {
      t.exports = 'MTPredictor';
    },
    67861: (t) => {
      t.exports = [
        'Переместите точку, чтобы установить якорь, затем нажмите, чтобы разместить',
      ];
    },
    45828: (t) => {
      t.exports = ['Переместить'];
    },
    44302: (t) => {
      t.exports = ['Переместить шкалу влево'];
    },
    94338: (t) => {
      t.exports = ['Переместить шкалу вправо'];
    },
    66276: (t) => {
      t.exports = ['Измененные Шифа'];
    },
    18559: (t) => {
      t.exports = ['Видоизмененные вилы Шифа'];
    },
    18665: (t) => {
      t.exports = ['Москва'];
    },
    58038: (t) => {
      t.exports = ['Мадрид'];
    },
    34190: (t) => {
      t.exports = ['Мальта'];
    },
    90271: (t) => {
      t.exports = ['Манила'];
    },
    51369: (t) => {
      t.exports = ['Мар'];
    },
    85095: (t) => {
      t.exports = ['Мехико'];
    },
    75633: (t) => {
      t.exports = ['Объединить все шкалы в одну'];
    },
    95093: (t) => {
      t.exports = ['Разные'];
    },
    10931: (t) => {
      t.exports = ['Микро'];
    },
    58397: (t) => {
      t.exports = ['Миллениум'];
    },
    85884: (t) => {
      t.exports = ['Минуэт'];
    },
    9632: (t) => {
      t.exports = ['Минускул'];
    },
    63158: (t) => {
      t.exports = ['Отобразить по вертикали'];
    },
    42769: (t) => {
      t.exports = ['Маскат'];
    },
    43088: (t) => {
      t.exports = ['Н/Д'];
    },
    95222: (t) => {
      t.exports = ['Нет данных'];
    },
    3485: (t) => {
      t.exports = ['Без шкалы (на весь экран)'];
    },
    8886: (t) => {
      t.exports = ['Не синхронизировать'];
    },
    16971: (t) => {
      t.exports = ['Нет данных объёма'];
    },
    75549: (t) => {
      t.exports = ['Заметка'];
    },
    71230: (t) => {
      t.exports = ['Ноя'];
    },
    99203: (t) => {
      t.exports = ['Остров Норфолк'];
    },
    79023: (t) => {
      t.exports = ['Найроби'];
    },
    91203: (t) => {
      t.exports = ['Нью-Йорк'];
    },
    24143: (t) => {
      t.exports = ['Новая Зеландия'];
    },
    40887: (t) => {
      t.exports = ['Выше, на новую панель'];
    },
    96712: (t) => {
      t.exports = ['Ниже, на новую панель'];
    },
    33566: (t) => {
      t.exports = ['Никосия'];
    },
    64968: (t) => {
      t.exports = ['Что-то пошло не так. Попробуйте позже, пожалуйста.'];
    },
    10520: (t) => {
      t.exports = ['Сохранить график'];
    },
    9908: (t) => {
      t.exports = ['Сохранить как'];
    },
    68553: (t) => {
      t.exports = ['Сан-Сальвадор'];
    },
    65412: (t) => {
      t.exports = ['Сантьяго'];
    },
    13538: (t) => {
      t.exports = ['Сан-Паулу'];
    },
    37207: (t) => {
      t.exports = ['Игнорировать шкалу индикаторов'];
    },
    51464: (t) => {
      t.exports = ['Шифа'];
    },
    98114: (t) => {
      t.exports = ['Вилы Шифа'];
    },
    1535: (t) => {
      t.exports = [
        'Обновления скрипта могут не сохраниться, если вы закроете страницу.',
      ];
    },
    89517: (t) => {
      t.exports = ['Настройки'];
    },
    43247: (t) => {
      t.exports = ['Вторая дробная часть неверна.'];
    },
    19796: (t) => {
      t.exports = ['Отправить назад'];
    },
    23221: (t) => {
      t.exports = ['На один слой назад'];
    },
    5961: (t) => {
      t.exports = ['Сеул'];
    },
    57902: (t) => {
      t.exports = ['Сен'];
    },
    25866: (t) => {
      t.exports = ['Сессия'];
    },
    59827: (t) => {
      t.exports = ['Границы сессий'];
    },
    69240: (t) => {
      t.exports = ['Шанхай'];
    },
    37819: (t) => {
      t.exports = ['Короткая позиция'];
    },
    81428: (t) => {
      t.exports = ['Показать'];
    },
    98116: (t) => {
      t.exports = ['Показать все объекты рисования'];
    },
    39046: (t) => {
      t.exports = ['Показать все объекты и индикаторы'];
    },
    38293: (t) => {
      t.exports = [
        'Показать все объекты рисования, индикаторы, позиции и заявки',
      ];
    },
    49982: (t) => {
      t.exports = ['Показать все индикаторы'];
    },
    48284: (t) => {
      t.exports = ['Все идеи'];
    },
    62632: (t) => {
      t.exports = ['Показать все позиции и заявки'];
    },
    24620: (t) => {
      t.exports = ['Показывать переключение непрерывного контракта'];
    },
    84813: (t) => {
      t.exports = ['Показывать срок действия контракта'];
    },
    66263: (t) => {
      t.exports = ['Отображать дивиденды'];
    },
    46771: (t) => {
      t.exports = ['Отображать прибыль на акцию'];
    },
    87933: (t) => {
      t.exports = ['Идеи тех, на кого подписан'];
    },
    30709: (t) => {
      t.exports = ['Показать последние новости'];
    },
    58669: (t) => {
      t.exports = ['Только мои идеи'];
    },
    30816: (t) => {
      t.exports = ['Отображать сплит акций'];
    },
    68161: (t) => {
      t.exports = ['Знак'];
    },
    56683: (t) => {
      t.exports = ['Сингапур'];
    },
    69502: (t) => {
      t.exports = ['Синусоида'];
    },
    44904: (t) => {
      t.exports = ['Квадрат'];
    },
    70213: (t) => {
      t.exports = [
        'Достигнут лимит индикаторов/сигналов: {number} элементов на шаблон графиков.\nПожалуйста, удалите некоторые инструменты.',
      ];
    },
    32733: (t) => {
      t.exports = ['Стиль'];
    },
    65323: (t) => {
      t.exports = ['Слева'];
    },
    14113: (t) => {
      t.exports = ['Справа'];
    },
    93161: (t) => {
      t.exports = ['Оставаться в режиме рисования'];
    },
    48767: (t) => {
      t.exports = ['Стокгольм'];
    },
    29662: (t) => {
      t.exports = ['Субмикро'];
    },
    9753: (t) => {
      t.exports = ['Субмиллениум'];
    },
    71722: (t) => {
      t.exports = ['Субминуэт'];
    },
    91889: (t) => {
      t.exports = ['Суперцикл'];
    },
    33820: (t) => {
      t.exports = ['Супермиллениум'];
    },
    11020: (t) => {
      t.exports = ['Сидней'];
    },
    89659: (t) => {
      t.exports = ['Ошибка символа'];
    },
    90932: (t) => {
      t.exports = ['Метка имени инструмента'];
    },
    65986: (t) => {
      t.exports = ['Информация по инструменту'];
    },
    52054: (t) => {
      t.exports = ['Метка последнего значения символа'];
    },
    33606: (t) => {
      t.exports = ['Синхр. везде'];
    },
    18008: (t) => {
      t.exports = ['Синхронизировать на всех графиках'];
    },
    99969: (t) => {
      t.exports = ['Крестики-нолики'];
    },
    53047: (t) => {
      t.exports = ['Ломаная линия'];
    },
    34402: (t) => {
      t.exports = ['Траектория'];
    },
    70394: (t) => {
      t.exports = ['Параллельный канал'];
    },
    95995: (t) => {
      t.exports = ['Париж'];
    },
    29682: (t) => {
      t.exports = ['Вставить'];
    },
    51102: (t) => {
      t.exports = ['Процентная'];
    },
    35590: (t) => {
      t.exports = ['Перт'];
    },
    19093: (t) => {
      t.exports = ['Финикс'];
    },
    22293: (t) => {
      t.exports = ['Наклонный веер'];
    },
    43852: (t) => {
      t.exports = ['Вилы'];
    },
    37680: (t) => {
      t.exports = ['Закрепить на новой левой шкале'];
    },
    43707: (t) => {
      t.exports = ['Закрепить на новой правой шкале'];
    },
    91130: (t) => {
      t.exports = ['Закрепить на левой шкале'];
    },
    61201: (t) => {
      t.exports = ['Закрепить на левой шкале (скрыто)'];
    },
    764: (t) => {
      t.exports = ['Закрепить на правой шкале'];
    },
    20207: (t) => {
      t.exports = ['Закрепить на правой шкале (скрыто)'];
    },
    66156: (t) => {
      t.exports = ['Закрепить на шкале (сейчас слева)'];
    },
    54727: (t) => {
      t.exports = ['Закрепить на шкале (сейчас без шкалы)'];
    },
    76598: (t) => {
      t.exports = ['Закрепить на шкале (сейчас справа)'];
    },
    39065: (t) => {
      t.exports = ['Закрепить на шкале (сейчас {label})'];
    },
    97324: (t) => {
      t.exports = ['Закрепить на шкале {label}'];
    },
    56948: (t) => {
      t.exports = ['Закрепить на шкале {label} (скрыто)'];
    },
    32156: (t) => {
      t.exports = ['Закреплено на левой шкале'];
    },
    8128: (t) => {
      t.exports = ['Закреплено на левой шкале (скрыто)'];
    },
    3822: (t) => {
      t.exports = ['Закреплено на правой шкале'];
    },
    44538: (t) => {
      t.exports = ['Закрепить на правой шкале (скрыто)'];
    },
    65810: (t) => {
      t.exports = ['Закреплено на шкале {label}'];
    },
    14125: (t) => {
      t.exports = ['Закреплено на шкале {label} (скрыто)'];
    },
    97378: (t) => {
      t.exports = ['Кнопка Плюс'];
    },
    46669: (t) => {
      t.exports = [
        'Пожалуйста, предоставьте доступ к записи в буфер обмена в вашем браузере или нажмите {keystroke}',
      ];
    },
    35963: (t) => {
      t.exports = [
        'Зажмите и держите {key} во время масштабирования, чтобы сохранить позицию графика',
      ];
    },
    95921: (t) => {
      t.exports = ['Метка цены'];
    },
    28625: (t) => {
      t.exports = ['Ценовая заметка'];
    },
    2032: (t) => {
      t.exports = ['Диапазон цен'];
    },
    32061: (t) => {
      t.exports = ['Формат цены не поддерживается.'];
    },
    91492: (t) => {
      t.exports = ['Линия цены'];
    },
    48404: (t) => {
      t.exports = ['Первичная'];
    },
    87086: (t) => {
      t.exports = ['Проекция'];
    },
    10160: (t) => {
      t.exports = ['Опубликовано на {customer}, {date}'];
    },
    19056: (t) => {
      t.exports = ['Катар'];
    },
    9998: (t) => {
      t.exports = ['Вращающийся прямоугольник'];
    },
    74214: (t) => {
      t.exports = ['Рим'];
    },
    50470: (t) => {
      t.exports = ['Луч'];
    },
    90357: (t) => {
      t.exports = 'Range';
    },
    26833: (t) => {
      t.exports = ['Рейкьявик'];
    },
    328: (t) => {
      t.exports = ['Прямоугольник'];
    },
    41615: (t) => {
      t.exports = ['Повторить'];
    },
    35001: (t) => {
      t.exports = ['Регрессионный тренд'];
    },
    34596: (t) => {
      t.exports = ['Удалить'];
    },
    1434: (t) => {
      t.exports = ['Удалить объекты рисования'];
    },
    13951: (t) => {
      t.exports = ['Удалить индикаторы'];
    },
    4142: (t) => {
      t.exports = ['Переименовать график'];
    },
    20801: (t) => {
      t.exports = ['Ренко'];
    },
    34301: (t) => {
      t.exports = ['Сбросить состояние графика'];
    },
    17258: (t) => {
      t.exports = ['Сбросить масштаб ценовой шкалы'];
    },
    25333: (t) => {
      t.exports = ['Сбросить масштаб временной шкалы'];
    },
    52588: (t) => {
      t.exports = ['Эр-Рияд'];
    },
    5871: (t) => {
      t.exports = ['Рига'];
    },
    33603: (t) => {
      t.exports = ['Предупреждение'];
    },
    48474: (t) => {
      t.exports = ['Варшава'];
    },
    20466: (t) => {
      t.exports = ['Токелау'];
    },
    94284: (t) => {
      t.exports = ['Токио'];
    },
    83836: (t) => {
      t.exports = ['Торонто'];
    },
    38788: (t) => {
      t.exports = ['Тайбей'];
    },
    39108: (t) => {
      t.exports = ['Таллин'];
    },
    37229: (t) => {
      t.exports = ['Текст'];
    },
    16267: (t) => {
      t.exports = ['Тегеран'];
    },
    19611: (t) => {
      t.exports = ['Шаблон'];
    },
    29198: (t) => {
      t.exports = [
        'Поставщик данных не предоставляет данные об объеме для этого инструмента.',
      ];
    },
    8162: (t) => {
      t.exports = [
        'Невозможно загрузить предпросмотр публикации. Отключите расширения браузера и попробуйте снова.',
      ];
    },
    65943: (t) => {
      t.exports = ['Данный индикатор нельзя применить к другому индикатору.'];
    },
    74986: (t) => {
      t.exports = [
        'Доступ к этому скрипту ограничен. Запросите доступ у автора скрипта.',
      ];
    },
    98538: (t) => {
      t.exports = ['Паттерн трёх движений'];
    },
    30973: (t) => {
      t.exports = ['Тики'];
    },
    31976: (t) => {
      t.exports = ['Время'];
    },
    64375: (t) => {
      t.exports = ['Часовой пояс'];
    },
    95005: (t) => {
      t.exports = ['Временные циклы'];
    },
    87085: (t) => {
      t.exports = ['Торговля'];
    },
    94770: (t) => {
      t.exports = ['Угол тренда'];
    },
    23104: (t) => {
      t.exports = ['Линия тренда'];
    },
    15501: (t) => {
      t.exports = ['Расширение Фибоначчи, основанное на тренде'];
    },
    31196: (t) => {
      t.exports = ['Периоды Фибоначчи, основанные на тренде'];
    },
    29245: (t) => {
      t.exports = ['Треугольник'];
    },
    83356: (t) => {
      t.exports = ['Треугольник вниз'];
    },
    12390: (t) => {
      t.exports = ['Шаблон "Треугольник"'];
    },
    28340: (t) => {
      t.exports = ['Треугольник вверх'];
    },
    93855: (t) => {
      t.exports = ['Тунис'];
    },
    50406: (t) => {
      t.exports = 'UTC';
    },
    38587: (t) => {
      t.exports = ['Понятно'];
    },
    81320: (t) => {
      t.exports = ['Отменить'];
    },
    25933: (t) => {
      t.exports = ['Количество'];
    },
    15101: (t) => {
      t.exports = ['Разблокировать'];
    },
    34150: (t) => {
      t.exports = ['Восходящая волна 4'];
    },
    83927: (t) => {
      t.exports = ['Восходящая волна 5'];
    },
    58976: (t) => {
      t.exports = ['Восходящая волна 1 или А'];
    },
    11661: (t) => {
      t.exports = ['Восходящая волна 2 или B'];
    },
    53958: (t) => {
      t.exports = ['Восходящая волна 3'];
    },
    66560: (t) => {
      t.exports = ['Восходящая волна С'];
    },
    18426: (t) => {
      t.exports = ['Фиксированный профиль объема'];
    },
    61022: (t) => {
      t.exports = [
        'Индикатор Профиль объёма доступен только в платных подписках.',
      ];
    },
    15771: (t) => {
      t.exports = ['Ванкувер'];
    },
    56211: (t) => {
      t.exports = ['Вертикальная линия'];
    },
    75354: (t) => {
      t.exports = ['Вильнюс'];
    },
    21852: (t) => {
      t.exports = ['Видимость'];
    },
    27557: (t) => {
      t.exports = ['Видимость на интервалах'];
    },
    89960: (t) => {
      t.exports = ['При наведении курсора'];
    },
    22198: (t) => {
      t.exports = ['Порядок слоев'];
    },
    7050: (t) => {
      t.exports = ['X Пересечение'];
    },
    66527: (t) => {
      t.exports = ['Шаблон XABCD'];
    },
    17126: (t) => {
      t.exports = [
        'Нельзя использовать этот временной интервал точек разворота на этом интервале',
      ];
    },
    69293: (t) => {
      t.exports = ['Янгон'];
    },
    84301: (t) => {
      t.exports = ['Цюрих'];
    },
    76020: (t) => {
      t.exports = ['изменение угла Эллиотта'];
    },
    83935: (t) => {
      t.exports = ['изменение: не перекрывать метки'];
    },
    39402: (t) => {
      t.exports = ['изменение видимости метки средней цены закрытия'];
    },
    98866: (t) => {
      t.exports = ['изменение видимости линии средней цены закрытия'];
    },
    5100: (t) => {
      t.exports = ['изменение видимости меток цен покупки и продажи'];
    },
    32311: (t) => {
      t.exports = ['изменение видимости линий цен покупки и продажи'];
    },
    22641: (t) => {
      t.exports = ['изменение валюты'];
    },
    30501: (t) => {
      t.exports = ['изменение формата отображения графиков на {title}'];
    },
    7017: (t) => {
      t.exports = ['изменение видимости переключения непрерывного контракта'];
    },
    58108: (t) => {
      t.exports = ['изменение видимости обратного отсчета до закрытия бара'];
    },
    7151: (t) => {
      t.exports = ['изменение диапазона дат'];
    },
    84944: (t) => {
      t.exports = ['изменение отображения дивидендов'];
    },
    79574: (t) => {
      t.exports = ['изменение видимости событий на графике'];
    },
    88217: (t) => {
      t.exports = ['изменение отображения прибыли'];
    },
    28288: (t) => {
      t.exports = ['изменение видимости срока действия фьючерсного контракта'];
    },
    66805: (t) => {
      t.exports = ['изменение видимости меток макс. и мин. цен'];
    },
    92556: (t) => {
      t.exports = ['изменение видимости линий макс. и мин. цен'];
    },
    87027: (t) => {
      t.exports = ['изменение видимости меток названий индикаторов'];
    },
    14922: (t) => {
      t.exports = ['изменение видимости меток значений индикаторов'];
    },
    77578: (t) => {
      t.exports = ['изменение видимости последних новостей'];
    },
    87510: (t) => {
      t.exports = ['изменение высоты панели'];
    },
    50190: (t) => {
      t.exports = ['изменение видимости кнопки Плюс'];
    },
    49889: (t) => {
      t.exports = ['изменение видимости меток цены пре-/постмаркета'];
    },
    16750: (t) => {
      t.exports = ['изменение видимости линии цены пре-/постмаркета'];
    },
    59883: (t) => {
      t.exports = ['изменение видимости линии цены предыдущего закрытия'];
    },
    67761: (t) => {
      t.exports = ['изменение видимости линии цены'];
    },
    69510: (t) => {
      t.exports = ['изменение соотношения цена/бар'];
    },
    32303: (t) => {
      t.exports = ['изменение разрешения'];
    },
    526: (t) => {
      t.exports = ['смену инструмента'];
    },
    9402: (t) => {
      t.exports = ['изменение видимости меток инструментов'];
    },
    53150: (t) => {
      t.exports = ['изменение видимости последнего значения инструмента'];
    },
    12707: (t) => {
      t.exports = [
        'изменение видимости предыдущего значения закрытия инструмента',
      ];
    },
    65303: (t) => {
      t.exports = ['изменение сессии'];
    },
    15403: (t) => {
      t.exports = ['изменение видимости границ сессии'];
    },
    53438: (t) => {
      t.exports = ['изменение стиля серии'];
    },
    74488: (t) => {
      t.exports = ['изменение отображения сплитов'];
    },
    20505: (t) => {
      t.exports = ['изменение часового пояса'];
    },
    39028: (t) => {
      t.exports = ['изменение единиц'];
    },
    21511: (t) => {
      t.exports = ['изменение отображения'];
    },
    16698: (t) => {
      t.exports = ['изменение видимости на текущем интервале'];
    },
    78422: (t) => {
      t.exports = ['изменение видимости на текущем интервале и выше'];
    },
    49529: (t) => {
      t.exports = ['изменение видимости на текущем интервале и ниже'];
    },
    66927: (t) => {
      t.exports = ['изменение видимости на всех интервалах'];
    },
    74428: (t) => {
      t.exports = ['изменение стиля {title}'];
    },
    72032: (t) => {
      t.exports = ['изменение точки {pointIndex}'];
    },
    65911: (t) => {
      t.exports = ['графики от TradingView'];
    },
    5179: (t) => {
      t.exports = ['клонирование объектов рисования'];
    },
    3195: (t) => {
      t.exports = ['создание группы инструментов рисования'];
    },
    92659: (t) => {
      t.exports = ['создание группы из выбранных инструментов рисования'];
    },
    81791: (t) => {
      t.exports = ['создание {tool}'];
    },
    63649: (t) => {
      t.exports = ['вырезание объектов'];
    },
    78755: (t) => {
      t.exports = ['вырезание {title}'];
    },
    99113: (t) => {
      t.exports = [
        'добавление инструмента рисования {lineTool} в группу {name}',
      ];
    },
    40242: (t) => {
      t.exports = ['добавление инструмента(ов) рисования в группу {group}'];
    },
    22856: (t) => {
      t.exports = ['добавление фин. показателя на все графики в окне'];
    },
    82388: (t) => {
      t.exports = ['добавление индикатора на все графики в окне'];
    },
    94292: (t) => {
      t.exports = ['добавление стратегии ко всем графикам в окне'];
    },
    27982: (t) => {
      t.exports = ['добавление символа на все графики в окне'];
    },
    66568: (t) => {
      t.exports = ['применение цветовой темы для графика'];
    },
    64034: (t) => {
      t.exports = ['применение ко всем свойствам графика'];
    },
    49037: (t) => {
      t.exports = ['применение шаблона графических объектов'];
    },
    96996: (t) => {
      t.exports = ['применение настроек по умолчанию к выбранным объектам'];
    },
    44547: (t) => {
      t.exports = ['применение индикаторов ко всем графикам в окне'];
    },
    26065: (t) => {
      t.exports = ['применение шаблона индикаторов {template}'];
    },
    58570: (t) => {
      t.exports = ['применение темы для панелей инструментов'];
    },
    27195: (t) => {
      t.exports = ['перемещение на один слой вперед группы {title}'];
    },
    78246: (t) => {
      t.exports = ['перемещение поверх: {title}'];
    },
    56763: (t) => {
      t.exports = ['перемещение вперед: {title}'];
    },
    5607: (t) => {
      t.exports = ['от TradingView'];
    },
    90621: (t) => {
      t.exports = ['закрепление диапазона дат'];
    },
    12962: (t) => {
      t.exports = ['удаление линии уровня'];
    },
    63391: (t) => {
      t.exports = ['удаление инструменты рисования из группы {group}'];
    },
    59942: (t) => {
      t.exports = ['отражение по горизонтали шаблонов баров'];
    },
    70301: (t) => {
      t.exports = ['скрытие: {title}'];
    },
    91842: (t) => {
      t.exports = ['скрытие линии меток оповещений'];
    },
    54781: (t) => {
      t.exports = ['скрытие всех объектов рисования'];
    },
    44974: (t) => {
      t.exports = ['скрытие отметок на барах'];
    },
    28916: (t) => {
      t.exports = ['закрепление интервала'];
    },
    94245: (t) => {
      t.exports = ['инвертирование шкалы'];
    },
    90743: (t) => {
      t.exports = ['добавление {title}'];
    },
    53146: (t) => {
      t.exports = ['добавление {title} после {targetTitle}'];
    },
    74055: (t) => {
      t.exports = ['перемещение {title} после {target}'];
    },
    11231: (t) => {
      t.exports = ['перемещение {title} перед {target}'];
    },
    67176: (t) => {
      t.exports = ['перемещение {title} перед {targetTitle}'];
    },
    54597: (t) => {
      t.exports = ['загрузку шаблона по умолчанию'];
    },
    30295: (t) => {
      t.exports = ['загрузка...'];
    },
    50193: (t) => {
      t.exports = ['блокировку: {title}'];
    },
    4963: (t) => {
      t.exports = ['закрепление группы {group}'];
    },
    68163: (t) => {
      t.exports = ['прикрепление объектов'];
    },
    47107: (t) => {
      t.exports = ['перемещение'];
    },
    11303: (t) => {
      t.exports = ['перемещение на новую левую шкалу: {title}'];
    },
    45544: (t) => {
      t.exports = ['перемещение на новую правую шкалу: {title}'];
    },
    81898: (t) => {
      t.exports = ['перемещение всех шкал влево'];
    },
    22863: (t) => {
      t.exports = ['перемещение всех шкал вправо'];
    },
    45356: (t) => {
      t.exports = ['перемещение объекта(ов) рисования'];
    },
    15086: (t) => {
      t.exports = ['перемещение влево'];
    },
    61711: (t) => {
      t.exports = ['перемещение вправо'];
    },
    4184: (t) => {
      t.exports = ['перемещение шкалы'];
    },
    74642: (t) => {
      t.exports = ['режим Без шкалы (на весь экран) для {title}'];
    },
    45223: (t) => {
      t.exports = ['невидимость группы {group}'];
    },
    87927: (t) => {
      t.exports = ['видимость группы {group}'];
    },
    62153: (t) => {
      t.exports = ['перемещение ниже'];
    },
    70746: (t) => {
      t.exports = ['перемещение на панель'];
    },
    66143: (t) => {
      t.exports = ['перемещение выше'];
    },
    81870: (t) => {
      t.exports = ['отражение по вертикали шаблонов баров'];
    },
    16542: (t) => {
      t.exports = ['н/д'];
    },
    47222: (t) => {
      t.exports = ['изменение ценовой шкалы'];
    },
    99042: (t) => {
      t.exports = ['игнорирование шкалы индикаторов'];
    },
    35962: (t) => {
      t.exports = ['изменение временной шкалы'];
    },
    68193: (t) => {
      t.exports = ['прокрутку'];
    },
    70009: (t) => {
      t.exports = ['прокрутку временной шкалы'];
    },
    69485: (t) => {
      t.exports = ['применение стратегии выбора ценовой шкалы для {title}'];
    },
    16259: (t) => {
      t.exports = ['перемещение назад: {title}'];
    },
    66781: (t) => {
      t.exports = ['перемещение назад: {title}'];
    },
    4998: (t) => {
      t.exports = ['перемещение на один слой назад группы {title}'];
    },
    64704: (t) => {
      t.exports = ['возможность делиться объектами рисования везде'];
    },
    77554: (t) => {
      t.exports = ['возможность делиться объектами рисования на всех графиках'];
    },
    16237: (t) => {
      t.exports = ['отображение линий меток оповещений'];
    },
    13622: (t) => {
      t.exports = ['отображение всех идей'];
    },
    26267: (t) => {
      t.exports = ['отображение идей тех, на кого подписан'];
    },
    40061: (t) => {
      t.exports = ['отображение только собственных идей'];
    },
    52010: (t) => {
      t.exports = ['нахождение в режиме рисования'];
    },
    98784: (t) => {
      t.exports = ['прекращение синхронизации объектов рисования'];
    },
    57011: (t) => {
      t.exports = ['прекращение синхронизации линий тренда'];
    },
    92831: (t) => {
      t.exports = ['закрепление символа'];
    },
    60635: (t) => {
      t.exports = ['синхронизацию времени'];
    },
    99769: (t) => {
      t.exports = ['при поддержке'];
    },
    68111: (t) => {
      t.exports = ['технология TradingView'];
    },
    96916: (t) => {
      t.exports = ['вставку объекта рисования'];
    },
    80611: (t) => {
      t.exports = ['вставку индикатора'];
    },
    41601: (t) => {
      t.exports = ['вставку {title}'];
    },
    84018: (t) => {
      t.exports = ['закрепление на левой шкале'];
    },
    22615: (t) => {
      t.exports = ['закрепление на правой шкале'];
    },
    56015: (t) => {
      t.exports = ['закрепление на шкале {label}'];
    },
    33348: (t) => {
      t.exports = ['перестановку панелей'];
    },
    15516: (t) => {
      t.exports = ['удаление всех индикаторов'];
    },
    80171: (t) => {
      t.exports = ['удаление всех объектов рисования и индикаторов'];
    },
    59211: (t) => {
      t.exports = ['удаление невыбранных пустых объектов рисования'];
    },
    44656: (t) => {
      t.exports = ['удаление объектов рисования'];
    },
    70653: (t) => {
      t.exports = ['удаление группы объектов рисования'];
    },
    66414: (t) => {
      t.exports = ['удаление источников данных линии'];
    },
    47637: (t) => {
      t.exports = ['удаление панели'];
    },
    39859: (t) => {
      t.exports = ['удаление {title}'];
    },
    78811: (t) => {
      t.exports = ['удаление группы инструментов рисования: {name}'];
    },
    16338: (t) => {
      t.exports = ['переименование группы {group} на {newName}'];
    },
    30910: (t) => {
      t.exports = ['сброс настроек размера графика'];
    },
    21948: (t) => {
      t.exports = ['сброс состояния графика'];
    },
    55064: (t) => {
      t.exports = ['сброс масштабов временной шкалы'];
    },
    13034: (t) => {
      t.exports = ['изменение размера графика'];
    },
    9608: (t) => {
      t.exports = ['сброс настроек'];
    },
    63060: (t) => {
      t.exports = ['включение/выключение автоматического масштаба'];
    },
    98860: (t) => {
      t.exports = ['включение/выключение индексированной на 100 шкалы'];
    },
    21203: (t) => {
      t.exports = ['включение/выключение закрепления шкалы'];
    },
    60166: (t) => {
      t.exports = ['включение/выключение логарифмической шкалы'];
    },
    68642: (t) => {
      t.exports = ['включение/выключение процентной шкалы'];
    },
    33714: (t) => {
      t.exports = ['включение/выключение равномерной шкалы'];
    },
    47122: (t) => {
      t.exports = ['время отслеживания'];
    },
    28068: (t) => {
      t.exports = ['отключение возможности делиться объектами рисования'];
    },
    66824: (t) => {
      t.exports = ['открепление объектов'];
    },
    51114: (t) => {
      t.exports = ['открепление группы {group}'];
    },
    92421: (t) => {
      t.exports = ['разблокирование: {title}'];
    },
    20057: (t) => {
      t.exports = ['перемещение на новую нижнюю панель'];
    },
    52540: (t) => {
      t.exports = ['перемещение выше, на новую панель'];
    },
    86949: (t) => {
      t.exports = ['перемещение ниже, на новую панель'];
    },
    50728: (t) => {
      t.exports = ['обновление скрипта {title}'];
    },
    33355: (t) => {
      t.exports = ['Бары: {count}'];
    },
    88841: (t) => {
      t.exports = ['Финансовые показатели {symbol} от TradingView'];
    },
    38641: (t) => {
      t.exports = ['{userName} опубликовал(а) на {customer}, {date}'];
    },
    59833: (t) => {
      t.exports = ['изменение масштаба'];
    },
    19813: (t) => {
      t.exports = ['увеличение масштаба'];
    },
    9645: (t) => {
      t.exports = ['уменьшение масштаба'];
    },
    30572: (t) => {
      t.exports = ['день', 'дня', 'дней', 'дней'];
    },
    52254: (t) => {
      t.exports = ['час', 'часа', 'часов', 'часов'];
    },
    99062: (t) => {
      t.exports = ['месяц', 'месяца', 'месяцев', 'месяцев'];
    },
    69143: (t) => {
      t.exports = ['минута', 'минуты', 'минут', 'минут'];
    },
    71787: (t) => {
      t.exports = ['секунда', 'секунды', 'секунд', 'секунд'];
    },
    82797: (t) => {
      t.exports = 'range';
    },
    47966: (t) => {
      t.exports = ['неделя', 'недели', 'недель', 'недель'];
    },
    99136: (t) => {
      t.exports = ['тик', 'тика', 'тиков', 'тиков'];
    },
    18562: (t) => {
      (t.exports = Object.create(null)),
        (t.exports['#AAPL-symbol-description'] = 'Apple Inc'),
        (t.exports['#AUDCAD-symbol-description'] = [
          'Австралийский доллар / Канадский доллар',
        ]),
        (t.exports['#AUDCHF-symbol-description'] = [
          'Австралийский доллар / Швейцарский франк',
        ]),
        (t.exports['#AUDJPY-symbol-description'] = [
          'Австралийский доллар / Японская иена',
        ]),
        (t.exports['#AUDNZD-symbol-description'] = [
          'Австралийский доллар / Новозеландский доллар',
        ]),
        (t.exports['#AUDRUB-symbol-description'] = [
          'Австралийский доллар / Российский рубль',
        ]),
        (t.exports['#AUDUSD-symbol-description'] = [
          'Австралийский доллар / Доллар США',
        ]),
        (t.exports['#BRLJPY-symbol-description'] = [
          'Бразильский реал / Японская иена',
        ]),
        (t.exports['#BTCCAD-symbol-description'] = [
          'Биткоин / Канадский доллар',
        ]),
        (t.exports['#BTCCNY-symbol-description'] = [
          'Биткоин / Китайский юань',
        ]),
        (t.exports['#BTCEUR-symbol-description'] = ['Биткоин / Евро']),
        (t.exports['#BTCKRW-symbol-description'] = [
          'Биткоин / Южнокорейская вона',
        ]),
        (t.exports['#BTCRUR-symbol-description'] = [
          'Биткоин / Российский рубль',
        ]),
        (t.exports['#BTCUSD-symbol-description'] = ['Биткоин / Доллар США']),
        (t.exports['#BVSP-symbol-description'] = ['Индекс BOVESPA']),
        (t.exports['#CADJPY-symbol-description'] = [
          'Канадский доллар / Японская иена',
        ]),
        (t.exports['#CB1!-symbol-description'] = ['Нефть марки Brent']),
        (t.exports['#CHFJPY-symbol-description'] = [
          'Швейцарский франк / Японская иена',
        ]),
        (t.exports['#COPPER-symbol-description'] = ['CFD на медь']),
        (t.exports['#ES1-symbol-description'] = ['Фьючерсы на E-Mini S&P 500']),
        (t.exports['#ESP35-symbol-description'] = ['Индекс IBEX 35']),
        (t.exports['#EUBUND-symbol-description'] = ['Еврооблигации']),
        (t.exports['#EURAUD-symbol-description'] = [
          'Евро / Австралийский доллар',
        ]),
        (t.exports['#EURBRL-symbol-description'] = ['Евро / Бразильский реал']),
        (t.exports['#EURCAD-symbol-description'] = ['Евро / Канадский доллар']),
        (t.exports['#EURCHF-symbol-description'] = [
          'Евро / Швейцарский франк',
        ]),
        (t.exports['#EURGBP-symbol-description'] = ['Евро / Британский фунт']),
        (t.exports['#EURJPY-symbol-description'] = ['Евро / Японская иена']),
        (t.exports['#EURNZD-symbol-description'] = [
          'Евро / Новозеландский доллар',
        ]),
        (t.exports['#EURRUB-symbol-description'] = ['Евро / Российский рубль']),
        (t.exports['#EURRUB_TOM-symbol-description'] = [
          'Евро / Российский рубль TOM',
        ]),
        (t.exports['#EURSEK-symbol-description'] = ['Евро / Шведская крона']),
        (t.exports['#EURTRY-symbol-description'] = ['Евро / Турецкая лира']),
        (t.exports['#EURUSD-symbol-description'] = ['Евро / Доллар США']),
        (t.exports['#EUSTX50-symbol-description'] = ['Индекс Euro Stoxx 50']),
        (t.exports['#FRA40-symbol-description'] = ['Индекс CAC 40']),
        (t.exports['#GB10-symbol-description'] = [
          '10-летние облигации Британии',
        ]),
        (t.exports['#GBPAUD-symbol-description'] = [
          'Британский фунт / Австралийский доллар',
        ]),
        (t.exports['#GBPCAD-symbol-description'] = [
          'Британский фунт / Канадский доллар',
        ]),
        (t.exports['#GBPCHF-symbol-description'] = [
          'Британский фунт / Швейцарский франк',
        ]),
        (t.exports['#GBPEUR-symbol-description'] = ['Британский фунт / Евро']),
        (t.exports['#GBPJPY-symbol-description'] = [
          'Британский фунт / Японская иена',
        ]),
        (t.exports['#GBPNZD-symbol-description'] = [
          'Британский фунт / Новозеландский доллар',
        ]),
        (t.exports['#GBPRUB-symbol-description'] = [
          'Британский фунт / Российский рубль',
        ]),
        (t.exports['#GBPUSD-symbol-description'] = [
          'Британский фунт / Доллар США',
        ]),
        (t.exports['#GER30-symbol-description'] = ['Индекс DAX']),
        (t.exports['#GOOGL-symbol-description'] =
          'Alphabet Inc (Google) Class A'),
        (t.exports['#ITA40-symbol-description'] = ['Индекс FTSE MIB']),
        (t.exports['#JPN225-symbol-description'] = ['Индекс Nikkei 225']),
        (t.exports['#JPYKRW-symbol-description'] = [
          'Японская иена / Южнокорейская вона',
        ]),
        (t.exports['#JPYRUB-symbol-description'] = [
          'Японская иена / Российский рубль',
        ]),
        (t.exports['#KA1-symbol-description'] = ['Фьючерсы на сахар']),
        (t.exports['#KG1-symbol-description'] = ['Фьючерсы на хлопок']),
        (t.exports['#KT1-symbol-description'] = 'Key Tronic Corр.'),
        (t.exports['#LKOH-symbol-description'] = ['ЛУКОЙЛ']),
        (t.exports['#LTCBTC-symbol-description'] = ['Лайткоин / Биткоин']),
        (t.exports['#MGNT-symbol-description'] = ['Магнит']),
        (t.exports['#MICEX-symbol-description'] = ['Индекс МосБиржи']),
        (t.exports['#MNOD_ME.EQRP-symbol-description'] = [
          'ОАО ГМК Норильский никель (РЕПО)',
        ]),
        (t.exports['#MSFT-symbol-description'] = [
          'Microsoft Corporation (Майкрософт)',
        ]),
        (t.exports['#NAS100-symbol-description'] = 'US 100 Cash CFD'),
        (t.exports['#NGAS-symbol-description'] = ['Природный газ (Henry Hub)']),
        (t.exports['#NKY-symbol-description'] = ['Идекс Nikkei 225']),
        (t.exports['#NZDJPY-symbol-description'] = [
          'Новозеландский доллар / Японская иена',
        ]),
        (t.exports['#NZDUSD-symbol-description'] = [
          'Новозеландский доллар / Доллар США',
        ]),
        (t.exports['#RB1-symbol-description'] = ['Фьючерсы на бензин RBOB']),
        (t.exports['#RTS-symbol-description'] = ['Индекс РТС']),
        (t.exports['#SBER-symbol-description'] = ['Сбербанк']),
        (t.exports['#SPX500-symbol-description'] = ['Индекс S&P 500']),
        (t.exports['#TWTR-symbol-description'] = ['Twitter Inc (Твиттер)']),
        (t.exports['#UK100-symbol-description'] = ['Индекс FTSE 100']),
        (t.exports['#USDBRL-symbol-description'] = [
          'Доллар США / Бразильский реал',
        ]),
        (t.exports['#USDCAD-symbol-description'] = [
          'Доллар США / Канадский доллар',
        ]),
        (t.exports['#USDCHF-symbol-description'] = [
          'Доллар США / Швейцарский франк',
        ]),
        (t.exports['#USDCNY-symbol-description'] = [
          'Доллар США / Китайский юань',
        ]),
        (t.exports['#USDDKK-symbol-description'] = [
          'Доллар США / Датская крона',
        ]),
        (t.exports['#USDHKD-symbol-description'] = [
          'Доллар США / Гонконгский доллар',
        ]),
        (t.exports['#USDIDR-symbol-description'] = [
          'Доллар США / Индонезийская рупия',
        ]),
        (t.exports['#USDINR-symbol-description'] = [
          'Доллар США / Индийская рупия',
        ]),
        (t.exports['#USDJPY-symbol-description'] = [
          'Доллар США / Японская иена',
        ]),
        (t.exports['#USDKRW-symbol-description'] = [
          'Доллар США / Южнокорейская вона',
        ]),
        (t.exports['#USDMXN-symbol-description'] = [
          'Доллар США / Мексиканский песо',
        ]),
        (t.exports['#USDPHP-symbol-description'] = [
          'Доллар США / Филиппинское песо',
        ]),
        (t.exports['#USDRUB-symbol-description'] = [
          'Доллар США / Российский рубль',
        ]),
        (t.exports['#USDRUB_TOM-symbol-description'] = [
          'Доллар США / Российский рубль TOM',
        ]),
        (t.exports['#USDSEK-symbol-description'] = [
          'Доллар США / Шведская крона',
        ]),
        (t.exports['#USDSGD-symbol-description'] = [
          'Доллар США / Сингапурский доллар',
        ]),
        (t.exports['#USDTRY-symbol-description'] = [
          'Доллар США / Турецкая лира',
        ]),
        (t.exports['#VTBR-symbol-description'] = ['Банк ВТБ']),
        (t.exports['#XAGUSD-symbol-description'] = ['Серебро / Доллар США']),
        (t.exports['#XAUUSD-symbol-description'] = ['Золото / Доллар США']),
        (t.exports['#XPDUSD-symbol-description'] = ['CFD на палладий']),
        (t.exports['#XPTUSD-symbol-description'] = ['Платина / Доллар США']),
        (t.exports['#ZS1-symbol-description'] = [
          'Фьючерсы на соевые бобы - ECBT',
        ]),
        (t.exports['#ZW1-symbol-description'] = ['Фьючерсы на пшеницу']),
        (t.exports['#BTCGBP-symbol-description'] = [
          'Биткоин / Британский фунт',
        ]),
        (t.exports['#MICEXINDEXCF-symbol-description'] = ['Индекс МосБиржи']),
        (t.exports['#BTCAUD-symbol-description'] = [
          'Биткоин / Австралийский доллар',
        ]),
        (t.exports['#BTCJPY-symbol-description'] = ['Биткоин / Японская иена']),
        (t.exports['#BTCBRL-symbol-description'] = [
          'Биткоин / Бразильская лира',
        ]),
        (t.exports['#PT10-symbol-description'] = [
          '10-летние облигации Португалии',
        ]),
        (t.exports['#TXSX-symbol-description'] = ['Индекс TSX 60']),
        (t.exports['#VIXC-symbol-description'] = ['Индекс TSX 60 VIX']),
        (t.exports['#USDPLN-symbol-description'] = [
          'Доллар США / Польский злотый',
        ]),
        (t.exports['#EURPLN-symbol-description'] = ['Евро / Польский злотый']),
        (t.exports['#BTCPLN-symbol-description'] = [
          'Биткоин / Польский злотый',
        ]),
        (t.exports['#CAC40-symbol-description'] = ['Индекс CAC 40']),
        (t.exports['#XBTCAD-symbol-description'] = [
          'Биткоин / Канадский доллар',
        ]),
        (t.exports['#ITI2!-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIF2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIF2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIF2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIG2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIG2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIG2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIH2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIH2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIH2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIJ2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIJ2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIJ2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIK2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIK2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIK2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIM2017-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIM2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIM2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIM2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIN2017-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIN2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIN2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIN2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIQ2017-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIQ2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIQ2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIQ2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIU2017-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIU2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIU2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIU2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIV2017-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIV2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIV2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIV2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIX2017-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIX2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIX2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIX2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIZ2017-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIZ2018-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIZ2019-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#ITIZ2020-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#AMEX:GXF-symbol-description'] =
          'Global x FTSE Nordic Region ETF'),
        (t.exports['#ASX:XAF-symbol-description'] = [
          'Индекс S&P/ASX All Australian 50',
        ]),
        (t.exports['#ASX:XAT-symbol-description'] = [
          'Индекс S&P/ASX All Australian 200',
        ]),
        (t.exports['#BIST:XU100-symbol-description'] = ['Индекс BIST 100']),
        (t.exports['#GPW:WIG20-symbol-description'] = ['Индекс WIG20']),
        (t.exports['#INDEX:JKSE-symbol-description'] = [
          'Индекс Jakarta Composite',
        ]),
        (t.exports['#INDEX:KLSE-symbol-description'] = [
          'Индекс Bursa Malaysia KLCI',
        ]),
        (t.exports['#INDEX:NZD-symbol-description'] = ['Индекс NZX 50']),
        (t.exports['#INDEX:STI-symbol-description'] = ['Индекс STI']),
        (t.exports['#INDEX:XLY0-symbol-description'] = [
          'Индекс Shanghai Composite',
        ]),
        (t.exports['#MOEX:MICEXINDEXCF-symbol-description'] = [
          'Индекс МосБиржи',
        ]),
        (t.exports['#NYMEX:KT1!-symbol-description'] = ['Фьючерсы на кофе']),
        (t.exports['#OANDA:NATGASUSD-symbol-description'] = [
          'CFD на природный газ',
        ]),
        (t.exports['#OANDA:USDPLN-symbol-description'] = [
          'Доллар США / Польский злотый',
        ]),
        (t.exports['#TSX:TX60-symbol-description'] = ['Индекс S&P/TSX 60']),
        (t.exports['#TSX:VBU-symbol-description'] = [
          'Совокупный индекс облигаций США, ETF (CAD-hedged) UN',
        ]),
        (t.exports['#TSX:VIXC-symbol-description'] = ['Индекс S&P/TSX 60 VIX']),
        (t.exports['#TVC:CAC40-symbol-description'] = ['Индекс CAC 40']),
        (t.exports['#TVC:ES10-symbol-description'] = [
          '10-летние облигации Испании',
        ]),
        (t.exports['#TVC:EUBUND-symbol-description'] = ['Еврооблигации']),
        (t.exports['#TVC:GB02-symbol-description'] = [
          '2-летние облигации Британии',
        ]),
        (t.exports['#TVC:GB10-symbol-description'] = [
          '10-летние облигации Британии',
        ]),
        (t.exports['#TVC:GOLD-symbol-description'] = [
          'CFD на золото (US$ / OZ)',
        ]),
        (t.exports['#TVC:ID03-symbol-description'] = [
          '3-летние облигации Индонезии',
        ]),
        (t.exports['#TVC:ID10-symbol-description'] = [
          '10-летние облигации Индонезии',
        ]),
        (t.exports['#TVC:PALLADIUM-symbol-description'] = [
          'CFD на палладий (US$ / OZ)',
        ]),
        (t.exports['#TVC:PT10-symbol-description'] = [
          '10-летние облигации Португалии',
        ]),
        (t.exports['#TVC:SILVER-symbol-description'] = [
          'CFD на серебро (US$ / OZ)',
        ]),
        (t.exports['#TVC:RUT-symbol-description'] = ['Индекс Russell 2000']),
        (t.exports['#TSX:TSX-symbol-description'] = [
          'Индекс S&P/TSX Composite',
        ]),
        (t.exports['#OANDA:CH20CHF-symbol-description'] = ['Индекс Swiss 20']),
        (t.exports['#TVC:SHCOMP-symbol-description'] = [
          'Индекс Shanghai Composite',
        ]),
        (t.exports['#NZX:ALLC-symbol-description'] = [
          'Индекс S&P/NZX ALL (Capital Index)',
        ]),
        (t.exports['#AMEX:SHYG-symbol-description'] = [
          'Доходность корпоративных облигаций США 0-5 лет',
        ]),
        (t.exports['#TVC:AU10-symbol-description'] = [
          '10-летние облигации Австралии',
        ]),
        (t.exports['#TVC:CN10-symbol-description'] = [
          '10-летние облигации Китая',
        ]),
        (t.exports['#TVC:KR10-symbol-description'] = [
          '10-летние облигации Кореи',
        ]),
        (t.exports['#NYMEX:RB1!-symbol-description'] = [
          'Фьючерсы на бензин RBOB',
        ]),
        (t.exports['#NYMEX:HO1!-symbol-description'] = [
          'Фьючерсы NY Harbor ULSD',
        ]),
        (t.exports['#NYMEX:AEZ1!-symbol-description'] = ['Фьючерсы на этанол']),
        (t.exports['#OANDA:XCUUSD-symbol-description'] = [
          'CFD на медь (US$ / lb)',
        ]),
        (t.exports['#COMEX:ZA1!-symbol-description'] = ['Фьючерсы на цинк']),
        (t.exports['#CBOT:ZW1!-symbol-description'] = ['Фьючерсы на пшеницу']),
        (t.exports['#NYMEX:KA1!-symbol-description'] = [
          'Фьючерсы на сахар США #11',
        ]),
        (t.exports['#CBOT:QBC1!-symbol-description'] = [
          'Фьючерсы на кукурузу',
        ]),
        (t.exports['#CME:E61!-symbol-description'] = ['Фьючерсы на Евро']),
        (t.exports['#CME:B61!-symbol-description'] = [
          'Фьючерсы на британский фунт',
        ]),
        (t.exports['#CME:QJY1!-symbol-description'] = [
          'Фьючерсы на японскую иену',
        ]),
        (t.exports['#CME:A61!-symbol-description'] = [
          'Фьючерсы на австралийский доллар',
        ]),
        (t.exports['#CME:D61!-symbol-description'] = [
          'Фьючерсы на канадский доллар',
        ]),
        (t.exports['#CME:SP1!-symbol-description'] = ['Фьючерсы на S&P 500']),
        (t.exports['#CME_MINI:NQ1!-symbol-description'] = [
          'Фьючерсы на NASDAQ 100 E-Mini',
        ]),
        (t.exports['#CBOT_MINI:YM1!-symbol-description'] = [
          'Фьючерсы на E-mini Dow Jones ($5)',
        ]),
        (t.exports['#CME:NY1!-symbol-description'] = [
          'Фьючерсы на индекс Nikkei 225',
        ]),
        (t.exports['#EUREX:DY1!-symbol-description'] = ['Индекс DAX']),
        (t.exports['#CME:IF1!-symbol-description'] = [
          'Фьючерсы на индекс IBOVESPA в US$',
        ]),
        (t.exports['#CBOT:TY1!-symbol-description'] = [
          'Фьючерсы на 10-летние среднесрочные казначейские облигации',
        ]),
        (t.exports['#CBOT:FV1!-symbol-description'] = [
          'Фьючерсы на 5-летние среднесрочные казначейские облигации',
        ]),
        (t.exports['#CBOT:ZE1!-symbol-description'] = [
          'Фьючерсы на 3-летние среднесрочные казначейские облигации',
        ]),
        (t.exports['#CBOT:TU1!-symbol-description'] = [
          'Фьючерсы на 2-летние среднесрочные казначейские облигации',
        ]),
        (t.exports['#CBOT:FF1!-symbol-description'] = [
          'Фьючерсы на ставку по федеральным фондам',
        ]),
        (t.exports['#CBOT:US1!-symbol-description'] = [
          'Фьючерсы на долгосрочные казначейские облигации',
        ]),
        (t.exports['#TVC:EXY-symbol-description'] = ['Индекс евро']),
        (t.exports['#TVC:JXY-symbol-description'] = ['Индекс японской иены']),
        (t.exports['#TVC:BXY-symbol-description'] = [
          'Индекс британского фунта',
        ]),
        (t.exports['#TVC:AXY-symbol-description'] = [
          'Индекс австралийского доллара',
        ]),
        (t.exports['#TVC:CXY-symbol-description'] = [
          'Индекс канадского доллара',
        ]),
        (t.exports['#FRED:GDP-symbol-description'] = [
          'Валовой внутренний продукт, 1 десятичный знак',
        ]),
        (t.exports['#FRED:UNRATE-symbol-description'] = [
          'Безработица среди гражданского населения',
        ]),
        (t.exports['#FRED:POP-symbol-description'] = [
          'Общая численность населения: всех возрастов, включая войска, расположенные за границей',
        ]),
        (t.exports['#ETHUSD-symbol-description'] = ['Эфириум / Доллар США']),
        (t.exports['#BMFBOVESPA:IBOV-symbol-description'] = [
          'Индекс IBovespa',
        ]),
        (t.exports['#BMFBOVESPA:IBRA-symbol-description'] = ['Индекс IBrasil']),
        (t.exports['#BMFBOVESPA:IBXL-symbol-description'] = ['Индекс IBRX 50']),
        (t.exports['#COMEX:HG1!-symbol-description'] = ['Фьючерсы на медь']),
        (t.exports['#INDEX:HSCE-symbol-description'] = [
          'Индекс Hang Seng China Enterprises',
        ]),
        (t.exports['#NYMEX:CL1!-symbol-description'] = [
          'Фьючерсы на нефть WTI',
        ]),
        (t.exports['#OTC:IHRMF-symbol-description'] = 'Ishares MSCI Japan SHS'),
        (t.exports['#TVC:DAX-symbol-description'] = ['Индекс DAX']),
        (t.exports['#TVC:DE10-symbol-description'] = [
          '10-летние облигации Германии',
        ]),
        (t.exports['#TVC:DJI-symbol-description'] = [
          'Промышленный индекс Доу — Джонса',
        ]),
        (t.exports['#TVC:DXY-symbol-description'] = ['Индекс доллара США']),
        (t.exports['#TVC:FR10-symbol-description'] = [
          '10-летние облигации Франции',
        ]),
        (t.exports['#TVC:HSI-symbol-description'] = ['Индекс Hang Seng']),
        (t.exports['#TVC:IBEX35-symbol-description'] = ['Индекс IBEX 35']),
        (t.exports['#FX:AUS200-symbol-description'] = ['Индекс S&P/ASX']),
        (t.exports['#AMEX:SHY-symbol-description'] = [
          'Казначейские облигации США 1-3 года (ETF)',
        ]),
        (t.exports['#ASX:XJO-symbol-description'] = ['Индекс S&P/ASX 200']),
        (t.exports['#BSE:SENSEX-symbol-description'] = [
          'Индекс S&P BSE SENSEX',
        ]),
        (t.exports['#INDEX:MIB-symbol-description'] = ['Индекс MIB']),
        (t.exports['#INDEX:MOY0-symbol-description'] = [
          'Индекс Euro Stoxx 50',
        ]),
        (t.exports['#MOEX:RTSI-symbol-description'] = ['Индекс РТС']),
        (t.exports['#NSE:NIFTY-symbol-description'] = ['Индекс Nifty 50']),
        (t.exports['#NYMEX:NG1!-symbol-description'] = [
          'Фьючерсы на природный газ',
        ]),
        (t.exports['#NYMEX:ZC1!-symbol-description'] = [
          'Фьючерсы на кукурузу',
        ]),
        (t.exports['#TVC:IN10-symbol-description'] = [
          '10-летние облигации Индии',
        ]),
        (t.exports['#TVC:IT10-symbol-description'] = [
          '10-летние облигации Италии',
        ]),
        (t.exports['#TVC:JP10-symbol-description'] = [
          '10-летние облигации Японии',
        ]),
        (t.exports['#TVC:NDX-symbol-description'] = ['Индекс US 100']),
        (t.exports['#TVC:NI225-symbol-description'] = ['Индекс Nikkei 225']),
        (t.exports['#TVC:SPX-symbol-description'] = ['Индекс S&P 500']),
        (t.exports['#TVC:SX5E-symbol-description'] = ['Индекс Euro Stoxx 50']),
        (t.exports['#TVC:TR10-symbol-description'] = [
          '10-летние облигации Турции',
        ]),
        (t.exports['#TVC:UKOIL-symbol-description'] = [
          'CFD на нефть марки Brent',
        ]),
        (t.exports['#TVC:UKX-symbol-description'] = ['Индекс UK 100']),
        (t.exports['#TVC:US02-symbol-description'] = [
          '2-летние облигации США',
        ]),
        (t.exports['#TVC:US05-symbol-description'] = [
          '5-летние облигации США',
        ]),
        (t.exports['#TVC:US10-symbol-description'] = [
          '10-летние облигации США',
        ]),
        (t.exports['#TVC:USOIL-symbol-description'] = [
          'CFD на нефть марки WTI',
        ]),
        (t.exports['#NYMEX:ITI1!-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#NASDAQ:SHY-symbol-description'] = [
          'Казначейские облигации США 1-3 года (ETF)',
        ]),
        (t.exports['#AMEX:ALD-symbol-description'] =
          'WisdomTree Asia Local Debt ETF'),
        (t.exports['#NASDAQ:AMD-symbol-description'] = [
          'AMD (Advanced Micro Devices Inc.)',
        ]),
        (t.exports['#NYSE:BABA-symbol-description'] =
          'Alibaba Group Holdings Ltd.'),
        (t.exports['#ICEEUR:CB-symbol-description'] = ['Нефть Brent']),
        (t.exports['#ICEEUR:CB1!-symbol-description'] = ['Нефть марки Brent']),
        (t.exports['#ICEUSA:CC-symbol-description'] = ['Какао']),
        (t.exports['#NYMEX:CL-symbol-description'] = ['Нефть марки WTI']),
        (t.exports['#ICEUSA:CT-symbol-description'] = ['Хлопок №2']),
        (t.exports['#NASDAQ:CTRV-symbol-description'] =
          'ContraVir Pharmaceuticals Inc'),
        (t.exports['#CME:DL-symbol-description'] = ['Молоко класса III']),
        (t.exports['#NYSE:F-symbol-description'] = [
          'Ford Motor Company (Форд)',
        ]),
        (t.exports['#MOEX:GAZP-symbol-description'] = ['Газпром']),
        (t.exports['#COMEX:GC-symbol-description'] = ['Золото']),
        (t.exports['#CME:GF-symbol-description'] = ['Крупный рогатый скот']),
        (t.exports['#CME:HE-symbol-description'] = ['Свинина']),
        (t.exports['#NASDAQ:IEF-symbol-description'] = [
          'Казначейские облигации США 7-10 лет (ETF)',
        ]),
        (t.exports['#NASDAQ:IEI-symbol-description'] = [
          'Казначейские облигации США 3-7 лет (ETF)',
        ]),
        (t.exports['#NYMEX:KA1-symbol-description'] = [
          'Фьючерсы на сахар США №11',
        ]),
        (t.exports['#ICEUSA:KC-symbol-description'] = ['Кофе']),
        (t.exports['#NYMEX:KG1-symbol-description'] = ['Фьючерсы на хлопок']),
        (t.exports['#FWB:KT1-symbol-description'] = 'Key Tronic Corр.'),
        (t.exports['#CME:LE-symbol-description'] = ['Живой скот']),
        (t.exports['#ICEEUR:LO-symbol-description'] = ['Мазут (ICE)']),
        (t.exports['#CME:LS-symbol-description'] = ['Лес (пиломатериалы)']),
        (t.exports['#MOEX:MGNT-symbol-description'] = ['Магнит']),
        (t.exports['#LSIN:MNOD-symbol-description'] = [
          'ОАО ГМК Норильский никель (РЕПО)',
        ]),
        (t.exports['#NYMEX:NG-symbol-description'] = ['Природный газ']),
        (t.exports['#ICEUSA:OJ-symbol-description'] = ['Апельсиновый сок']),
        (t.exports['#NYMEX:PA-symbol-description'] = ['Палладий']),
        (t.exports['#NYSE:PBR-symbol-description'] = [
          'Petroleo Brasileiro Petrobras SA',
        ]),
        (t.exports['#NYMEX:PL-symbol-description'] = ['Платина']),
        (t.exports['#COMEX_MINI:QC-symbol-description'] = ['Медь (E-мини)']),
        (t.exports['#NYMEX:RB-symbol-description'] = ['Бензин RBOB']),
        (t.exports['#NYMEX:RB1-symbol-description'] = [
          'Фьючерсы на бензин RBOB',
        ]),
        (t.exports['#MOEX:SBER-symbol-description'] = ['Сбербанк']),
        (t.exports['#AMEX:SCHO-symbol-description'] = [
          'Краткосрочные облигации США SCHWAB ETF',
        ]),
        (t.exports['#COMEX:SI-symbol-description'] = ['Серебро']),
        (t.exports['#NASDAQ:TLT-symbol-description'] = [
          'Казначейские облигации США 20+ лет (ETF)',
        ]),
        (t.exports['#TVC:VIX-symbol-description'] = [
          'Индекс волатильности S&P 500',
        ]),
        (t.exports['#MOEX:VTBR-symbol-description'] = ['Банк ВТБ']),
        (t.exports['#COMEX:ZA-symbol-description'] = ['Цинк']),
        (t.exports['#CBOT:ZC-symbol-description'] = ['Кукуруза']),
        (t.exports['#CBOT:ZK-symbol-description'] = ['Фьючерсы на этанол']),
        (t.exports['#CBOT:ZL-symbol-description'] = ['Соевое масло']),
        (t.exports['#CBOT:ZO-symbol-description'] = ['Овёс']),
        (t.exports['#CBOT:ZR-symbol-description'] = ['Грубый рис']),
        (t.exports['#CBOT:ZS-symbol-description'] = ['Соевые бобы']),
        (t.exports['#CBOT:ZS1-symbol-description'] = [
          'Фьючерсы на соевые бобы',
        ]),
        (t.exports['#CBOT:ZW-symbol-description'] = ['Пшеница']),
        (t.exports['#CBOT:ZW1-symbol-description'] = ['Фьючерсы на пшеницу']),
        (t.exports['#NASDAQ:ITI-symbol-description'] = 'Iteris Inc'),
        (t.exports['#NYMEX:ITI2!-symbol-description'] = [
          'Фьючерсы на железную руду',
        ]),
        (t.exports['#CADUSD-symbol-description'] = [
          'Канадский доллар / Доллар США',
        ]),
        (t.exports['#CHFUSD-symbol-description'] = [
          'Швейцарский франк / Доллар США',
        ]),
        (t.exports['#GPW:ACG-symbol-description'] = 'Acautogaz'),
        (t.exports['#JPYUSD-symbol-description'] = [
          'Японская иена / Доллар США',
        ]),
        (t.exports['#USDAUD-symbol-description'] = [
          'Доллар США / Австралийский доллар',
        ]),
        (t.exports['#USDEUR-symbol-description'] = ['Доллар США / Евро']),
        (t.exports['#USDGBP-symbol-description'] = [
          'Доллар США / Британский фунт',
        ]),
        (t.exports['#USDNZD-symbol-description'] = [
          'Доллар США / Новозеландский доллар',
        ]),
        (t.exports['#UKOIL-symbol-description'] = ['CFD на нефть Brent']),
        (t.exports['#USOIL-symbol-description'] = ['CFD на нефть WTI']),
        (t.exports['#US30-symbol-description'] = [
          'Промышленный индекс Доу — Джонса',
        ]),
        (t.exports['#BCHUSD-symbol-description'] = [
          'Bitcoin Cash / Доллар США',
        ]),
        (t.exports['#ETCUSD-symbol-description'] = [
          'Эфириум Классик / Доллар США',
        ]),
        (t.exports['#GOOG-symbol-description'] =
          'Alphabet Inc (Google) Class C'),
        (t.exports['#LTCUSD-symbol-description'] = ['Лайткоин / Доллар США']),
        (t.exports['#XRPUSD-symbol-description'] = ['XRP / Доллар США']),
        (t.exports['#SP:SPX-symbol-description'] = ['Индекс S&P 500']),
        (t.exports['#ETCBTC-symbol-description'] = [
          'Эфириум Классик / Биткоин',
        ]),
        (t.exports['#ETHBTC-symbol-description'] = ['Эфириум / Биткоин']),
        (t.exports['#XRPBTC-symbol-description'] = ['XRP / Биткоин']),
        (t.exports['#TVC:US30-symbol-description'] = [
          '30-летние облигации США',
        ]),
        (t.exports['#COMEX:SI1!-symbol-description'] = ['Фьючерсы на серебро']),
        (t.exports['#BTGUSD-symbol-description'] = [
          'Bitcoin Gold / Доллар США',
        ]),
        (t.exports['#IOTUSD-symbol-description'] = ['IOTA / Доллар США']),
        (t.exports['#CME:BTC1!-symbol-description'] = [
          'Фьючерсы на биткоин (CME)',
        ]),
        (t.exports['#COMEX:GC1!-symbol-description'] = ['Фьючерсы на золото']),
        (t.exports['#CORNUSD-symbol-description'] = ['CFD на кукурузу']),
        (t.exports['#COTUSD-symbol-description'] = ['CFD на хлопок']),
        (t.exports['#DJ:DJA-symbol-description'] = [
          'Индекс Dow Jones Composite Average',
        ]),
        (t.exports['#DJ:DJI-symbol-description'] = [
          'Промышленный индекс Доу — Джонса',
        ]),
        (t.exports['#ETHEUR-symbol-description'] = ['Эфириум / Евро']),
        (t.exports['#ETHGBP-symbol-description'] = [
          'Эфириум / Британский фунт',
        ]),
        (t.exports['#ETHJPY-symbol-description'] = ['Эфириум / Японская иена']),
        (t.exports['#EURNOK-symbol-description'] = ['Евро / Норвежская крона']),
        (t.exports['#GBPPLN-symbol-description'] = [
          'Британский фунт / Польский злотый',
        ]),
        (t.exports['#MOEX:BR1!-symbol-description'] = [
          'Фьючерсы на нефть Brent',
        ]),
        (t.exports['#NYMEX:KG1!-symbol-description'] = ['Фьючерсы на хлопок']),
        (t.exports['#NYMEX:PL1!-symbol-description'] = ['Фьючерсы на платину']),
        (t.exports['#SOYBNUSD-symbol-description'] = ['CFD на соевые бобы']),
        (t.exports['#SUGARUSD-symbol-description'] = ['CFD на сахар']),
        (t.exports['#TVC:IXIC-symbol-description'] = ['Индекс US Composite']),
        (t.exports['#TVC:RU-symbol-description'] = ['Индекс Russell 1000']),
        (t.exports['#USDZAR-symbol-description'] = [
          'Доллар США / Южноафриканский ранд',
        ]),
        (t.exports['#WHEATUSD-symbol-description'] = ['CFD на пшеницу']),
        (t.exports['#XRPEUR-symbol-description'] = ['XRP / Евро']),
        (t.exports['#CBOT:S1!-symbol-description'] = [
          'Фьючерсы на соевые бобы',
        ]),
        (t.exports['#SP:MID-symbol-description'] = ['Индекс S&P 400']),
        (t.exports['#TSX:XCUUSD-symbol-description'] = ['CFD на медь']),
        (t.exports['#TVC:NYA-symbol-description'] = ['Индекс NYSE Composite']),
        (t.exports['#TVC:PLATINUM-symbol-description'] = [
          'CFD на платину (US$ / OZ)',
        ]),
        (t.exports['#TVC:SSMI-symbol-description'] = ['Индекс Swiss Market']),
        (t.exports['#TVC:SXY-symbol-description'] = [
          'Индекс швейцарского франка',
        ]),
        (t.exports['#TVC:RUI-symbol-description'] = ['Индекс Russell 1000']),
        (t.exports['#MOEX:RI1!-symbol-description'] = [
          'Фьючерсы на индекс РТС',
        ]),
        (t.exports['#MOEX:MX1!-symbol-description'] = [
          'Фьючерсы на индекс МосБиржи',
        ]),
        (t.exports['#CBOE:BG1!-symbol-description'] = [
          'Фьючерсы на биткоин (CBOE)',
        ]),
        (t.exports['#TVC:MY10-symbol-description'] = [
          '10-летние облигации Малайзии',
        ]),
        (t.exports['#CME:S61!-symbol-description'] = [
          'Фьючерсы на швейцарский франк',
        ]),
        (t.exports['#TVC:DEU30-symbol-description'] = ['Индекс DAX']),
        (t.exports['#BCHEUR-symbol-description'] = ['Bitcoin Cash / Евро']),
        (t.exports['#TVC:ZXY-symbol-description'] = [
          'Индекс новозеландского доллара',
        ]),
        (t.exports['#MIL:FTSEMIB-symbol-description'] = ['Индекс FTSE MIB']),
        (t.exports['#XETR:DAX-symbol-description'] = ['Индекс DAX']),
        (t.exports['#MOEX:IMOEX-symbol-description'] = ['Индекс МосБиржи']),
        (t.exports['#FX:US30-symbol-description'] = [
          'Промышленный индекс Доу — Джонса',
        ]),
        (t.exports['#MOEX:RUAL-symbol-description'] =
          'United Company RUSAL PLC'),
        (t.exports['#MOEX:MX2!-symbol-description'] = [
          'Фьючерсы на индекс МосБиржи',
        ]),
        (t.exports['#NEOUSD-symbol-description'] = ['NEO / Доллар США']),
        (t.exports['#XMRUSD-symbol-description'] = ['Монеро / Доллар США']),
        (t.exports['#ZECUSD-symbol-description'] = ['Zcash / Доллар США']),
        (t.exports['#TVC:CAC-symbol-description'] = ['Индекс CAC 40']),
        (t.exports['#NASDAQ:ZS-symbol-description'] = 'Zscaler Inc'),
        (t.exports['#TVC:GB10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Британии',
        ]),
        (t.exports['#TVC:AU10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Австралии',
        ]),
        (t.exports['#TVC:CN10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Китая',
        ]),
        (t.exports['#TVC:DE10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Германии',
        ]),
        (t.exports['#TVC:ES10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Испании',
        ]),
        (t.exports['#TVC:FR10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Франции',
        ]),
        (t.exports['#TVC:IN10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Индии',
        ]),
        (t.exports['#TVC:IT10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Италии',
        ]),
        (t.exports['#TVC:JP10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Японии',
        ]),
        (t.exports['#TVC:KR10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Кореи',
        ]),
        (t.exports['#TVC:MY10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Малайзии',
        ]),
        (t.exports['#TVC:PT10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Португалии',
        ]),
        (t.exports['#TVC:TR10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Турции',
        ]),
        (t.exports['#TVC:US02Y-symbol-description'] = [
          'Доходность 2-летних облигаций США',
        ]),
        (t.exports['#TVC:US05Y-symbol-description'] = [
          'Доходность 5-летних облигаций США',
        ]),
        (t.exports['#TVC:US10Y-symbol-description'] = [
          'Доходность 10-летних облигаций США',
        ]),
        (t.exports['#INDEX:TWII-symbol-description'] = [
          'Тайваньский взвешенный индекс',
        ]),
        (t.exports['#CME:J61!-symbol-description'] = [
          'Фьючерсы на японскую иену',
        ]),
        (t.exports['#CME_MINI:J71!-symbol-description'] = [
          'Фьючерсы на японскую иену (E-mini)',
        ]),
        (t.exports['#CME_MINI:WM1!-symbol-description'] = [
          'Фьючерсы на японскую иену / доллар США (E-micro)',
        ]),
        (t.exports['#CME:M61!-symbol-description'] = [
          'Фьючерсы на мексиканский песо',
        ]),
        (t.exports['#CME:T61!-symbol-description'] = [
          'Фьючерсы на южноафриканский ранд',
        ]),
        (t.exports['#CME:SK1!-symbol-description'] = [
          'Фьючерсы на шведскую крону',
        ]),
        (t.exports['#CME:QT1!-symbol-description'] = [
          'Фьючерсы на китайский юань Жэньминьби / доллар США',
        ]),
        (t.exports['#COMEX:AUP1!-symbol-description'] = [
          'Фьючерсы на Aluminum MW U.S. Transaction Premium Platts (25MT)',
        ]),
        (t.exports['#CME:L61!-symbol-description'] = [
          'Фьючерсы на бразильский реал',
        ]),
        (t.exports['#CME:WP1!-symbol-description'] = [
          'Фьючерсы на польский злотый',
        ]),
        (t.exports['#CME:N61!-symbol-description'] = [
          'Фьючерсы на новозеландский доллар',
        ]),
        (t.exports['#CME_MINI:MG1!-symbol-description'] = [
          'Фьючерсы на австралийский доллар / доллар США (E-micro)',
        ]),
        (t.exports['#CME_MINI:WN1!-symbol-description'] = [
          'Фьючерсы на швейцарский франк / доллар США (E-micro)',
        ]),
        (t.exports['#CME_MINI:MF1!-symbol-description'] = [
          'Фьючерсы на евро / доллар США (E-micro)',
        ]),
        (t.exports['#CME_MINI:E71!-symbol-description'] = [
          'Фьючерсы на евро (E-mini)',
        ]),
        (t.exports['#CBOT:ZK1!-symbol-description'] = ['Фьючерсы на этанол']),
        (t.exports['#CME_MINI:MB1!-symbol-description'] = [
          'Фьючерсы на британский фунт / доллар США (E-micro)',
        ]),
        (t.exports['#NYMEX_MINI:QU1!-symbol-description'] = [
          'Фьючерсы на бензин (E-mini)',
        ]),
        (t.exports['#NYMEX_MINI:QX1!-symbol-description'] = [
          'Фьючерсы на мазут (E-mini)',
        ]),
        (t.exports['#COMEX_MINI:QC1!-symbol-description'] = [
          'Фьючерсы на медь (E-mini)',
        ]),
        (t.exports['#NYMEX_MINI:QG1!-symbol-description'] = [
          'Фьючерсы на природный газ (E-mini)',
        ]),
        (t.exports['#CME:E41!-symbol-description'] = [
          'Фьючерсы на доллар США / турецкую лиру',
        ]),
        (t.exports['#COMEX_MINI:QI1!-symbol-description'] = [
          'Фьючерсы на серебро (мини)',
        ]),
        (t.exports['#CME:DL1!-symbol-description'] = [
          'Фьючерсы на молоко класса III',
        ]),
        (t.exports['#NYMEX:UX1!-symbol-description'] = ['Фьючерсы на уран']),
        (t.exports['#CBOT:BO1!-symbol-description'] = [
          'Фьючерсы на соевое масло',
        ]),
        (t.exports['#CME:HE1!-symbol-description'] = ['Фьючерсы на свинину']),
        (t.exports['#NYMEX:IAC1!-symbol-description'] = [
          'Фьючерсы на Newcastle Coal',
        ]),
        (t.exports['#NYMEX_MINI:QM1!-symbol-description'] = [
          'Фьючерсы на E-mini Light Crude Oil',
        ]),
        (t.exports['#NYMEX:JMJ1!-symbol-description'] = [
          'Фьючерсы на Mini Brent Financial',
        ]),
        (t.exports['#COMEX:AEP1!-symbol-description'] = [
          'Фьючерсы на Aluminium European Premium',
        ]),
        (t.exports['#CBOT:ZQ1!-symbol-description'] = [
          'Фьючерсы на 30-дневную процентную ставку по федеральным фондам',
        ]),
        (t.exports['#CME:LE1!-symbol-description'] = [
          'Фьючерсы на живой скот',
        ]),
        (t.exports['#CME:UP1!-symbol-description'] = [
          'Фьючерсы на швейцарский франк / японскую иену',
        ]),
        (t.exports['#CBOT:ZN1!-symbol-description'] = [
          'Фьючерсы на 10-летние среднесрочные казначейские облигации',
        ]),
        (t.exports['#CBOT:ZB1!-symbol-description'] = [
          'Фьючерсы на долгосрочные казначейские облигации',
        ]),
        (t.exports['#CME:GF1!-symbol-description'] = [
          'Фьючерсы на крупный рогатый скот',
        ]),
        (t.exports['#CBOT:UD1!-symbol-description'] = [
          'Фьючерсы на Ultra T-Bond',
        ]),
        (t.exports['#CME:I91!-symbol-description'] = [
          'CME фьючерсы на недвижимость — Washington DC',
        ]),
        (t.exports['#CBOT:ZO1!-symbol-description'] = ['Фьючерсы на овёс']),
        (t.exports['#CBOT:ZM1!-symbol-description'] = [
          'Фьючерсы на соевую муку',
        ]),
        (t.exports['#CBOT_MINI:XN1!-symbol-description'] = [
          'Фьючерсы на кукурузу (мини)',
        ]),
        (t.exports['#CBOT:ZC1!-symbol-description'] = ['Фьючерсы на кукурузу']),
        (t.exports['#CME:LS1!-symbol-description'] = [
          'Фьючерсы на лесоматериалы',
        ]),
        (t.exports['#CBOT_MINI:XW1!-symbol-description'] = [
          'Фьючерсы на пшеницу (мини)',
        ]),
        (t.exports['#CBOT_MINI:XK1!-symbol-description'] = [
          'Фьючерсы на соевые бобы (мини)',
        ]),
        (t.exports['#CBOT:ZS1!-symbol-description'] = [
          'Фьючерсы на соевые бобы',
        ]),
        (t.exports['#NYMEX:PA1!-symbol-description'] = [
          'Фьючерсы на палладий',
        ]),
        (t.exports['#CME:FTU1!-symbol-description'] = [
          'Фьючерсы на E-mini FTSE 100 Index USD',
        ]),
        (t.exports['#CBOT:ZR1!-symbol-description'] = ['Фьючерсы на рис']),
        (t.exports['#COMEX_MINI:GR1!-symbol-description'] = [
          'Фьючерсы на золото (E-micro)',
        ]),
        (t.exports['#COMEX_MINI:QO1!-symbol-description'] = [
          'Фьючерсы на золото (мини)',
        ]),
        (t.exports['#CME_MINI:RL1!-symbol-description'] = [
          'Фьючерсы на E-mini Russell 1000',
        ]),
        (t.exports['#CME_MINI:EW1!-symbol-description'] = [
          'Фьючерсы на S&P 400 Midcap E-mini',
        ]),
        (t.exports['#COMEX:LD1!-symbol-description'] = ['Фьючерсы на свинец']),
        (t.exports['#CME_MINI:ES1!-symbol-description'] = [
          'Фьючерсы на E-mini S&P 500',
        ]),
        (t.exports['#TVC:SA40-symbol-description'] = [
          'Индекс South Africa Top 40',
        ]),
        (t.exports['#BMV:ME-symbol-description'] = ['Индекс IPC Mexico']),
        (t.exports['#BCBA:IMV-symbol-description'] = ['Индекс MERVAL']),
        (t.exports['#HSI:HSI-symbol-description'] = ['Индекс Hang Seng']),
        (t.exports['#BVL:SPBLPGPT-symbol-description'] = [
          'Индекс S&P / BVL Peru General (PEN)',
        ]),
        (t.exports['#EGX:EGX30-symbol-description'] = [
          'Индекс EGX 30 Price Return',
        ]),
        (t.exports['#BVC:IGBC-symbol-description'] = ['Индекс IGBC']),
        (t.exports['#TWSE:TAIEX-symbol-description'] = [
          'Индекс Taiwan Capitalization Weighted Stock',
        ]),
        (t.exports['#QSE:GNRI-symbol-description'] = ['Индекс QE']),
        (t.exports['#BME:IBC-symbol-description'] = ['Индекс IBEX 35']),
        (t.exports['#NZX:NZ50G-symbol-description'] = [
          'Индекс S&P / NZX 50 Gross',
        ]),
        (t.exports['#SIX:SMI-symbol-description'] = ['Индекс Swiss Market']),
        (t.exports['#SZSE:399001-symbol-description'] = [
          'Индекс SZSE Component',
        ]),
        (t.exports['#TADAWUL:TASI-symbol-description'] = [
          'Индекс Tadawul All Shares',
        ]),
        (t.exports['#IDX:COMPOSITE-symbol-description'] = [
          'Индекс IDX Composite',
        ]),
        (t.exports['#EURONEXT:PX1-symbol-description'] = ['Индекс CAC 40']),
        (t.exports['#OMXHEX:OMXH25-symbol-description'] = [
          'Индекс OMX Helsinki 25',
        ]),
        (t.exports['#EURONEXT:BEL20-symbol-description'] = ['Индекс BEL 20']),
        (t.exports['#TVC:STI-symbol-description'] = ['Индекс Straits Times']),
        (t.exports['#DFM:DFMGI-symbol-description'] = ['Индекс DFM']),
        (t.exports['#TVC:KOSPI-symbol-description'] = [
          'Индекс Korea Composite Stock Price',
        ]),
        (t.exports['#FTSEMYX:FBMKLCI-symbol-description'] = [
          'Индекс FTSE Bursa Malaysia KLCI',
        ]),
        (t.exports['#TASE:TA35-symbol-description'] = ['Индекс TA-35']),
        (t.exports['#OMXSTO:OMXS30-symbol-description'] = [
          'Индекс OMX Stockholm 30',
        ]),
        (t.exports['#OMXICE:OMXI8-symbol-description'] = [
          'Индекс OMX Iceland 8',
        ]),
        (t.exports['#NSENG:NSE30-symbol-description'] = ['Индекс NSE 30']),
        (t.exports['#BAHRAIN:BSEX-symbol-description'] = [
          'Индекс Bahrain All Share',
        ]),
        (t.exports['#OMXTSE:OMXTGI-symbol-description'] = [
          'Индекс OMX Tallinn GI',
        ]),
        (t.exports['#OMXCOP:OMXC25-symbol-description'] = [
          'Индекс OMX Copenhagen 25',
        ]),
        (t.exports['#OMXRSE:OMXRGI-symbol-description'] = [
          'Индекс OMX Riga GI',
        ]),
        (t.exports['#BELEX:BELEX15-symbol-description'] = ['Индекс BELEX 15']),
        (t.exports['#OMXVSE:OMXVGI-symbol-description'] = [
          'Индекс OMX Vilnius GI',
        ]),
        (t.exports['#EURONEXT:AEX-symbol-description'] = ['Индекс AEX']),
        (t.exports['#CBOE:VIX-symbol-description'] = [
          'Индекс волатильности S&P 500',
        ]),
        (t.exports['#NASDAQ:XAU-symbol-description'] = [
          'Индекс PHLX Gold and Silver Sector',
        ]),
        (t.exports['#DJ:DJUSCL-symbol-description'] = [
          'Индекс Dow Jones U.S. Coal',
        ]),
        (t.exports['#DJ:DJCIKC-symbol-description'] = [
          'Индекс Dow Jones Commodity Coffee',
        ]),
        (t.exports['#DJ:DJCIEN-symbol-description'] = [
          'Индекс Dow Jones Commodity Energy',
        ]),
        (t.exports['#NASDAQ:OSX-symbol-description'] = [
          'Индекс PHLX Oil Service Sector',
        ]),
        (t.exports['#DJ:DJCISB-symbol-description'] = [
          'Индекс Dow Jones Commodity Sugar',
        ]),
        (t.exports['#DJ:DJCICC-symbol-description'] = [
          'Индекс Dow Jones Commodity Cocoa',
        ]),
        (t.exports['#DJ:DJCIGR-symbol-description'] = [
          'Индекс Dow Jones Commodity Grains',
        ]),
        (t.exports['#DJ:DJCIAGC-symbol-description'] = [
          'Индекс Dow Jones Commodity Agriculture Capped Component',
        ]),
        (t.exports['#DJ:DJCISI-symbol-description'] = [
          'Индекс Dow Jones Commodity Silver',
        ]),
        (t.exports['#DJ:DJCIIK-symbol-description'] = [
          'Индекс Dow Jones Commodity Nickel',
        ]),
        (t.exports['#NASDAQ:HGX-symbol-description'] = [
          'Индекс PHLX Housing Sector',
        ]),
        (t.exports['#DJ:DJCIGC-symbol-description'] = [
          'Индекс Dow Jones Commodity Gold',
        ]),
        (t.exports['#SP:SPGSCI-symbol-description'] = [
          'Индекс S&P Goldman Sachs Commodity',
        ]),
        (t.exports['#NASDAQ:UTY-symbol-description'] = [
          'Индекс PHLX Utility Sector',
        ]),
        (t.exports['#DJ:DJU-symbol-description'] = [
          'Индекс Dow Jones Utility Average',
        ]),
        (t.exports['#SP:SVX-symbol-description'] = ['Индекс S&P 500 Value']),
        (t.exports['#SP:OEX-symbol-description'] = ['Индекс S&P 100']),
        (t.exports['#CBOE:OEX-symbol-description'] = ['Индекс S&P 100']),
        (t.exports['#NASDAQ:SOX-symbol-description'] = [
          'Индекс Philadelphia Semiconductor',
        ]),
        (t.exports['#RUSSELL:RUI-symbol-description'] = [
          'Индекс Russell 1000',
        ]),
        (t.exports['#RUSSELL:RUA-symbol-description'] = [
          'Индекс Russell 3000',
        ]),
        (t.exports['#RUSSELL:RUT-symbol-description'] = [
          'Индекс Russell 2000',
        ]),
        (t.exports['#NYSE:XMI-symbol-description'] = [
          'Индекс NYSE ARCA Major Market',
        ]),
        (t.exports['#NYSE:XAX-symbol-description'] = ['Индекс AMEX Composite']),
        (t.exports['#NASDAQ:NDX-symbol-description'] = ['Индекс Nasdaq 100']),
        (t.exports['#NASDAQ:IXIC-symbol-description'] = [
          'Индекс Nasdaq Composite',
        ]),
        (t.exports['#DJ:DJT-symbol-description'] = [
          'Индекс Dow Jones Transportation Average',
        ]),
        (t.exports['#NYSE:NYA-symbol-description'] = ['Индекс NYSE Composite']),
        (t.exports['#NYMEX:CJ1!-symbol-description'] = ['Фьючерсы на какао']),
        (t.exports['#USDILS-symbol-description'] = [
          'Доллар США / Израильский шекель',
        ]),
        (t.exports['#TSXV:F-symbol-description'] = 'Fiore Gold Inc'),
        (t.exports['#SIX:F-symbol-description'] = [
          'Ford Motor Company (Форд)',
        ]),
        (t.exports['#BMV:F-symbol-description'] = [
          'Ford Motor Company (Форд)',
        ]),
        (t.exports['#TWII-symbol-description'] = [
          'Тайваньский взвешенный индекс',
        ]),
        (t.exports['#TVC:PL10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Польши',
        ]),
        (t.exports['#TVC:PL05Y-symbol-description'] = [
          'Доходность 5-летних облигаций Польши',
        ]),
        (t.exports['#SET:GC-symbol-description'] =
          'Global Connections Public Company'),
        (t.exports['#TSX:GC-symbol-description'] =
          'Great Canadian Gaming Corporation'),
        (t.exports['#TVC:FTMIB-symbol-description'] = ['Индекс MIB']),
        (t.exports['#OANDA:SPX500USD-symbol-description'] = ['Индекс S&P 500']),
        (t.exports['#BMV:CT-symbol-description'] = 'China SX20 RT'),
        (t.exports['#TSXV:CT-symbol-description'] =
          'Centenera Mining Corporation'),
        (t.exports['#BYBIT:ETHUSD-symbol-description'] = [
          'ETHUSD Бессрочный контракт',
        ]),
        (t.exports['#BYBIT:XRPUSD-symbol-description'] = [
          'XRPUSD Бессрочный контракт',
        ]),
        (t.exports['#BYBIT:BTCUSD-symbol-description'] = [
          'BTCUSD Бессрочный контракт',
        ]),
        (t.exports['#BITMEX:ETHUSD-symbol-description'] = [
          'ETHUSD Бессрочный контракт',
        ]),
        (t.exports['#DERIBIT:BTCUSD-symbol-description'] = [
          'BTCUSD Бессрочный контракт',
        ]),
        (t.exports['#DERIBIT:ETHUSD-symbol-description'] = [
          'ETHUSD Бессрочный контракт',
        ]),
        (t.exports['#USDHUF-symbol-description'] = [
          'Доллар США / Венгерский форинт',
        ]),
        (t.exports['#USDTHB-symbol-description'] = [
          'Доллар США / Тайский бат',
        ]),
        (t.exports['#FOREXCOM:US2000-symbol-description'] =
          'US Small Cap 2000'),
        (t.exports['#TSXV:PBR-symbol-description'] = 'Para Resources Inc'),
        (t.exports['#NYSE:SI-symbol-description'] =
          'Silvergate Capital Corporation'),
        (t.exports['#NASDAQ:LE-symbol-description'] = "Lands' End Inc"),
        (t.exports['#CME:CB1!-symbol-description'] =
          'Butter Futures-Cash (Continuous: Current contract in front)'),
        (t.exports['#LSE:SCHO-symbol-description'] =
          'Scholium Group Plc Ord 1P'),
        (t.exports['#NEO:HE-symbol-description'] =
          'Hanwei Energy Services Corp.'),
        (t.exports['#NYSE:HE-symbol-description'] =
          'Hawaiian Electric Industries'),
        (t.exports['#OMXCOP:SCHO-symbol-description'] = 'Schouw & Co A/S'),
        (t.exports['#TSX:HE-symbol-description'] =
          'Hanwei Energy Services Corp.'),
        (t.exports['#BSE:ITI-symbol-description'] = 'ITI Ltd'),
        (t.exports['#NSE:ITI-symbol-description'] =
          'Indian Telephone Industries Limited'),
        (t.exports['#TSX:LS-symbol-description'] =
          'Middlefield Healthcare & Life Sciences Dividend Fund'),
        (t.exports['#BITMEX:XBT-symbol-description'] = [
          'Биткоин / Индекс доллара США',
        ]),
        (t.exports['#CME_MINI:RTY1!-symbol-description'] = [
          'Фьючерсы на Индекс E-Mini Russell 2000',
        ]),
        (t.exports['#CRYPTOCAP:TOTAL-symbol-description'] = [
          'Общая рыночная капитализация криптовалют, $',
        ]),
        (t.exports['#ICEUS:DX1!-symbol-description'] = [
          'Фьючерсы на Индекс доллара США',
        ]),
        (t.exports['#NYMEX:TT1!-symbol-description'] = ['Фьючерсы на хлопок']),
        (t.exports['#PHEMEX:BTCUSD-symbol-description'] = [
          'BTC ETHUSD Бессрочный контракт',
        ]),
        (t.exports['#PHEMEX:ETHUSD-symbol-description'] = [
          'ETH Бессрочный контракт',
        ]),
        (t.exports['#PHEMEX:XRPUSD-symbol-description'] = [
          'XRP Бессрочный контракт',
        ]),
        (t.exports['#PHEMEX:LTCUSD-symbol-description'] = [
          'LTC Бессрочный контракт',
        ]),
        (t.exports['#BITCOKE:BCHUSD-symbol-description'] = ['BCH своп кванто']),
        (t.exports['#BITCOKE:BTCUSD-symbol-description'] = ['BTC своп кванто']),
        (t.exports['#BITCOKE:ETHUSD-symbol-description'] = ['ETH своп кванто']),
        (t.exports['#BITCOKE:LTCUSD-symbol-description'] = ['LTC своп кванто']),
        (t.exports['#TVC:CA10-symbol-description'] = [
          'Гособлигации Канады, 10 лет',
        ]),
        (t.exports['#TVC:CA10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Канады',
        ]),
        (t.exports['#TVC:ID10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Индонезии',
        ]),
        (t.exports['#TVC:NL10-symbol-description'] = [
          'Гособлигации Нидерландов, 10 лет',
        ]),
        (t.exports['#TVC:NL10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Нидерландов',
        ]),
        (t.exports['#TVC:NZ10-symbol-description'] = [
          'Гособлигации Новой Зеландии, 10 лет',
        ]),
        (t.exports['#TVC:NZ10Y-symbol-description'] = [
          'Доходность 10-летних облигаций Новой Зеландии',
        ]),
        (t.exports['#SOLUSD-symbol-description'] = ['Solana / Доллар США']),
        (t.exports['#LUNAUSD-symbol-description'] = ['Luna / Доллар США']),
        (t.exports['#UNIUSD-symbol-description'] = ['Uniswap / Доллар США']),
        (t.exports['#LTCBRL-symbol-description'] = [
          'Лайткоин / Бразильский реал',
        ]),
        (t.exports['#ETCEUR-symbol-description'] = ['Эфириум Классик / Евро']),
        (t.exports['#ETHKRW-symbol-description'] = [
          'Эфириум / Южнокорейская вона',
        ]),
        (t.exports['#BTCRUB-symbol-description'] = [
          'Биткоин / Российский рубль',
        ]),
        (t.exports['#BTCTHB-symbol-description'] = ['Биткоин / Тайский бат']),
        (t.exports['#ETHTHB-symbol-description'] = ['Эфириум / Тайский бат']),
        (t.exports['#TVC:EU10YY-symbol-description'] = [
          'Доходность 10-летних еврооблигаций',
        ]);
    },
  },
]);
