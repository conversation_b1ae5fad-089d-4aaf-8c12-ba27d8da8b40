(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (e) => {
      e.exports = ['Ekle'];
    },
    53585: (e) => {
      e.exports = ['Özel renk ekle'];
    },
    81865: (e) => {
      e.exports = ['Şeffaflık'];
    },
    73755: (e) => {
      e.exports = ['Başka sembol'];
    },
    16936: (e) => {
      e.exports = ['Geri'];
    },
    88046: (e) => {
      e.exports = ['Temel grafik sembolü'];
    },
    9898: (e) => {
      e.exports = ['Sağ'];
    },
    20036: (e) => {
      e.exports = ['İptal'];
    },
    23398: (e) => {
      e.exports = ['Sembolu değiştir'];
    },
    94551: (e) => {
      e.exports = ['Grafik'];
    },
    64498: (e) => {
      e.exports = ['Tüm kaynaklar'];
    },
    73226: (e) => {
      e.exports = ['Uygula'];
    },
    79852: (e) => {
      e.exports = ['Tahvil'];
    },
    56095: (e) => {
      e.exports = ['Azalt'];
    },
    29601: (e) => {
      e.exports = ['Açıklama'];
    },
    46812: (e) => {
      e.exports = ['Yükseliş'];
    },
    89298: (e) => {
      e.exports = ['Ofset'];
    },
    68988: (e) => {
      e.exports = ['Tamam'];
    },
    29673: (e) => {
      e.exports = ['Kriterlerinize uygun borsa yok'];
    },
    41379: (e) => {
      e.exports = ['Kriterlerinize uygun sembol yok'];
    },
    35563: (e) => {
      e.exports = ['Numara formatı geçersiz'];
    },
    19724: (e) => {
      e.exports = ['Kaynak'];
    },
    59877: (e) => {
      e.exports = [
        'Saat ve fiyatı ayarlama{inputInline} {studyShortDescription}',
      ];
    },
    18571: (e) => {
      e.exports = [
        '"{studyShortDescription}" için "{inputTitle}" zamanını ayarlayın',
      ];
    },
    58552: (e) => {
      e.exports = [
        '"{studyShortDescription}" için "{inputTitle}" fiyatını ayarlayın',
      ];
    },
    80481: (e) => {
      e.exports = ['"{studyShortDescription}" için saati ve fiyatı ayarlayın'];
    },
    42917: (e) => {
      e.exports = ['"{studyShortDescription}" için zamanı ayarlayın'];
    },
    6083: (e) => {
      e.exports = ['"{studyShortDescription}" için tarih seti'];
    },
    52298: (e) => {
      e.exports = ['Ara'];
    },
    13269: (e) => {
      e.exports = ['Kaynak seç'];
    },
    2607: (e) => {
      e.exports = [
        'Belirtilen değer, enstrümanın en fazla değerinden daha fazladır {max}.',
      ];
    },
    53669: (e) => {
      e.exports = [
        'Belirtilen değer enstrümanın minimum değerinden küçük {min}.',
      ];
    },
    89053: (e) => {
      e.exports = ['Sembol'];
    },
    48490: (e) => {
      e.exports = ['Sembol ve açıklama'];
    },
    99983: (e) => {
      e.exports = ['Sembol Arama'];
    },
    54336: (e) => {
      e.exports = ['Rengi kaldır'];
    },
    60142: (e) => {
      e.exports = ['Kalınlık'];
    },
    87592: (e) => {
      e.exports = 'cfd';
    },
    17023: (e) => {
      e.exports = ['opaklık değiştir'];
    },
    13066: (e) => {
      e.exports = ['renk değiştir'];
    },
    95657: (e) => {
      e.exports = ['kalınlık değiştir'];
    },
    18567: (e) => {
      e.exports = ['{propertyName} özelliğini dğş'];
    },
    36962: (e) => {
      e.exports = ['kapanış'];
    },
    8448: (e) => {
      e.exports = ['kripto'];
    },
    1328: (e) => {
      e.exports = 'dr';
    },
    88720: (e) => {
      e.exports = ['ekonomi'];
    },
    39512: (e) => {
      e.exports = ['döviz'];
    },
    81859: (e) => {
      e.exports = ['vadeli'];
    },
    39337: (e) => {
      e.exports = ['yüksek'];
    },
    91815: (e) => {
      e.exports = 'hl2';
    },
    40771: (e) => {
      e.exports = ['ydk3'];
    },
    9523: (e) => {
      e.exports = 'hlcc4';
    },
    12754: (e) => {
      e.exports = ['endeks'];
    },
    38071: (e) => {
      e.exports = ['endeks'];
    },
    12504: (e) => {
      e.exports = 'ohlc4';
    },
    38466: (e) => {
      e.exports = ['açılış'];
    },
    3919: (e) => {
      e.exports = ['düşük'];
    },
    36931: (e) => {
      e.exports = ['hisse'];
    },
  },
]);
