(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4800],
  {
    91282: (t) => {
      t.exports = ['#1 (K棒)'];
    },
    1961: (t) => {
      t.exports = ['#1(價格)'];
    },
    12706: (t) => {
      t.exports = ['#1(價格,K棒)'];
    },
    92195: (t) => {
      t.exports = ['#1 (垂直位置%，K線)'];
    },
    66187: (t) => {
      t.exports = ['中立線'];
    },
    5066: (t) => {
      t.exports = '%';
    },
    89795: (t) => {
      t.exports = 'Counterclockwise';
    },
    43809: (t) => {
      t.exports = ['以百分比表示的係數'];
    },
    40054: (t) => {
      t.exports = ['顏色'];
    },
    47737: (t) => {
      t.exports = ['精簡統計模式'];
    },
    76655: (t) => {
      t.exports = ['現金'];
    },
    72171: (t) => {
      t.exports = ['中心'];
    },
    99120: (t) => {
      t.exports = ['管道'];
    },
    36150: (t) => {
      t.exports = ['角度'];
    },
    38280: (t) => {
      t.exports = ['角度'];
    },
    95264: (t) => {
      t.exports = ['賬戶規模'];
    },
    85160: (t) => {
      t.exports = ['總是顯示統計數據'];
    },
    54189: (t) => {
      t.exports = ['弧形'];
    },
    34674: (t) => {
      t.exports = ['最小刻度的高低平均價'];
    },
    91757: (t) => {
      t.exports = ['底部'];
    },
    17608: (t) => {
      t.exports = ['底部標籤'];
    },
    48848: (t) => {
      t.exports = ['框線'];
    },
    72269: (t) => {
      t.exports = ['邊框'];
    },
    27331: (t) => {
      t.exports = ['背景'];
    },
    19949: (t) => {
      t.exports = ['K線範圍'];
    },
    81260: (t) => {
      t.exports = ['格線'];
    },
    67114: (t) => {
      t.exports = ['日期/時間範圍'];
    },
    75460: (t) => {
      t.exports = ['距離'];
    },
    46211: (t) => {
      t.exports = ['表情貼'];
    },
    46001: (t) => {
      t.exports = ['進場價：'];
    },
    1220: (t) => {
      t.exports = ['延伸'];
    },
    71116: (t) => {
      t.exports = ['延伸底部'];
    },
    45809: (t) => {
      t.exports = ['向左延伸'];
    },
    25892: (t) => {
      t.exports = ['延伸左邊線條'];
    },
    3304: (t) => {
      t.exports = ['左側延長線'];
    },
    83095: (t) => {
      t.exports = ['向右延長線'];
    },
    14025: (t) => {
      t.exports = ['向右延伸'];
    },
    74395: (t) => {
      t.exports = ['延伸右邊線條'];
    },
    85197: (t) => {
      t.exports = ['延伸頂部'];
    },
    17006: (t) => {
      t.exports = ['字體大小'];
    },
    31343: (t) => {
      t.exports = ['失敗文本'];
    },
    28565: (t) => {
      t.exports = ['失敗背景'];
    },
    87931: (t) => {
      t.exports = ['扇形'];
    },
    39836: (t) => {
      t.exports = ['基於對數坐標的Fib水平'];
    },
    10578: (t) => {
      t.exports = ['完整圓圈'];
    },
    25264: (t) => {
      t.exports = ['HL K線'];
    },
    77405: (t) => {
      t.exports = ['橫式'];
    },
    66049: (t) => {
      t.exports = ['OC Bars'];
    },
    27531: (t) => {
      t.exports = ['手數'];
    },
    85206: (t) => {
      t.exports = ['標籤'];
    },
    75332: (t) => {
      t.exports = ['標籤邊框'];
    },
    14773: (t) => {
      t.exports = ['標籤背景顏色'];
    },
    37126: (t) => {
      t.exports = ['標籤文字'];
    },
    79106: (t) => {
      t.exports = ['水平'];
    },
    95610: (t) => {
      t.exports = ['水平線'];
    },
    19286: (t) => {
      t.exports = ['左'];
    },
    79307: (t) => {
      t.exports = ['左標籤'];
    },
    49286: (t) => {
      t.exports = ['線 - HL/2'];
    },
    17676: (t) => {
      t.exports = ['線 - 開盤價'];
    },
    47669: (t) => {
      t.exports = ['線 - 收盤價'];
    },
    71899: (t) => {
      t.exports = ['線 - 最高價'];
    },
    83394: (t) => {
      t.exports = ['線 - 最低價'];
    },
    60489: (t) => {
      t.exports = ['線條顏色'];
    },
    53889: (t) => {
      t.exports = ['模式'];
    },
    76476: (t) => {
      t.exports = ['中間'];
    },
    24510: (t) => {
      t.exports = ['中點'];
    },
    22213: (t) => {
      t.exports = ['來源背景'];
    },
    15500: (t) => {
      t.exports = ['來源邊界'];
    },
    79238: (t) => {
      t.exports = ['來源文本'];
    },
    37249: (t) => {
      t.exports = ['統計數據'];
    },
    28712: (t) => {
      t.exports = ['統計位置'];
    },
    50948: (t) => {
      t.exports = ['停損顏色：'];
    },
    56119: (t) => {
      t.exports = ['停損水平'];
    },
    69835: (t) => {
      t.exports = ['成功文本'];
    },
    91141: (t) => {
      t.exports = ['成功背景'];
    },
    650: (t) => {
      t.exports = ['百分比'];
    },
    25684: (t) => {
      t.exports = ['價格'];
    },
    23675: (t) => {
      t.exports = ['價格標籤'];
    },
    75675: (t) => {
      t.exports = ['價格標籤'];
    },
    16103: (t) => {
      t.exports = ['價格水平'];
    },
    46964: (t) => {
      t.exports = ['價格範圍'];
    },
    59771: (t) => {
      t.exports = ['價格/K線比'];
    },
    29072: (t) => {
      t.exports = ['價格'];
    },
    2635: (t) => {
      t.exports = ['停利水平'];
    },
    33886: (t) => {
      t.exports = ['範圍和比例'];
    },
    24186: (t) => {
      t.exports = ['反轉'];
    },
    21141: (t) => {
      t.exports = ['右'];
    },
    91367: (t) => {
      t.exports = ['右邊標籤'];
    },
    63833: (t) => {
      t.exports = ['風險'];
    },
    95545: (t) => {
      t.exports = ['波浪'];
    },
    26458: (t) => {
      t.exports = ['燭芯'];
    },
    65994: (t) => {
      t.exports = ['頂部'];
    },
    10209: (t) => {
      t.exports = ['熱門標籤'];
    },
    98001: (t) => {
      t.exports = ['目標背景'];
    },
    89258: (t) => {
      t.exports = ['目標邊界'];
    },
    45302: (t) => {
      t.exports = ['獲利顏色：'];
    },
    74289: (t) => {
      t.exports = ['目標文本'];
    },
    17932: (t) => {
      t.exports = ['自動換行'];
    },
    92960: (t) => {
      t.exports = ['文字對齊'];
    },
    90581: (t) => {
      t.exports = ['文字方向'];
    },
    55325: (t) => {
      t.exports = ['時間標籤'];
    },
    77838: (t) => {
      t.exports = ['時間水平'];
    },
    2295: (t) => {
      t.exports = ['透明度'];
    },
    4372: (t) => {
      t.exports = ['趨勢線'];
    },
    12374: (t) => {
      t.exports = ['使用一個顏色'];
    },
    91322: (t) => {
      t.exports = ['數值'];
    },
    25227: (t) => {
      t.exports = ['方差'];
    },
    44085: (t) => {
      t.exports = ['直式'];
    },
    1670: (t) => {
      t.exports = ['更改角度'];
    },
    54119: (t) => {
      t.exports = ['更改箭頭顏色'];
    },
    72080: (t) => {
      t.exports = ['更改旗標顏色'];
    },
    98905: (t) => {
      t.exports = ['更改上邊距'];
    },
    11049: (t) => {
      t.exports = ['更改垂直位置Y坐標'];
    },
    31804: (t) => {
      t.exports = ['更改{title}逆時針'];
    },
    99128: (t) => {
      t.exports = ['更改{title}係數為百分比可見性'];
    },
    20216: (t) => {
      t.exports = ['更改{title}顏色'];
    },
    35435: (t) => {
      t.exports = ['更改{title}緊湊統計模式'];
    },
    550: (t) => {
      t.exports = ['更改{title}K線邊框上漲顏色'];
    },
    22313: (t) => {
      t.exports = ['更改{title}K線邊框可見性'];
    },
    7373: (t) => {
      t.exports = ['更改{title}K線邊框下跌顏色'];
    },
    38742: (t) => {
      t.exports = ['更改{title}K線下跌顏色'];
    },
    42273: (t) => {
      t.exports = ['更改{title}K線上漲顏色'];
    },
    76054: (t) => {
      t.exports = ['更改{title}K線影線顏色'];
    },
    27029: (t) => {
      t.exports = ['更改{title}K線影線可見性'];
    },
    45537: (t) => {
      t.exports = ['更改{title}角度可見性'];
    },
    31775: (t) => {
      t.exports = ['更改{title}帳戶大小'];
    },
    37913: (t) => {
      t.exports = ['更改{title}總是顯示統計資訊'];
    },
    15521: (t) => {
      t.exports = ['更改{title}所有線條顏色'];
    },
    17466: (t) => {
      t.exports = ['更改{title}弧形{index}線條顏色'];
    },
    72307: (t) => {
      t.exports = ['更改{title}弧形{index}線條寬度'];
    },
    13853: (t) => {
      t.exports = ['更改{title}弧形{index}線條可見性'];
    },
    78680: (t) => {
      t.exports = ['更改{title}平均HL值'];
    },
    15802: (t) => {
      t.exports = ['更改{title}底部標籤可見性'];
    },
    36438: (t) => {
      t.exports = ['更改{title}背景透明度'];
    },
    82465: (t) => {
      t.exports = ['更改{title}背景可見性'];
    },
    75312: (t) => {
      t.exports = ['更改{title}背景顏色'];
    },
    39651: (t) => {
      t.exports = ['更改{title}背景顏色1'];
    },
    78177: (t) => {
      t.exports = ['更改{title}背景顏色2'];
    },
    42746: (t) => {
      t.exports = ['更改{title}K線範圍可見性'];
    },
    53770: (t) => {
      t.exports = ['更改{title}網格可見性'];
    },
    29145: (t) => {
      t.exports = ['更改{title}網格線顏色'];
    },
    64949: (t) => {
      t.exports = ['更改{title}網格線樣式'];
    },
    93548: (t) => {
      t.exports = ['更改{title}網格線寬度'];
    },
    15485: (t) => {
      t.exports = ['更改{title}日期/時間範圍可見性'];
    },
    3400: (t) => {
      t.exports = ['更改{title}角度'];
    },
    91534: (t) => {
      t.exports = ['更改{title}距離可見性'];
    },
    65056: (t) => {
      t.exports = ['更改{title}表情符號'];
    },
    65899: (t) => {
      t.exports = ['更改{title}表情符號可見性'];
    },
    59354: (t) => {
      t.exports = ['更改{title}入場價格'];
    },
    1447: (t) => {
      t.exports = ['更改{title}延長底部'];
    },
    15258: (t) => {
      t.exports = ['更改{title}延長左邊'];
    },
    896: (t) => {
      t.exports = ['更改{title}延長頂部'];
    },
    3708: (t) => {
      t.exports = ['更改{title}向左延伸'];
    },
    45719: (t) => {
      t.exports = ['更改{title}向右延伸'];
    },
    86647: (t) => {
      t.exports = ['更改{title}擴展名'];
    },
    3156: (t) => {
      t.exports = ['更改{title}失敗文本顏色'];
    },
    49885: (t) => {
      t.exports = ['更改{title}失敗背景顏色'];
    },
    89126: (t) => {
      t.exports = ['改變{title}扇形{index}線條可見性'];
    },
    30016: (t) => {
      t.exports = ['更改{title}扇形{index}線條寬度'];
    },
    36147: (t) => {
      t.exports = ['更改{title}扇形{index}線條顏色'];
    },
    78142: (t) => {
      t.exports = ['更改{title}扇形可見性'];
    },
    79467: (t) => {
      t.exports = ['更改{title}扇形線條顏色'];
    },
    45739: (t) => {
      t.exports = ['在對數刻度上更改{title}斐波那契水平'];
    },
    99670: (t) => {
      t.exports = ['更改{title}翻轉'];
    },
    35165: (t) => {
      t.exports = ['更改{title}完整圓圈的可見性'];
    },
    48983: (t) => {
      t.exports = ['更改{title}圖片背景顏色'];
    },
    45025: (t) => {
      t.exports = ['更改{title}手數大小'];
    },
    81170: (t) => {
      t.exports = ['更改{title}標籤對齊方式'];
    },
    22775: (t) => {
      t.exports = ['更改{title}標籤字體大小'];
    },
    24338: (t) => {
      t.exports = ['更改{title}標籤可見性'];
    },
    32891: (t) => {
      t.exports = ['更改{title}水平{index}線系數'];
    },
    85551: (t) => {
      t.exports = ['更改{title}水平{index}線顏色'];
    },
    47840: (t) => {
      t.exports = ['更改{title}水平{index}線樣式'];
    },
    45463: (t) => {
      t.exports = ['更改{title}水平{index}線可見性'];
    },
    90098: (t) => {
      t.exports = ['更改{title}水平{index}線寬度'];
    },
    26710: (t) => {
      t.exports = ['更改{title}水平可見性'];
    },
    2359: (t) => {
      t.exports = ['更改{title}左側標籤可見性'];
    },
    44643: (t) => {
      t.exports = ['更改{title}線條寬度'];
    },
    20563: (t) => {
      t.exports = ['更改{title}線條顏色'];
    },
    66982: (t) => {
      t.exports = ['更改{title}線條樣式'];
    },
    94441: (t) => {
      t.exports = ['更改{title}模式'];
    },
    89996: (t) => {
      t.exports = ['更改{title}中點可見性'];
    },
    36618: (t) => {
      t.exports = ['更改{title}鏡像'];
    },
    18544: (t) => {
      t.exports = ['更改{title}源背景顏色'];
    },
    48035: (t) => {
      t.exports = ['更改{title}源邊框顏色'];
    },
    42286: (t) => {
      t.exports = ['更改{title}源文字顏色'];
    },
    588: (t) => {
      t.exports = ['更改{title}統計位置'];
    },
    54659: (t) => {
      t.exports = ['更改{title}停損顏色'];
    },
    89182: (t) => {
      t.exports = ['更改{title}停損水平'];
    },
    82224: (t) => {
      t.exports = ['更改{title}停損價格'];
    },
    88383: (t) => {
      t.exports = ['更改{title}成功文字顏色'];
    },
    26967: (t) => {
      t.exports = ['更改{title}成功背景顏色'];
    },
    45936: (t) => {
      t.exports = ['更改{title}價格標籤可見性'];
    },
    88577: (t) => {
      t.exports = ['更改{title}價格標籤可見性'];
    },
    47045: (t) => {
      t.exports = ['更改{title}價格範圍可見性'];
    },
    56175: (t) => {
      t.exports = ['更改{title}價格可見性'];
    },
    44539: (t) => {
      t.exports = ['更改{title}停利水平'];
    },
    41646: (t) => {
      t.exports = ['更改{title}停利價格'];
    },
    52877: (t) => {
      t.exports = ['更改{title}翻轉'];
    },
    16598: (t) => {
      t.exports = ['更改{title}右側標籤可見性'];
    },
    31553: (t) => {
      t.exports = ['更改{title}風險'];
    },
    40344: (t) => {
      t.exports = ['更改{title}風險顯示模式'];
    },
    73137: (t) => {
      t.exports = ['更改{title}頂部標籤可見性'];
    },
    52387: (t) => {
      t.exports = ['更改{title}目標背景顏色'];
    },
    6921: (t) => {
      t.exports = ['更改{title}目標邊框顏色'];
    },
    97573: (t) => {
      t.exports = ['更改{title}目標顏色'];
    },
    27634: (t) => {
      t.exports = ['更改{title}目標文字顏色'];
    },
    33822: (t) => {
      t.exports = ['更改{title}時間標籤可見性'];
    },
    84321: (t) => {
      t.exports = ['更改{title}透明度'];
    },
    12355: (t) => {
      t.exports = ['更改{title}方差值'];
    },
    25937: (t) => {
      t.exports = ['更改{toolName}標籤垂直對齊'];
    },
    46991: (t) => {
      t.exports = ['更改{toolName}標籤水平對齊'];
    },
    73080: (t) => {
      t.exports = ['更改{toolName}標籤方向'];
    },
    24272: (t) => {
      t.exports = ['更改{toolName}線條可見性'];
    },
    46404: (t) => {
      t.exports = ['更改{toolName}線條寬度'];
    },
    50265: (t) => {
      t.exports = ['更改{toolName}線條顏色'];
    },
    72781: (t) => {
      t.exports = ['更改{toolName}線條向左延伸'];
    },
    84613: (t) => {
      t.exports = ['更改{toolName}線條向右延伸'];
    },
    62603: (t) => {
      t.exports = ['更改{toolName}線條左端'];
    },
    62412: (t) => {
      t.exports = ['更改{toolName}線條右端'];
    },
    35422: (t) => {
      t.exports = ['更改{toolName}線條樣式'];
    },
    77690: (t) => {
      t.exports = ['更改{toolName}文字'];
    },
    69871: (t) => {
      t.exports = ['更改{toolName}文字可見性'];
    },
    25878: (t) => {
      t.exports = ['更改{toolName}文字換行'];
    },
    91832: (t) => {
      t.exports = ['更改{toolName}文字背景顏色'];
    },
    18610: (t) => {
      t.exports = ['更改{toolName}文字背景可見性'];
    },
    44755: (t) => {
      t.exports = ['更改{toolName}文字邊框顏色'];
    },
    6324: (t) => {
      t.exports = ['更改{toolName}文字邊框寬度'];
    },
    45529: (t) => {
      t.exports = ['更改{toolName}文字邊框可見性'];
    },
    6500: (t) => {
      t.exports = ['更改{toolName}文字顏色'];
    },
    51614: (t) => {
      t.exports = ['更改{toolName}文字字體加粗'];
    },
    18572: (t) => {
      t.exports = ['更改{toolName}文字字體斜體'];
    },
    48382: (t) => {
      t.exports = ['更改{toolName}文字字體大小'];
    },
    21926: (t) => {
      t.exports = ['背景顏色'];
    },
    52241: (t) => {
      t.exports = ['背景填充'];
    },
    70607: (t) => {
      t.exports = ['線條顏色'];
    },
    41075: (t) => {
      t.exports = ['線條樣式'];
    },
    73043: (t) => {
      t.exports = ['線條寬度'];
    },
    41437: (t) => {
      t.exports = ['文字顏色'];
    },
  },
]);
