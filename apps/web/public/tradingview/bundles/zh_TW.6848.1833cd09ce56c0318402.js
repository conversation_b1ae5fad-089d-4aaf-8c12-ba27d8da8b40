(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (e) => {
      e.exports = ['增加'];
    },
    53585: (e) => {
      e.exports = ['增加自訂顏色'];
    },
    81865: (e) => {
      e.exports = ['不透明度'];
    },
    60558: (e) => {
      e.exports = ['動物&自然'];
    },
    14232: (e) => {
      e.exports = ['活動'];
    },
    35305: (e) => {
      e.exports = ['食物&飲料'];
    },
    49546: (e) => {
      e.exports = ['旗幟'];
    },
    72302: (e) => {
      e.exports = ['物品'];
    },
    96330: (e) => {
      e.exports = ['笑臉&人像'];
    },
    6878: (e) => {
      e.exports = ['符號'];
    },
    15426: (e) => {
      e.exports = ['最近使用'];
    },
    15395: (e) => {
      e.exports = ['旅遊&地點'];
    },
    73755: (e) => {
      e.exports = ['另一個代碼'];
    },
    16936: (e) => {
      e.exports = ['返回'];
    },
    88046: (e) => {
      e.exports = ['主圖表代碼'];
    },
    9898: (e) => {
      e.exports = ['認股權'];
    },
    20036: (e) => {
      e.exports = ['取消'];
    },
    72171: (e) => {
      e.exports = ['中心'];
    },
    23398: (e) => {
      e.exports = ['變更商品'];
    },
    94551: (e) => {
      e.exports = ['圖表'];
    },
    64498: (e) => {
      e.exports = ['全部來源'];
    },
    91757: (e) => {
      e.exports = ['底部'];
    },
    79852: (e) => {
      e.exports = ['債券'];
    },
    16079: (e) => {
      e.exports = ['漸層'];
    },
    42973: (e) => {
      e.exports = ['點虛線'];
    },
    59317: (e) => {
      e.exports = ['短虛線'];
    },
    56095: (e) => {
      e.exports = ['減少'];
    },
    29601: (e) => {
      e.exports = ['描述'];
    },
    77405: (e) => {
      e.exports = ['橫式'];
    },
    46812: (e) => {
      e.exports = ['增加'];
    },
    89298: (e) => {
      e.exports = ['偏移'];
    },
    68988: (e) => {
      e.exports = ['確認'];
    },
    19286: (e) => {
      e.exports = ['左'];
    },
    76476: (e) => {
      e.exports = ['中間'];
    },
    29673: (e) => {
      e.exports = ['沒有交易所符合您的條件'];
    },
    41379: (e) => {
      e.exports = ['沒有商品符合您的條件'];
    },
    55362: (e) => {
      e.exports = ['正常'];
    },
    35563: (e) => {
      e.exports = ['號碼格式無效。'];
    },
    19724: (e) => {
      e.exports = ['來源'];
    },
    35637: (e) => {
      e.exports = 'Solid';
    },
    52298: (e) => {
      e.exports = ['搜尋'];
    },
    13269: (e) => {
      e.exports = ['選擇來源'];
    },
    2607: (e) => {
      e.exports = ['指定值大於商品最大值{max}。'];
    },
    53669: (e) => {
      e.exports = ['指定值小於商品最小值{min}。'];
    },
    89053: (e) => {
      e.exports = ['商品代碼'];
    },
    48490: (e) => {
      e.exports = ['商品&描述'];
    },
    99983: (e) => {
      e.exports = ['商品搜尋'];
    },
    54336: (e) => {
      e.exports = ['移除顏色'];
    },
    21141: (e) => {
      e.exports = ['右'];
    },
    65994: (e) => {
      e.exports = ['頂部'];
    },
    92960: (e) => {
      e.exports = ['文字對齊'];
    },
    90581: (e) => {
      e.exports = ['文字方向'];
    },
    60142: (e) => {
      e.exports = ['厚度'];
    },
    78019: (e) => {
      e.exports = [
        '使用特殊的數學符號替換選定的圖形：+,-,/,* 表示價格，+,- 表示K線指數。',
      ];
    },
    44085: (e) => {
      e.exports = ['直式'];
    },
    87592: (e) => {
      e.exports = 'cfd';
    },
    17023: (e) => {
      e.exports = ['改變不透明度'];
    },
    13066: (e) => {
      e.exports = ['更改顏色'];
    },
    95657: (e) => {
      e.exports = ['改變密度'];
    },
    18567: (e) => {
      e.exports = ['更改{propertyName}屬性'];
    },
    36962: (e) => {
      e.exports = ['收盤'];
    },
    8448: (e) => {
      e.exports = 'crypto';
    },
    1328: (e) => {
      e.exports = 'dr';
    },
    76080: (e) => {
      e.exports = ['例如+1'];
    },
    95166: (e) => {
      e.exports = ['例如 /2'];
    },
    88720: (e) => {
      e.exports = ['經濟'];
    },
    39512: (e) => {
      e.exports = ['外匯'];
    },
    81859: (e) => {
      e.exports = ['期貨'];
    },
    39337: (e) => {
      e.exports = ['高點'];
    },
    91815: (e) => {
      e.exports = ['高低2'];
    },
    40771: (e) => {
      e.exports = ['高低3'];
    },
    9523: (e) => {
      e.exports = 'hlcc4';
    },
    12754: (e) => {
      e.exports = ['指數'];
    },
    38071: (e) => {
      e.exports = ['指數'];
    },
    12504: (e) => {
      e.exports = ['開高低收4'];
    },
    38466: (e) => {
      e.exports = ['開盤'];
    },
    3919: (e) => {
      e.exports = ['低點'];
    },
    36931: (e) => {
      e.exports = ['股票'];
    },
  },
]);
