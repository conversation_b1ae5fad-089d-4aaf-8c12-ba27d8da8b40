(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (o) => {
      o.exports = ['Dodaj'];
    },
    53585: (o) => {
      o.exports = ['Dodaj niestandardowy kolor'];
    },
    81865: (o) => {
      o.exports = ['Przez<PERSON>czys<PERSON>ć'];
    },
    60558: (o) => {
      o.exports = ['zwierzęta i natura'];
    },
    14232: (o) => {
      o.exports = ['aktywność'];
    },
    35305: (o) => {
      o.exports = ['żywność i napoje'];
    },
    49546: (o) => {
      o.exports = ['flagi'];
    },
    72302: (o) => {
      o.exports = ['obiekty'];
    },
    96330: (o) => {
      o.exports = ['buźki i osoby'];
    },
    6878: (o) => {
      o.exports = ['symbole'];
    },
    15426: (o) => {
      o.exports = ['ostatnio używane'];
    },
    15395: (o) => {
      o.exports = ['podróże i miejsca'];
    },
    73755: (o) => {
      o.exports = ['Inny symbol'];
    },
    16936: (o) => {
      o.exports = ['Cofnij'];
    },
    88046: (o) => {
      o.exports = ['Główny symbol wykresu'];
    },
    9898: (o) => {
      o.exports = ['Prawo do udostępniania'];
    },
    20036: (o) => {
      o.exports = ['Anuluj'];
    },
    72171: (o) => {
      o.exports = ['Środek'];
    },
    23398: (o) => {
      o.exports = ['Zmień symbol'];
    },
    94551: (o) => {
      o.exports = ['Wykres'];
    },
    64498: (o) => {
      o.exports = ['Wszystkie źródła'];
    },
    91757: (o) => {
      o.exports = ['Dno'];
    },
    79852: (o) => {
      o.exports = ['Obligacja'];
    },
    16079: (o) => {
      o.exports = 'Gradient';
    },
    42973: (o) => {
      o.exports = ['Linia kropkowana'];
    },
    59317: (o) => {
      o.exports = ['Linia przerywana'];
    },
    56095: (o) => {
      o.exports = ['Pomniejsz'];
    },
    29601: (o) => {
      o.exports = ['Opis'];
    },
    77405: (o) => {
      o.exports = ['Poziomo'];
    },
    46812: (o) => {
      o.exports = ['Zwiększ'];
    },
    89298: (o) => {
      o.exports = ['Przesunięcie'];
    },
    68988: (o) => {
      o.exports = 'Ok';
    },
    19286: (o) => {
      o.exports = ['Lewo'];
    },
    76476: (o) => {
      o.exports = ['Środek'];
    },
    29673: (o) => {
      o.exports = ['Brak giełd spełniających Twoje kryteria'];
    },
    41379: (o) => {
      o.exports = ['Brak symboli spełniających Twoje kryteria'];
    },
    55362: (o) => {
      o.exports = ['Normalny'];
    },
    35563: (o) => {
      o.exports = ['Błędny format numeru.'];
    },
    19724: (o) => {
      o.exports = ['Źródła'];
    },
    35637: (o) => {
      o.exports = ['Jednolite'];
    },
    52298: (o) => {
      o.exports = ['Szukaj'];
    },
    13269: (o) => {
      o.exports = ['Wybierz źródło'];
    },
    2607: (o) => {
      o.exports = ['Podana wartość nie jest wielokrotnością {max}.'];
    },
    53669: (o) => {
      o.exports = [
        'Podana wartość jest mniejsza niż minimum instrumentu wynoszące {min}.',
      ];
    },
    89053: (o) => {
      o.exports = 'Symbol';
    },
    48490: (o) => {
      o.exports = ['Symbol i opis'];
    },
    99983: (o) => {
      o.exports = ['Wyszukiwanie symboli'];
    },
    54336: (o) => {
      o.exports = ['Usuń kolor'];
    },
    21141: (o) => {
      o.exports = ['Prawy'];
    },
    65994: (o) => {
      o.exports = ['Szczyt'];
    },
    92960: (o) => {
      o.exports = ['Wyrównanie tekstu'];
    },
    90581: (o) => {
      o.exports = ['Kierunek tekstu'];
    },
    60142: (o) => {
      o.exports = ['Grubość'];
    },
    78019: (o) => {
      o.exports = [
        'Użyj specjalnych znaków matematycznych, aby zastąpić wybrane rysunki: +,-,/,* dla ceny i +,- dla indeksu słupka.',
      ];
    },
    44085: (o) => {
      o.exports = ['Pionowo'];
    },
    87592: (o) => {
      o.exports = 'cfd';
    },
    17023: (o) => {
      o.exports = ['Zmień przezroczystość'];
    },
    13066: (o) => {
      o.exports = ['Zmień kolor'];
    },
    95657: (o) => {
      o.exports = ['Zmień grubość'];
    },
    18567: (o) => {
      o.exports = ['zmień właściwość {propertyName}'];
    },
    36962: (o) => {
      o.exports = ['zamknięcie'];
    },
    8448: (o) => {
      o.exports = ['krypto'];
    },
    1328: (o) => {
      o.exports = ['Potwierdzenie wpłaty'];
    },
    76080: (o) => {
      o.exports = ['np. +1'];
    },
    95166: (o) => {
      o.exports = ['np. /2'];
    },
    88720: (o) => {
      o.exports = ['gospodarka'];
    },
    39512: (o) => {
      o.exports = 'forex';
    },
    81859: (o) => {
      o.exports = ['Kontrakty terminowe'];
    },
    39337: (o) => {
      o.exports = ['maksimum'];
    },
    91815: (o) => {
      o.exports = 'hl2';
    },
    40771: (o) => {
      o.exports = 'hlc3';
    },
    9523: (o) => {
      o.exports = 'hlcc4';
    },
    12754: (o) => {
      o.exports = ['indeks'];
    },
    38071: (o) => {
      o.exports = ['indeksy'];
    },
    12504: (o) => {
      o.exports = 'ohlc4';
    },
    38466: (o) => {
      o.exports = ['otwarcie'];
    },
    3919: (o) => {
      o.exports = 'low';
    },
    36931: (o) => {
      o.exports = ['akcja'];
    },
  },
]);
