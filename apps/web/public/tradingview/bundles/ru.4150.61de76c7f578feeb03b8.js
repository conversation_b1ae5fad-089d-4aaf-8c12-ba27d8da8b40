(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4150],
  {
    74274: (t) => {
      t.exports = ['Перекрестия'];
    },
    41596: (t) => {
      t.exports = ['Метки на ценовой шкале'];
    },
    23545: (t) => {
      t.exports = ['Значения в строке статуса'];
    },
    39495: (t) => {
      t.exports = ['Окружности'];
    },
    41389: (t) => {
      t.exports = ['Бар выше'];
    },
    29520: (t) => {
      t.exports = ['Абсолютные значения'];
    },
    58102: (t) => {
      t.exports = ['Применить по умолчанию'];
    },
    65262: (t) => {
      t.exports = ['Область с разрывами'];
    },
    83760: (t) => {
      t.exports = ['Тело'];
    },
    48848: (t) => {
      t.exports = ['Граница'];
    },
    27331: (t) => {
      t.exports = ['Фон'];
    },
    78626: (t) => {
      t.exports = ['Бар ниже'];
    },
    41361: (t) => {
      t.exports = ['Вниз'];
    },
    22192: (t) => {
      t.exports = ['Дни'];
    },
    31577: (t) => {
      t.exports = ['Динамическая зона стоимости (VA)'];
    },
    4329: (t) => {
      t.exports = ['Не задано'];
    },
    98938: (t) => {
      t.exports = ['По умолчанию'];
    },
    63099: (t) => {
      t.exports = ['Часы'];
    },
    11091: (t) => {
      t.exports = ['Гистограмма'];
    },
    66304: (t) => {
      t.exports = ['Аргументы'];
    },
    40297: (t) => {
      t.exports = ['Выходные значения'];
    },
    36993: (t) => {
      t.exports = ['Минимальное изменение цены'];
    },
    64606: (t) => {
      t.exports = ['Шрифт меток'];
    },
    54934: (t) => {
      t.exports = ['Линия с разрывами'];
    },
    95543: (t) => {
      t.exports = ['Месяцы'];
    },
    28134: (t) => {
      t.exports = ['Минуты'];
    },
    18229: (t) => {
      t.exports = ['Сделать по умолчанию'];
    },
    71129: (t) => {
      t.exports = ['Секунды'];
    },
    86520: (t) => {
      t.exports = ['Метки сигнала'];
    },
    79511: (t) => {
      t.exports = ['Ступенчатая линия'];
    },
    64108: (t) => {
      t.exports = 'Step line with breaks';
    },
    67767: (t) => {
      t.exports = ['Ступенчатая линия с ромбами'];
    },
    21861: (t) => {
      t.exports = ['Расположение'];
    },
    73947: (t) => {
      t.exports = ['Точность'];
    },
    66596: (t) => {
      t.exports = ['Количество'];
    },
    86672: (t) => {
      t.exports = ['Диапазоны'];
    },
    79782: (t) => {
      t.exports = ['Сбросить настройки'];
    },
    21594: (t) => {
      t.exports = ['Недели'];
    },
    26458: (t) => {
      t.exports = ['Фитиль'];
    },
    95247: (t) => {
      t.exports = ['Ширина (% от прямоугольника)'];
    },
    19221: (t) => {
      t.exports = ['Цвет текста'];
    },
    7138: (t) => {
      t.exports = ['Сделки на графике'];
    },
    98802: (t) => {
      t.exports = ['Вверх'];
    },
    14414: (t) => {
      t.exports = ['Профиль объёма'];
    },
    91322: (t) => {
      t.exports = ['Значения'];
    },
    20834: (t) => {
      t.exports = ['изменение мин. тик. значения'];
    },
    98491: (t) => {
      t.exports = ['изменение значка'];
    },
    7378: (t) => {
      t.exports = ['изменение размера шрифта'];
    },
    28691: (t) => {
      t.exports = ['изменение стиля линии'];
    },
    38361: (t) => {
      t.exports = ['изменение местоположения'];
    },
    51081: (t) => {
      t.exports = ['изменение ширины в процентах'];
    },
    47634: (t) => {
      t.exports = ['изменение расположения'];
    },
    15683: (t) => {
      t.exports = ['изменение типа отображения'];
    },
    164: (t) => {
      t.exports = ['изменение точности'];
    },
    86888: (t) => {
      t.exports = ['изменение фигуры'];
    },
    50463: (t) => {
      t.exports = ['изменение значения'];
    },
    12628: (t) => {
      t.exports = ['изменение видимости значений'];
    },
    13355: (t) => {
      t.exports = ['изменение дневной видимости {title} (предельная точка)'];
    },
    41377: (t) => {
      t.exports = ['изменение дневной видимости {title} (начальная точка)'];
    },
    35388: (t) => {
      t.exports = ['изменение часовой видимости {title} (начальная точка)'];
    },
    78586: (t) => {
      t.exports = ['изменение часовой видимости {title} (предельная точка)'];
    },
    59635: (t) => {
      t.exports = ['изменение месячной видимости {title} (начальная точка)'];
    },
    74266: (t) => {
      t.exports = ['изменение месячной видимости {title} (предельная точка)'];
    },
    91633: (t) => {
      t.exports = ['изменение минутной видимости {title} (предельная точка)'];
    },
    15106: (t) => {
      t.exports = ['изменение минутной видимости {title} (начальная точка)'];
    },
    66161: (t) => {
      t.exports = ['изменение секундной видимости {title} (предельная точка)'];
    },
    2822: (t) => {
      t.exports = ['изменение секундной видимости {title} (начальная точка)'];
    },
    21339: (t) => {
      t.exports = ['изменение недельной видимости {title} (начальная точка)'];
    },
    68643: (t) => {
      t.exports = ['изменение недельной видимости {title} (предельная точка)'];
    },
    30810: (t) => {
      t.exports = ['изменение видимости {title} на тиках'];
    },
    24941: (t) => {
      t.exports = ['изменение видимости {title} на неделях'];
    },
    29088: (t) => {
      t.exports = ['изменение видимости {title} на днях'];
    },
    68971: (t) => {
      t.exports = ['изменение видимости {title} на часах'];
    },
    64370: (t) => {
      t.exports = ['изменение видимости {title} на минутах'];
    },
    6659: (t) => {
      t.exports = ['изменение видимости {title} на месяцах'];
    },
    29091: (t) => {
      t.exports = ['изменение видимости {title} на диапазонах'];
    },
    46948: (t) => {
      t.exports = ['изменение видимости {title} на секундах'];
    },
    82211: (t) => {
      t.exports = ['Дни'];
    },
    33486: (t) => {
      t.exports = ['дневную видимость до'];
    },
    14077: (t) => {
      t.exports = ['дневную видимость с'];
    },
    3143: (t) => {
      t.exports = ['Часы'];
    },
    84775: (t) => {
      t.exports = ['часовую видимость с'];
    },
    11255: (t) => {
      t.exports = ['часовую видимость до'];
    },
    58964: (t) => {
      t.exports = ['Месяцы'];
    },
    71770: (t) => {
      t.exports = ['месячную видимость с'];
    },
    37179: (t) => {
      t.exports = ['месячную видимость до'];
    },
    16465: (t) => {
      t.exports = ['Минуты'];
    },
    72317: (t) => {
      t.exports = ['минутную видимость до'];
    },
    25586: (t) => {
      t.exports = ['минутную видимость с'];
    },
    32925: (t) => {
      t.exports = ['секундную видимость'];
    },
    39017: (t) => {
      t.exports = ['секундную видимость до'];
    },
    6049: (t) => {
      t.exports = ['секундную видимость с'];
    },
    13604: (t) => {
      t.exports = ['диапазоны'];
    },
    93016: (t) => {
      t.exports = ['недельную видимость'];
    },
    32002: (t) => {
      t.exports = ['недельную видимость с'];
    },
    28091: (t) => {
      t.exports = ['недельную видимость до'];
    },
    59523: (t) => {
      t.exports = ['тики'];
    },
  },
]);
