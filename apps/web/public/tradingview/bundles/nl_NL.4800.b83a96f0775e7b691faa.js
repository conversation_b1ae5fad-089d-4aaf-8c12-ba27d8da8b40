(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4800],
  {
    91282: (e) => {
      e.exports = '#1 (bar)';
    },
    1961: (e) => {
      e.exports = '#1 (price)';
    },
    12706: (e) => {
      e.exports = '#1 (price, bar)';
    },
    92195: (e) => {
      e.exports = '#1 (vertical position %, bar)';
    },
    66187: (e) => {
      e.exports = ['Mediaan'];
    },
    5066: (e) => {
      e.exports = '%';
    },
    89795: (e) => {
      e.exports = 'Counterclockwise';
    },
    43809: (e) => {
      e.exports = 'Coeffs as percents';
    },
    40054: (e) => {
      e.exports = ['Kleur'];
    },
    47737: (e) => {
      e.exports = 'Compact stats mode';
    },
    76655: (e) => {
      e.exports = 'Cash';
    },
    72171: (e) => {
      e.exports = 'Center';
    },
    99120: (e) => {
      e.exports = ['Kanaal'];
    },
    36150: (e) => {
      e.exports = ['Hoek'];
    },
    38280: (e) => {
      e.exports = 'Angles';
    },
    95264: (e) => {
      e.exports = 'Account size';
    },
    85160: (e) => {
      e.exports = 'Always show stats';
    },
    54189: (e) => {
      e.exports = ['Bogen'];
    },
    34674: (e) => {
      e.exports = 'Avg HL in minticks';
    },
    91757: (e) => {
      e.exports = 'Bottom';
    },
    17608: (e) => {
      e.exports = ['Onderste labels'];
    },
    48848: (e) => {
      e.exports = ['Rand'];
    },
    72269: (e) => {
      e.exports = ['Randen'];
    },
    27331: (e) => {
      e.exports = ['Achtergrond'];
    },
    19949: (e) => {
      e.exports = 'Bars range';
    },
    81260: (e) => {
      e.exports = ['Rooster'];
    },
    67114: (e) => {
      e.exports = 'Date/time range';
    },
    75460: (e) => {
      e.exports = 'Distance';
    },
    46211: (e) => {
      e.exports = 'Emoji pin';
    },
    46001: (e) => {
      e.exports = ['Openingsprijs'];
    },
    1220: (e) => {
      e.exports = ['Rek uit'];
    },
    71116: (e) => {
      e.exports = 'Extend bottom';
    },
    45809: (e) => {
      e.exports = ['Rek uit naar links'];
    },
    25892: (e) => {
      e.exports = 'Extend left line';
    },
    3304: (e) => {
      e.exports = ['Extend Lines Left'];
    },
    83095: (e) => {
      e.exports = 'Extend lines right';
    },
    14025: (e) => {
      e.exports = ['Rek uit naar rechts'];
    },
    74395: (e) => {
      e.exports = 'Extend right line';
    },
    85197: (e) => {
      e.exports = 'Extend top';
    },
    17006: (e) => {
      e.exports = ['Lettertype grootte'];
    },
    31343: (e) => {
      e.exports = 'Failure text';
    },
    28565: (e) => {
      e.exports = 'Failure background';
    },
    87931: (e) => {
      e.exports = ['Waaiers'];
    },
    39836: (e) => {
      e.exports = 'Fib levels based on log scale';
    },
    10578: (e) => {
      e.exports = ['Full Circles'];
    },
    25264: (e) => {
      e.exports = ['HL Bars'];
    },
    77405: (e) => {
      e.exports = 'Horizontal';
    },
    66049: (e) => {
      e.exports = ['OC Bars'];
    },
    27531: (e) => {
      e.exports = 'Lot size';
    },
    85206: (e) => {
      e.exports = 'Label';
    },
    75332: (e) => {
      e.exports = 'Label border';
    },
    14773: (e) => {
      e.exports = ['Label achtergrond'];
    },
    37126: (e) => {
      e.exports = 'Label text';
    },
    79106: (e) => {
      e.exports = 'Levels';
    },
    95610: (e) => {
      e.exports = ['Levels Line'];
    },
    19286: (e) => {
      e.exports = 'Left';
    },
    79307: (e) => {
      e.exports = ['Linker labels'];
    },
    49286: (e) => {
      e.exports = 'Line - HL/2';
    },
    17676: (e) => {
      e.exports = ['Line - Open'];
    },
    47669: (e) => {
      e.exports = ['Line - Close'];
    },
    71899: (e) => {
      e.exports = ['Line - High'];
    },
    83394: (e) => {
      e.exports = ['Line - Low'];
    },
    60489: (e) => {
      e.exports = 'Line color';
    },
    53889: (e) => {
      e.exports = 'Mode';
    },
    76476: (e) => {
      e.exports = 'Middle';
    },
    24510: (e) => {
      e.exports = 'Middle point';
    },
    22213: (e) => {
      e.exports = 'Source background';
    },
    15500: (e) => {
      e.exports = 'Source border';
    },
    79238: (e) => {
      e.exports = 'Source text';
    },
    37249: (e) => {
      e.exports = 'Stats';
    },
    28712: (e) => {
      e.exports = ['Stats Position'];
    },
    50948: (e) => {
      e.exports = ['Stop kleur'];
    },
    56119: (e) => {
      e.exports = 'Stop level';
    },
    69835: (e) => {
      e.exports = 'Success text';
    },
    91141: (e) => {
      e.exports = 'Success background';
    },
    650: (e) => {
      e.exports = 'Percents';
    },
    25684: (e) => {
      e.exports = ['Prijs'];
    },
    23675: (e) => {
      e.exports = ['Prijs label'];
    },
    75675: (e) => {
      e.exports = 'Price labels';
    },
    16103: (e) => {
      e.exports = ['Prijs levels'];
    },
    46964: (e) => {
      e.exports = ['Prijs gebied'];
    },
    59771: (e) => {
      e.exports = ['Price/Bar Ratio'];
    },
    29072: (e) => {
      e.exports = ['Prijzen'];
    },
    2635: (e) => {
      e.exports = 'Profit level';
    },
    33886: (e) => {
      e.exports = 'Ranges and ratio';
    },
    24186: (e) => {
      e.exports = ['Keer om'];
    },
    21141: (e) => {
      e.exports = 'Right';
    },
    91367: (e) => {
      e.exports = ['Rechter labels'];
    },
    63833: (e) => {
      e.exports = 'Risk';
    },
    95545: (e) => {
      e.exports = 'Wave';
    },
    26458: (e) => {
      e.exports = ['Lont'];
    },
    65994: (e) => {
      e.exports = 'Top';
    },
    10209: (e) => {
      e.exports = ['Bovenste labels'];
    },
    98001: (e) => {
      e.exports = 'Target background';
    },
    89258: (e) => {
      e.exports = 'Target border';
    },
    45302: (e) => {
      e.exports = ['Doel kleur:'];
    },
    74289: (e) => {
      e.exports = 'Target text';
    },
    17932: (e) => {
      e.exports = 'Text wrap';
    },
    92960: (e) => {
      e.exports = 'Text alignment';
    },
    90581: (e) => {
      e.exports = 'Text orientation';
    },
    55325: (e) => {
      e.exports = 'Time label';
    },
    77838: (e) => {
      e.exports = 'Time levels';
    },
    2295: (e) => {
      e.exports = ['Transparantie'];
    },
    4372: (e) => {
      e.exports = 'Trend line';
    },
    12374: (e) => {
      e.exports = 'Use one color';
    },
    91322: (e) => {
      e.exports = 'Values';
    },
    25227: (e) => {
      e.exports = 'Variance';
    },
    44085: (e) => {
      e.exports = 'Vertical';
    },
    1670: (e) => {
      e.exports = 'change angle';
    },
    54119: (e) => {
      e.exports = 'change arrow color';
    },
    72080: (e) => {
      e.exports = 'change flag color';
    },
    98905: (e) => {
      e.exports = 'change top margin';
    },
    11049: (e) => {
      e.exports = 'change vertical position Y coordinate';
    },
    31804: (e) => {
      e.exports = 'change {title} counterclockwise';
    },
    99128: (e) => {
      e.exports = 'change {title} coeffs as percents visibility';
    },
    20216: (e) => {
      e.exports = 'change {title} color';
    },
    35435: (e) => {
      e.exports = 'change {title} compact stats mode';
    },
    550: (e) => {
      e.exports = 'change {title} candle border up color';
    },
    22313: (e) => {
      e.exports = 'change {title} candle border visibility';
    },
    7373: (e) => {
      e.exports = 'change {title} candle border down color';
    },
    38742: (e) => {
      e.exports = 'change {title} candle down color';
    },
    42273: (e) => {
      e.exports = 'change {title} candle up color';
    },
    76054: (e) => {
      e.exports = 'change {title} candle wick color';
    },
    27029: (e) => {
      e.exports = 'change {title} candle wick visibility';
    },
    45537: (e) => {
      e.exports = 'change {title} angle visibility';
    },
    31775: (e) => {
      e.exports = 'change {title} account size';
    },
    37913: (e) => {
      e.exports = 'change {title} always show stats';
    },
    15521: (e) => {
      e.exports = 'change {title} all lines color';
    },
    17466: (e) => {
      e.exports = 'change {title} arcs {index} line color';
    },
    72307: (e) => {
      e.exports = 'change {title} arcs {index} line width';
    },
    13853: (e) => {
      e.exports = 'change {title} arcs {index} line visibility';
    },
    78680: (e) => {
      e.exports = 'change {title} average HL value';
    },
    15802: (e) => {
      e.exports = 'change {title} bottom labels visibility';
    },
    36438: (e) => {
      e.exports = 'change {title} background transparency';
    },
    82465: (e) => {
      e.exports = 'change {title} background visibility';
    },
    75312: (e) => {
      e.exports = 'change {title} background color';
    },
    39651: (e) => {
      e.exports = 'change {title} background color 1';
    },
    78177: (e) => {
      e.exports = 'change {title} background color 2';
    },
    42746: (e) => {
      e.exports = 'change {title} bars range visibility';
    },
    53770: (e) => {
      e.exports = 'change {title} grid visibility';
    },
    29145: (e) => {
      e.exports = 'change {title} grid line color';
    },
    64949: (e) => {
      e.exports = 'change {title} grid line style';
    },
    93548: (e) => {
      e.exports = 'change {title} grid line width';
    },
    15485: (e) => {
      e.exports = 'change {title} date/time range visibility';
    },
    3400: (e) => {
      e.exports = 'change {title} degree';
    },
    91534: (e) => {
      e.exports = 'change {title} distance visibility';
    },
    65056: (e) => {
      e.exports = 'change {title} emoji';
    },
    65899: (e) => {
      e.exports = 'change {title} emoji visibility';
    },
    59354: (e) => {
      e.exports = 'change {title} entry price';
    },
    1447: (e) => {
      e.exports = 'change {title} extend bottom';
    },
    15258: (e) => {
      e.exports = 'change {title} extend left';
    },
    896: (e) => {
      e.exports = 'change {title} extend top';
    },
    3708: (e) => {
      e.exports = 'change {title} extending left';
    },
    45719: (e) => {
      e.exports = 'change {title} extending right';
    },
    86647: (e) => {
      e.exports = 'change {title} extension';
    },
    3156: (e) => {
      e.exports = 'change {title} failure text color';
    },
    49885: (e) => {
      e.exports = 'change {title} failure background color';
    },
    89126: (e) => {
      e.exports = 'change {title} fan {index} line visibility';
    },
    30016: (e) => {
      e.exports = 'change {title} fan {index} line width';
    },
    36147: (e) => {
      e.exports = 'change {title} fan {index} line color';
    },
    78142: (e) => {
      e.exports = 'change {title} fans visibility';
    },
    79467: (e) => {
      e.exports = 'change {title} fans line color';
    },
    45739: (e) => {
      e.exports = 'change {title} fib levels based on log scale';
    },
    99670: (e) => {
      e.exports = 'change {title} flipped';
    },
    35165: (e) => {
      e.exports = 'change {title} full circles visibility';
    },
    48983: (e) => {
      e.exports = 'change {title} image background color';
    },
    45025: (e) => {
      e.exports = 'change {title} lot size';
    },
    81170: (e) => {
      e.exports = 'change {title} labels alignment';
    },
    22775: (e) => {
      e.exports = 'change {title} labels font size';
    },
    24338: (e) => {
      e.exports = 'change {title} labels visibility';
    },
    32891: (e) => {
      e.exports = 'change {title} level {index} line coeff';
    },
    85551: (e) => {
      e.exports = 'change {title} level {index} line color';
    },
    47840: (e) => {
      e.exports = 'change {title} level {index} line style';
    },
    45463: (e) => {
      e.exports = 'change {title} level {index} line visibility';
    },
    90098: (e) => {
      e.exports = 'change {title} level {index} line width';
    },
    26710: (e) => {
      e.exports = 'change {title} levels visibility';
    },
    2359: (e) => {
      e.exports = 'change {title} left labels visibility';
    },
    44643: (e) => {
      e.exports = 'change {title} line width';
    },
    20563: (e) => {
      e.exports = 'change {title} line color';
    },
    66982: (e) => {
      e.exports = 'change {title} line style';
    },
    94441: (e) => {
      e.exports = 'change {title} mode';
    },
    89996: (e) => {
      e.exports = 'change {title} middle point visibility';
    },
    36618: (e) => {
      e.exports = 'change {title} mirrored';
    },
    18544: (e) => {
      e.exports = 'change {title} source background color';
    },
    48035: (e) => {
      e.exports = 'change {title} source border color';
    },
    42286: (e) => {
      e.exports = 'change {title} source text color';
    },
    588: (e) => {
      e.exports = 'change {title} stats position';
    },
    54659: (e) => {
      e.exports = 'change {title} stop color';
    },
    89182: (e) => {
      e.exports = 'change {title} stop level';
    },
    82224: (e) => {
      e.exports = 'change {title} stop price';
    },
    88383: (e) => {
      e.exports = 'change {title} success text color';
    },
    26967: (e) => {
      e.exports = 'change {title} success background color';
    },
    45936: (e) => {
      e.exports = 'change {title} price label visibility';
    },
    88577: (e) => {
      e.exports = 'change {title} price labels visibility';
    },
    47045: (e) => {
      e.exports = 'change {title} price range visibility';
    },
    56175: (e) => {
      e.exports = 'change {title} prices visibility';
    },
    44539: (e) => {
      e.exports = 'change {title} profit level';
    },
    41646: (e) => {
      e.exports = 'change {title} profit price';
    },
    52877: (e) => {
      e.exports = 'change {title} reverse';
    },
    16598: (e) => {
      e.exports = 'change {title} right labels visibility';
    },
    31553: (e) => {
      e.exports = 'change {title} risk';
    },
    40344: (e) => {
      e.exports = 'change {title} risk display mode';
    },
    73137: (e) => {
      e.exports = 'change {title} top labels visibility';
    },
    52387: (e) => {
      e.exports = 'change {title} target background color';
    },
    6921: (e) => {
      e.exports = 'change {title} target border color';
    },
    97573: (e) => {
      e.exports = 'change {title} target color';
    },
    27634: (e) => {
      e.exports = 'change {title} target text color';
    },
    33822: (e) => {
      e.exports = 'change {title} time label visibility';
    },
    84321: (e) => {
      e.exports = 'change {title} transparency';
    },
    12355: (e) => {
      e.exports = 'change {title} variance value';
    },
    25937: (e) => {
      e.exports = 'change {toolName} labels alignment vertical';
    },
    46991: (e) => {
      e.exports = 'change {toolName} labels alignment horizontal';
    },
    73080: (e) => {
      e.exports = 'change {toolName} labels direction';
    },
    24272: (e) => {
      e.exports = 'change {toolName} line visibility';
    },
    46404: (e) => {
      e.exports = 'change {toolName} line width';
    },
    50265: (e) => {
      e.exports = 'change {toolName} line color';
    },
    72781: (e) => {
      e.exports = 'change {toolName} line extending left';
    },
    84613: (e) => {
      e.exports = 'change {toolName} line extending right';
    },
    62603: (e) => {
      e.exports = 'change {toolName} line left end';
    },
    62412: (e) => {
      e.exports = 'change {toolName} line right end';
    },
    35422: (e) => {
      e.exports = 'change {toolName} line style';
    },
    77690: (e) => {
      e.exports = 'change {toolName} text';
    },
    69871: (e) => {
      e.exports = 'change {toolName} text visibility';
    },
    25878: (e) => {
      e.exports = 'change {toolName} text wrap';
    },
    91832: (e) => {
      e.exports = 'change {toolName} text background color';
    },
    18610: (e) => {
      e.exports = 'change {toolName} text background visibility';
    },
    44755: (e) => {
      e.exports = 'change {toolName} text border color';
    },
    6324: (e) => {
      e.exports = 'change {toolName} text border width';
    },
    45529: (e) => {
      e.exports = 'change {toolName} text border visibility';
    },
    6500: (e) => {
      e.exports = 'change {toolName} text color';
    },
    51614: (e) => {
      e.exports = 'change {toolName} text font bold';
    },
    18572: (e) => {
      e.exports = 'change {toolName} text font italic';
    },
    48382: (e) => {
      e.exports = 'change {toolName} text font size';
    },
    21926: (e) => {
      e.exports = 'backgrounds color';
    },
    52241: (e) => {
      e.exports = 'backgrounds filled';
    },
    70607: (e) => {
      e.exports = 'lines color';
    },
    41075: (e) => {
      e.exports = 'lines style';
    },
    73043: (e) => {
      e.exports = 'lines width';
    },
    41437: (e) => {
      e.exports = ['Tekstkleur'];
    },
  },
]);
