(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6444],
  {
    50831: (e) => {
      e.exports = ['çubuklar'];
    },
    19648: (e) => {
      e.exports = ['12-saat'];
    },
    55838: (e) => {
      e.exports = ['24-saat'];
    },
    88364: (e) => {
      e.exports = ['Basit grafik stilleri'];
    },
    46720: (e) => {
      e.exports = ['Artı işareti'];
    },
    50985: (e) => {
      e.exports = ['Döviz'];
    },
    17319: (e) => {
      e.exports = ['Para Birimi ve Birim'];
    },
    19481: (e) => {
      e.exports = ['Görünüm'];
    },
    68791: (e) => {
      e.exports = ['Argümanlar'];
    },
    95036: (e) => {
      e.exports = ['Ortalama kapanış fiyatı'];
    },
    27331: (e) => {
      e.exports = ['Arkaplan'];
    },
    22519: (e) => {
      e.exports = ['Çubuk Değişim <PERSON>'];
    },
    87845: (e) => {
      e.exports = ['Tuşlar'];
    },
    25209: (e) => {
      e.exports = ['Tarih biçimi'];
    },
    55090: (e) => {
      e.exports = ['Zaman çizelgesi etiketlerinde haftanın günleri'];
    },
    29601: (e) => {
      e.exports = ['Açıklama'];
    },
    26897: (e) => {
      e.exports = ['Olaylar'];
    },
    95338: (e) => {
      e.exports = ['Yatay kılavuz çizgileri'];
    },
    60971: (e) => {
      e.exports = ['Yüksek ve düşük fiyat'];
    },
    61142: (e) => {
      e.exports = ['Göstergeler'];
    },
    34905: (e) => {
      e.exports = ['Gösterge değeri'];
    },
    29687: (e) => {
      e.exports = ['Göstergeler ve finansallar değerleri'];
    },
    25084: (e) => {
      e.exports = ['Göstergeler ve finansallar adı'];
    },
    9654: (e) => {
      e.exports = ['Göstergelerin adı'];
    },
    99487: (e) => {
      e.exports = ['OHLC değerler'];
    },
    75991: (e) => {
      e.exports = ['Açık piyasa durumu'];
    },
    96073: (e) => {
      e.exports = 'Long Description';
    },
    70500: (e) => {
      e.exports = ['Para'];
    },
    66653: (e) => {
      e.exports = ['Marjlar'];
    },
    42502: (e) => {
      e.exports = ['Örtüşme kapalı'];
    },
    74343: (e) => {
      e.exports = ['Navigasyon'];
    },
    43115: (e) => {
      e.exports = ['Ölçekler'];
    },
    53224: (e) => {
      e.exports = ['Ölçekleri yerleştirme'];
    },
    79194: (e) => {
      e.exports = ['Durum çizgisi'];
    },
    89053: (e) => {
      e.exports = ['Sembol'];
    },
    35383: (e) => {
      e.exports = ['Sembol Adı'];
    },
    27767: (e) => {
      e.exports = ['Sembol son fiyat'];
    },
    40847: (e) => {
      e.exports = ['Sembol önceki gün kapanış fiyatı'];
    },
    50446: (e) => {
      e.exports = ['Bölme'];
    },
    73908: (e) => {
      e.exports = ['Bölme ayırıcıları'];
    },
    36014: (e) => {
      e.exports = ['Yüzde'];
    },
    78621: (e) => {
      e.exports = ['Pip'];
    },
    74823: (e) => {
      e.exports = ['Piyasa Öncesi/Sonrası Fiyat'];
    },
    64859: (e) => {
      e.exports = ['Fiyat Ölçeği'];
    },
    76523: (e) => {
      e.exports = ['Fiyat ve yüzdelik değeri'];
    },
    40187: (e) => {
      e.exports = 'Right Margin';
    },
    77705: (e) => {
      e.exports = ['Filigran'];
    },
    67369: (e) => {
      e.exports = ['Başlık'];
    },
    31326: (e) => {
      e.exports = ['Başlıklar'];
    },
    23097: (e) => {
      e.exports = ['Sembol'];
    },
    82168: (e) => {
      e.exports = ['Sembol ve açıklama'];
    },
    43637: (e) => {
      e.exports = ['Zaman Ölçeği'];
    },
    97316: (e) => {
      e.exports = ['Saat biçimi'];
    },
    90801: (e) => {
      e.exports = ['İşlem'];
    },
    77534: (e) => {
      e.exports = ['Birim'];
    },
    1111: (e) => {
      e.exports = ['Hacim'];
    },
    80170: (e) => {
      e.exports = ['Ölçeğe göre değer'];
    },
    91322: (e) => {
      e.exports = ['Değerler'];
    },
    70353: (e) => {
      e.exports = ['Dikey kılavuz çizgileri'];
    },
    57889: (e) => {
      e.exports = ['OHLC değerlerinin görünürlüğünü değiştir'];
    },
    35646: (e) => {
      e.exports = ['gezinme düğmelerinin görünürlüğünü değiştir'];
    },
    18644: (e) => {
      e.exports = ['açık piyasa durumu görünürlüğünü değiştir'];
    },
    45110: (e) => {
      e.exports = ['çubuk değiştirme görünürlüğünü değiştir'];
    },
    10349: (e) => {
      e.exports = ['alt kenar boşluğunu değiştir'];
    },
    88161: (e) => {
      e.exports = ['para birimi ve birim etiketleri görünürlüğünü değiştir'];
    },
    79570: (e) => {
      e.exports = ['para birimi etiketi görünürlüğünü değiştir'];
    },
    99011: (e) => {
      e.exports = ['grafik arka plan rengini değiştir'];
    },
    72458: (e) => {
      e.exports = ['grafik arka plan türünü değiştir'];
    },
    37034: (e) => {
      e.exports = ['artı işareti genişliğini değiştir'];
    },
    29951: (e) => {
      e.exports = ['artı işareti rengini değiştir'];
    },
    92027: (e) => {
      e.exports = ['artı stilini değiştir'];
    },
    50457: (e) => {
      e.exports = ['tarih biçimini dğş'];
    },
    7104: (e) => {
      e.exports = ['zaman çizelgesi etiketlerinde haftanın gününü değiştir'];
    },
    88096: (e) => {
      e.exports = ['yatay ızgara çizgilerinin rengini değiştir'];
    },
    2523: (e) => {
      e.exports = ['yatay kılavuz çizgi stilini değiştir'];
    },
    31325: (e) => {
      e.exports = ['gösterge başlıklarının görünürlüğünü değiştir'];
    },
    99774: (e) => {
      e.exports = ['gösterge değerleri görünürlüğünü değiştir'];
    },
    96162: (e) => {
      e.exports = ['gösterge argümanlarının görünürlüğünü değiştir'];
    },
    59820: (e) => {
      e.exports = [
        'göstergeleri ve finansal isim etiketlerinin görünürlüğünü değiştir',
      ];
    },
    90512: (e) => {
      e.exports = [
        'göstergeleri ve finansal değer etiketlerinin görünürlüğünü değiştir',
      ];
    },
    97956: (e) => {
      e.exports = ['efsane arka plan şeffaflığını değiştir'];
    },
    61061: (e) => {
      e.exports = ['efsane arka plan görünürlüğünü değiştir'];
    },
    37730: (e) => {
      e.exports = ['bölme düğmelerinin görünürlüğünü değiştir'];
    },
    89032: (e) => {
      e.exports = ['bölme ayırıcıların rengini değiştir'];
    },
    35636: (e) => {
      e.exports = ['sağ kenar boşluğunu değiştir'];
    },
    66601: (e) => {
      e.exports = 'change right margin percentage';
    },
    25616: (e) => {
      e.exports = ['sembol filigran rengini değiştir'];
    },
    87159: (e) => {
      e.exports = ['sembol filigran görünürlüğünü değiştir'];
    },
    26717: (e) => {
      e.exports = ['sembol açıklama görünürlüğünü değiştir'];
    },
    6091: (e) => {
      e.exports = 'change symbol field visibility';
    },
    28741: (e) => {
      e.exports = ['sembol son değer modunu dğş'];
    },
    95071: (e) => {
      e.exports = ['sembol açıklama biçimini değiştir'];
    },
    35065: (e) => {
      e.exports = ['ölçek metin rengini değiştir'];
    },
    84382: (e) => {
      e.exports = ['ölçek yazı tipi boyutunu değiştir'];
    },
    12468: (e) => {
      e.exports = ['ölçek çizgi rengini değiştir'];
    },
    71589: (e) => {
      e.exports = ['oturumları aralık görünürlüğü değiştir'];
    },
    15035: (e) => {
      e.exports = ['oturum aralık genişliği değiştir'];
    },
    1579: (e) => {
      e.exports = ['oturum aralık rengini değiştir'];
    },
    21460: (e) => {
      e.exports = ['oturum aralık stilini değiştir'];
    },
    76991: (e) => {
      e.exports = ['saat biçimini değiştir'];
    },
    98905: (e) => {
      e.exports = ['üst kenar boşluğunu değiştir'];
    },
    7011: (e) => {
      e.exports = ['birim etiketi görünürlüğünü değiştir'];
    },
    22722: (e) => {
      e.exports = ['dikey ızgara çizgilerinin rengini değiştir'];
    },
    22867: (e) => {
      e.exports = ['dikey ızgara çizgileri stilini değiştir'];
    },
    9455: (e) => {
      e.exports = ['hacim değerleri görünürlüğünü değiştir'];
    },
  },
]);
