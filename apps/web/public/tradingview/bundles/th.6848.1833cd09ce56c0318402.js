(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (e) => {
      e.exports = ['เพิ่ม'];
    },
    53585: (e) => {
      e.exports = ['เพิ่มสีตั้งค่าด้วยตนเอง'];
    },
    81865: (e) => {
      e.exports = ['ความโปร่งใส'];
    },
    60558: (e) => {
      e.exports = ['สัตว์และธรรมชาติ'];
    },
    14232: (e) => {
      e.exports = ['กิจกรรม'];
    },
    35305: (e) => {
      e.exports = ['อาหารเครื่องดื่ม'];
    },
    49546: (e) => {
      e.exports = ['ธง'];
    },
    72302: (e) => {
      e.exports = ['วัตถุ'];
    },
    96330: (e) => {
      e.exports = ['รอยยิ้มและผู้คน'];
    },
    6878: (e) => {
      e.exports = ['สัญญาลักษณ์'];
    },
    15426: (e) => {
      e.exports = ['ที่เพิ่งใช้ล่าสุด'];
    },
    15395: (e) => {
      e.exports = ['การเดินทางและสถานที่'];
    },
    73755: (e) => {
      e.exports = ['สัญลักษณ์อีกอัน'];
    },
    16936: (e) => {
      e.exports = ['กลับไป'];
    },
    88046: (e) => {
      e.exports = ['สัญลักษณ์ของชาร์ตหลัก'];
    },
    9898: (e) => {
      e.exports = ['สิทธิ'];
    },
    20036: (e) => {
      e.exports = ['ยกเลิก'];
    },
    72171: (e) => {
      e.exports = ['ตรงกลาง'];
    },
    23398: (e) => {
      e.exports = ['เปลี่ยนสัญลักษณ์'];
    },
    94551: (e) => {
      e.exports = ['ชาร์ต'];
    },
    64498: (e) => {
      e.exports = ['แหล่งที่มาทั้งหมด'];
    },
    91757: (e) => {
      e.exports = ['ข้างล่าง'];
    },
    79852: (e) => {
      e.exports = ['พันธบัตร'];
    },
    16079: (e) => {
      e.exports = ['ไล่เฉดสี'];
    },
    42973: (e) => {
      e.exports = ['เส้นไข่ปลา'];
    },
    59317: (e) => {
      e.exports = ['เส้นประ'];
    },
    56095: (e) => {
      e.exports = ['ลดลง'];
    },
    29601: (e) => {
      e.exports = ['คำอธิบาย'];
    },
    77405: (e) => {
      e.exports = ['แนวนอน'];
    },
    46812: (e) => {
      e.exports = ['เพิ่มขึ้น'];
    },
    89298: (e) => {
      e.exports = ['สิ่งชดเชย'];
    },
    68988: (e) => {
      e.exports = ['ตกลง'];
    },
    19286: (e) => {
      e.exports = ['ซ้าย'];
    },
    76476: (e) => {
      e.exports = ['ตรงกลาง'];
    },
    29673: (e) => {
      e.exports = ['ไม่มีตลาดแลกเปลี่ยนใดๆ ตรงตามเงื่อนไขของคุณ'];
    },
    41379: (e) => {
      e.exports = ['ไม่มีสัญลักษณ์ที่ตรงกับการค้นหาของคุณ'];
    },
    55362: (e) => {
      e.exports = ['ปกติ'];
    },
    35563: (e) => {
      e.exports = ['รูปแบบตัวเลขไม่ถูกต้อง'];
    },
    19724: (e) => {
      e.exports = ['แหล่งที่มา'];
    },
    35637: (e) => {
      e.exports = ['สีเดียว'];
    },
    52298: (e) => {
      e.exports = ['ค้นหา'];
    },
    13269: (e) => {
      e.exports = ['เลือกแหล่งที่มา'];
    },
    2607: (e) => {
      e.exports = ['ค่าที่ระบุมากกว่าค่าสูงสุดของเครื่องมือ {max}'];
    },
    53669: (e) => {
      e.exports = ['ค่าที่ระบุน้อยกว่าค่าต่ำสุดของเครื่องมือ {min}'];
    },
    89053: (e) => {
      e.exports = ['สัญลักษณ์'];
    },
    48490: (e) => {
      e.exports = ['สัญลักษณ์และคำอธิบาย'];
    },
    99983: (e) => {
      e.exports = ['ค้นหาตัวย่อ'];
    },
    54336: (e) => {
      e.exports = ['เอาสีออก'];
    },
    21141: (e) => {
      e.exports = ['ขวา'];
    },
    65994: (e) => {
      e.exports = ['บน'];
    },
    92960: (e) => {
      e.exports = ['การจัดตำแหน่งตัวอักษร'];
    },
    90581: (e) => {
      e.exports = ['การเรียงตัวของตัวอักษร'];
    },
    60142: (e) => {
      e.exports = ['ความหนา'];
    },
    78019: (e) => {
      e.exports = [
        'ใช้เครื่องหมายทางคณิตศาสตร์พิเศษเพื่อแทนที่ภาพวาดที่เลือก: +,-,/,* สำหรับราคา และ +,- สำหรับดัชนีแท่ง',
      ];
    },
    44085: (e) => {
      e.exports = ['แนวตั้ง'];
    },
    87592: (e) => {
      e.exports = 'cfd';
    },
    17023: (e) => {
      e.exports = ['เปลี่ยนความทึบแสง'];
    },
    13066: (e) => {
      e.exports = ['เปลี่ยนสี'];
    },
    95657: (e) => {
      e.exports = ['เปลี่ยนความหนา'];
    },
    18567: (e) => {
      e.exports = ['เปลี่ยนคุณสมบัติ {propertyName}'];
    },
    36962: (e) => {
      e.exports = ['ปิด'];
    },
    8448: (e) => {
      e.exports = ['คริปโต'];
    },
    1328: (e) => {
      e.exports = 'dr';
    },
    76080: (e) => {
      e.exports = ['เช่น. +1'];
    },
    95166: (e) => {
      e.exports = ['ยกตัวอย่าง/2'];
    },
    88720: (e) => {
      e.exports = ['เศรษฐกิจ'];
    },
    39512: (e) => {
      e.exports = ['ฟอเร็กซ์'];
    },
    81859: (e) => {
      e.exports = ['ฟิวเจอร์ส'];
    },
    39337: (e) => {
      e.exports = ['สูง'];
    },
    91815: (e) => {
      e.exports = 'hl2';
    },
    40771: (e) => {
      e.exports = 'hlc3';
    },
    9523: (e) => {
      e.exports = 'hlcc4';
    },
    12754: (e) => {
      e.exports = ['ดัชนี'];
    },
    38071: (e) => {
      e.exports = ['ดัชนี'];
    },
    12504: (e) => {
      e.exports = 'ohlc4';
    },
    38466: (e) => {
      e.exports = ['เปิด'];
    },
    3919: (e) => {
      e.exports = ['ต่ำ'];
    },
    36931: (e) => {
      e.exports = ['หุ้น'];
    },
  },
]);
