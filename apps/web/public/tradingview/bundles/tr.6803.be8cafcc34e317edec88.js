(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6803],
  {
    22353: (e) => {
      e.exports = ['(A + Y + D + K)/4'];
    },
    94884: (e) => {
      e.exports = ['(Y + D + K)/3'];
    },
    10591: (e) => {
      e.exports = ['(Y + D)/2'];
    },
    63243: (e) => {
      e.exports = ['Önceki kapanışa göre çubuk rengi'];
    },
    72171: (e) => {
      e.exports = ['Orta'];
    },
    9994: (e) => {
      e.exports = ['Verileri temettülere göre düzelt'];
    },
    10989: (e) => {
      e.exports = ['Sözleşme değişiklikleri için ayarlama'];
    },
    70816: (e) => {
      e.exports = ['Ortalama kapanış'];
    },
    91757: (e) => {
      e.exports = ['Alt'];
    },
    50430: (e) => {
      e.exports = ['Alt çizgi'];
    },
    83760: (e) => {
      e.exports = ['Temel hat'];
    },
    72269: (e) => {
      e.exports = ['Kenarlar'];
    },
    7445: (e) => {
      e.exports = ['Temel seviye'];
    },
    47586: (e) => {
      e.exports = ['Alım ve Satım'];
    },
    39667: (e) => {
      e.exports = ['Düşüş Çubukları'];
    },
    87151: (e) => {
      e.exports = ['Düşüş Rengi'];
    },
    81285: (e) => {
      e.exports = ['Veri değişimi'];
    },
    4329: (e) => {
      e.exports = ['Varsayılan'];
    },
    86846: (e) => {
      e.exports = ['Doldur'];
    },
    58747: (e) => {
      e.exports = ['Üst alanı doldurun'];
    },
    11157: (e) => {
      e.exports = ['Alt alanı doldur'];
    },
    86953: (e) => {
      e.exports = ['HLC Barları'];
    },
    77405: (e) => {
      e.exports = ['Yatay'];
    },
    39292: (e) => {
      e.exports = ['Yüksek ve düşük'];
    },
    15107: (e) => {
      e.exports = ['Son'];
    },
    19286: (e) => {
      e.exports = ['Sol'];
    },
    76476: (e) => {
      e.exports = ['Orta'];
    },
    27879: (e) => {
      e.exports = ['Basit'];
    },
    2391: (e) => {
      e.exports = ['Önlem'];
    },
    6350: (e) => {
      e.exports = ['Piyasa Öncesi/Sonrası'];
    },
    62521: (e) => {
      e.exports = ['Piyasa saatleri öncesi/sonrası arka planı'];
    },
    73947: (e) => {
      e.exports = ['Hassasiyet'];
    },
    8094: (e) => {
      e.exports = ['Önceki Gün Kapanışı'];
    },
    77986: (e) => {
      e.exports = ['Fiyat çizgileri'];
    },
    24248: (e) => {
      e.exports = ['Fiyat Kaynağı'];
    },
    94089: (e) => {
      e.exports = ['Artış çubuğun projeksiyonu'];
    },
    5704: (e) => {
      e.exports = ['Düşüş çubuğun projeksiyonu'];
    },
    29881: (e) => {
      e.exports = [
        'Fiyat ölçeğinde gerçek fiyatlar (Heikin-Ashi fiyatı yerine)',
      ];
    },
    21141: (e) => {
      e.exports = ['Sağ'];
    },
    44673: (e) => {
      e.exports = ['İşaretçiler İle'];
    },
    26458: (e) => {
      e.exports = ['Fitil'];
    },
    65994: (e) => {
      e.exports = ['Üst'];
    },
    57417: (e) => {
      e.exports = ['Üst çizgi'];
    },
    92960: (e) => {
      e.exports = ['Metin hizalama'];
    },
    90581: (e) => {
      e.exports = ['Metin yönü'];
    },
    55314: (e) => {
      e.exports = ['İnce çubuklar'];
    },
    87492: (e) => {
      e.exports = ['Saat Dilimi'];
    },
    58416: (e) => {
      e.exports = ['Tip'];
    },
    5536: (e) => {
      e.exports = ['Artış Rengi'];
    },
    83610: (e) => {
      e.exports = ['Artış çubukları'];
    },
    23500: (e) => {
      e.exports = ['Ödemeyi günlük aralıklarla yakın olarak kullanma'];
    },
    44085: (e) => {
      e.exports = ['Dikey'];
    },
    30792: (e) => {
      e.exports = ['mum'];
    },
    55740: (e) => {
      e.exports = ['HLC çubuk dğş'];
    },
    90168: (e) => {
      e.exports = ['ortalama kapanış fiyatı çizgi genişliğini değiştir'];
    },
    30385: (e) => {
      e.exports = ['ortalama kapanış fiyatı çizgi rengini değiştir'];
    },
    97008: (e) => {
      e.exports = ['alan dolgu rengini değiştir'];
    },
    6610: (e) => {
      e.exports = ['alan çizgi genişliğini değiştir'];
    },
    661: (e) => {
      e.exports = ['alan çizgi rengini değiştir'];
    },
    1316: (e) => {
      e.exports = ['alan fiyat kaynağını değiştir'];
    },
    29180: (e) => {
      e.exports = ['al satırı rengini dğş'];
    },
    31547: (e) => {
      e.exports = ['taban seviyesini değiştir'];
    },
    4164: (e) => {
      e.exports = ['taban çizgisi alt çizgi rengini değiştir'];
    },
    38990: (e) => {
      e.exports = ['taban çizgisi alt çizgi genişliğini değiştir'];
    },
    73163: (e) => {
      e.exports = ['taban çizgisi dolgu alt alanı rengini değiştir'];
    },
    12673: (e) => {
      e.exports = ['taban çizgisi dolgu üst alan rengini değiştir'];
    },
    56819: (e) => {
      e.exports = ['temel fiyat kaynağını değiştir'];
    },
    68621: (e) => {
      e.exports = ['taban çizgisi üst çizgi rengini değiştir'];
    },
    35339: (e) => {
      e.exports = ['taban çizgisi üst çizgi genişliğini değiştir'];
    },
    76804: (e) => {
      e.exports = ['çubuğun üst rengini dğş'];
    },
    71816: (e) => {
      e.exports = ['çubuğun alt rengini değiştir'];
    },
    36703: (e) => {
      e.exports = ['sat satırı rengini dğş'];
    },
    29353: (e) => {
      e.exports = ['önceki kapanışa göre renk çubuklarını dğş'];
    },
    85709: (e) => {
      e.exports = ['yukarı sütun rengini değiştir'];
    },
    12155: (e) => {
      e.exports = ['aşağı sütun rengini değiştir'];
    },
    66890: (e) => {
      e.exports = ['sütun fiyat kaynağını değiştir'];
    },
    71809: (e) => {
      e.exports = ['ondalık basamakları dğş'];
    },
    31317: (e) => {
      e.exports = ['uzatılmış saat rengini dğş'];
    },
    60944: (e) => {
      e.exports = ['yüksek ve düşük fiyat çizgilerinin rengini değiştir'];
    },
    83708: (e) => {
      e.exports = ['yüksek ve düşük fiyat çizgilerinin genişliğini değiştir'];
    },
    81080: (e) => {
      e.exports = ['yüksek-düşük orta rengi dğş'];
    },
    30033: (e) => {
      e.exports = ['yüksek-düşük gövde görünürlüğünü değiştir'];
    },
    76885: (e) => {
      e.exports = ['yüksek-düşük kenar rengi dğş'];
    },
    79236: (e) => {
      e.exports = ['yüksek-alt sınır görünürlüğünü dğş'];
    },
    42981: (e) => {
      e.exports = ['yüksek-düşük etiket görünümü dğş'];
    },
    31937: (e) => {
      e.exports = ['yüksek-düşük etiket rengi dğş'];
    },
    87828: (e) => {
      e.exports = ['çizgi rengini değiştir'];
    },
    17119: (e) => {
      e.exports = ['çizgi fiyatı kaynağını değiştir'];
    },
    69125: (e) => {
      e.exports = ['çizgi genişliğini değiştir'];
    },
    70054: (e) => {
      e.exports = ['satır türünü dğş'];
    },
    49973: (e) => {
      e.exports = ['pazar sonrası rengini dğş'];
    },
    5969: (e) => {
      e.exports = ['piyasa sonrası çizgi rengini dğş'];
    },
    50393: (e) => {
      e.exports = [
        'piyasa öncesi/sonrası fiyat çizgilerinin görünürlüğünü değiştir',
      ];
    },
    46257: (e) => {
      e.exports = ['piyasa öncesi rengini dğş'];
    },
    60852: (e) => {
      e.exports = ['piyasa öncesi çizgi rengini dğş'];
    },
    91183: (e) => {
      e.exports = ['önceki kapanış fiyatı satırı rengi dğş'];
    },
    87631: (e) => {
      e.exports = ['önceki kapanış fiyat satırı genişliğini dğş'];
    },
    77640: (e) => {
      e.exports = ['fiyat satırı rengini dğş'];
    },
    97322: (e) => {
      e.exports = ['fiyat satırı genişliğini dğş'];
    },
    28143: (e) => {
      e.exports = ['ince çubuk aralığı dğş'];
    },
    75986: (e) => {
      e.exports = ['renko fitil aşağı rengi dğş'];
    },
    7747: (e) => {
      e.exports = ['renko fitil rengi değiştir'];
    },
    9473: (e) => {
      e.exports = ['renko fitil görünümünü dğş'];
    },
    39783: (e) => {
      e.exports = [
        'fiyat ölçeğinde gerçek fiyatların görüntüsünü dğş (Heiken-Ashi fiyatı yerine)',
      ];
    },
    72886: (e) => {
      e.exports = ['ince çubukları değiştir'];
    },
    5464: (e) => {
      e.exports = ['{candleType} yukarı kenar rengi dğş'];
    },
    61118: (e) => {
      e.exports = ['{candleType} rengi değiştir'];
    },
    60164: (e) => {
      e.exports = ['{candleType} fitil rengi dğş'];
    },
    45543: (e) => {
      e.exports = ['{candleType} fitil rengini dğş'];
    },
    39987: (e) => {
      e.exports = ['{candleType} fitil görünürlüğünü dğş'];
    },
    47202: (e) => {
      e.exports = ['{candleType} gövde görünümü dğş'];
    },
    23986: (e) => {
      e.exports = ['{candleType} sınır görünürlüğünü dğş'];
    },
    92330: (e) => {
      e.exports = ['{candleType} alt kenar rengi dğş'];
    },
    36320: (e) => {
      e.exports = ['{candleType} alt rengi dğş'];
    },
    79088: (e) => {
      e.exports = ['{chartType} kenarlık çubuğu rengi dğş'];
    },
    11107: (e) => {
      e.exports = ['{chartType} kenarlık çubuğu rengi dğş'];
    },
    85503: (e) => {
      e.exports = ['{chartType} aşağı rengi dğş'];
    },
    61250: (e) => {
      e.exports = ['{chartType} projeksiyon sınır çubuğu rengi dğş'];
    },
    18465: (e) => {
      e.exports = ['{chartType} projeksiyon çubuğu rengi dğş'];
    },
    50453: (e) => {
      e.exports = ['{chartType} projeksiyon çubuğunun rengi dğş'];
    },
    59414: (e) => {
      e.exports = ['{chartType} rengi değiştir'];
    },
    21547: (e) => {
      e.exports = ['{inputName} özelliğini dğş'];
    },
    42390: (e) => {
      e.exports = ['temettü verilerini ayarlayın'];
    },
    99511: (e) => {
      e.exports = ['sözleşme değişikliklerini ayarla'];
    },
    75165: (e) => {
      e.exports = ['İçi Boş Mumlar'];
    },
    18995: (e) => {
      e.exports = ['aralık', 'aralık'];
    },
    47500: (e) => {
      e.exports = 'renko';
    },
    98402: (e) => {
      e.exports = ['yerleşimi günlük aralıklara yakın olarak kullan'];
    },
  },
]);
