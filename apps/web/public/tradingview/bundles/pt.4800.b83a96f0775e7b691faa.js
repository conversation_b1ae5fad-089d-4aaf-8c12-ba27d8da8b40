(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4800],
  {
    91282: (e) => {
      e.exports = ['#1 (barra)'];
    },
    1961: (e) => {
      e.exports = ['#1 (preço)'];
    },
    12706: (e) => {
      e.exports = ['#1 (preço, barra)'];
    },
    92195: (e) => {
      e.exports = ['#1 (% posição vertical, barra)'];
    },
    66187: (e) => {
      e.exports = ['Mediana'];
    },
    5066: (e) => {
      e.exports = '%';
    },
    89795: (e) => {
      e.exports = ['Anti-horário'];
    },
    43809: (e) => {
      e.exports = ['Coeficientes como porcentagem'];
    },
    40054: (e) => {
      e.exports = ['Cor'];
    },
    47737: (e) => {
      e.exports = ['Modo de estatísticas compactas'];
    },
    76655: (e) => {
      e.exports = ['Dinheiro'];
    },
    72171: (e) => {
      e.exports = ['Centro'];
    },
    99120: (e) => {
      e.exports = ['Canal'];
    },
    36150: (e) => {
      e.exports = ['Ângulo'];
    },
    38280: (e) => {
      e.exports = ['Ângulos'];
    },
    95264: (e) => {
      e.exports = ['Tamanho da Conta'];
    },
    85160: (e) => {
      e.exports = ['Sempre mostrar informações'];
    },
    54189: (e) => {
      e.exports = ['Arcos'];
    },
    34674: (e) => {
      e.exports = ['HL médio em minticks'];
    },
    91757: (e) => {
      e.exports = ['Em baixo'];
    },
    17608: (e) => {
      e.exports = ['Legendas Inferiores'];
    },
    48848: (e) => {
      e.exports = ['Contorno'];
    },
    72269: (e) => {
      e.exports = ['Contorno'];
    },
    27331: (e) => {
      e.exports = ['Fundo'];
    },
    19949: (e) => {
      e.exports = ['Intervalo de barras'];
    },
    81260: (e) => {
      e.exports = ['Grade'];
    },
    67114: (e) => {
      e.exports = ['Intervalo de data/hora'];
    },
    75460: (e) => {
      e.exports = ['Distância'];
    },
    46211: (e) => {
      e.exports = 'Emoji pin';
    },
    46001: (e) => {
      e.exports = ['Preço de entrada'];
    },
    1220: (e) => {
      e.exports = ['Estender'];
    },
    71116: (e) => {
      e.exports = ['Fundo ampliado'];
    },
    45809: (e) => {
      e.exports = ['Estender à esquerda'];
    },
    25892: (e) => {
      e.exports = ['Estender Linha à Esquerda'];
    },
    3304: (e) => {
      e.exports = ['Estender as linhas à esquerda'];
    },
    83095: (e) => {
      e.exports = ['Estender Linhas à Direita'];
    },
    14025: (e) => {
      e.exports = ['Estender à direita'];
    },
    74395: (e) => {
      e.exports = ['Estender Linha à Direita'];
    },
    85197: (e) => {
      e.exports = ['Topo ampliado'];
    },
    17006: (e) => {
      e.exports = ['Tamanho da fonte'];
    },
    31343: (e) => {
      e.exports = ['Texto falha'];
    },
    28565: (e) => {
      e.exports = ['Fundo falha'];
    },
    87931: (e) => {
      e.exports = ['Leques'];
    },
    39836: (e) => {
      e.exports = ['Níveis de Fib com base na escala log'];
    },
    10578: (e) => {
      e.exports = ['Círculos completos'];
    },
    25264: (e) => {
      e.exports = ['Barras máx./mín.'];
    },
    77405: (e) => {
      e.exports = 'Horizontal';
    },
    66049: (e) => {
      e.exports = ['Barras OC'];
    },
    27531: (e) => {
      e.exports = ['Tamanho do lote'];
    },
    85206: (e) => {
      e.exports = ['Legenda'];
    },
    75332: (e) => {
      e.exports = ['Borda da legenda'];
    },
    14773: (e) => {
      e.exports = ['Fundo da legenda'];
    },
    37126: (e) => {
      e.exports = ['Texto da legenda'];
    },
    79106: (e) => {
      e.exports = ['Níveis'];
    },
    95610: (e) => {
      e.exports = ['Linhas de nível'];
    },
    19286: (e) => {
      e.exports = ['Esquerda'];
    },
    79307: (e) => {
      e.exports = ['Legendas à Esquerda'];
    },
    49286: (e) => {
      e.exports = ['Linha - MáxMín/2'];
    },
    17676: (e) => {
      e.exports = ['Linha - Abertura'];
    },
    47669: (e) => {
      e.exports = ['Linha - Fechamento'];
    },
    71899: (e) => {
      e.exports = ['Linha - Máximo'];
    },
    83394: (e) => {
      e.exports = ['Linha - Mínimo'];
    },
    60489: (e) => {
      e.exports = ['Cor da linha'];
    },
    53889: (e) => {
      e.exports = ['Modo'];
    },
    76476: (e) => {
      e.exports = ['No meio'];
    },
    24510: (e) => {
      e.exports = ['Ponto intermediário'];
    },
    22213: (e) => {
      e.exports = ['Fundo da Fonte'];
    },
    15500: (e) => {
      e.exports = ['Borda Fonte'];
    },
    79238: (e) => {
      e.exports = ['Texto Fonte'];
    },
    37249: (e) => {
      e.exports = ['Estatísticas'];
    },
    28712: (e) => {
      e.exports = ['Posição das Informações'];
    },
    50948: (e) => {
      e.exports = ['Cor de Stop'];
    },
    56119: (e) => {
      e.exports = ['Nível de stop'];
    },
    69835: (e) => {
      e.exports = ['Texto sucesso'];
    },
    91141: (e) => {
      e.exports = ['Fundo sucesso'];
    },
    650: (e) => {
      e.exports = ['Porcentuais'];
    },
    25684: (e) => {
      e.exports = ['Preço'];
    },
    23675: (e) => {
      e.exports = ['Legenda de Preços'];
    },
    75675: (e) => {
      e.exports = ['Legendas de preços'];
    },
    16103: (e) => {
      e.exports = ['Níveis de preços'];
    },
    46964: (e) => {
      e.exports = ['Intervalo de Preços'];
    },
    59771: (e) => {
      e.exports = ['Razão Preço/Barra'];
    },
    29072: (e) => {
      e.exports = ['Preços'];
    },
    2635: (e) => {
      e.exports = ['Nível de lucro'];
    },
    33886: (e) => {
      e.exports = ['Intervalos e proporção'];
    },
    24186: (e) => {
      e.exports = ['Reverter'];
    },
    21141: (e) => {
      e.exports = ['Direita'];
    },
    91367: (e) => {
      e.exports = ['Legendas a Direita'];
    },
    63833: (e) => {
      e.exports = ['Risco'];
    },
    95545: (e) => {
      e.exports = ['Ondas'];
    },
    26458: (e) => {
      e.exports = ['Pavio'];
    },
    65994: (e) => {
      e.exports = ['Em cima'];
    },
    10209: (e) => {
      e.exports = ['Legendas Superiores'];
    },
    98001: (e) => {
      e.exports = ['Fundo alvo'];
    },
    89258: (e) => {
      e.exports = ['Borda alvo'];
    },
    45302: (e) => {
      e.exports = ['Cor do objetivo:'];
    },
    74289: (e) => {
      e.exports = ['Texto alvo'];
    },
    17932: (e) => {
      e.exports = ['Disposição do texto'];
    },
    92960: (e) => {
      e.exports = ['Alinhamento do texto'];
    },
    90581: (e) => {
      e.exports = ['Orientação do Texto'];
    },
    55325: (e) => {
      e.exports = ['Legenda de horário'];
    },
    77838: (e) => {
      e.exports = ['Níveis de tempo'];
    },
    2295: (e) => {
      e.exports = ['Transparência'];
    },
    4372: (e) => {
      e.exports = ['Linha de Tendência'];
    },
    12374: (e) => {
      e.exports = ['Usar uma cor'];
    },
    91322: (e) => {
      e.exports = ['Valores'];
    },
    25227: (e) => {
      e.exports = ['Variância'];
    },
    44085: (e) => {
      e.exports = 'Vertical';
    },
    1670: (e) => {
      e.exports = ['alterar ângulo'];
    },
    54119: (e) => {
      e.exports = ['alterar cor da seta'];
    },
    72080: (e) => {
      e.exports = ['alterar cor da bandeira'];
    },
    98905: (e) => {
      e.exports = ['alterar margem superior'];
    },
    11049: (e) => {
      e.exports = ['alterar posição vertical da coordenada Y'];
    },
    31804: (e) => {
      e.exports = ['alterar {title} no sentido anti-horário'];
    },
    99128: (e) => {
      e.exports = [
        'alterar a visibilidade das porcentagens dos coeficientes de {title}',
      ];
    },
    20216: (e) => {
      e.exports = ['alterar cor de {title}'];
    },
    35435: (e) => {
      e.exports = ['alterar modo estatísticas compactas de {title}'];
    },
    550: (e) => {
      e.exports = ['alterar cor da borda da vela para cima de {title}'];
    },
    22313: (e) => {
      e.exports = ['alterar visibilidade da borda da vela de {title}'];
    },
    7373: (e) => {
      e.exports = ['alterar cor da borda da vela para baixo de {title}'];
    },
    38742: (e) => {
      e.exports = ['alterar cor da vela para baixo de {title}'];
    },
    42273: (e) => {
      e.exports = ['alterar cor da vela para cima de {title}'];
    },
    76054: (e) => {
      e.exports = ['alterar a cor de pavio da vela de {title}'];
    },
    27029: (e) => {
      e.exports = ['alterar visibilidade do pavio da vela de {title}'];
    },
    45537: (e) => {
      e.exports = ['alterar a visibilidade da distância de {title}'];
    },
    31775: (e) => {
      e.exports = ['alterar tamanho da conta de {title}'];
    },
    37913: (e) => {
      e.exports = ['alterar exibir sempre estatística de {title}'];
    },
    15521: (e) => {
      e.exports = ['alterar a cor de todas as linhas de {title}'];
    },
    17466: (e) => {
      e.exports = ['alterar a cor das linhas de arcos {index} de {title}'];
    },
    72307: (e) => {
      e.exports = ['alterar largura das linhas de arcos {index} de {title}'];
    },
    13853: (e) => {
      e.exports = [
        'alterar visibilidade das linhas de arcos {index} de {title}',
      ];
    },
    78680: (e) => {
      e.exports = ['alterar valor médio HL de {title}'];
    },
    15802: (e) => {
      e.exports = ['alterar visibilidade da legenda de {title} da base'];
    },
    36438: (e) => {
      e.exports = ['alterar a transparência do fundo de {title}'];
    },
    82465: (e) => {
      e.exports = ['alterar visibilidadde do fundo de {title}'];
    },
    75312: (e) => {
      e.exports = ['alterar cor do fundo de {title}'];
    },
    39651: (e) => {
      e.exports = ['alterar cor do fundo 1 de {title}'];
    },
    78177: (e) => {
      e.exports = ['alterar cor do fundo 2 de {title}'];
    },
    42746: (e) => {
      e.exports = ['alterar a visibilidade do intervalo de barras de {title}'];
    },
    53770: (e) => {
      e.exports = ['alterar a visibilidade da grade de {title}'];
    },
    29145: (e) => {
      e.exports = ['alterar a cor da linha da grade de {title}'];
    },
    64949: (e) => {
      e.exports = ['alterar estilo da linha da grade de {title}'];
    },
    93548: (e) => {
      e.exports = ['alterar largura da grade de {title}'];
    },
    15485: (e) => {
      e.exports = ['alterar visibilidade do intervalo de data/hora de {title}'];
    },
    3400: (e) => {
      e.exports = ['alterar o ângulo de {title}'];
    },
    91534: (e) => {
      e.exports = ['alterar visibilidade da distância do {title}'];
    },
    65056: (e) => {
      e.exports = ['alterar emoji de {title}'];
    },
    65899: (e) => {
      e.exports = ['alterar visibilidade do emoji de {title}'];
    },
    59354: (e) => {
      e.exports = ['alterar o preço de entrada de {title}'];
    },
    1447: (e) => {
      e.exports = ['alterar base ampliada de {title}'];
    },
    15258: (e) => {
      e.exports = ['alterar {title} ampliado a esquerda'];
    },
    896: (e) => {
      e.exports = ['alterar topo ampliado de {title}'];
    },
    3708: (e) => {
      e.exports = ['alterar extensão a esquerda de {title}'];
    },
    45719: (e) => {
      e.exports = ['alterar extensão a direita de {title}'];
    },
    86647: (e) => {
      e.exports = ['alterar a extensão de {title}'];
    },
    3156: (e) => {
      e.exports = ['alterar a cor do texto de insucesso de {title}'];
    },
    49885: (e) => {
      e.exports = ['alterar a cor do fundo de insucesso de {title}'];
    },
    89126: (e) => {
      e.exports = ['alterar visibilidade da linha {index} do leque {title}'];
    },
    30016: (e) => {
      e.exports = ['alterar largura da linha {index} do leque de {title}'];
    },
    36147: (e) => {
      e.exports = ['alterar cor da linha {index} do leque de {title}'];
    },
    78142: (e) => {
      e.exports = ['alterar visibilidade do leque de {title}'];
    },
    79467: (e) => {
      e.exports = ['alterar as cores das linhas dos leques de {title}'];
    },
    45739: (e) => {
      e.exports = ['alterar níveis de fib na escala log de {title}'];
    },
    99670: (e) => {
      e.exports = ['alterar {title} invertido'];
    },
    35165: (e) => {
      e.exports = ['alterar visibilidade dos círculos completos de {title}'];
    },
    48983: (e) => {
      e.exports = ['alterar cor da imagem do fundo de {title}'];
    },
    45025: (e) => {
      e.exports = ['alterar tamanho do lote de {title}'];
    },
    81170: (e) => {
      e.exports = ['alterar alinhamento das legendas de {title}'];
    },
    22775: (e) => {
      e.exports = ['mudar o tamanho da fonte das legendas de {title}'];
    },
    24338: (e) => {
      e.exports = ['alterar visibilidade das legendas de {title}'];
    },
    32891: (e) => {
      e.exports = [
        'alterar o coeficiente da linha do nível {index} de {title}',
      ];
    },
    85551: (e) => {
      e.exports = ['alterar a cor da linha do nível {index} de {title}'];
    },
    47840: (e) => {
      e.exports = ['alterar estilo da linha do nível {index} de {title}'];
    },
    45463: (e) => {
      e.exports = [
        'alterar o nível de {index} de visibilidade da linha de {title}',
      ];
    },
    90098: (e) => {
      e.exports = ['alterar a largura de linha do nível {index} de  {title}'];
    },
    26710: (e) => {
      e.exports = ['alterar os níveis de visibilidade de {title}'];
    },
    2359: (e) => {
      e.exports = ['alterar a visibilidade das legendas de {title} a esquerda'];
    },
    44643: (e) => {
      e.exports = ['alterar a largura da linha de {title}'];
    },
    20563: (e) => {
      e.exports = ['alterar cor da linha de {title}'];
    },
    66982: (e) => {
      e.exports = ['alterar o estilo da linha de {title}'];
    },
    94441: (e) => {
      e.exports = ['alterar modo de {title}'];
    },
    89996: (e) => {
      e.exports = ['alterar a visibilidade do ponto intermediário de {title}'];
    },
    36618: (e) => {
      e.exports = ['alterar {title} espelhado'];
    },
    18544: (e) => {
      e.exports = ['alterar cor do fundo da fonte de {title}'];
    },
    48035: (e) => {
      e.exports = ['alterar cor da borda da fonte de {title}'];
    },
    42286: (e) => {
      e.exports = ['alterar cor do texto da fonte de {title}'];
    },
    588: (e) => {
      e.exports = ['alterar posição das estatísticas de {title}'];
    },
    54659: (e) => {
      e.exports = ['alterar cor de stop de {title}'];
    },
    89182: (e) => {
      e.exports = ['alterar nível de stop de {title}'];
    },
    82224: (e) => {
      e.exports = ['alterar preço de stop de {title}'];
    },
    88383: (e) => {
      e.exports = ['alterar a cor do texto de sucesso de {title}'];
    },
    26967: (e) => {
      e.exports = ['alterar a cor do fundo de sucesso de {title}'];
    },
    45936: (e) => {
      e.exports = ['alterar a visibilidade da legenda de preço de {title}'];
    },
    88577: (e) => {
      e.exports = ['alterar a visibilidade da legenda de preços de {title}'];
    },
    47045: (e) => {
      e.exports = ['alterar a visibilidade do intervalo de preço de {title}'];
    },
    56175: (e) => {
      e.exports = ['alterar visibilidade do preço de {title}'];
    },
    44539: (e) => {
      e.exports = ['alterar o nível de lucro de {title}'];
    },
    41646: (e) => {
      e.exports = ['alterar preço de lucros de {title}'];
    },
    52877: (e) => {
      e.exports = ['alterar reversão de {title}'];
    },
    16598: (e) => {
      e.exports = ['alterar a visibilidade das legendas de {title} a direita'];
    },
    31553: (e) => {
      e.exports = ['alterar risco de {title}'];
    },
    40344: (e) => {
      e.exports = ['alterar modo de exibição de {title}'];
    },
    73137: (e) => {
      e.exports = ['alterar visibilidade da legenda de {title} do topo'];
    },
    52387: (e) => {
      e.exports = ['alterar cor do fundo do alvo de {title}'];
    },
    6921: (e) => {
      e.exports = ['alterar cor da borda do alvo de {title}'];
    },
    97573: (e) => {
      e.exports = ['alterar cor de alvo de {title}'];
    },
    27634: (e) => {
      e.exports = ['alterar cor do texto alvo de {title}'];
    },
    33822: (e) => {
      e.exports = ['alterar visibilidade da legenda de horário de {title}'];
    },
    84321: (e) => {
      e.exports = ['alterar transparência de {title}'];
    },
    12355: (e) => {
      e.exports = ['alterar valor de variância de {title}'];
    },
    25937: (e) => {
      e.exports = ['alterar alinhamento vertical das legendas de {toolName}'];
    },
    46991: (e) => {
      e.exports = ['alterar alinhamento horizontal das legendas de {toolName}'];
    },
    73080: (e) => {
      e.exports = ['alterar a direção das legendas de {toolName}'];
    },
    24272: (e) => {
      e.exports = ['alterar visibilidade da linha de {toolName}'];
    },
    46404: (e) => {
      e.exports = ['alterar largura da linha de {toolName}'];
    },
    50265: (e) => {
      e.exports = ['alterar cor da linha de {toolName}'];
    },
    72781: (e) => {
      e.exports = [
        'alterar largura da linha de {toolName} estendida à esquerda',
      ];
    },
    84613: (e) => {
      e.exports = [
        'alterar largura da linha de {toolName} estendida à direita',
      ];
    },
    62603: (e) => {
      e.exports = [
        'alterar largura da ponta da linha de {toolName} à esquerda',
      ];
    },
    62412: (e) => {
      e.exports = ['alterar largura da ponta da linha de {toolName} à direita'];
    },
    35422: (e) => {
      e.exports = ['alterar estilo da linha de {toolName}'];
    },
    77690: (e) => {
      e.exports = ['alterar texto de {toolName}'];
    },
    69871: (e) => {
      e.exports = ['alterar a visibilidade do texto de {toolName}'];
    },
    25878: (e) => {
      e.exports = ['alterar a disposição do texto de {toolName}'];
    },
    91832: (e) => {
      e.exports = ['alterar a cor do fundo do texto de {toolName}'];
    },
    18610: (e) => {
      e.exports = ['alterar visibilidade do fundo do texto de {toolName}'];
    },
    44755: (e) => {
      e.exports = ['alterar a cor da borda do texto de {toolName}'];
    },
    6324: (e) => {
      e.exports = ['alterar a largura da borda do texto de {toolName}'];
    },
    45529: (e) => {
      e.exports = ['alterar a visibilidade da borda do texto de {toolName}'];
    },
    6500: (e) => {
      e.exports = ['alterar cor do texto de {toolName}'];
    },
    51614: (e) => {
      e.exports = ['altear a fonte do texto em negrito de {toolName}'];
    },
    18572: (e) => {
      e.exports = ['alterar a fonte do texto em itálico de {toolName}'];
    },
    48382: (e) => {
      e.exports = ['mudar tamanho da fonte do texto de {toolName}'];
    },
    21926: (e) => {
      e.exports = ['cor do fundo'];
    },
    52241: (e) => {
      e.exports = ['fundos preenchidos'];
    },
    70607: (e) => {
      e.exports = ['cor das linhas'];
    },
    41075: (e) => {
      e.exports = ['estilo das linhas'];
    },
    73043: (e) => {
      e.exports = ['largura das linhas'];
    },
    41437: (e) => {
      e.exports = ['Cor do texto'];
    },
  },
]);
