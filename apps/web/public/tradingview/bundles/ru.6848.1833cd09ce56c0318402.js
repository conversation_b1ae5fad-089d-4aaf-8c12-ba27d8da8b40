(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (e) => {
      e.exports = ['Добавить'];
    },
    53585: (e) => {
      e.exports = ['Добавить свой цвет'];
    },
    81865: (e) => {
      e.exports = ['Прозрачность'];
    },
    60558: (e) => {
      e.exports = ['животные и природа'];
    },
    14232: (e) => {
      e.exports = ['спорт и активность'];
    },
    35305: (e) => {
      e.exports = ['еда и напитки'];
    },
    49546: (e) => {
      e.exports = ['флаги'];
    },
    72302: (e) => {
      e.exports = ['предметы'];
    },
    96330: (e) => {
      e.exports = ['эмоции и люди'];
    },
    6878: (e) => {
      e.exports = ['символы'];
    },
    15426: (e) => {
      e.exports = ['недавние'];
    },
    15395: (e) => {
      e.exports = ['путешествия'];
    },
    73755: (e) => {
      e.exports = ['Другой символ'];
    },
    16936: (e) => {
      e.exports = ['Назад'];
    },
    88046: (e) => {
      e.exports = ['Главный символ графика'];
    },
    9898: (e) => {
      e.exports = ['Право на акцию'];
    },
    20036: (e) => {
      e.exports = ['Отмена'];
    },
    72171: (e) => {
      e.exports = ['По центру'];
    },
    23398: (e) => {
      e.exports = ['Сменить инструмент'];
    },
    94551: (e) => {
      e.exports = ['График'];
    },
    64498: (e) => {
      e.exports = ['Все источники'];
    },
    91757: (e) => {
      e.exports = ['Снизу'];
    },
    79852: (e) => {
      e.exports = ['Облигации'];
    },
    16079: (e) => {
      e.exports = ['Градиент'];
    },
    42973: (e) => {
      e.exports = ['Точечный пунктир'];
    },
    59317: (e) => {
      e.exports = ['Штриховой пунктир'];
    },
    56095: (e) => {
      e.exports = ['Уменьшить'];
    },
    29601: (e) => {
      e.exports = ['Описание'];
    },
    77405: (e) => {
      e.exports = ['Горизонтальная'];
    },
    46812: (e) => {
      e.exports = ['Увеличить'];
    },
    89298: (e) => {
      e.exports = ['Смещение'];
    },
    68988: (e) => {
      e.exports = ['Ок'];
    },
    19286: (e) => {
      e.exports = ['Слева'];
    },
    76476: (e) => {
      e.exports = ['По центру'];
    },
    29673: (e) => {
      e.exports = ['Нет подходящих бирж'];
    },
    41379: (e) => {
      e.exports = ['Нет подходящих символов'];
    },
    55362: (e) => {
      e.exports = ['Обычный'];
    },
    35563: (e) => {
      e.exports = ['Неверный формат числа.'];
    },
    19724: (e) => {
      e.exports = ['Источники'];
    },
    35637: (e) => {
      e.exports = ['Сплошной'];
    },
    52298: (e) => {
      e.exports = ['Поиск'];
    },
    13269: (e) => {
      e.exports = ['Выбрать источник'];
    },
    2607: (e) => {
      e.exports = [
        'Указанное значение больше допустимого максимума для {max}.',
      ];
    },
    53669: (e) => {
      e.exports = ['Указанное значение меньше допустимого минимума для {min}.'];
    },
    89053: (e) => {
      e.exports = ['Инструмент'];
    },
    48490: (e) => {
      e.exports = ['Инструмент и описание'];
    },
    99983: (e) => {
      e.exports = ['Поиск инструментов'];
    },
    54336: (e) => {
      e.exports = ['Удалить цвет'];
    },
    21141: (e) => {
      e.exports = ['Справа'];
    },
    65994: (e) => {
      e.exports = ['Сверху'];
    },
    92960: (e) => {
      e.exports = ['Выравнивание текста'];
    },
    90581: (e) => {
      e.exports = ['Ориентация текста'];
    },
    60142: (e) => {
      e.exports = ['Толщина'];
    },
    78019: (e) => {
      e.exports = [
        'Используйте специальные символы, чтобы перемещать выбранные объекты рисования: +, -, /, * для цены и +, - для индекса бара.',
      ];
    },
    44085: (e) => {
      e.exports = ['Вертикальная'];
    },
    87592: (e) => {
      e.exports = 'cfd';
    },
    17023: (e) => {
      e.exports = ['изменение прозрачности'];
    },
    13066: (e) => {
      e.exports = ['изменение цвета'];
    },
    95657: (e) => {
      e.exports = ['изменение толщины'];
    },
    18567: (e) => {
      e.exports = ['изменение свойств {propertyName}'];
    },
    36962: (e) => {
      e.exports = ['закр'];
    },
    8448: (e) => {
      e.exports = ['криптовалюты'];
    },
    1328: (e) => {
      e.exports = ['Депоз. расписки'];
    },
    76080: (e) => {
      e.exports = ['н-р, +1'];
    },
    95166: (e) => {
      e.exports = ['н-р, /2'];
    },
    88720: (e) => {
      e.exports = ['экономические данные'];
    },
    39512: (e) => {
      e.exports = ['форекс'];
    },
    81859: (e) => {
      e.exports = ['фьючерсы'];
    },
    39337: (e) => {
      e.exports = ['макс.'];
    },
    91815: (e) => {
      e.exports = ['МаксМин2'];
    },
    40771: (e) => {
      e.exports = ['МаксМинЗакр3'];
    },
    9523: (e) => {
      e.exports = ['МаксМинЗакрЗакр4'];
    },
    12754: (e) => {
      e.exports = ['индекс'];
    },
    38071: (e) => {
      e.exports = ['индексы'];
    },
    12504: (e) => {
      e.exports = ['ОткрМаксМинЗакр4'];
    },
    38466: (e) => {
      e.exports = ['откр.'];
    },
    3919: (e) => {
      e.exports = ['мин.'];
    },
    36931: (e) => {
      e.exports = ['акция'];
    },
  },
]);
