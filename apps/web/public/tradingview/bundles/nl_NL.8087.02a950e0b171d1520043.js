(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (e) => {
      e.exports = 'Add';
    },
    53585: (e) => {
      e.exports = ['Add Custom Color'];
    },
    81865: (e) => {
      e.exports = 'Opacity';
    },
    73755: (e) => {
      e.exports = 'Another symbol';
    },
    16936: (e) => {
      e.exports = 'Back';
    },
    88046: (e) => {
      e.exports = 'Main chart symbol';
    },
    9898: (e) => {
      e.exports = 'Right';
    },
    20036: (e) => {
      e.exports = ['Annuleren'];
    },
    23398: (e) => {
      e.exports = 'Change symbol';
    },
    94551: (e) => {
      e.exports = 'Chart';
    },
    64498: (e) => {
      e.exports = 'All sources';
    },
    73226: (e) => {
      e.exports = ['Toepassen'];
    },
    79852: (e) => {
      e.exports = 'Bond';
    },
    56095: (e) => {
      e.exports = 'Decrease';
    },
    29601: (e) => {
      e.exports = ['Beschrijving'];
    },
    46812: (e) => {
      e.exports = 'Increase';
    },
    89298: (e) => {
      e.exports = ['Afstand'];
    },
    68988: (e) => {
      e.exports = 'Ok';
    },
    29673: (e) => {
      e.exports = 'No exchanges match your criteria';
    },
    41379: (e) => {
      e.exports = 'No symbols match your criteria';
    },
    35563: (e) => {
      e.exports = 'Number format is invalid.';
    },
    19724: (e) => {
      e.exports = 'Sources';
    },
    59877: (e) => {
      e.exports =
        'Set the "{inputInline}" time and price for "{studyShortDescription}"';
    },
    18571: (e) => {
      e.exports = 'Set the "{inputTitle}" time for "{studyShortDescription}"';
    },
    58552: (e) => {
      e.exports = 'Set the "{inputTitle}" price for "{studyShortDescription}"';
    },
    80481: (e) => {
      e.exports = 'Set the time and price for "{studyShortDescription}"';
    },
    42917: (e) => {
      e.exports = 'Set the time for "{studyShortDescription}"';
    },
    6083: (e) => {
      e.exports = 'Set the price for "{studyShortDescription}"';
    },
    52298: (e) => {
      e.exports = ['Zoeken'];
    },
    13269: (e) => {
      e.exports = 'Select source';
    },
    2607: (e) => {
      e.exports =
        'Specified value is more than the instrument maximum of {max}.';
    },
    53669: (e) => {
      e.exports =
        'Specified value is less than the instrument minimum of {min}.';
    },
    89053: (e) => {
      e.exports = ['Symbool'];
    },
    48490: (e) => {
      e.exports = 'Symbol & description';
    },
    99983: (e) => {
      e.exports = 'Symbol Search';
    },
    54336: (e) => {
      e.exports = 'Remove color';
    },
    60142: (e) => {
      e.exports = 'Thickness';
    },
    87592: (e) => {
      e.exports = 'cfd';
    },
    17023: (e) => {
      e.exports = 'change opacity';
    },
    13066: (e) => {
      e.exports = 'change color';
    },
    95657: (e) => {
      e.exports = 'change thickness';
    },
    18567: (e) => {
      e.exports = 'change {propertyName} property';
    },
    36962: (e) => {
      e.exports = 'close';
    },
    8448: (e) => {
      e.exports = 'crypto';
    },
    1328: (e) => {
      e.exports = 'dr';
    },
    88720: (e) => {
      e.exports = 'economy';
    },
    39512: (e) => {
      e.exports = 'forex';
    },
    81859: (e) => {
      e.exports = 'futures';
    },
    39337: (e) => {
      e.exports = 'high';
    },
    91815: (e) => {
      e.exports = 'hl2';
    },
    40771: (e) => {
      e.exports = 'hlc3';
    },
    9523: (e) => {
      e.exports = 'hlcc4';
    },
    12754: (e) => {
      e.exports = 'index';
    },
    38071: (e) => {
      e.exports = ['indexen'];
    },
    12504: (e) => {
      e.exports = 'ohlc4';
    },
    38466: (e) => {
      e.exports = 'open';
    },
    3919: (e) => {
      e.exports = 'low';
    },
    36931: (e) => {
      e.exports = 'stock';
    },
  },
]);
