(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4782],
  {
    14281: (r) => {
      r.exports = ['valuta'];
    },
    60558: (r) => {
      r.exports = ['djur & natur'];
    },
    14232: (r) => {
      r.exports = ['aktiviteter'];
    },
    57792: (r) => {
      r.exports = ['pilar'];
    },
    33628: (r) => {
      r.exports = ['gester & smileys'];
    },
    35305: (r) => {
      r.exports = ['mat & dryck'];
    },
    49546: (r) => {
      r.exports = ['flaggor'];
    },
    72302: (r) => {
      r.exports = ['objekt'];
    },
    11739: (r) => {
      r.exports = ['natur'];
    },
    96330: (r) => {
      r.exports = ['leenden & människor'];
    },
    6878: (r) => {
      r.exports = ['symboler'];
    },
    77011: (r) => {
      r.exports = ['symboler & flaggor'];
    },
    15426: (r) => {
      r.exports = ['nyligen använd'];
    },
    15395: (r) => {
      r.exports = ['resor & platser'];
    },
    82401: (r) => {
      r.exports = ['Markörer'];
    },
    99878: (r) => {
      r.exports = ['Anteckningsverktyg'];
    },
    44629: (r) => {
      r.exports = ['Lägg till som favorit'];
    },
    75377: (r) => {
      r.exports = ['Gann and Fibonacci - verktyg'];
    },
    95537: (r) => {
      r.exports = ['Geometriska former'];
    },
    60925: (r) => {
      r.exports = ['Punkt'];
    },
    19570: (r) => {
      r.exports = 'Emojis';
    },
    99289: (r) => {
      r.exports = ['Suddgummi'];
    },
    17517: (r) => {
      r.exports = ['Dölj alla ritverktyg'];
    },
    96411: (r) => {
      r.exports = ['Dölj ritverktygsfält'];
    },
    92464: (r) => {
      r.exports = ['Ikoner'];
    },
    37057: (r) => {
      r.exports = ['Lås alla ritverktyg'];
    },
    37140: (r) => {
      r.exports = [
        'Magnet-läget tar ritningar som placeras nära prisstängerna till närmaste OHLC-värde',
      ];
    },
    59607: (r) => {
      r.exports = ['Mäta'];
    },
    36551: (r) => {
      r.exports =
        'New drawings are replicated to all charts in the layout and shown when the same ticker is selected';
    },
    63354: (r) => {
      r.exports = ['Visa ritverktygsfält'];
    },
    49616: (r) => {
      r.exports = ['Visa verktygslist med favoritritverktyg'];
    },
    91977: (r) => {
      r.exports = ['Visa dolda verktyg'];
    },
    51072: (r) => {
      r.exports = ['Visa objektträd'];
    },
    49421: (r) => {
      r.exports = ['Stanna kvar i ritläge'];
    },
    85422: (r) => {
      r.exports = ['Stark magnet'];
    },
    19693: (r) => {
      r.exports = ['Mönster'];
    },
    10234: (r) => {
      r.exports = ['Verktyg för förutsägelse och mätning'];
    },
    76091: (r) => {
      r.exports = ['Ta bort ritningar'];
    },
    45286: (r) => {
      r.exports = ['Ta bort objekt'];
    },
    72482: (r) => {
      r.exports = ['Ta bort från favoriter'];
    },
    30513: (r) => {
      r.exports = ['Ta bort {drawings}'];
    },
    10049: (r) => {
      r.exports = ['Ta bort {drawings} & {indicators}'];
    },
    55084: (r) => {
      r.exports = ['Ta bort {indicators}'];
    },
    45265: (r) => {
      r.exports = ['Svag magnet'];
    },
    52899: (r) => {
      r.exports = ['Verktyg för trendlinje'];
    },
    38925: (r) => {
      r.exports = ['Förstora'];
    },
    49895: (r) => {
      r.exports = ['Förminska'];
    },
    32868: (r) => {
      r.exports = ['{hotKey_0} + klicka på diagram'];
    },
    68125: (r) => {
      r.exports = ['{hotKey_0} - cirkel'];
    },
    40234: (r) => {
      r.exports = ['{hotKey_0} - rita en rak linje i 45 graders vinklar'];
    },
    10289: (r) => {
      r.exports = ['{hotKey_0} - fasta steg'];
    },
    81591: (r) => {
      r.exports = ['{hotKey_0} - kvadrat'];
    },
    93030: (r) => {
      r.exports = ['{amount} ritning', '{amount} ritningar'];
    },
    80437: (r) => {
      r.exports = ['{amount} indikator', '{amount} indikatorer'];
    },
  },
]);
