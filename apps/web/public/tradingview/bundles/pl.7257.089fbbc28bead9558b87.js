(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [7257],
  {
    19801: (e) => {
      e.exports = ['Pt'];
    },
    11268: (e) => {
      e.exports = ['Pn'];
    },
    63331: (e) => {
      e.exports = ['Sob.'];
    },
    85954: (e) => {
      e.exports = ['Nd.'];
    },
    26230: (e) => {
      e.exports = ['Śr'];
    },
    24793: (e) => {
      e.exports = ['Czw.'];
    },
    31533: (e) => {
      e.exports = ['Wt'];
    },
    89790: (e) => {
      e.exports = ['Kod źródłowy Pine niedostępny'];
    },
    39589: (e) => {
      e.exports = ['Zwiń okienko'];
    },
    38154: (e) => {
      e.exports = ['Potwierdź usunięcie drzewa testowego'];
    },
    65636: (e) => {
      e.exports = 'Cboe BZX';
    },
    36004: (e) => {
      e.exports = ['<PERSON><PERSON>ł<PERSON>ż bezpłatne konto'];
    },
    69419: (e) => {
      e.exports = ['Wszystko w porządku — rynek jest otwarty.'];
    },
    97637: (e) => {
      e.exports = ['Kwiecień'];
    },
    86797: (e) => {
      e.exports = ['Sierpień'];
    },
    22519: (e) => {
      e.exports = ['Parametry zmiany słupka'];
    },
    52003: (e) => {
      e.exports = [
        'Czy napewno chcesz usunąć analizę wraz ze skojarzonymi z nią elementami?',
      ];
    },
    68854: (e) => {
      e.exports = ['Kliknij dwukrotnie'];
    },
    97325: (e) => {
      e.exports = ['Problem z danymi'];
    },
    52916: (e) => {
      e.exports = ['Dane są aktualizowane raz dziennie.'];
    },
    25978: (e) => {
      e.exports = [
        'Dane są aktualizowane raz na sekundę, nawet jeśli na rynku jest więcej aktualizacji.',
      ];
    },
    57310: (e) => {
      e.exports = ['Dane są opóźnione'];
    },
    49321: (e) => {
      e.exports = [
        'Dane dostarczane w ramach planu Basic są aktualizowane raz na sekundę, nawet jeśli na rynku jest więcej aktualizacji.',
      ];
    },
    55669: (e) => {
      e.exports = ['Grudzień'];
    },
    83498: (e) => {
      e.exports = ['Usuń panel'];
    },
    59315: (e) => {
      e.exports = ['Dane End of Day'];
    },
    82751: (e) => {
      e.exports = ['Błąd'];
    },
    40519: (e) => {
      e.exports = [
        'Dobry wieczór. Rynek jest aktualnie otwarty tylko dla transakcji post-market.',
      ];
    },
    80227: (e) => {
      e.exports = ['Strefa czasowa giełdy'];
    },
    16467: (e) => {
      e.exports = ['Luty'];
    },
    25046: (e) => {
      e.exports = ['Wypełnij Umowę Dostępu do Danych'];
    },
    93666: (e) => {
      e.exports = ['Zaznacz Symbol'];
    },
    564: (e) => {
      e.exports = ['Pt'];
    },
    72970: (e) => {
      e.exports = ['Piątek'];
    },
    88958: (e) => {
      e.exports = ['Święto'];
    },
    21686: (e) => {
      e.exports = ['Ukryj legendę wskaźnika'];
    },
    26935: (e) => {
      e.exports = ['Argumenty wskaźników'];
    },
    26315: (e) => {
      e.exports = ['Nazwa wskaźnika'];
    },
    84098: (e) => {
      e.exports = ['Wartość wskaźnika'];
    },
    91459: (e) => {
      e.exports = [
        'Jeśli potrzebujesz danych w czasie rzeczywistym z {listedExchange}, musisz wypełnić Umowę Dostępu do Danych. Nie martw się, to zajmuje tylko kilka kliknięć',
      ];
    },
    50634: (e) => {
      e.exports = ['Przejdzie do handlu posesyjnego za {remainingTime}.'];
    },
    74537: (e) => {
      e.exports = ['Otworzy się dla handlu przedsesyjnego za {remainingTime}.'];
    },
    26910: (e) => {
      e.exports = ['Styczeń'];
    },
    23230: (e) => {
      e.exports = ['Lipiec'];
    },
    49385: (e) => {
      e.exports = ['Czerwiec'];
    },
    99487: (e) => {
      e.exports = ['Wartości OHLC'];
    },
    15815: (e) => {
      e.exports = ['Jedna aktualizacja na sekundę'];
    },
    90784: (e) => {
      e.exports = ['Październik'];
    },
    75991: (e) => {
      e.exports = ['Pokaż status Rynek otwarty'];
    },
    18429: (e) => {
      e.exports = ['Dowiedz się więcej'];
    },
    39899: (e) => {
      e.exports = ['Przesuń panel w dół'];
    },
    70343: (e) => {
      e.exports = ['Przesuń panel w górę'];
    },
    83085: (e) => {
      e.exports = ['Pon'];
    },
    61199: (e) => {
      e.exports = ['Poniedziałek'];
    },
    41610: (e) => {
      e.exports = ['Więcej'];
    },
    1653: (e) => {
      e.exports = [
        'Dzień dobry. Rynek jest aktualnie otwarty tylko dla transakcji pre-market.',
      ];
    },
    56470: (e) => {
      e.exports = ['Maksymalizuj wykres'];
    },
    19603: (e) => {
      e.exports = ['Rozwiń panel'];
    },
    68327: (e) => {
      e.exports = ['Maj'];
    },
    35732: (e) => {
      e.exports = ['Zarządzanie panelami'];
    },
    84675: (e) => {
      e.exports = ['Marzec'];
    },
    83949: (e) => {
      e.exports = ['Rynek jest otwarty'];
    },
    35701: (e) => {
      e.exports = ['Rynek otwiera się za {remainingTime}.'];
    },
    95814: (e) => {
      e.exports = ['Rynek jest zamknięty'];
    },
    98105: (e) => {
      e.exports = ['Rynek zamyka się za {remainingTime}.'];
    },
    87202: (e) => {
      e.exports = ['Dzisiaj rynek ma wolne.'];
    },
    71194: (e) => {
      e.exports = ['Listopad'];
    },
    66324: (e) => {
      e.exports = ['Kod źródłowy'];
    },
    36835: (e) => {
      e.exports = ['Sob'];
    },
    1144: (e) => {
      e.exports = ['Sobota'];
    },
    40653: (e) => {
      e.exports = ['Przewiń w lewo'];
    },
    26721: (e) => {
      e.exports = ['Przejdź do ostatniego słupka'];
    },
    35809: (e) => {
      e.exports = ['Przewiń w prawo'];
    },
    61132: (e) => {
      e.exports = ['Wrzesień'];
    },
    28705: (e) => {
      e.exports = ['Pokaż legendę wskaźnika'];
    },
    51072: (e) => {
      e.exports = ['Pokaż drzewo obiektów'];
    },
    37809: (e) => {
      e.exports = ['Pokaż ustawienia interwału'];
    },
    39045: (e) => {
      e.exports = ['Błąd wskaźnika'];
    },
    86577: (e) => {
      e.exports = ['Niedz.'];
    },
    72149: (e) => {
      e.exports = ['Niedziela'];
    },
    46041: (e) => {
      e.exports = 'Symbol price source';
    },
    39339: (e) => {
      e.exports = ['Tytuł symbolu'];
    },
    29985: (e) => {
      e.exports = 'Post-market';
    },
    28412: (e) => {
      e.exports = [
        'Płatne subskrypcje zawierają częstszą aktualizację danych.',
      ];
    },
    56042: (e) => {
      e.exports = 'Pre-market';
    },
    36015: (e) => {
      e.exports = [
        'Dane w czasie rzeczywistym dla {description} nie są obecnie dostępne. Mamy nadzieję, że w przyszłości zaoferujemy użytkownikom dostęp do tych danych.',
      ];
    },
    6667: (e) => {
      e.exports = [
        'Dane w czasie rzeczywistym dla {symbolName} zostały dostarczone przez giełdę {exchange}.',
      ];
    },
    48293: (e) => {
      e.exports = ['Przywróć wykres'];
    },
    91029: (e) => {
      e.exports = ['Przywróć panel'];
    },
    75094: (e) => {
      e.exports = ['Śr.'];
    },
    7147: (e) => {
      e.exports = ['Środa'];
    },
    52984: (e) => {
      e.exports = [
        'Aby uzyskać dane w czasie rzeczywistym dla {description}, musisz wykupić subskrypcję dodatkowych danych rynkowych.',
      ];
    },
    9787: (e) => {
      e.exports = ['Czw.'];
    },
    7951: (e) => {
      e.exports = ['Czwartek'];
    },
    57918: (e) => {
      e.exports = [
        'Dane są aktualizowane w czasie rzeczywistym, ale różnią się nieznacznie od oficjalnych danych z głównych giełd.',
      ];
    },
    68025: (e) => {
      e.exports = [
        'Dane są aktualizowane w czasie rzeczywistym, ale różnią się nieznacznie od oficjalnych danych z {exchange}.',
      ];
    },
    73717: (e) => {
      e.exports = ['Nie ma takiego symbolu, wybierz jakiś inny.'];
    },
    57048: (e) => {
      e.exports = ['Czas na spacer — ten rynek jest zamknięty.'];
    },
    94316: (e) => {
      e.exports = ['Wt.'];
    },
    44979: (e) => {
      e.exports = ['Wtorek'];
    },
    8209: (e) => {
      e.exports = ['Odznacz Symbol'];
    },
    1111: (e) => {
      e.exports = ['Wolumen'];
    },
    61311: (e) => {
      e.exports = ['Przybliż'];
    },
    47602: (e) => {
      e.exports = ['Oddal'];
    },
    57889: (e) => {
      e.exports = ['zmień widoczności wartości OHLC'];
    },
    18644: (e) => {
      e.exports = ['zmień widoczność statusu otwartego rynku'];
    },
    45110: (e) => {
      e.exports = ['zmień widoczność zmian słupków'];
    },
    31325: (e) => {
      e.exports = ['Zmień widoczność tytułów wskaźników'];
    },
    99774: (e) => {
      e.exports = ['Zmień widoczność wartości wskaźników'];
    },
    96162: (e) => {
      e.exports = ['Zmień widoczność argumentów wskaźnika'];
    },
    26717: (e) => {
      e.exports = ['zmień widoczność opisu symbolu'];
    },
    6091: (e) => {
      e.exports = 'change symbol field visibility';
    },
    9455: (e) => {
      e.exports = ['zmień widoczność wartości wolumenu'];
    },
    39348: (e) => {
      e.exports = ['mniej niż 1 minuta'];
    },
    87358: (e) => {
      e.exports = ['pokaż {title}'];
    },
    7827: (e) => {
      e.exports = ['{days} i {hours}'];
    },
    7435: (e) => {
      e.exports = ['{exchange} od {originalExchange}'];
    },
    19830: (e) => {
      e.exports = ['{hours} i {minutes}'];
    },
    1084: (e) => {
      e.exports = [
        'Dane w czasie rzeczywistym z {listedExchange} są dostępne bezpłatnie dla zarejestrowanych użytkowników.',
      ];
    },
    11155: (e) => {
      e.exports = ['{symbolName} dane są opóźnione o {time} minut.'];
    },
    77033: (e) => {
      e.exports = [
        'Dane są aktualizowane co {amount} sekundę, nawet jeśli na rynku jest więcej aktualizacji.',
        'Dane są aktualizowane co {amount} sekundy, nawet jeśli na rynku jest więcej aktualizacji.',
        'Dane są aktualizowane co {amount} sekund, nawet jeśli na rynku jest więcej aktualizacji.',
        'Dane są aktualizowane co {amount} sekund, nawet jeśli na rynku jest więcej aktualizacji.',
      ];
    },
    2121: (e) => {
      e.exports = [
        'Dane dotyczące naszego planu Basic są aktualizowane co {amount} sekundę, nawet jeśli na rynku jest więcej aktualizacji.',
        'Dane dotyczące naszego planu Basic są aktualizowane co {amount} sekundy, nawet jeśli na rynku jest więcej aktualizacji.',
        'Dane dotyczące naszego planu Basic są aktualizowane co {amount} sekund, nawet jeśli na rynku jest więcej aktualizacji.',
        'Dane dotyczące naszego planu Basic są aktualizowane co {amount} sekund, nawet jeśli na rynku jest więcej aktualizacji.',
      ];
    },
    5223: (e) => {
      e.exports = [
        'Jedna aktualizacja co {amount} sekundę',
        'Jedna aktualizacja co {amount} sekund',
        'Jedna aktualizacja co {amount} sekund',
        'Jedna aktualizacja co {amount} sekund',
      ];
    },
    58609: (e) => {
      e.exports = [
        '{number} dzień',
        '{number} dni',
        '{number} dni',
        '{number} dni',
      ];
    },
    24430: (e) => {
      e.exports = [
        '{number} godzina',
        '{number} godziny',
        '{number} godzina',
        '{number} godzin',
      ];
    },
    67151: (e) => {
      e.exports = [
        '{number} minuta',
        '{number} minuty',
        '{number} minut',
        '{number} minut',
      ];
    },
  },
]);
