(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (o) => {
      o.exports = ['Adicionar'];
    },
    53585: (o) => {
      o.exports = ['Adicionar cor customizada'];
    },
    81865: (o) => {
      o.exports = ['Opacidade'];
    },
    60558: (o) => {
      o.exports = ['animais & natureza'];
    },
    14232: (o) => {
      o.exports = ['atividade'];
    },
    35305: (o) => {
      o.exports = ['comida & bebida'];
    },
    49546: (o) => {
      o.exports = ['bandeiras'];
    },
    72302: (o) => {
      o.exports = ['objetos'];
    },
    96330: (o) => {
      o.exports = ['smiles & pessoas'];
    },
    6878: (o) => {
      o.exports = ['símbolos'];
    },
    15426: (o) => {
      o.exports = ['usados recentemente'];
    },
    15395: (o) => {
      o.exports = ['viagens & lugares'];
    },
    73755: (o) => {
      o.exports = ['Outro Símbolo'];
    },
    16936: (o) => {
      o.exports = ['Voltar'];
    },
    88046: (o) => {
      o.exports = ['Símbolo do gráfico principal'];
    },
    9898: (o) => {
      o.exports = ['Direita'];
    },
    20036: (o) => {
      o.exports = ['Cancelar'];
    },
    72171: (o) => {
      o.exports = ['Centro'];
    },
    23398: (o) => {
      o.exports = ['Mudar símbolo'];
    },
    94551: (o) => {
      o.exports = ['Gráfico'];
    },
    64498: (o) => {
      o.exports = ['Todas as fontes'];
    },
    91757: (o) => {
      o.exports = ['Em baixo'];
    },
    79852: (o) => {
      o.exports = ['Título'];
    },
    16079: (o) => {
      o.exports = ['Gradiente'];
    },
    42973: (o) => {
      o.exports = ['Linha Pontilhada'];
    },
    59317: (o) => {
      o.exports = ['Linha Tracejada'];
    },
    56095: (o) => {
      o.exports = ['Diminuir'];
    },
    29601: (o) => {
      o.exports = ['Descrição'];
    },
    77405: (o) => {
      o.exports = 'Horizontal';
    },
    46812: (o) => {
      o.exports = ['Aumentar'];
    },
    89298: (o) => {
      o.exports = ['Desvio'];
    },
    68988: (o) => {
      o.exports = 'Ok';
    },
    19286: (o) => {
      o.exports = ['Esquerda'];
    },
    76476: (o) => {
      o.exports = ['No meio'];
    },
    29673: (o) => {
      o.exports = ['Nenhuma bolsa corresponde ao seu critério'];
    },
    41379: (o) => {
      o.exports = ['Nenhum símbolo compatível com seu critério'];
    },
    55362: (o) => {
      o.exports = 'Normal';
    },
    35563: (o) => {
      o.exports = ['O formato numérico é inválido.'];
    },
    19724: (o) => {
      o.exports = ['Fontes'];
    },
    35637: (o) => {
      o.exports = ['Sólido'];
    },
    52298: (o) => {
      o.exports = ['Pesquisar'];
    },
    13269: (o) => {
      o.exports = ['Selecionar fonte'];
    },
    2607: (o) => {
      o.exports = [
        'O valor especificado é maior que o instrumento máximo de {max}.',
      ];
    },
    53669: (o) => {
      o.exports = [
        'O valor especificado é maior que o instrumento máximo de {min}.',
      ];
    },
    89053: (o) => {
      o.exports = ['Símbolo'];
    },
    48490: (o) => {
      o.exports = ['Símbolo & descrição'];
    },
    99983: (o) => {
      o.exports = ['Pesquisa de Símbolo'];
    },
    54336: (o) => {
      o.exports = ['Remover cor'];
    },
    21141: (o) => {
      o.exports = ['Direita'];
    },
    65994: (o) => {
      o.exports = ['Em cima'];
    },
    92960: (o) => {
      o.exports = ['Alinhamento do texto'];
    },
    90581: (o) => {
      o.exports = ['Orientação do Texto'];
    },
    60142: (o) => {
      o.exports = ['Espessura'];
    },
    78019: (o) => {
      o.exports = [
        'Usar símbolos matemáticos especiais para deslocar os desenhos selecionados: +,-,/,* para preço e +,- para o índice de barras.',
      ];
    },
    44085: (o) => {
      o.exports = 'Vertical';
    },
    87592: (o) => {
      o.exports = 'cfd';
    },
    17023: (o) => {
      o.exports = ['Mudar Opacidade'];
    },
    13066: (o) => {
      o.exports = ['Mudar Cor'];
    },
    95657: (o) => {
      o.exports = ['Mudar Espessura'];
    },
    18567: (o) => {
      o.exports = ['alterar propriedade {propertyName}'];
    },
    36962: (o) => {
      o.exports = ['fch'];
    },
    8448: (o) => {
      o.exports = ['Cripto'];
    },
    1328: (o) => {
      o.exports = 'dr';
    },
    76080: (o) => {
      o.exports = ['p. ex. +1'];
    },
    95166: (o) => {
      o.exports = ['p. ex. /2'];
    },
    88720: (o) => {
      o.exports = ['economia'];
    },
    39512: (o) => {
      o.exports = 'forex';
    },
    81859: (o) => {
      o.exports = ['futuros'];
    },
    39337: (o) => {
      o.exports = ['máx'];
    },
    91815: (o) => {
      o.exports = 'hl2';
    },
    40771: (o) => {
      o.exports = 'hlc3';
    },
    9523: (o) => {
      o.exports = 'hlcc4';
    },
    12754: (o) => {
      o.exports = ['índice'];
    },
    38071: (o) => {
      o.exports = ['índices'];
    },
    12504: (o) => {
      o.exports = 'ohlc4';
    },
    38466: (o) => {
      o.exports = ['abertura'];
    },
    3919: (o) => {
      o.exports = ['mín'];
    },
    36931: (o) => {
      o.exports = ['ação'];
    },
  },
]);
