(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4150],
  {
    74274: (e) => {
      e.exports = ['Krzyżyk'];
    },
    41596: (e) => {
      e.exports = ['Etykiety w skali cen'];
    },
    23545: (e) => {
      e.exports = ['Wartości w wierszu statusu'];
    },
    39495: (e) => {
      e.exports = ['Koła'];
    },
    41389: (e) => {
      e.exports = ['Ponad świeczką'];
    },
    29520: (e) => {
      e.exports = ['Absolutny'];
    },
    58102: (e) => {
      e.exports = ['Zastosuj domyślne'];
    },
    65262: (e) => {
      e.exports = ['Obszar z przerwami'];
    },
    83760: (e) => {
      e.exports = ['Korpus'];
    },
    48848: (e) => {
      e.exports = ['Obramowanie'];
    },
    27331: (e) => {
      e.exports = ['Tło'];
    },
    78626: (e) => {
      e.exports = ['Poniżej świeczki'];
    },
    41361: (e) => {
      e.exports = ['W dół'];
    },
    22192: (e) => {
      e.exports = ['Dni'];
    },
    31577: (e) => {
      e.exports = ['Dynamika VA'];
    },
    4329: (e) => {
      e.exports = ['Domyślnie'];
    },
    98938: (e) => {
      e.exports = ['Domyślne'];
    },
    63099: (e) => {
      e.exports = ['Godziny'];
    },
    11091: (e) => {
      e.exports = 'Histogram';
    },
    66304: (e) => {
      e.exports = ['Argumenty'];
    },
    40297: (e) => {
      e.exports = ['Wyjścia'];
    },
    36993: (e) => {
      e.exports = ['Zmień min tick'];
    },
    64606: (e) => {
      e.exports = ['Czcionka etykiety'];
    },
    54934: (e) => {
      e.exports = ['Linia z Przerwami'];
    },
    95543: (e) => {
      e.exports = ['Miesiące'];
    },
    28134: (e) => {
      e.exports = ['Minuty'];
    },
    18229: (e) => {
      e.exports = ['Zapisz jako domyślny'];
    },
    71129: (e) => {
      e.exports = ['Sekundy'];
    },
    86520: (e) => {
      e.exports = ['Etykiety sygnału'];
    },
    79511: (e) => {
      e.exports = ['Wyższa linia'];
    },
    64108: (e) => {
      e.exports = 'Step line with breaks';
    },
    67767: (e) => {
      e.exports = ['Linia schodkowa z diamentami'];
    },
    21861: (e) => {
      e.exports = ['Lokalizacja'];
    },
    73947: (e) => {
      e.exports = ['Precyzja'];
    },
    66596: (e) => {
      e.exports = ['Wielkość'];
    },
    86672: (e) => {
      e.exports = ['Zakresy'];
    },
    79782: (e) => {
      e.exports = ['Resetuj ustawienia'];
    },
    21594: (e) => {
      e.exports = ['Tygodnie'];
    },
    26458: (e) => {
      e.exports = ['Knot'];
    },
    95247: (e) => {
      e.exports = ['Szerokość (% ramki)'];
    },
    19221: (e) => {
      e.exports = ['Kolor tekstu'];
    },
    7138: (e) => {
      e.exports = ['Transakcje na wykresie'];
    },
    98802: (e) => {
      e.exports = ['W górę'];
    },
    14414: (e) => {
      e.exports = ['Profil wolumenu'];
    },
    91322: (e) => {
      e.exports = ['Wartości'];
    },
    20834: (e) => {
      e.exports = ['Zmień min. Tick'];
    },
    98491: (e) => {
      e.exports = ['Zmień znak'];
    },
    7378: (e) => {
      e.exports = ['zmień rozmiar czcionki'];
    },
    28691: (e) => {
      e.exports = ['Zmień styl linii'];
    },
    38361: (e) => {
      e.exports = ['Zmień lokalizację'];
    },
    51081: (e) => {
      e.exports = ['Zmień Szerokość Procentową'];
    },
    47634: (e) => {
      e.exports = ['Zmień Położenie'];
    },
    15683: (e) => {
      e.exports = ['Zmień typ wykresu'];
    },
    164: (e) => {
      e.exports = ['Zmień dokładność'];
    },
    86888: (e) => {
      e.exports = ['Zmień kształt'];
    },
    50463: (e) => {
      e.exports = ['Zmień wartość'];
    },
    12628: (e) => {
      e.exports = ['zmień widoczność wartości'];
    },
    13355: (e) => {
      e.exports = ['zmień {title} dni na'];
    },
    41377: (e) => {
      e.exports = ['zmień {title} dni z'];
    },
    35388: (e) => {
      e.exports = ['zmień {title} godziny z'];
    },
    78586: (e) => {
      e.exports = ['zmień {title} godziny na'];
    },
    59635: (e) => {
      e.exports = ['zmień {title} miesięcy z'];
    },
    74266: (e) => {
      e.exports = ['zmień {title} miesięcy na'];
    },
    91633: (e) => {
      e.exports = ['zmień {title} minut na'];
    },
    15106: (e) => {
      e.exports = ['zmień {title} minut z'];
    },
    66161: (e) => {
      e.exports = ['zmień {title} sekund na'];
    },
    2822: (e) => {
      e.exports = ['zmień {title} sekund z'];
    },
    21339: (e) => {
      e.exports = ['zmień {title} tygodni z'];
    },
    68643: (e) => {
      e.exports = ['zmień {title} tygodni na'];
    },
    30810: (e) => {
      e.exports = ['zmień widoczność {title} na tickach'];
    },
    24941: (e) => {
      e.exports = ['zmień widoczność {title} na tygodnie'];
    },
    29088: (e) => {
      e.exports = ['zmień widoczność {title} na dni'];
    },
    68971: (e) => {
      e.exports = ['zmień widoczność {title} na godziny'];
    },
    64370: (e) => {
      e.exports = ['zmień widoczność {title} na minuty'];
    },
    6659: (e) => {
      e.exports = ['zmień widoczność {title} na miesiące'];
    },
    29091: (e) => {
      e.exports = ['zmień widoczność {title} na zakresach'];
    },
    46948: (e) => {
      e.exports = ['zmień widoczność {title} na sekundach'];
    },
    82211: (e) => {
      e.exports = ['Dni'];
    },
    33486: (e) => {
      e.exports = ['dni na'];
    },
    14077: (e) => {
      e.exports = ['dni z'];
    },
    3143: (e) => {
      e.exports = ['Godziny'];
    },
    84775: (e) => {
      e.exports = ['godziny z'];
    },
    11255: (e) => {
      e.exports = ['godziny na'];
    },
    58964: (e) => {
      e.exports = ['Miesiące'];
    },
    71770: (e) => {
      e.exports = ['miesiące z'];
    },
    37179: (e) => {
      e.exports = ['miesiące na'];
    },
    16465: (e) => {
      e.exports = ['Minuty'];
    },
    72317: (e) => {
      e.exports = ['minuty na'];
    },
    25586: (e) => {
      e.exports = ['minuty z'];
    },
    32925: (e) => {
      e.exports = ['sekundy'];
    },
    39017: (e) => {
      e.exports = ['sekundy na'];
    },
    6049: (e) => {
      e.exports = ['sekundy z'];
    },
    13604: (e) => {
      e.exports = ['Zakresy'];
    },
    93016: (e) => {
      e.exports = ['tygodnie'];
    },
    32002: (e) => {
      e.exports = ['tygodnie z'];
    },
    28091: (e) => {
      e.exports = ['tygodnie na'];
    },
    59523: (e) => {
      e.exports = ['Tiki'];
    },
  },
]);
