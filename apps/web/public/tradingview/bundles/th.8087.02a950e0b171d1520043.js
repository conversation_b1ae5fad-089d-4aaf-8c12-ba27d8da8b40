(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (t) => {
      t.exports = ['เพิ่ม'];
    },
    53585: (t) => {
      t.exports = ['เพิ่มสีตั้งค่าด้วยตนเอง'];
    },
    81865: (t) => {
      t.exports = ['ความโปร่งใส'];
    },
    73755: (t) => {
      t.exports = ['สัญลักษณ์อีกอัน'];
    },
    16936: (t) => {
      t.exports = ['กลับไป'];
    },
    88046: (t) => {
      t.exports = ['สัญลักษณ์ของชาร์ตหลัก'];
    },
    9898: (t) => {
      t.exports = ['สิทธิ'];
    },
    20036: (t) => {
      t.exports = ['ยกเลิก'];
    },
    23398: (t) => {
      t.exports = ['เปลี่ยนสัญลักษณ์'];
    },
    94551: (t) => {
      t.exports = ['ชาร์ต'];
    },
    64498: (t) => {
      t.exports = ['แหล่งที่มาทั้งหมด'];
    },
    73226: (t) => {
      t.exports = ['บันทึก'];
    },
    79852: (t) => {
      t.exports = ['พันธบัตร'];
    },
    56095: (t) => {
      t.exports = ['ลดลง'];
    },
    29601: (t) => {
      t.exports = ['คำอธิบาย'];
    },
    46812: (t) => {
      t.exports = ['เพิ่มขึ้น'];
    },
    89298: (t) => {
      t.exports = ['สิ่งชดเชย'];
    },
    68988: (t) => {
      t.exports = ['ตกลง'];
    },
    29673: (t) => {
      t.exports = ['ไม่มีตลาดแลกเปลี่ยนใดๆ ตรงตามเงื่อนไขของคุณ'];
    },
    41379: (t) => {
      t.exports = ['ไม่มีสัญลักษณ์ที่ตรงกับการค้นหาของคุณ'];
    },
    35563: (t) => {
      t.exports = ['รูปแบบตัวเลขไม่ถูกต้อง'];
    },
    19724: (t) => {
      t.exports = ['แหล่งที่มา'];
    },
    59877: (t) => {
      t.exports = [
        'กำหนดเวลาและราคา {inputInline} สำหรับ {studyShortDescription}',
      ];
    },
    18571: (t) => {
      t.exports = ['กำหนดเวลา {inputTitle} สำหรับ {studyShortDescription}'];
    },
    58552: (t) => {
      t.exports = ['กำหนดราคา {inputTitle} สำหรับ {studyShortDescription}'];
    },
    80481: (t) => {
      t.exports = ['ตั้งเวลาและราคาสำหรับ "{studyShortDescription}"'];
    },
    42917: (t) => {
      t.exports = ['ตั้งเวลาสำหรับ "{studyShortDescription}"'];
    },
    6083: (t) => {
      t.exports = ['กำหนดราคาสำหรับ "{studyShortDescription}"'];
    },
    52298: (t) => {
      t.exports = ['ค้นหา'];
    },
    13269: (t) => {
      t.exports = ['เลือกแหล่งที่มา'];
    },
    2607: (t) => {
      t.exports = ['ค่าที่ระบุมากกว่าค่าสูงสุดของเครื่องมือ {max}'];
    },
    53669: (t) => {
      t.exports = ['ค่าที่ระบุน้อยกว่าค่าต่ำสุดของเครื่องมือ {min}'];
    },
    89053: (t) => {
      t.exports = ['สัญลักษณ์'];
    },
    48490: (t) => {
      t.exports = ['สัญลักษณ์และคำอธิบาย'];
    },
    99983: (t) => {
      t.exports = ['ค้นหาตัวย่อ'];
    },
    54336: (t) => {
      t.exports = ['เอาสีออก'];
    },
    60142: (t) => {
      t.exports = ['ความหนา'];
    },
    87592: (t) => {
      t.exports = 'cfd';
    },
    17023: (t) => {
      t.exports = ['เปลี่ยนความทึบแสง'];
    },
    13066: (t) => {
      t.exports = ['เปลี่ยนสี'];
    },
    95657: (t) => {
      t.exports = ['เปลี่ยนความหนา'];
    },
    18567: (t) => {
      t.exports = ['เปลี่ยนคุณสมบัติ {propertyName}'];
    },
    36962: (t) => {
      t.exports = ['ปิด'];
    },
    8448: (t) => {
      t.exports = ['คริปโต'];
    },
    1328: (t) => {
      t.exports = 'dr';
    },
    88720: (t) => {
      t.exports = ['เศรษฐกิจ'];
    },
    39512: (t) => {
      t.exports = ['ฟอเร็กซ์'];
    },
    81859: (t) => {
      t.exports = ['ฟิวเจอร์ส'];
    },
    39337: (t) => {
      t.exports = ['สูง'];
    },
    91815: (t) => {
      t.exports = 'hl2';
    },
    40771: (t) => {
      t.exports = 'hlc3';
    },
    9523: (t) => {
      t.exports = 'hlcc4';
    },
    12754: (t) => {
      t.exports = ['ดัชนี'];
    },
    38071: (t) => {
      t.exports = ['ดัชนี'];
    },
    12504: (t) => {
      t.exports = 'ohlc4';
    },
    38466: (t) => {
      t.exports = ['เปิด'];
    },
    3919: (t) => {
      t.exports = ['ต่ำ'];
    },
    36931: (t) => {
      t.exports = ['หุ้น'];
    },
  },
]);
