(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4150],
  {
    74274: (a) => {
      a.exports = ['Silang'];
    },
    41596: (a) => {
      a.exports = ['Label-label pada skala harga'];
    },
    23545: (a) => {
      a.exports = ['Nilai di garisan status'];
    },
    39495: (a) => {
      a.exports = ['Bulatan'];
    },
    41389: (a) => {
      a.exports = ['Melebihi Bar'];
    },
    29520: (a) => {
      a.exports = ['Mutlak'];
    },
    58102: (a) => {
      a.exports = ['Guna Pakai Lalai'];
    },
    65262: (a) => {
      a.exports = ['Kawasan Terputus-putus'];
    },
    83760: (a) => {
      a.exports = ['Badan'];
    },
    48848: (a) => {
      a.exports = ['Sempadan'];
    },
    27331: (a) => {
      a.exports = ['Latar Belakang'];
    },
    78626: (a) => {
      a.exports = ['Di bawah Bar'];
    },
    41361: (a) => {
      a.exports = ['Bawah'];
    },
    22192: (a) => {
      a.exports = ['Hari'];
    },
    31577: (a) => {
      a.exports = ['Membangunkan VA'];
    },
    4329: (a) => {
      a.exports = ['Lalai'];
    },
    98938: (a) => {
      a.exports = ['Lalai'];
    },
    63099: (a) => {
      a.exports = ['Jam'];
    },
    11091: (a) => {
      a.exports = 'Histogram';
    },
    66304: (a) => {
      a.exports = ['Input'];
    },
    40297: (a) => {
      a.exports = ['Keluaran'];
    },
    36993: (a) => {
      a.exports = ['Melarang Tanda Semak Minimum'];
    },
    64606: (a) => {
      a.exports = ['Fon Label'];
    },
    54934: (a) => {
      a.exports = ['Garisan Terputus-putus'];
    },
    95543: (a) => {
      a.exports = ['Bulan'];
    },
    28134: (a) => {
      a.exports = ['Minit'];
    },
    18229: (a) => {
      a.exports = ['Simpan Sebagai Lalai'];
    },
    71129: (a) => {
      a.exports = ['Saat'];
    },
    86520: (a) => {
      a.exports = ['Label Isyarat'];
    },
    79511: (a) => {
      a.exports = ['Garis Langkah'];
    },
    64108: (a) => {
      a.exports = ['Garisan tangga dengan henti'];
    },
    67767: (a) => {
      a.exports = ['Step Line bersama Rhombuses'];
    },
    21861: (a) => {
      a.exports = ['Penempatan'];
    },
    73947: (a) => {
      a.exports = ['Ketepatan'];
    },
    66596: (a) => {
      a.exports = ['Kuantiti'];
    },
    86672: (a) => {
      a.exports = ['Julat'];
    },
    79782: (a) => {
      a.exports = ['Set semula tetapan'];
    },
    21594: (a) => {
      a.exports = ['Minggu'];
    },
    26458: (a) => {
      a.exports = ['Sumbu'];
    },
    95247: (a) => {
      a.exports = ['Lebar (% daripada Kotak)'];
    },
    19221: (a) => {
      a.exports = ['Warna teks'];
    },
    7138: (a) => {
      a.exports = ['Dagangan Di Carta'];
    },
    98802: (a) => {
      a.exports = ['Naik'];
    },
    14414: (a) => {
      a.exports = ['Profil Volum'];
    },
    91322: (a) => {
      a.exports = ['Nilai'];
    },
    20834: (a) => {
      a.exports = ['Ubah Tik Minimum'];
    },
    98491: (a) => {
      a.exports = ['Tukar Char'];
    },
    7378: (a) => {
      a.exports = ['Tukar Saiz Tulisan'];
    },
    28691: (a) => {
      a.exports = ['Tukar Bentuk Garis'];
    },
    38361: (a) => {
      a.exports = ['Tukar Lokasi'];
    },
    51081: (a) => {
      a.exports = ['Tukar Lebar Peratusan'];
    },
    47634: (a) => {
      a.exports = ['Tukar Penempatan'];
    },
    15683: (a) => {
      a.exports = ['Tukar Jenis Plot'];
    },
    164: (a) => {
      a.exports = ['Tukar Ketepatan'];
    },
    86888: (a) => {
      a.exports = ['Tukar Bentuk'];
    },
    50463: (a) => {
      a.exports = ['Tukar Nilai'];
    },
    12628: (a) => {
      a.exports = ['tukar nilai-nilai keterlihatan'];
    },
    13355: (a) => {
      a.exports = ['tukar {title} hari kepada'];
    },
    41377: (a) => {
      a.exports = ['tukar {title} hari dari'];
    },
    35388: (a) => {
      a.exports = ['tukar {title} jam dari'];
    },
    78586: (a) => {
      a.exports = ['tukar {title} jam kepada'];
    },
    59635: (a) => {
      a.exports = ['tukar {title} bulan dari'];
    },
    74266: (a) => {
      a.exports = ['tukar {title} bulan kepada'];
    },
    91633: (a) => {
      a.exports = ['tukar {title} minit kepada'];
    },
    15106: (a) => {
      a.exports = ['tukar {title} minit dari'];
    },
    66161: (a) => {
      a.exports = ['tukar {title} saat kepada'];
    },
    2822: (a) => {
      a.exports = ['tukar {title} saat dari'];
    },
    21339: (a) => {
      a.exports = ['tukar {title} minggu dari'];
    },
    68643: (a) => {
      a.exports = ['tukar {title} minggu kepada'];
    },
    30810: (a) => {
      a.exports = ['tukar kebolehlihatan {title} pada tick'];
    },
    24941: (a) => {
      a.exports = ['tukar kebolehlihatan {title} pada minggu'];
    },
    29088: (a) => {
      a.exports = ['tukar kebolehlihatan {title} pada hari'];
    },
    68971: (a) => {
      a.exports = ['tukar kebolehlihatan {title} pada jam'];
    },
    64370: (a) => {
      a.exports = ['tukar kebolehlihatan {title} pada minit'];
    },
    6659: (a) => {
      a.exports = ['tukar kebolehlihatan {title} pada bulan'];
    },
    29091: (a) => {
      a.exports = ['tukar kebolehlihatan {title} pada julat'];
    },
    46948: (a) => {
      a.exports = ['tukar kebolehlihatan {title} saat'];
    },
    82211: (a) => {
      a.exports = ['Hari'];
    },
    33486: (a) => {
      a.exports = ['hari kepada'];
    },
    14077: (a) => {
      a.exports = ['hari dari'];
    },
    3143: (a) => {
      a.exports = ['Jam'];
    },
    84775: (a) => {
      a.exports = ['jam dari'];
    },
    11255: (a) => {
      a.exports = ['jam kepada'];
    },
    58964: (a) => {
      a.exports = ['Bulan'];
    },
    71770: (a) => {
      a.exports = ['bulan dari'];
    },
    37179: (a) => {
      a.exports = ['bulan kepada'];
    },
    16465: (a) => {
      a.exports = ['Minit'];
    },
    72317: (a) => {
      a.exports = ['minit kepada'];
    },
    25586: (a) => {
      a.exports = ['minit dari'];
    },
    32925: (a) => {
      a.exports = ['saat'];
    },
    39017: (a) => {
      a.exports = ['saat kepada'];
    },
    6049: (a) => {
      a.exports = ['saat dari'];
    },
    13604: (a) => {
      a.exports = ['Julat'];
    },
    93016: (a) => {
      a.exports = ['minggu'];
    },
    32002: (a) => {
      a.exports = ['minggu dari'];
    },
    28091: (a) => {
      a.exports = ['minggu kepada'];
    },
    59523: (a) => {
      a.exports = ['Tanda'];
    },
  },
]);
