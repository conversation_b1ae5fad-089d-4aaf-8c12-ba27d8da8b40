(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4782],
  {
    14281: (e) => {
      e.exports = ['สกุลเงิน'];
    },
    60558: (e) => {
      e.exports = ['สัตว์และธรรมชาติ'];
    },
    14232: (e) => {
      e.exports = ['กิจกรรม'];
    },
    57792: (e) => {
      e.exports = ['ลูกศร'];
    },
    33628: (e) => {
      e.exports = ['ท่าทางและรอยยิ้ม'];
    },
    35305: (e) => {
      e.exports = ['อาหารเครื่องดื่ม'];
    },
    49546: (e) => {
      e.exports = ['ธง'];
    },
    72302: (e) => {
      e.exports = ['วัตถุ'];
    },
    11739: (e) => {
      e.exports = ['ธรรมชาติ'];
    },
    96330: (e) => {
      e.exports = ['รอยยิ้มและผู้คน'];
    },
    6878: (e) => {
      e.exports = ['สัญญาลักษณ์'];
    },
    77011: (e) => {
      e.exports = ['สัญลักษณ์และธง'];
    },
    15426: (e) => {
      e.exports = ['ที่เพิ่งใช้ล่าสุด'];
    },
    15395: (e) => {
      e.exports = ['การเดินทางและสถานที่'];
    },
    82401: (e) => {
      e.exports = ['เคอร์เซอร์'];
    },
    99878: (e) => {
      e.exports = ['เครื่องมืออธิบายประกอบ'];
    },
    44629: (e) => {
      e.exports = ['เพิ่มลงรายการโปรด'];
    },
    75377: (e) => {
      e.exports = ['เครื่องมือแกนน์และฟิโบนัชชี'];
    },
    95537: (e) => {
      e.exports = ['รูปเรขาคณิต'];
    },
    60925: (e) => {
      e.exports = ['จุด'];
    },
    19570: (e) => {
      e.exports = ['อีโมจิ'];
    },
    99289: (e) => {
      e.exports = ['ยางลบ'];
    },
    17517: (e) => {
      e.exports = ['ซ่อนรูปวาดทั้งหมด'];
    },
    96411: (e) => {
      e.exports = ['ซ่อนแถบเครื่องมือวาด'];
    },
    92464: (e) => {
      e.exports = ['ไอคอน'];
    },
    37057: (e) => {
      e.exports = ['ล๊อครูปวาดทั้งหมด'];
    },
    37140: (e) => {
      e.exports = [
        'โหมดแม่เหล็กจะนำการวาดต่างๆ ที่อยู่ใกล้กับแท่งราคาไปเกาะกับค่า OHLC ที่ใกล้เคียงที่สุด',
      ];
    },
    59607: (e) => {
      e.exports = ['การวัด'];
    },
    36551: (e) => {
      e.exports = [
        'รูปวาดใหม่ถูกทำซ้ำไปยังชาร์ตทั้งหมดในเลย์เอาท์และแสดงเมื่อทิคเกอร์เดิมได้ถูกเลือก',
      ];
    },
    63354: (e) => {
      e.exports = ['แสดงทูลบาร์วาดเขียน'];
    },
    49616: (e) => {
      e.exports = ['แสดงแถบเครื่องมือการวาดที่ชื่นชอบ'];
    },
    91977: (e) => {
      e.exports = ['แสดงเครื่องมือที่ซ่อนไว้'];
    },
    51072: (e) => {
      e.exports = ['แสดงแผนผังวัตถุ'];
    },
    49421: (e) => {
      e.exports = ['อยู่ในโหมดการวาดเขียน'];
    },
    85422: (e) => {
      e.exports = ['แม่เหล็กแข็งแกร่ง'];
    },
    19693: (e) => {
      e.exports = ['รูปแบบ'];
    },
    10234: (e) => {
      e.exports = ['เครื่องมือคาดการณ์และการวัด'];
    },
    76091: (e) => {
      e.exports = ['ลบการวาดต่างๆ ออกไป'];
    },
    45286: (e) => {
      e.exports = ['เอาวัตถุออก'];
    },
    72482: (e) => {
      e.exports = ['ลบออกจากรายการโปรด'];
    },
    30513: (e) => {
      e.exports = ['ลบ {drawings}'];
    },
    10049: (e) => {
      e.exports = ['ลบ {drawings} & {indicators}'];
    },
    55084: (e) => {
      e.exports = ['ลบ {indicators}'];
    },
    45265: (e) => {
      e.exports = ['แม่เหล็กอ่อนค่า'];
    },
    52899: (e) => {
      e.exports = ['เครื่องมือเส้นแนวโน้ม'];
    },
    38925: (e) => {
      e.exports = ['ขยายเข้า'];
    },
    49895: (e) => {
      e.exports = ['ขยายออก'];
    },
    32868: (e) => {
      e.exports = ['{hotKey_0} + กดบนกราฟ'];
    },
    68125: (e) => {
      e.exports = ['{hotKey_0} — วงกลม'];
    },
    40234: (e) => {
      e.exports = ['{hotKey_0} — วาดเส้นตรง 1 เส้นที่ทำมุม 45 องศา'];
    },
    10289: (e) => {
      e.exports = ['{hotKey_0} - เพิ่มขึ้นแบบคงที่'];
    },
    81591: (e) => {
      e.exports = ['{hotKey_0} — สี่เหลี่ยม'];
    },
    93030: (e) => {
      e.exports = ['{amount} การวาด'];
    },
    80437: (e) => {
      e.exports = ['{amount} อินดิเคเตอร์'];
    },
  },
]);
