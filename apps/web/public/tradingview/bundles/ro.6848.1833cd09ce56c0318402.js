(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6848],
  {
    40276: (e) => {
      e.exports = 'Add';
    },
    53585: (e) => {
      e.exports = ['Add Custom Color'];
    },
    81865: (e) => {
      e.exports = 'Opacity';
    },
    60558: (e) => {
      e.exports = 'animals & nature';
    },
    14232: (e) => {
      e.exports = 'activity';
    },
    35305: (e) => {
      e.exports = 'food & drink';
    },
    49546: (e) => {
      e.exports = 'flags';
    },
    72302: (e) => {
      e.exports = 'objects';
    },
    96330: (e) => {
      e.exports = 'smiles & people';
    },
    6878: (e) => {
      e.exports = 'symbols';
    },
    15426: (e) => {
      e.exports = 'recently used';
    },
    15395: (e) => {
      e.exports = 'travel & places';
    },
    73755: (e) => {
      e.exports = 'Another symbol';
    },
    16936: (e) => {
      e.exports = 'Back';
    },
    88046: (e) => {
      e.exports = 'Main chart symbol';
    },
    9898: (e) => {
      e.exports = 'Right';
    },
    20036: (e) => {
      e.exports = 'Cancel';
    },
    72171: (e) => {
      e.exports = 'Center';
    },
    23398: (e) => {
      e.exports = 'Change symbol';
    },
    94551: (e) => {
      e.exports = 'Chart';
    },
    64498: (e) => {
      e.exports = 'All sources';
    },
    91757: (e) => {
      e.exports = 'Bottom';
    },
    79852: (e) => {
      e.exports = 'Bond';
    },
    16079: (e) => {
      e.exports = 'Gradient';
    },
    42973: (e) => {
      e.exports = 'Dotted line';
    },
    59317: (e) => {
      e.exports = 'Dashed line';
    },
    56095: (e) => {
      e.exports = 'Decrease';
    },
    29601: (e) => {
      e.exports = 'Description';
    },
    77405: (e) => {
      e.exports = 'Horizontal';
    },
    46812: (e) => {
      e.exports = 'Increase';
    },
    89298: (e) => {
      e.exports = 'Offset';
    },
    68988: (e) => {
      e.exports = 'Ok';
    },
    19286: (e) => {
      e.exports = 'Left';
    },
    76476: (e) => {
      e.exports = 'Middle';
    },
    29673: (e) => {
      e.exports = 'No exchanges match your criteria';
    },
    41379: (e) => {
      e.exports = 'No symbols match your criteria';
    },
    55362: (e) => {
      e.exports = 'Normal';
    },
    35563: (e) => {
      e.exports = 'Number format is invalid.';
    },
    19724: (e) => {
      e.exports = 'Sources';
    },
    35637: (e) => {
      e.exports = 'Solid';
    },
    52298: (e) => {
      e.exports = 'Search';
    },
    13269: (e) => {
      e.exports = 'Select source';
    },
    2607: (e) => {
      e.exports =
        'Specified value is more than the instrument maximum of {max}.';
    },
    53669: (e) => {
      e.exports =
        'Specified value is less than the instrument minimum of {min}.';
    },
    89053: (e) => {
      e.exports = 'Symbol';
    },
    48490: (e) => {
      e.exports = 'Symbol & description';
    },
    99983: (e) => {
      e.exports = 'Symbol Search';
    },
    54336: (e) => {
      e.exports = 'Remove color';
    },
    21141: (e) => {
      e.exports = 'Right';
    },
    65994: (e) => {
      e.exports = 'Top';
    },
    92960: (e) => {
      e.exports = 'Text alignment';
    },
    90581: (e) => {
      e.exports = 'Text orientation';
    },
    60142: (e) => {
      e.exports = 'Thickness';
    },
    78019: (e) => {
      e.exports =
        'Use special math signs to displace selected drawings: +,-,/,* for price and +,- for bar index.';
    },
    44085: (e) => {
      e.exports = 'Vertical';
    },
    87592: (e) => {
      e.exports = 'cfd';
    },
    17023: (e) => {
      e.exports = ['Change Opacity'];
    },
    13066: (e) => {
      e.exports = ['Change Color'];
    },
    95657: (e) => {
      e.exports = ['Change Thickness'];
    },
    18567: (e) => {
      e.exports = 'change {propertyName} property';
    },
    36962: (e) => {
      e.exports = 'close';
    },
    8448: (e) => {
      e.exports = 'crypto';
    },
    1328: (e) => {
      e.exports = 'dr';
    },
    76080: (e) => {
      e.exports = 'e.g. +1';
    },
    95166: (e) => {
      e.exports = 'e.g. /2';
    },
    88720: (e) => {
      e.exports = 'economy';
    },
    39512: (e) => {
      e.exports = 'forex';
    },
    81859: (e) => {
      e.exports = 'futures';
    },
    39337: (e) => {
      e.exports = 'high';
    },
    91815: (e) => {
      e.exports = 'hl2';
    },
    40771: (e) => {
      e.exports = 'hlc3';
    },
    9523: (e) => {
      e.exports = 'hlcc4';
    },
    12754: (e) => {
      e.exports = 'index';
    },
    38071: (e) => {
      e.exports = 'indices';
    },
    12504: (e) => {
      e.exports = 'ohlc4';
    },
    38466: (e) => {
      e.exports = 'open';
    },
    3919: (e) => {
      e.exports = 'low';
    },
    36931: (e) => {
      e.exports = 'stock';
    },
  },
]);
