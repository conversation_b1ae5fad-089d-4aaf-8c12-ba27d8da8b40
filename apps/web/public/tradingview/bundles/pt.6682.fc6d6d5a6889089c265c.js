(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6682],
  {
    9671: (e) => {
      e.exports = ['#{count} (preço, barra)'];
    },
    4639: (e) => {
      e.exports = ['Coordenadas'];
    },
    22192: (e) => {
      e.exports = ['Dias'];
    },
    37067: (e) => {
      e.exports = ['Deslocamento (preço, barra)'];
    },
    13611: (e) => {
      e.exports = ['Estender Linhas'];
    },
    63099: (e) => {
      e.exports = ['Horas'];
    },
    66304: (e) => {
      e.exports = ['Valores'];
    },
    95543: (e) => {
      e.exports = ['Meses'];
    },
    28134: (e) => {
      e.exports = ['Minutos'];
    },
    71129: (e) => {
      e.exports = ['Segundos'];
    },
    86672: (e) => {
      e.exports = 'Ranges';
    },
    21594: (e) => {
      e.exports = ['Semanas'];
    },
    23723: (e) => {
      e.exports = ['mudar a coordenada da barra X'];
    },
    66266: (e) => {
      e.exports = ['mudar o preço da coordenada Y'];
    },
    13355: (e) => {
      e.exports = ['alterar {title} em dias para'];
    },
    41377: (e) => {
      e.exports = ['alterar {title} em dias de'];
    },
    96902: (e) => {
      e.exports = ['alterar linhas estendidas de {title}'];
    },
    35388: (e) => {
      e.exports = ['alterar {title} em horas de'];
    },
    78586: (e) => {
      e.exports = ['alterar {title} em horas para'];
    },
    59635: (e) => {
      e.exports = ['alterar {title} em meses de'];
    },
    74266: (e) => {
      e.exports = ['alterar {title} em meses para'];
    },
    91633: (e) => {
      e.exports = ['alterar {title} em minutos para'];
    },
    15106: (e) => {
      e.exports = ['alterar {title} em minutos de'];
    },
    66161: (e) => {
      e.exports = ['alterar {title} em segundos para'];
    },
    2822: (e) => {
      e.exports = ['alterar {title} em segundos de'];
    },
    21339: (e) => {
      e.exports = ['alterar {title} em semanas de'];
    },
    68643: (e) => {
      e.exports = ['alterar {title} em semanas para'];
    },
    30810: (e) => {
      e.exports = ['alterar a visibilidade de {title} nos ticks'];
    },
    24941: (e) => {
      e.exports = ['alterar visibilidade em semanas de {title}'];
    },
    29088: (e) => {
      e.exports = ['alterar visibilidade em dias de {title}'];
    },
    68971: (e) => {
      e.exports = ['alterar a visibilidade em horas de {title}'];
    },
    64370: (e) => {
      e.exports = ['alterar visibilidade em minutos de {title}'];
    },
    6659: (e) => {
      e.exports = ['alterar visibilidade em meses de {title}'];
    },
    29091: (e) => {
      e.exports = ['alterar visibilidade no range de {title}'];
    },
    46948: (e) => {
      e.exports = ['alterar a visibilidade em segundos de {title}'];
    },
    18567: (e) => {
      e.exports = ['alterar propriedade {propertyName}'];
    },
    82211: (e) => {
      e.exports = ['Dias'];
    },
    33486: (e) => {
      e.exports = ['dias para'];
    },
    14077: (e) => {
      e.exports = ['dias a partir de'];
    },
    3143: (e) => {
      e.exports = ['Horas'];
    },
    84775: (e) => {
      e.exports = ['horas a partir de'];
    },
    11255: (e) => {
      e.exports = ['horas para'];
    },
    72223: (e) => {
      e.exports = ['mover desenhos'];
    },
    58964: (e) => {
      e.exports = ['Meses'];
    },
    71770: (e) => {
      e.exports = ['meses a partir de'];
    },
    37179: (e) => {
      e.exports = ['meses para'];
    },
    16465: (e) => {
      e.exports = ['Minutos'];
    },
    72317: (e) => {
      e.exports = ['minutos para'];
    },
    25586: (e) => {
      e.exports = ['minutos a partir de'];
    },
    32925: (e) => {
      e.exports = ['segundos'];
    },
    39017: (e) => {
      e.exports = ['segundos para'];
    },
    6049: (e) => {
      e.exports = ['segundos a partir de'];
    },
    13604: (e) => {
      e.exports = ['Ranges'];
    },
    93016: (e) => {
      e.exports = ['semanas'];
    },
    32002: (e) => {
      e.exports = ['semanas a partir de'];
    },
    28091: (e) => {
      e.exports = ['semanas para'];
    },
    59523: (e) => {
      e.exports = ['Ticks'];
    },
  },
]);
