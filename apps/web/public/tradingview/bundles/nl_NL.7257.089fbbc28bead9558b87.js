(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [7257],
  {
    19801: (e) => {
      e.exports = 'Fr';
    },
    11268: (e) => {
      e.exports = 'Mo';
    },
    63331: (e) => {
      e.exports = 'Sa';
    },
    85954: (e) => {
      e.exports = 'Su';
    },
    26230: (e) => {
      e.exports = 'We';
    },
    24793: (e) => {
      e.exports = 'Th';
    },
    31533: (e) => {
      e.exports = 'Tu';
    },
    89790: (e) => {
      e.exports = 'Could not get Pine source code.';
    },
    39589: (e) => {
      e.exports = 'Collapse pane';
    },
    38154: (e) => {
      e.exports = 'Confirm Remove Study Tree';
    },
    65636: (e) => {
      e.exports = 'Cboe BZX';
    },
    36004: (e) => {
      e.exports = 'Create a free account';
    },
    69419: (e) => {
      e.exports = "All's well — Market is open.";
    },
    97637: (e) => {
      e.exports = 'April';
    },
    86797: (e) => {
      e.exports = 'August';
    },
    22519: (e) => {
      e.exports = ['Bar Change Values'];
    },
    52003: (e) => {
      e.exports =
        "Do you really want to delete study and all of it's children?";
    },
    68854: (e) => {
      e.exports = 'Double click';
    },
    97325: (e) => {
      e.exports = 'Data error';
    },
    52916: (e) => {
      e.exports = 'Data is updated once a day.';
    },
    25978: (e) => {
      e.exports =
        'Data is updated once per second, even if there are more updates on the market.';
    },
    57310: (e) => {
      e.exports = 'Data is delayed';
    },
    49321: (e) => {
      e.exports =
        'Data on our Basic plan is updated once per second, even if there are more updates on the market.';
    },
    55669: (e) => {
      e.exports = 'December';
    },
    83498: (e) => {
      e.exports = 'Delete pane';
    },
    59315: (e) => {
      e.exports = 'End of day data';
    },
    82751: (e) => {
      e.exports = ['Fout'];
    },
    40519: (e) => {
      e.exports = 'Evening. Market is open for post-market trading.';
    },
    80227: (e) => {
      e.exports = 'Exchange timezone';
    },
    16467: (e) => {
      e.exports = 'February';
    },
    25046: (e) => {
      e.exports = 'Fill out Exchange Agreements';
    },
    93666: (e) => {
      e.exports = 'Flag Symbol';
    },
    564: (e) => {
      e.exports = 'Fri';
    },
    72970: (e) => {
      e.exports = 'Friday';
    },
    88958: (e) => {
      e.exports = 'Holiday';
    },
    21686: (e) => {
      e.exports = 'Hide Indicator Legend';
    },
    26935: (e) => {
      e.exports = ['Indicator Arguments'];
    },
    26315: (e) => {
      e.exports = ['Indicator Titles'];
    },
    84098: (e) => {
      e.exports = ['Indicator Values'];
    },
    91459: (e) => {
      e.exports =
        "If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks";
    },
    50634: (e) => {
      e.exports = "It'll go to post-market trading in {remainingTime}.";
    },
    74537: (e) => {
      e.exports = "It'll open for pre-market trading in {remainingTime}.";
    },
    26910: (e) => {
      e.exports = 'January';
    },
    23230: (e) => {
      e.exports = 'July';
    },
    49385: (e) => {
      e.exports = 'June';
    },
    99487: (e) => {
      e.exports = ['OHLC Values'];
    },
    15815: (e) => {
      e.exports = 'One update per second';
    },
    90784: (e) => {
      e.exports = 'October';
    },
    75991: (e) => {
      e.exports = 'Open market status';
    },
    18429: (e) => {
      e.exports = 'Learn more';
    },
    39899: (e) => {
      e.exports = 'Move pane down';
    },
    70343: (e) => {
      e.exports = 'Move pane up';
    },
    83085: (e) => {
      e.exports = 'Mon';
    },
    61199: (e) => {
      e.exports = 'Monday';
    },
    41610: (e) => {
      e.exports = ['Meer'];
    },
    1653: (e) => {
      e.exports = 'Morning. Market is open for pre-market trading.';
    },
    56470: (e) => {
      e.exports = 'Maximize chart';
    },
    19603: (e) => {
      e.exports = 'Maximize pane';
    },
    68327: (e) => {
      e.exports = ['Mei'];
    },
    35732: (e) => {
      e.exports = 'Manage panes';
    },
    84675: (e) => {
      e.exports = 'March';
    },
    83949: (e) => {
      e.exports = 'Market open';
    },
    35701: (e) => {
      e.exports = 'Market opens in {remainingTime}.';
    },
    95814: (e) => {
      e.exports = 'Market closed';
    },
    98105: (e) => {
      e.exports = 'Market closes in {remainingTime}.';
    },
    87202: (e) => {
      e.exports = 'Market is currently on holiday. Lucky them.';
    },
    71194: (e) => {
      e.exports = 'November';
    },
    66324: (e) => {
      e.exports = 'Source code';
    },
    36835: (e) => {
      e.exports = 'Sat';
    },
    1144: (e) => {
      e.exports = 'Saturday';
    },
    40653: (e) => {
      e.exports = 'Scroll to the left';
    },
    26721: (e) => {
      e.exports = 'Scroll to the most recent bar';
    },
    35809: (e) => {
      e.exports = 'Scroll to the right';
    },
    61132: (e) => {
      e.exports = 'September';
    },
    28705: (e) => {
      e.exports = 'Show Indicator Legend';
    },
    51072: (e) => {
      e.exports = 'Show Object Tree';
    },
    37809: (e) => {
      e.exports = 'Show interval settings';
    },
    39045: (e) => {
      e.exports = 'Study Error';
    },
    86577: (e) => {
      e.exports = 'Sun';
    },
    72149: (e) => {
      e.exports = 'Sunday';
    },
    46041: (e) => {
      e.exports = 'Symbol price source';
    },
    39339: (e) => {
      e.exports = 'Symbol title';
    },
    29985: (e) => {
      e.exports = 'Post-market';
    },
    28412: (e) => {
      e.exports = 'Paid plans feature faster data updates.';
    },
    56042: (e) => {
      e.exports = 'Pre-market';
    },
    36015: (e) => {
      e.exports =
        'Real-time data for {description} is not supported right now. We may support it in the future.';
    },
    6667: (e) => {
      e.exports =
        'Real-time data for {symbolName} is provided by {exchange} exchange.';
    },
    48293: (e) => {
      e.exports = 'Restore chart';
    },
    91029: (e) => {
      e.exports = 'Restore pane';
    },
    75094: (e) => {
      e.exports = 'Wed';
    },
    7147: (e) => {
      e.exports = 'Wednesday';
    },
    52984: (e) => {
      e.exports =
        'To get real-time data for {description}, please buy the real-time data package.';
    },
    9787: (e) => {
      e.exports = 'Thu';
    },
    7951: (e) => {
      e.exports = 'Thursday';
    },
    57918: (e) => {
      e.exports =
        'This data is real-time, but it’s slightly different to its official counterpart coming from primary exchanges.';
    },
    68025: (e) => {
      e.exports =
        'This data is real-time, but it’s slightly different to its official counterpart coming from {exchange}.';
    },
    73717: (e) => {
      e.exports = "This symbol doesn't exist, please pick another one.";
    },
    57048: (e) => {
      e.exports = 'Time for a walk — this market is closed.';
    },
    94316: (e) => {
      e.exports = 'Tue';
    },
    44979: (e) => {
      e.exports = 'Tuesday';
    },
    8209: (e) => {
      e.exports = 'Unflag Symbol';
    },
    1111: (e) => {
      e.exports = 'Volume';
    },
    61311: (e) => {
      e.exports = ['Inzoomen'];
    },
    47602: (e) => {
      e.exports = ['Uitzoomen'];
    },
    57889: (e) => {
      e.exports = 'change OHLC values visibility';
    },
    18644: (e) => {
      e.exports = 'change open market status visibility';
    },
    45110: (e) => {
      e.exports = 'change bar change visibility';
    },
    31325: (e) => {
      e.exports = 'change indicator titles visibility';
    },
    99774: (e) => {
      e.exports = 'change indicator values visibility';
    },
    96162: (e) => {
      e.exports = 'change indicator arguments visibility';
    },
    26717: (e) => {
      e.exports = 'change symbol description visibility';
    },
    6091: (e) => {
      e.exports = 'change symbol field visibility';
    },
    9455: (e) => {
      e.exports = 'change volume values visibility';
    },
    39348: (e) => {
      e.exports = 'less than 1 minute';
    },
    87358: (e) => {
      e.exports = 'show {title}';
    },
    7827: (e) => {
      e.exports = '{days} and {hours}';
    },
    7435: (e) => {
      e.exports = '{exchange} by {originalExchange}';
    },
    19830: (e) => {
      e.exports = '{hours} and {minutes}';
    },
    1084: (e) => {
      e.exports =
        '{listedExchange} real-time data is available for free to registered users.';
    },
    11155: (e) => {
      e.exports = '{symbolName} data is delayed by {time} minutes.';
    },
    77033: (e) => {
      e.exports =
        'Data is updated once every {amount} second, even if there are more updates on the market.';
    },
    2121: (e) => {
      e.exports =
        'Data on our Basic plan is updated once every {amount} second, even if there are more updates on the market.';
    },
    5223: (e) => {
      e.exports = 'One update every {amount} second';
    },
    58609: (e) => {
      e.exports = '{number} day';
    },
    24430: (e) => {
      e.exports = '{number} hour';
    },
    67151: (e) => {
      e.exports = '{number} minute';
    },
  },
]);
