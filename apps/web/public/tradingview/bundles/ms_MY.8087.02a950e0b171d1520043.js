(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (a) => {
      a.exports = ['Tambah'];
    },
    53585: (a) => {
      a.exports = ['Tambah warna khusus'];
    },
    81865: (a) => {
      a.exports = ['Ketelusan'];
    },
    73755: (a) => {
      a.exports = ['Simbol lain'];
    },
    16936: (a) => {
      a.exports = ['Kembali'];
    },
    88046: (a) => {
      a.exports = ['Simbol utama carta'];
    },
    9898: (a) => {
      a.exports = ['Benar'];
    },
    20036: (a) => {
      a.exports = ['Batal'];
    },
    23398: (a) => {
      a.exports = ['Tukar simbol'];
    },
    94551: (a) => {
      a.exports = ['Carta'];
    },
    64498: (a) => {
      a.exports = ['Kesemua sumber'];
    },
    73226: (a) => {
      a.exports = ['Gunakan'];
    },
    79852: (a) => {
      a.exports = ['Bon'];
    },
    56095: (a) => {
      a.exports = ['Pengurangan'];
    },
    29601: (a) => {
      a.exports = ['Huraian'];
    },
    46812: (a) => {
      a.exports = ['Pertambahan'];
    },
    89298: (a) => {
      a.exports = ['Ofset'];
    },
    68988: (a) => {
      a.exports = 'Ok';
    },
    29673: (a) => {
      a.exports = ['Tiada bursa saham yang memenuhi kriteria anda.'];
    },
    41379: (a) => {
      a.exports = ['Tiada Simbol yang menepati kriteria anda'];
    },
    35563: (a) => {
      a.exports = ['Format nombor tidak sah.'];
    },
    19724: (a) => {
      a.exports = ['Sumber-sumber'];
    },
    59877: (a) => {
      a.exports = [
        'Tetapkan masa {inputInline} dan harga untuk {studyShortDescription}',
      ];
    },
    18571: (a) => {
      a.exports = [
        'Tetapkan masa {inputTitle} dan harga untuk {studyShortDescription}',
      ];
    },
    58552: (a) => {
      a.exports = ['Tetapkan harga {inputTitle} untuk {studyShortDescription}'];
    },
    80481: (a) => {
      a.exports = ['Tetapkan masa dan harga untuk "{studyShortDescription}"'];
    },
    42917: (a) => {
      a.exports = ['Tetapkan masa untuk "{studyShortDescription}"'];
    },
    6083: (a) => {
      a.exports = ['Tetapkan harga untuk "{studyShortDescription}"'];
    },
    52298: (a) => {
      a.exports = ['Cari'];
    },
    13269: (a) => {
      a.exports = ['Pilih sumber'];
    },
    2607: (a) => {
      a.exports = [
        'Nilai yang ditentukan adalah lebih daripada instrumen maksimum {max}.',
      ];
    },
    53669: (a) => {
      a.exports = [
        'Nilai yang ditentukan adalah kurang daripada instrumen minimum {min}.',
      ];
    },
    89053: (a) => {
      a.exports = ['Simbol'];
    },
    48490: (a) => {
      a.exports = ['Simbol & penjelasan'];
    },
    99983: (a) => {
      a.exports = ['Cari simbol'];
    },
    54336: (a) => {
      a.exports = ['Buang warna'];
    },
    60142: (a) => {
      a.exports = ['Ketebalan'];
    },
    87592: (a) => {
      a.exports = 'cfd';
    },
    17023: (a) => {
      a.exports = ['Tukar Kelegapan'];
    },
    13066: (a) => {
      a.exports = ['Tukar Warna'];
    },
    95657: (a) => {
      a.exports = ['Tukar Ketebalan'];
    },
    18567: (a) => {
      a.exports = ['tukar {propertyName} sifat'];
    },
    36962: (a) => {
      a.exports = ['Tutup'];
    },
    8448: (a) => {
      a.exports = ['kripto'];
    },
    1328: (a) => {
      a.exports = 'dr';
    },
    88720: (a) => {
      a.exports = ['ekonomi'];
    },
    39512: (a) => {
      a.exports = 'forex';
    },
    81859: (a) => {
      a.exports = ['pasaran hadapan'];
    },
    39337: (a) => {
      a.exports = ['Tinggi'];
    },
    91815: (a) => {
      a.exports = 'hl2';
    },
    40771: (a) => {
      a.exports = 'hlc3';
    },
    9523: (a) => {
      a.exports = 'hlcc4';
    },
    12754: (a) => {
      a.exports = ['indeks'];
    },
    38071: (a) => {
      a.exports = ['indeks'];
    },
    12504: (a) => {
      a.exports = 'ohlc4';
    },
    38466: (a) => {
      a.exports = ['Buka'];
    },
    3919: (a) => {
      a.exports = ['Rendah'];
    },
    36931: (a) => {
      a.exports = ['saham'];
    },
  },
]);
