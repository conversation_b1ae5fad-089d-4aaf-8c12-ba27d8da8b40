(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [6803],
  {
    22353: (t) => {
      t.exports = ['(Mở cửa + <PERSON> nhất + Thấp nhất + <PERSON><PERSON><PERSON> cửa) / 4'];
    },
    94884: (t) => {
      t.exports = ['(<PERSON> nhất + <PERSON>h<PERSON><PERSON> nhất + <PERSON><PERSON><PERSON> cửa) / 3'];
    },
    10591: (t) => {
      t.exports = ['(<PERSON> nhất + Thấp nhất) / 2'];
    },
    63243: (t) => {
      t.exports = ['Các thanh màu dựa trên đóng cửa phiên trước'];
    },
    72171: (t) => {
      t.exports = ['Trung tâm'];
    },
    9994: (t) => {
      t.exports = ['Điều chỉnh dữ liệu cho Cổ tức'];
    },
    10989: (t) => {
      t.exports = ['Điều chỉnh để thay đổi hợp đồng'];
    },
    70816: (t) => {
      t.exports = ['Đóng cửa trung bình'];
    },
    91757: (t) => {
      t.exports = ['Đáy'];
    },
    50430: (t) => {
      t.exports = ['Đường thấp nhất'];
    },
    83760: (t) => {
      t.exports = ['Thân'];
    },
    72269: (t) => {
      t.exports = ['Đường viền'];
    },
    7445: (t) => {
      t.exports = ['Cấp Cơ sở'];
    },
    47586: (t) => {
      t.exports = ['Giá mua và bán'];
    },
    39667: (t) => {
      t.exports = ['Thanh dưới'];
    },
    87151: (t) => {
      t.exports = ['Giảm Màu'];
    },
    81285: (t) => {
      t.exports = ['Điều chỉnh dữ liệu'];
    },
    4329: (t) => {
      t.exports = ['Mặc định'];
    },
    86846: (t) => {
      t.exports = ['Điền vào'];
    },
    58747: (t) => {
      t.exports = ['Vùng Khớp lệnh nhiều nhất'];
    },
    11157: (t) => {
      t.exports = ['Điền vào Khu vực thấp nhất'];
    },
    86953: (t) => {
      t.exports = ['Thanh HLC'];
    },
    77405: (t) => {
      t.exports = ['Ngang'];
    },
    39292: (t) => {
      t.exports = ['Cao và thấp'];
    },
    15107: (t) => {
      t.exports = ['Lần cuối'];
    },
    19286: (t) => {
      t.exports = ['Bên trái'];
    },
    76476: (t) => {
      t.exports = ['Giữa'];
    },
    27879: (t) => {
      t.exports = ['Đơn giản'];
    },
    2391: (t) => {
      t.exports = ['Bước'];
    },
    6350: (t) => {
      t.exports = ['Trước/sau giờ mở cửa'];
    },
    62521: (t) => {
      t.exports = ['Nền thị trường trước/sau giờ mở cửa'];
    },
    73947: (t) => {
      t.exports = ['Độ chính xác'];
    },
    8094: (t) => {
      t.exports = ['Đóng cửa ngày hôm trước'];
    },
    77986: (t) => {
      t.exports = ['Đường giá'];
    },
    24248: (t) => {
      t.exports = ['Nguồn giá'];
    },
    94089: (t) => {
      t.exports = ['Chiếu thanh lên'];
    },
    5704: (t) => {
      t.exports = ['Chiếu thanh xuống'];
    },
    29881: (t) => {
      t.exports = ['Giá thực trên thang giá (thay vì giá Heikin-Ashi)'];
    },
    21141: (t) => {
      t.exports = ['Phải'];
    },
    44673: (t) => {
      t.exports = ['Với các Dấu hiệu'];
    },
    26458: (t) => {
      t.exports = ['Bóng nến'];
    },
    65994: (t) => {
      t.exports = ['Trên đầu'];
    },
    57417: (t) => {
      t.exports = ['Đường cao nhất'];
    },
    92960: (t) => {
      t.exports = ['Căn chỉnh chữ'];
    },
    90581: (t) => {
      t.exports = ['Chiều của chữ'];
    },
    55314: (t) => {
      t.exports = ['Thanh mỏng'];
    },
    87492: (t) => {
      t.exports = ['Múi giờ'];
    },
    58416: (t) => {
      t.exports = ['Loại'];
    },
    5536: (t) => {
      t.exports = ['Tăng Màu'];
    },
    83610: (t) => {
      t.exports = ['Thanh trên'];
    },
    23500: (t) => {
      t.exports = [
        'Sử dụng giải quyết càng gần vào khoảng thời gian hàng ngày',
      ];
    },
    44085: (t) => {
      t.exports = ['Dọc'];
    },
    30792: (t) => {
      t.exports = ['ký hiệu hình nến'];
    },
    55740: (t) => {
      t.exports = ['thay đổi các thanh HLC'];
    },
    90168: (t) => {
      t.exports = [
        'thay đổi bề rộng đường giá trung bình tại thời điểm đóng cửa',
      ];
    },
    30385: (t) => {
      t.exports = ['thay đổi màu đường giá trung bình tại thời điểm đóng cửa'];
    },
    97008: (t) => {
      t.exports = ['thay đổi màu sắc khu vực'];
    },
    6610: (t) => {
      t.exports = ['thay đổi độ rộng đường diện tích'];
    },
    661: (t) => {
      t.exports = ['thay đổi màu sắc đường diện tích'];
    },
    1316: (t) => {
      t.exports = ['thay đổi nguồn giá diện tích'];
    },
    29180: (t) => {
      t.exports = ['điều chỉnh màu sắc dòng bán'];
    },
    31547: (t) => {
      t.exports = ['thay đổi cấp độ cơ bản'];
    },
    4164: (t) => {
      t.exports = ['thay đổi màu đường cơ sở dưới'];
    },
    38990: (t) => {
      t.exports = ['thay đổi độ rộng đường cơ sở dưới'];
    },
    73163: (t) => {
      t.exports = ['thay đổi màu đường cơ sở khu vực dưới'];
    },
    12673: (t) => {
      t.exports = ['thay đổi màu đường cơ sở khu vực trên'];
    },
    56819: (t) => {
      t.exports = ['thay đổi nguồn giá đường cơ sở'];
    },
    68621: (t) => {
      t.exports = ['thay đổi màu đường cơ sở trên'];
    },
    35339: (t) => {
      t.exports = ['thay đổi độ rộng đường cơ sở trên'];
    },
    76804: (t) => {
      t.exports = ['tăng màu thanh lên'];
    },
    71816: (t) => {
      t.exports = ['giảm màu thanh xuống'];
    },
    36703: (t) => {
      t.exports = ['điều chỉnh màu sắc dòng mua'];
    },
    29353: (t) => {
      t.exports = ['thay đổi thanh màu dựa trên lần đóng trước đó'];
    },
    85709: (t) => {
      t.exports = ['thay đổi màu cột lên'];
    },
    12155: (t) => {
      t.exports = ['thay đổi màu cột xuống'];
    },
    66890: (t) => {
      t.exports = ['thay đổi nguồn giá cột'];
    },
    71809: (t) => {
      t.exports = ['thay đổi chữ số thập phân'];
    },
    31317: (t) => {
      t.exports = ['thay đổi màu giờ kéo dài'];
    },
    60944: (t) => {
      t.exports = ['thay đổi màu đường giá cao và thấp'];
    },
    83708: (t) => {
      t.exports = ['thay đổi chiều rộng đường cao và thấp'];
    },
    81080: (t) => {
      t.exports = ['thay đổi màu sắc nội dung cao-thấp'];
    },
    30033: (t) => {
      t.exports = ['thay đổi khả năng hiển thị nội dung từ cao xuống thấp'];
    },
    76885: (t) => {
      t.exports = ['thay đổi màu sắc đường viền cao-thấp'];
    },
    79236: (t) => {
      t.exports = ['thay đổi hiển thị đường viền cao-thấp'];
    },
    42981: (t) => {
      t.exports = ['thay đổi hiển thị nhãn cao-thấp'];
    },
    31937: (t) => {
      t.exports = ['thay đổi màu sắc nhãn cao-thấp'];
    },
    87828: (t) => {
      t.exports = ['thay đổi màu sắc đường'];
    },
    17119: (t) => {
      t.exports = ['thay đổi đường nguồn giá'];
    },
    69125: (t) => {
      t.exports = ['thay đổi độ rộng của đường'];
    },
    70054: (t) => {
      t.exports = ['thay đổi kiểu đường'];
    },
    49973: (t) => {
      t.exports = ['thay đổi màu sắc sau khi kết thúc phiên giao dịch'];
    },
    5969: (t) => {
      t.exports = ['thay đổi màu sắc đường sau khi kết thúc phiên giao dịch'];
    },
    50393: (t) => {
      t.exports = ['thay đổi khả năng hiển thị đường giá thị trường trước/sau'];
    },
    46257: (t) => {
      t.exports = ['điều chỉnh màu trước khi mở cửa thị trường'];
    },
    60852: (t) => {
      t.exports = ['điều chỉnh màu sắc đường trước khi thị trường mở'];
    },
    91183: (t) => {
      t.exports = ['điều chỉnh màu sắc dòng giá đóng cửa trước đó'];
    },
    87631: (t) => {
      t.exports = ['điều chỉnh độ rộng đường giá đóng cửa trước đó'];
    },
    77640: (t) => {
      t.exports = ['điều chỉnh màu sắc dòng giá'];
    },
    97322: (t) => {
      t.exports = ['điều chỉnh độ rộng dòng giá'];
    },
    28143: (t) => {
      t.exports = ['thay đổi phạm vi thanh mỏng'];
    },
    75986: (t) => {
      t.exports = ['giảm mức hiển thị wick của biểu đồ renko'];
    },
    7747: (t) => {
      t.exports = ['tăng mức hiển thị wick của biểu đồ renko'];
    },
    9473: (t) => {
      t.exports = ['thay đổi mức hiển thị wick của biểu đồ renko'];
    },
    39783: (t) => {
      t.exports = [
        'thay đổi hiển thị giá thực trên thang giá (thay vì giá Heiken-Ashi)',
      ];
    },
    72886: (t) => {
      t.exports = ['thay đổi thanh mỏng'];
    },
    5464: (t) => {
      t.exports = ['thay đổi màu đường viền trên {candleType}'];
    },
    61118: (t) => {
      t.exports = ['thay đổi màu phía trên {candleType}'];
    },
    60164: (t) => {
      t.exports = ['thay đổi màu ký tự wick dưới {candleType}'];
    },
    45543: (t) => {
      t.exports = ['thay đổi màu ký tự wick trên {candleType}'];
    },
    39987: (t) => {
      t.exports = [
        'thay đổi mức hiển thị wick, một ký tự hình cây nến dài {candleType}',
      ];
    },
    47202: (t) => {
      t.exports = ['thay đổi mức hiển thị phần thân {candleType}'];
    },
    23986: (t) => {
      t.exports = ['thay đổi màu đường viền {candleType}'];
    },
    92330: (t) => {
      t.exports = ['thay đổi màu đường viền dưới {candleType}'];
    },
    36320: (t) => {
      t.exports = ['thay đổi màu phía dưới {candleType}'];
    },
    79088: (t) => {
      t.exports = ['thay đổi màu thanh viền dưới {chartType}'];
    },
    11107: (t) => {
      t.exports = ['thay đổi màu thanh viền trên {chartType}'];
    },
    85503: (t) => {
      t.exports = ['thay đổi màu dưới {chartType}'];
    },
    61250: (t) => {
      t.exports = ['thay đổi màu viền thanh chiếu {chartType}'];
    },
    18465: (t) => {
      t.exports = ['thay đổi màu thanh chiếu dưới {chartType}'];
    },
    50453: (t) => {
      t.exports = ['thay đổi màu thanh chiếu trên {chartType}'];
    },
    59414: (t) => {
      t.exports = ['thay đổi màu trên {chartType}'];
    },
    21547: (t) => {
      t.exports = ['thay đổi thuộc tính {inputName}'];
    },
    42390: (t) => {
      t.exports = ['Điều chỉnh dữ liệu cho Cổ tức'];
    },
    99511: (t) => {
      t.exports = ['Điều chỉnh để thay đổi hợp đồng'];
    },
    75165: (t) => {
      t.exports = ['Biểu đồ nến Hollow'];
    },
    18995: (t) => {
      t.exports = ['phạm vi'];
    },
    47500: (t) => {
      t.exports = ['Renko'];
    },
    98402: (t) => {
      t.exports = ['Sử dụng giải quyết đóng tại khoảng thời gian hàng ngày'];
    },
  },
]);
