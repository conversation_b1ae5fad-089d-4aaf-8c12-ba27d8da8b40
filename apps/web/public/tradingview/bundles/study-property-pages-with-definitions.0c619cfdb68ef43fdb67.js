(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [607],
  {
    73856: (e, t, i) => {
      'use strict';
      i.r(t), i.d(t, { RegressionTrendDefinitionsViewModel: () => z });
      var l = i(44352),
        n = i(47539),
        r = (i(93731), i(65279)),
        a = i(46310),
        s = i(10381),
        o = i(23460);
      const d = new n.TranslatedString(
          'change {title} base line visibility',
          l.t(null, void 0, i(16688)),
        ),
        c = new n.TranslatedString(
          'change {title} base line color',
          l.t(null, void 0, i(97029)),
        ),
        u = new n.TranslatedString(
          'change {title} base line width',
          l.t(null, void 0, i(51676)),
        ),
        h = new n.TranslatedString(
          'change {title} base line style',
          l.t(null, void 0, i(3868)),
        ),
        p = new n.TranslatedString(
          'change {title} up line visibility',
          l.t(null, void 0, i(17564)),
        ),
        y = new n.TranslatedString(
          'change {title} up line color',
          l.t(null, void 0, i(25253)),
        ),
        g = new n.TranslatedString(
          'change {title} up line width',
          l.t(null, void 0, i(66118)),
        ),
        b = new n.TranslatedString(
          'change {title} up line style',
          l.t(null, void 0, i(58003)),
        ),
        _ = new n.TranslatedString(
          'change {title} down line visibility',
          l.t(null, void 0, i(23527)),
        ),
        v = new n.TranslatedString(
          'change {title} down line color',
          l.t(null, void 0, i(15438)),
        ),
        f = new n.TranslatedString(
          'change {title} down line width',
          l.t(null, void 0, i(44470)),
        ),
        m = new n.TranslatedString(
          'change {title} down line style',
          l.t(null, void 0, i(3782)),
        ),
        w = new n.TranslatedString(
          'change {title} extend lines',
          l.t(null, void 0, i(96902)),
        ),
        S = new n.TranslatedString(
          "change {title} show pearson's r",
          l.t(null, void 0, i(38317)),
        ),
        P = l.t(null, void 0, i(55719)),
        k = l.t(null, void 0, i(98802)),
        I = l.t(null, void 0, i(41361)),
        D = l.t(null, void 0, i(14105)),
        T = l.t(null, void 0, i(13611));
      class z extends a.StudyLineDataSourceDefinitionsViewModel {
        constructor(e, t) {
          super(e, t);
        }
        _stylePropertyDefinitions() {
          const e = this._source.properties().childs().styles.childs(),
            t = this._source.name(),
            i = (0, s.removeSpaces)(t),
            l = new n.TranslatedString(t, this._source.translatedType()),
            a = e.baseLine.childs(),
            z = (0, r.createLinePropertyDefinition)(
              {
                checked: (0, r.convertToDefinitionProperty)(
                  this._propertyApplier,
                  new o.StudyPlotVisibleProperty(a.display),
                  d.format({ title: l }),
                ),
                color: (0, r.getColorDefinitionProperty)(
                  this._propertyApplier,
                  a.color,
                  e.transparency,
                  c.format({ title: l }),
                ),
                width: (0, r.convertToDefinitionProperty)(
                  this._propertyApplier,
                  a.linewidth,
                  u.format({ title: l }),
                ),
                style: (0, r.convertToDefinitionProperty)(
                  this._propertyApplier,
                  a.linestyle,
                  h.format({ title: l }),
                ),
              },
              { id: `${i}BaseLine`, title: P },
            ),
            L = e.upLine.childs(),
            M = (0, r.createLinePropertyDefinition)(
              {
                checked: (0, r.convertToDefinitionProperty)(
                  this._propertyApplier,
                  new o.StudyPlotVisibleProperty(L.display),
                  p.format({ title: l }),
                ),
                color: (0, r.getColorDefinitionProperty)(
                  this._propertyApplier,
                  L.color,
                  e.transparency,
                  y.format({ title: l }),
                ),
                width: (0, r.convertToDefinitionProperty)(
                  this._propertyApplier,
                  L.linewidth,
                  g.format({ title: l }),
                ),
                style: (0, r.convertToDefinitionProperty)(
                  this._propertyApplier,
                  L.linestyle,
                  b.format({ title: l }),
                ),
              },
              { id: `${i}UpLine`, title: k },
            ),
            j = e.downLine.childs();
          return {
            definitions: [
              z,
              M,
              (0, r.createLinePropertyDefinition)(
                {
                  checked: (0, r.convertToDefinitionProperty)(
                    this._propertyApplier,
                    new o.StudyPlotVisibleProperty(j.display),
                    _.format({ title: l }),
                  ),
                  color: (0, r.getColorDefinitionProperty)(
                    this._propertyApplier,
                    j.color,
                    e.transparency,
                    v.format({ title: l }),
                  ),
                  width: (0, r.convertToDefinitionProperty)(
                    this._propertyApplier,
                    j.linewidth,
                    f.format({ title: l }),
                  ),
                  style: (0, r.convertToDefinitionProperty)(
                    this._propertyApplier,
                    j.linestyle,
                    m.format({ title: l }),
                  ),
                },
                { id: `${i}DownLine`, title: I },
              ),
              (0, r.createCheckablePropertyDefinition)(
                {
                  checked: (0, r.convertToDefinitionProperty)(
                    this._propertyApplier,
                    e.extendLines,
                    w.format({ title: l }),
                  ),
                },
                { id: `${i}ExtendLines`, title: T },
              ),
              (0, r.createCheckablePropertyDefinition)(
                {
                  checked: (0, r.convertToDefinitionProperty)(
                    this._propertyApplier,
                    e.showPearsons,
                    S.format({ title: l }),
                  ),
                },
                { id: `${i}Pearsons`, title: D },
              ),
            ],
          };
        }
      }
    },
    46310: (e, t, i) => {
      'use strict';
      i.r(t), i.d(t, { StudyLineDataSourceDefinitionsViewModel: () => u });
      var l = i(44352),
        n = (i(93731), i(26867)),
        r = i.n(n),
        a = i(65279),
        s = i(44785),
        o = i(46069),
        d = i(19402),
        c = i(10381);
      class u extends s.LineDataSourceDefinitionsViewModel {
        constructor(e, t) {
          super(e, t);
        }
        _inputsPropertyDefinitions() {
          return {
            definitions: [
              (0, a.createStudyInputsPropertyDefinition)(
                {},
                {
                  id: 'StudyInputs',
                  inputs: new o.MetaInfoHelper(
                    this._source.metaInfo(),
                  ).getUserEditableInputs(),
                  inputsTabProperty: this._source.properties(),
                  model: this._undoModel,
                  source: {
                    isInputsStudy: !0,
                    symbolsResolved: () => new (r())(),
                    resolvedSymbolInfoBySymbol: (e) => null,
                  },
                },
              ),
            ],
          };
        }
        _coordinatesPropertyDefinitions() {
          const e = this._source.points(),
            t = this._source.pointsProperty().childs().points,
            n = [];
          return (
            e.forEach((e, r) => {
              const s = t[r].childs();
              if (!s) return;
              const o = (0, d.getCoordinateXMetaInfo)(this._propertyApplier, s);
              n.push(
                (0, a.createCoordinatesPropertyDefinition)(
                  { x: o.property },
                  {
                    id: (0, c.removeSpaces)(`${this._source.name()}Point${r}`),
                    title: l
                      .t(null, { context: 'linetool point' }, i(63227))
                      .format({ count: (r + 1).toString() }),
                    ...o.info,
                  },
                ),
              );
            }),
            { definitions: n }
          );
        }
      }
    },
    4621: (e, t, i) => {
      'use strict';
      i.r(t), i.d(t, { StudyOverlayDefinitionsViewModel: () => H });
      var l = i(44352),
        n = i(47539),
        r = i(2484),
        a = i(50151),
        s = (i(93731), i(65279)),
        o = i(82623),
        d = i(8890),
        c = i(40549),
        u = i.n(c),
        h = i(10381);
      const p = new n.TranslatedString(
        'change {inputName} property',
        l.t(null, void 0, i(21547)),
      );
      function y(e, t) {
        const i = e.id;
        return (
          i !== d.RangeDependentStudyInputNames.FirstBar &&
          i !== d.RangeDependentStudyInputNames.LastBar &&
          'time' !== e.type &&
          !e.isHidden &&
          !(t && !e.confirm) &&
          void 0 === e.groupId
        );
      }
      function g(e) {
        return e.name || (0, h.capitalizeFirstLetterInWord)(e.id.toLowerCase());
      }
      function b(e) {
        return l.t(e, { context: 'input' }, i(88601));
      }
      var _ = i(68622),
        v = i(58403),
        f = i(92133),
        m = i(20196);
      const w = l.t(null, void 0, i(32733)),
        S = l.t(null, void 0, i(66304)),
        P = l.t(null, void 0, i(21852)),
        k = [
          '1',
          '3',
          '5',
          '15',
          '30',
          '45',
          '60',
          '120',
          '180',
          '240',
          '1D',
          '1W',
          '1M',
        ].map((e) => ({
          value: e,
          title: (0, f.getTranslatedResolutionModel)(e).hint,
        }));
      var I = i(36134),
        D = i(1930);
      const T = new n.TranslatedString(
          'change study overlay style',
          l.t(null, void 0, i(5529)),
        ),
        z = new n.TranslatedString(
          'change price line visibility',
          l.t(null, void 0, i(67761)),
        ),
        L = new n.TranslatedString(
          'change study overlay min tick',
          l.t(null, void 0, i(70016)),
        ),
        M = l.t(null, void 0, i(16812)),
        j = l.t(null, void 0, i(63528)),
        A = l.t(null, void 0, i(61582)),
        N = l.t(null, void 0, i(36018)),
        x = l.t(null, void 0, i(1277)),
        C = l.t(null, void 0, i(42097)),
        B = l.t(null, void 0, i(17712)),
        R = l.t(null, void 0, i(31994)),
        U = l.t(null, void 0, i(32733)),
        W = l.t(null, void 0, i(91492)),
        V = l.t(null, void 0, i(36993)),
        E = [
          { title: M, value: 0 },
          { title: j, value: 1 },
          { title: A, value: 9 },
          { title: N, value: 13 },
          { title: x, value: 2 },
          { title: C, value: 3 },
          { title: B, value: 10 },
        ];
      r.enabled('chart_style_hilo') && E.push({ title: R, value: 12 });
      class H extends class {
        constructor(e, t) {
          (this._inputSourceItems = null),
            (this._propertyPages = []),
            (this._sourceInput = null),
            (this._source = t),
            (this._undoModel = e);
          const i = this._sortInputs(this._source.metaInfo().inputs);
          for (const e of i) 'source' === e.type && (this._sourceInput = e);
          this._createPropertyRages(),
            null !== this._inputSourceItems &&
              this._undoModel
                .model()
                .dataSourceCollectionChanged()
                .subscribe(this, () => {
                  null !== this._inputSourceItems &&
                    this._inputSourceItems.setValue(
                      this._getInputSourceItems(),
                    );
                });
        }
        destroy() {
          null !== this._inputSourceItems &&
            this._undoModel
              .model()
              .dataSourceCollectionChanged()
              .unsubscribeAll(this),
            this._propertyPages.forEach((e) => {
              (0, s.destroyDefinitions)(e.definitions.value());
            });
        }
        propertyPages() {
          return Promise.resolve(this._propertyPages);
        }
        _createPropertyRages() {
          this._propertyPages = [];
          const e = this._createInputsPropertyPage();
          null !== e && this._propertyPages.push(e);
          const t = this._createStylePropertyPage();
          null !== t && this._propertyPages.push(t),
            this._propertyPages.push(this._createVisibilitiesPropertyPage());
        }
        _createStylePropertyPage() {
          const e = this._stylePropertyDefinitions();
          return null !== e ? (0, o.createPropertyPage)(e, 'style', w) : null;
        }
        _createVisibilitiesPropertyPage() {
          const e = this._source
            .properties()
            .childs()
            .intervalsVisibilities.childs();
          return (0, o.createPropertyPage)(
            (0, m.getIntervalsVisibilitiesPropertiesDefinitions)(
              this._undoModel,
              e,
              new n.TranslatedString(
                this._source.name(!0),
                this._source.title(!0),
              ),
            ),
            'visibility',
            P,
          );
        }
        _stylePropertyDefinitions() {
          return null;
        }
        _createInputsPropertyPage() {
          const e = this._inputsPropertyDefinitions();
          return null !== e ? (0, o.createPropertyPage)(e, 'inputs', S) : null;
        }
        _inputsPropertyDefinitions() {
          const e = this._sortInputs(this._source.metaInfo().inputs),
            t = this._source.properties().childs().inputs.childs();
          return (
            null !== this._sourceInput &&
              (this._inputSourceItems = new (u())(this._getInputSourceItems())),
            (function (e, t, r, o, d) {
              const c = [];
              for (const h of t) {
                if (!y(h, o)) continue;
                const t = g(h),
                  _ = `StudyInput${h.id}`,
                  v = b(t),
                  f = new n.TranslatedString(t, v);
                let m = null;
                if ('resolution' === h.type)
                  m = (0, s.createOptionsPropertyDefinition)(
                    {
                      option: (0, s.convertToDefinitionProperty)(
                        e,
                        r[h.id],
                        p.format({ inputName: f }),
                      ),
                    },
                    { id: _, title: v, options: new (u())(d.resolutionItems) },
                  );
                else if ('source' === h.type) {
                  const t = (0, a.ensure)(d.sourcesItems);
                  m = (0, s.createOptionsPropertyDefinition)(
                    {
                      option: (0, s.convertToDefinitionProperty)(
                        e,
                        r[h.id],
                        p.format({ inputName: f }),
                      ),
                    },
                    { id: _, title: v, options: t },
                  );
                } else if ('options' in h && void 0 !== h.options) {
                  const t = [];
                  for (const e of h.options) {
                    const n = (h.optionsTitles && h.optionsTitles[e]) || e,
                      r = l.t(n, { context: 'input' }, i(88601));
                    t.push({ value: e, title: r });
                  }
                  m = (0, s.createOptionsPropertyDefinition)(
                    {
                      option: (0, s.convertToDefinitionProperty)(
                        e,
                        r[h.id],
                        p.format({ inputName: f }),
                      ),
                    },
                    { id: _, title: v, options: new (u())(t) },
                  );
                } else if ('symbol' === h.type) {
                  const t = r[h.id],
                    i = (0, a.ensure)(d.getSymbolInfoBySymbol),
                    l = (0, a.ensure)(d.onSymbolsInfosChanged);
                  m = (0, s.createSymbolPropertyDefinition)(
                    {
                      symbol: (0, s.getSymbolDefinitionProperty)(
                        e,
                        t,
                        i,
                        l,
                        p.format({ inputName: f }),
                        d.customSymbolInputSetter,
                      ),
                    },
                    { id: _, title: v },
                  );
                } else if ('session' === h.type)
                  m = (0, s.createSessionPropertyDefinition)(
                    {
                      session: (0, s.convertToDefinitionProperty)(
                        e,
                        r[h.id],
                        p.format({ inputName: f }),
                      ),
                    },
                    { id: _, title: v },
                  );
                else if ('bool' === h.type)
                  m = (0, s.createCheckablePropertyDefinition)(
                    {
                      checked: (0, s.convertToDefinitionProperty)(
                        e,
                        r[h.id],
                        p.format({ inputName: f }),
                      ),
                    },
                    { id: _, title: v },
                  );
                else if (
                  'integer' === h.type ||
                  'float' === h.type ||
                  'price' === h.type
                ) {
                  const t = {
                    id: _,
                    title: v,
                    type: 'float' === h.type || 'price' === h.type ? 1 : 0,
                    defval: h.defval,
                  };
                  void 0 !== h.min && (t.min = new (u())(h.min)),
                    void 0 !== h.max && (t.max = new (u())(h.max)),
                    void 0 !== h.step &&
                      isFinite(h.step) &&
                      h.step > 0 &&
                      (t.step = new (u())(h.step)),
                    (m = (0, s.createNumberPropertyDefinition)(
                      {
                        value: (0, s.convertToDefinitionProperty)(
                          e,
                          r[h.id],
                          p.format({ inputName: f }),
                        ),
                      },
                      t,
                    ));
                } else
                  m = (0, s.createTextPropertyDefinition)(
                    {
                      text: (0, s.convertToDefinitionProperty)(
                        e,
                        r[h.id],
                        p.format({ inputName: f }),
                      ),
                    },
                    { id: _, title: v, isEditable: !0, isMultiLine: !1 },
                  );
                c.push(m);
              }
              return 0 === c.length ? null : { definitions: c };
            })(this._undoModel, e, t, !1, {
              resolutionItems: k,
              customSymbolInputSetter: this._customSymbolInputSetter(),
              getSymbolInfoBySymbol: this._getSymbolInfoBySymbol.bind(this),
              onSymbolsInfosChanged: this._source.symbolsResolved(),
              sourcesItems: this._inputSourceItems,
            })
          );
        }
        _sortInputs(e) {
          return e;
        }
        _getInputSourceItems() {
          const e = _.basePriceSources.slice(),
            t = (0, a.ensureNotNull)(this._sourceInput);
          if (this._source && this._source.isChildStudy()) {
            const i = this._source.inputs()[t.id],
              l = (0, a.ensureNotNull)(this._source.parentSourceForInput(i.v)),
              n = l.title(),
              r = v.StudyMetaInfo.getChildSourceInputTitles(t, l.metaInfo(), n);
            for (const t of Object.keys(r))
              e.push({ id: t, value: t, title: r[t] });
          }
          if (
            r.enabled('study_on_study') &&
            this._source &&
            (this._source.isChildStudy() ||
              v.StudyMetaInfo.canBeChild(this._source.metaInfo()))
          ) {
            const t = new Set([this._source, ...this._source.getAllChildren()]);
            this._undoModel
              .model()
              .allStudies()
              .filter((e) => e.canHaveChildren() && !t.has(e))
              .forEach((t) => {
                const i = t.title(!0, void 0, !0),
                  l = t.sourceId() || '#' + t.id(),
                  n = t.metaInfo(),
                  r = n.styles,
                  s = n.plots || [];
                if (1 === s.length) e.push({ id: l, value: l, title: i });
                else if (s.length > 1) {
                  const t = s.reduce((e, t, n) => {
                    if (!v.StudyMetaInfo.canPlotBeSourceOfChildStudy(t.type))
                      return e;
                    let s;
                    try {
                      s = (0, a.ensureDefined)(
                        (0, a.ensureDefined)(r)[t.id],
                      ).title;
                    } catch (e) {
                      s = t.id;
                    }
                    return { ...e, [`${l}$${n}`]: `${i}: ${s}` };
                  }, {});
                  for (const i of Object.keys(t))
                    e.push({ id: i, value: i, title: t[i] });
                }
              });
          }
          return e;
        }
        _customSymbolInputSetter() {}
        _getSymbolInfoBySymbol(e) {
          return this._source.resolvedSymbolInfoBySymbol(e.value());
        }
      } {
        constructor(e, t) {
          super(e, t),
            (this._stylesPropertyPage = null),
            this.propertyPages().then((e) => {
              this._stylesPropertyPage = e.filter((e) => 'style' === e.id)[0];
            }),
            this._source
              .properties()
              .childs()
              .style.subscribe(this, (e) => {
                var t;
                null !== this._stylesPropertyPage &&
                  ((0, s.destroyDefinitions)(
                    this._stylesPropertyPage.definitions.value(),
                  ),
                  this._stylesPropertyPage.definitions.setValue(
                    this._stylePropertyDefinitions().definitions,
                  )),
                  null === (t = this._availableStylesWV) ||
                    void 0 === t ||
                    t.setValue(this._availableStyles());
              });
        }
        destroy() {
          this._source.properties().childs().style.unsubscribeAll(this),
            this._source.symbolResolved().unsubscribeAll(this),
            super.destroy();
        }
        _customSymbolInputSetter() {
          return (e) => {
            this._undoModel.setSymbol(this._source, e);
          };
        }
        _stylePropertyDefinitions() {
          void 0 === this._availableStylesWV &&
            ((this._availableStylesWV = new (u())(this._availableStyles())),
            this._source.symbolResolved().subscribe(this, () => {
              var e;
              null === (e = this._availableStylesWV) ||
                void 0 === e ||
                e.setValue(this._availableStyles());
            }));
          const e = this._source.properties().childs(),
            t = (0, s.createOptionsPropertyDefinition)(
              {
                option: (0, s.convertToDefinitionProperty)(
                  this._undoModel,
                  e.style,
                  T,
                ),
              },
              {
                id: 'StudyOverlayStyle',
                title: U,
                options: this._availableStylesWV,
              },
            ),
            i = (0, s.createCheckablePropertyDefinition)(
              {
                checked: (0, s.convertToDefinitionProperty)(
                  this._undoModel,
                  e.showPriceLine,
                  z,
                ),
              },
              { id: 'StudyOverlayPriceLine', title: W },
            ),
            l = (0, s.createOptionsPropertyDefinition)(
              {
                option: (0, s.convertToDefinitionProperty)(
                  this._undoModel,
                  e.minTick,
                  L,
                ),
              },
              {
                id: 'StudyOverlayMinTick',
                title: V,
                options: new (u())((0, _.seriesPrecisionValues)()),
              },
            ),
            n = (0, h.removeSpaces)(this._source.title());
          return {
            definitions: [
              (0, s.createPropertyDefinitionsGeneralGroup)(
                [t, ...this._getSeriesStylesDefinitions()],
                `SeriesStyleGroup${n}`,
              ),
              i,
              l,
            ],
          };
        }
        _getSeriesStylesDefinitions() {
          const e = this._source.properties().childs(),
            t = e.style.value();
          return (0, I.getSeriesStylePropertiesDefinitions)(
            this._undoModel,
            e,
            t,
            {
              seriesPriceSources: _.basePriceSources,
              lineStyleTypes: _.lineStyleTypes,
              isJapaneseChartsAvailable: !1,
            },
            'mainSeries',
          );
        }
        _availableStyles() {
          const e = this._source.symbolInfo();
          return E.map((t) =>
            t.readonly
              ? t
              : {
                  readonly: !1,
                  value: t.value,
                  title: t.title,
                  disabled:
                    (0, D.isCloseBasedSymbol)(e) &&
                    !(0, D.isSingleValueBasedStyle)(t.value),
                },
          );
        }
      }
    },
    23460: (e, t, i) => {
      'use strict';
      i.d(t, { StudyPlotVisibleProperty: () => r });
      var l = i(26867),
        n = i.n(l);
      class r {
        constructor(e) {
          (this._subscribers = new (n())()),
            (this._displayProperty = e),
            this._displayProperty.subscribe(
              this,
              this._displayPropertyValueChanged,
            );
        }
        destroy() {
          this._displayProperty.unsubscribe(
            this,
            this._displayPropertyValueChanged,
          );
        }
        value() {
          return 0 !== this._displayProperty.value();
        }
        setValue(e, t) {
          this._displayProperty.setValue(e ? 15 : 0);
        }
        subscribe(e, t) {
          this._subscribers.subscribe(e, t, !1);
        }
        unsubscribe(e, t) {
          this._subscribers.unsubscribe(e, t);
        }
        unsubscribeAll(e) {
          this._subscribers.unsubscribeAll(e);
        }
        storeStateIfUndefined() {
          return !1;
        }
        _displayPropertyValueChanged() {
          this._subscribers.fire(this);
        }
      }
    },
    63227: (e) => {
      e.exports = {
        ar: ['#‎{count}‎ (عمود)'],
        ca_ES: ['#{count} (barra)'],
        cs: '#{count} (bar)',
        de: '#{count} (bar)',
        el: '#{count} (bar)',
        en: '#{count} (bar)',
        es: ['#{count} (barra)'],
        fa: '#{count} (bar)',
        fr: ['#{count} (barre)'],
        he_IL: ['# {count} (בר)'],
        hu_HU: '#{count} (bar)',
        id_ID: '#{count} (bar)',
        it: ['#{count} (barra)'],
        ja: ['#{count} (バー)'],
        ko: ['#{count} (바)'],
        ms_MY: '#{count} (bar)',
        nl_NL: '#{count} (bar)',
        pl: ['#{count} (słupek)'],
        pt: ['#{count} (barra)'],
        ro: '#{count} (bar)',
        ru: ['#{count} (бар)'],
        sv: ['#{count} (stapel)'],
        th: ['#{count} (แท่ง)'],
        tr: ['#{count} (çubuk)'],
        vi: ['#{count} (thanh)'],
        zh: ['#{count}（K线）'],
        zh_TW: ['#{count}（K棒）'],
      };
    },
    55719: (e) => {
      e.exports = {
        ar: ['نقطة الأساس'],
        ca_ES: 'Base',
        cs: 'Base',
        de: ['Basis'],
        el: 'Base',
        en: 'Base',
        es: 'Base',
        fa: 'Base',
        fr: 'Base',
        he_IL: ['בסיס'],
        hu_HU: ['Bázis'],
        id_ID: ['Dasar'],
        it: 'Base',
        ja: ['ベース'],
        ko: ['베이스'],
        ms_MY: ['Asas'],
        nl_NL: 'Base',
        pl: ['Baza'],
        pt: 'Base',
        ro: 'Base',
        ru: ['Базовая линия'],
        sv: ['Bas'],
        th: ['ฐาน'],
        tr: ['Taban'],
        vi: ['Cơ sở'],
        zh: ['基准线'],
        zh_TW: ['基準線'],
      };
    },
    41361: (e) => {
      e.exports = {
        ar: ['للأسفل'],
        ca_ES: ['Avall'],
        cs: 'Down',
        de: ['Runter'],
        el: 'Down',
        en: 'Down',
        es: ['Abajo'],
        fa: 'Down',
        fr: ['Bas'],
        he_IL: ['למטה'],
        hu_HU: ['Le'],
        id_ID: ['Turun'],
        it: ['Giù'],
        ja: ['下'],
        ko: ['다운'],
        ms_MY: ['Bawah'],
        nl_NL: 'Down',
        pl: ['W dół'],
        pt: ['Inferior'],
        ro: 'Down',
        ru: ['Вниз'],
        sv: ['Ned'],
        th: ['ทิศลง'],
        tr: ['Alt'],
        vi: ['Xuống'],
        zh: ['下'],
        zh_TW: ['下'],
      };
    },
    36993: (e) => {
      e.exports = {
        ar: ['تجاوز الحد الأدنى للتيك'],
        ca_ES: ['Anul·la el tick mínim'],
        cs: ['Přepsat Min Tick'],
        de: ['Min Tick überschreiben'],
        el: ['Override Min Tick'],
        en: 'Override min tick',
        es: ['Anular el tick mínimo'],
        fa: ['حداقل مقیاس قیمت'],
        fr: ['Ne pas tenir compte du Tick minimum'],
        he_IL: ['דריסת טיק מינימלי'],
        hu_HU: ['Min. Tick Felülírása'],
        id_ID: ['Menimpa Tick Min'],
        it: ['Sovrascrivi tick minimo'],
        ja: ['小数点表示'],
        ko: ['min tick 오버라이드'],
        ms_MY: ['Melarang Tanda Semak Minimum'],
        nl_NL: ['Overschrijven minimale tick'],
        pl: ['Zmień min tick'],
        pt: ['Alterar Tick Mín.'],
        ro: ['Override Min Tick'],
        ru: ['Минимальное изменение цены'],
        sv: ['Åsidosätt minimumkredit'],
        th: ['เขียนทับ Min Tick'],
        tr: ['Fiyatın Min Adımı'],
        vi: ['Ghi đè min tick'],
        zh: ['覆盖最小tick'],
        zh_TW: ['顯示最小刻度'],
      };
    },
    14105: (e) => {
      e.exports = {
        ar: ['معامل بيرسون'],
        ca_ES: ['Coeficient de correlació de Pearson'],
        cs: "Pearson's R",
        de: "Pearson's R",
        el: "Pearson's R",
        en: "Pearson's R",
        es: ['Coeficiente de correlación de Pearson'],
        fa: "Pearson's R",
        fr: ['Le R de Pearson'],
        he_IL: ['מתאם פירסון R'],
        hu_HU: "Pearson's R",
        id_ID: "Pearson's R",
        it: "Pearson's R",
        ja: ['ピアソンの積率相関係数'],
        ko: ["Pearson's 상관계수"],
        ms_MY: "Pearson's R",
        nl_NL: "Pearson's R",
        pl: ['R Pearsona'],
        pt: ['Correlação de Pearsons'],
        ro: "Pearson's R",
        ru: "Pearson's R",
        sv: "Pearson's R",
        th: ['เพียร์สัน อาร์'],
        tr: ['Pearson R'],
        vi: "Pearson's R",
        zh: ['相关系数'],
        zh_TW: ["皮爾遜相關係數(Pearson's R)"],
      };
    },
    98802: (e) => {
      e.exports = {
        ar: ['أعلى'],
        ca_ES: ['Amunt'],
        cs: 'Up',
        de: ['Aufwärts'],
        el: 'Up',
        en: 'Up',
        es: ['Arriba'],
        fa: 'Up',
        fr: ['Haut'],
        he_IL: ['למעלה'],
        hu_HU: ['Fel'],
        id_ID: ['Naik'],
        it: ['Su'],
        ja: ['上'],
        ko: ['업'],
        ms_MY: ['Naik'],
        nl_NL: 'Up',
        pl: ['W górę'],
        pt: ['Superior'],
        ro: 'Up',
        ru: ['Вверх'],
        sv: ['Upp'],
        th: ['บน'],
        tr: ['Üst'],
        vi: ['Lên'],
        zh: ['上'],
        zh_TW: ['上'],
      };
    },
    70016: (e) => {
      e.exports = {
        ar: ['تغيير تراكب أدنى تيك في الدراسة'],
        ca_ES: ["canvia ticks mínims de la superposició de l'estudi"],
        cs: 'change study overlay min tick',
        de: ['Overlay Studie in min tick ändern'],
        el: 'change study overlay min tick',
        en: 'change study overlay min tick',
        es: ['cambiar tics mínimos de la superposición del estudio'],
        fa: 'change study overlay min tick',
        fr: ["changer la superposition d'étude min tick"],
        he_IL: ['שנה מינימום טיק של שכבת המחקר'],
        hu_HU: 'change study overlay min tick',
        id_ID: ['ubah minimun tick overlay studi'],
        it: ['cambio tick min indicatore'],
        ja: ['インジケーターの最小ティックの変更'],
        ko: ['스터디 오버레이 최소 틱 바꾸기'],
        ms_MY: ['tukar tick minimum tindanan kajian'],
        nl_NL: 'change study overlay min tick',
        pl: ['Zmień minimalny tik wskaźnika'],
        pt: ['alterar a espessura min. do overlay do estudo'],
        ro: 'change study overlay min tick',
        ru: ['изменение мин. тик. значения символа сравнения'],
        sv: ['ändra överlagrings-min-tick för studien'],
        th: ['เปลี่ยน min tick ของ study overlay'],
        tr: ['min sembol kaplama çlş dğş'],
        vi: ['thay đổi lớp phủ nghiên cứu đánh dấu tối thiểu'],
        zh: ['更改研究覆盖最小tick'],
        zh_TW: ['更改研究覆蓋最小tick'],
      };
    },
    5529: (e) => {
      e.exports = {
        ar: ['تغيير نمط تراكب الدراسة'],
        ca_ES: ["canvia estil de superposició de l'estudi"],
        cs: 'change study overlay style',
        de: ['Overlay Stil ändern'],
        el: 'change study overlay style',
        en: 'change study overlay style',
        es: ['cambio estilo de superposición del estudio'],
        fa: 'change study overlay style',
        fr: ['changer le style de superposition des études'],
        he_IL: ['שנה את סגנון שכבת המחקר'],
        hu_HU: 'change study overlay style',
        id_ID: ['ubah corak overlay studi'],
        it: ['cambio stile indicatore'],
        ja: ['インジケーターのオーバーレイのスタイルの変更'],
        ko: ['스터디 오버레이 스타일 바꾸기'],
        ms_MY: ['tukar gaya tindanan kajian'],
        nl_NL: 'change study overlay style',
        pl: ['zmień styl nakładki badania'],
        pt: ['alterar estilo do overlay do estudo'],
        ro: 'change study overlay style',
        ru: ['изменение стиля символа сравнения'],
        sv: ['ändra överlagringsstil för studien'],
        th: ['เปลี่ยนรูปแบบ study overlay'],
        tr: ['çalışma yer paylaşımı stilini dğş'],
        vi: ['thay đổi kiểu bao phủ cho phần được nghiên cứu'],
        zh: ['更改研究覆盖样式'],
        zh_TW: ['改變研究覆蓋樣式'],
      };
    },
    97029: (e) => {
      e.exports = {
        ar: ['تغيير لون خط القاعدة {title}'],
        ca_ES: ['canvia el color de la línia de referència de {title}'],
        cs: 'change {title} base line color',
        de: ['{title} Farbe der Grundlinie ändern'],
        el: 'change {title} base line color',
        en: 'change {title} base line color',
        es: ['cambiar el color de la línea de referencia de {title}'],
        fa: 'change {title} base line color',
        fr: ['changer la couleur de la ligne de base de {title}'],
        he_IL: ['שנה את צבע קו הבסיס של {title}'],
        hu_HU: 'change {title} base line color',
        id_ID: ['ubah warna garis dasar {title}'],
        it: ['cambio colore linea base {title}'],
        ja: ['{title}のベースラインの色の変更'],
        ko: ['{title} 기본선 색상 변경'],
        ms_MY: ['tukar warna {title} garisan asas'],
        nl_NL: 'change {title} base line color',
        pl: ['zmień kolor linii bazowej dla {title}'],
        pt: ['alterar a cor da linha base de {title}'],
        ro: 'change {title} base line color',
        ru: ['изменение цвета линии стандарта: {title}'],
        sv: ['ändra baslinjefärg för {title}'],
        th: ['เปลี่ยนสี {title} เส้นพื้นฐาน'],
        tr: ['{title} temel çizgi rengini dğş'],
        vi: ['điều chỉnh màu đường cơ sở {title}'],
        zh: ['更改{title}基准线颜色'],
        zh_TW: ['更改{title}基準線顏色'],
      };
    },
    3868: (e) => {
      e.exports = {
        ar: ['تغيير نمط خط القاعدة {title}'],
        ca_ES: ["canvia l'estil de la línia de referència de {title}"],
        cs: 'change {title} base line style',
        de: ['{title} Stil der Grundlinie ändern'],
        el: 'change {title} base line style',
        en: 'change {title} base line style',
        es: ['cambiar el estilo de la línea de referencia de {title}'],
        fa: 'change {title} base line style',
        fr: ['changer le style de la ligne de base de {title}'],
        he_IL: ['שנה את סגנון קו הבסיס של {title}'],
        hu_HU: 'change {title} base line style',
        id_ID: ['ubah corak garis dasar {title}'],
        it: ['cambio stile linea base {title}'],
        ja: ['{title}のベースラインのスタイルの変更'],
        ko: ['{title} 기본선 스타일 변경'],
        ms_MY: ['tukar gaya {title} garisan jalur asas'],
        nl_NL: 'change {title} base line style',
        pl: ['zmień styl linii bazowej dla {title}'],
        pt: ['alterar o estilo da linha base de {title}'],
        ro: 'change {title} base line style',
        ru: ['изменение стиля линии стандарта: {title}'],
        sv: ['ändra baslinjestil för {title}'],
        th: ['เปลี่ยนรูปแบบ {title} เส้นพื้นฐาน'],
        tr: ['{title} temel çizgi stilini dğş'],
        vi: ['điều chỉnh kiểu đường cơ sở {title}'],
        zh: ['更改{title}基准线样式'],
        zh_TW: ['更改{title}基準線樣式'],
      };
    },
    16688: (e) => {
      e.exports = {
        ar: ['تغيير وضوح خط القاعدة {title}'],
        ca_ES: ['canvia la visibilitat de la línia de referència de {title}'],
        cs: 'change {title} base line visibility',
        de: ['{title} Sichtbarkeit der Grundlinie ändern'],
        el: 'change {title} base line visibility',
        en: 'change {title} base line visibility',
        es: ['cambiar la visibilidad de la línea de referencia de {title}'],
        fa: 'change {title} base line visibility',
        fr: ['changer la visibilité de la ligne de base de {title}'],
        he_IL: ['שנה את נראות קו הבסיס של {title}'],
        hu_HU: 'change {title} base line visibility',
        id_ID: ['ubah visibilitas garis dasar {title}'],
        it: ['cambio visibilità linea base {title}'],
        ja: ['{title}のベースラインの表示の変更'],
        ko: ['{title} 기본선 가시성 변경'],
        ms_MY: ['tukar kebolehlihatan {title} garisan asas'],
        nl_NL: 'change {title} base line visibility',
        pl: ['zmień widoczność linii bazowej dla {title}'],
        pt: ['alterar a visibilidade da linha base de {title}'],
        ro: 'change {title} base line visibility',
        ru: ['изменение видимости линии стандарта: {title}'],
        sv: ['ändra synlighet för {title}s baslinje'],
        th: ['เปลี่ยนการมองเห็น {title} เส้นพื้นฐาน'],
        tr: ['{title} temel çizgi görünürlüğünü dğş'],
        vi: ['điều chỉnh hiển thị đường cơ sở {title}'],
        zh: ['更改{title}基准线可见性'],
        zh_TW: ['更改{title}基準線可見性'],
      };
    },
    51676: (e) => {
      e.exports = {
        ar: ['تغيير عرض خط القاعدة {title}'],
        ca_ES: ["canvia l'ample de la línia de referència de {title}"],
        cs: 'change {title} base line width',
        de: ['{title} Linienbreite der Grundlinie ändern'],
        el: 'change {title} base line width',
        en: 'change {title} base line width',
        es: ['cambiar el ancho de la línea de referencia de {title}'],
        fa: 'change {title} base line width',
        fr: ['changer la largeur de la ligne de base de {title}'],
        he_IL: ['שנה את רוחב קו הבסיס של {title}'],
        hu_HU: 'change {title} base line width',
        id_ID: ['ubah lebar garis dasar {title}'],
        it: ['cambio spessore linea base {title}'],
        ja: ['{title}のベースラインの幅の変更'],
        ko: ['{title} 기본선 너비 변경'],
        ms_MY: ['tukar tebal {title} garisan jalur asas'],
        nl_NL: 'change {title} base line width',
        pl: ['zmień szerokość linii bazowej dla {title}'],
        pt: ['alterar a largura da linha base de {title}'],
        ro: 'change {title} base line width',
        ru: ['изменение толщины линии стандарта: {title}'],
        sv: ['ändra baslinjebredd för {title}'],
        th: ['เปลี่ยนความกว้าง {title} เส้นพื้นฐาน'],
        tr: ['{title} taban çizgisi genişliğini dğş'],
        vi: ['điều chỉnh độ rộng đường cơ sở {title}'],
        zh: ['更改{title}基准线宽度'],
        zh_TW: ['更改{title}基準線寬度'],
      };
    },
    15438: (e) => {
      e.exports = {
        ar: ['تغيير لون الخط الأدنى {title}'],
        ca_ES: ['canvia color de la línia inferior de {title}'],
        cs: 'change {title} down line color',
        de: ['{title} Farbe der Abwärtslinie ändern'],
        el: 'change {title} down line color',
        en: 'change {title} down line color',
        es: ['cambiar color de la línea inferior de {title}'],
        fa: 'change {title} down line color',
        fr: ['changer la couleur de la ligne du bas de {title}'],
        he_IL: ['שנה את צבע הקו התחתון של {title}'],
        hu_HU: 'change {title} down line color',
        id_ID: ['ubah warna garis bawah {title}'],
        it: ['cambio colore linea inf {title}'],
        ja: ['{title}の下降ラインの色の変更'],
        ko: ['{title} 다운 라인 컬러 바꾸기'],
        ms_MY: ['tukar warna {title} garisan bawah'],
        nl_NL: 'change {title} down line color',
        pl: ['zmień kolor linii spadkowej dla {title}'],
        pt: ['alterar a cor da linha inferior de {title}'],
        ro: 'change {title} down line color',
        ru: ['изменение цвета нижней линии: {title}'],
        sv: ['ändra den nedre linjefärgen för {title}'],
        th: ['เปลี่ยนสี {title} เส้นขาลง'],
        tr: ['{title} alt çizgi rengini dğş'],
        vi: ['điều chỉnh hiển thị đường dưới {title}'],
        zh: ['更改{title}向下线条颜色'],
        zh_TW: ['更改{title}向下線條顏色'],
      };
    },
    3782: (e) => {
      e.exports = {
        ar: ['تغيير نمط الخط الأدنى {title}'],
        ca_ES: ["canvia l'estil de la línia descendent de {title}"],
        cs: 'change {title} down line style',
        de: ['{title} Stil der Abwärtslinie ändern'],
        el: 'change {title} down line style',
        en: 'change {title} down line style',
        es: ['cambiar el estilo de la línea descendente de {title}'],
        fa: 'change {title} down line style',
        fr: ['changer le style de la ligne du bas de {title}'],
        he_IL: ['שנה את סגנון הקו התחתון של {title}'],
        hu_HU: 'change {title} down line style',
        id_ID: ['ubah corak garis bawah {title}'],
        it: ['cambio stile linea inf {title}'],
        ja: ['{title}の下降ラインのスタイルの変更'],
        ko: ['{title} 다운 라인 스타일 바꾸기'],
        ms_MY: ['tukar gaya {title} garisan bawah'],
        nl_NL: 'change {title} down line style',
        pl: ['zmień styl linii spadkowej dla {title}'],
        pt: ['alterar o estilo da linha inferior de {title}'],
        ro: 'change {title} down line style',
        ru: ['изменение стиля нижней линии: {title}'],
        sv: ['ändra nedre linjestilen för {title}'],
        th: ['เปลี่ยนรูปแบบ {title} เส้นขาลง'],
        tr: ['{title} alt satır stilini dğş'],
        vi: ['điều chỉnh kiểu đường dưới {title}'],
        zh: ['更改{title}向下线条样式'],
        zh_TW: ['更改{title}向下線條樣式'],
      };
    },
    23527: (e) => {
      e.exports = {
        ar: ['تغيير وضوح الخط الأدنى {title}'],
        ca_ES: ['canvia la visibilitat de la línia descendent de {title}'],
        cs: 'change {title} down line visibility',
        de: ['{title} Sichtbarkeit der Abwärtslinie ändern'],
        el: 'change {title} down line visibility',
        en: 'change {title} down line visibility',
        es: ['cambiar la visibilidad de la línea descendente de {title}'],
        fa: 'change {title} down line visibility',
        fr: ['changer la visibilité de la ligne du bas de {title}'],
        he_IL: ['שנה נראות קו תחתון של {title}'],
        hu_HU: 'change {title} down line visibility',
        id_ID: ['ubah visibilitas garis bawah {title}'],
        it: ['cambio visibilità linea inf {title}'],
        ja: ['{title}の下降ラインの表示の変更'],
        ko: ['{title} 다운 라인 비저빌리티 바꾸기'],
        ms_MY: ['tukar kebolehlihatan {title} garisan bawah'],
        nl_NL: 'change {title} down line visibility',
        pl: ['zmień widoczność linii spadkowej dla {title}'],
        pt: ['alterar a visibilidade da linha inferior de {title}'],
        ro: 'change {title} down line visibility',
        ru: ['изменение видимости нижней линии: {title}'],
        sv: ['ändra nedre linjesynligheten för {title}'],
        th: ['เปลี่ยนการมองเห็น {title} เส้นขาลง'],
        tr: ['{title} alt çizgi görünürlüğünü dğş'],
        vi: ['điều chỉnh hiển thị đường dưới {title}'],
        zh: ['更改{title}向下线条可见性'],
        zh_TW: ['更改{title}向下線條可見性'],
      };
    },
    44470: (e) => {
      e.exports = {
        ar: ['تغيير عرض الخط الأدنى {title}'],
        ca_ES: ["canvia l'ample de la línia inferior de {title}"],
        cs: 'change {title} down line width',
        de: ['{title} Breite der Abwärtslinie ändern'],
        el: 'change {title} down line width',
        en: 'change {title} down line width',
        es: ['cambiar el ancho de la línea inferior de {title}'],
        fa: 'change {title} down line width',
        fr: ['changer la largeur de la ligne du bas de {title}'],
        he_IL: ['שנה את רוחב הקו התחתון של {title}'],
        hu_HU: 'change {title} down line width',
        id_ID: ['ubah lebar garis bawah {title}'],
        it: ['cambio spessore linea inf {title}'],
        ja: ['{title}の下降ラインの幅の変更'],
        ko: ['{title} 다운 라인 너비 바꾸기'],
        ms_MY: ['tukar tebal {title} garisan bawah'],
        nl_NL: 'change {title} down line width',
        pl: ['zmień szerokość linii spadkowej dla {title}'],
        pt: ['alterar a largura da linha inferior de {title}'],
        ro: 'change {title} down line width',
        ru: ['изменение толщины нижней линии: {title}'],
        sv: ['ändra nedre linjebredden för {title}'],
        th: ['เปลี่ยนความกว้าง {title} เส้นขาลง'],
        tr: ['{title} alt çizgi genişliğini dğş'],
        vi: ['điều chỉnh độ rộng đường dưới {title}'],
        zh: ['更改{title}向下线条宽度'],
        zh_TW: ['更改{title}向下線條寬度'],
      };
    },
    38317: (e) => {
      e.exports = {
        ar: ["تغيير عرض {title} pearson's r"],
        ca_ES: ['canvia la visibilitat r de Pearson de {title}'],
        cs: "change {title} show pearson's r",
        de: ["{title} Pearson's R anzeigen"],
        el: "change {title} show pearson's r",
        en: "change {title} show pearson's r",
        es: ['cambiar la visibilidad r de Pearson de {title}'],
        fa: "change {title} show pearson's r",
        fr: ["changer show pearson's r de {title}"],
        he_IL: ['שנה {title} הצג את ה-r של פירסון'],
        hu_HU: "change {title} show pearson's r",
        id_ID: ['ubah {title} menampilkan r pearson'],
        it: ['cambio modifica visibilità r di Pearson {title}'],
        ja: ['{title}のピアソンの積率相関係数の表示の変更'],
        ko: ['{title} 피어슨 R 보기 바꾸기'],
        ms_MY: ["tukar {title} tunjuk pearson's r"],
        nl_NL: "change {title} show pearson's r",
        pl: [
          'Zmień sposób wyświetlania współczynnika korelacji momentu produktu Pearsona w {title}',
        ],
        pt: ['alterar exibir correlação de pearson de {title}'],
        ro: "change {title} show pearson's r",
        ru: ["отображение pearson's r: {title}"],
        sv: ['ändra {title} visa Pearsons k'],
        th: ['เปลี่ยน {title} การแสดงผลเพียร์สัน อาร์'],
        tr: ["{title} pearson'ın r'sini göster dğş"],
        vi: ["thay đổi {title} show pearson's r"],
        zh: ["更改{title}显示pearson's r"],
        zh_TW: ["更改{title}顯示pearson's r"],
      };
    },
    17564: (e) => {
      e.exports = {
        ar: ['تغيير وضوح الخط العلوي {title}'],
        ca_ES: ['canvia la visibilitat de la línia superior de {title}'],
        cs: 'change {title} up line visibility',
        de: ['{title} Sichtbarkeit der Aufwärtslinie ändern'],
        el: 'change {title} up line visibility',
        en: 'change {title} up line visibility',
        es: ['cambiar la visibilidad de la línea superior de {title}'],
        fa: 'change {title} up line visibility',
        fr: ['changer la visibilité de la ligne du haut de {title}'],
        he_IL: ['שנה את נראות הקו העליון של {title}'],
        hu_HU: 'change {title} up line visibility',
        id_ID: ['ubah visibilitas garis atas {title}'],
        it: ['cambio visibilità linea sup {title}'],
        ja: ['{title}の上昇ラインの表示の変更'],
        ko: ['{title} 업 라인 비저빌리티 바꾸기'],
        ms_MY: ['tukar kebolehlihatan {title} garisan atas'],
        nl_NL: 'change {title} up line visibility',
        pl: ['zmień widoczność linii wzrostowej dla {title}'],
        pt: ['alterar a visibilidade da linha superior de {title}'],
        ro: 'change {title} up line visibility',
        ru: ['изменение видимости верхней линии: {title}'],
        sv: ['ändra synlighet för {title}s övre linje'],
        th: ['เปลี่ยนการมองเห็น {title} เส้นขาขึ้น'],
        tr: ['{title} üst çizgi görünümünü dğş'],
        vi: ['điều chỉnh hiển thị đường trên {title}'],
        zh: ['更改{title}向上线条可见性'],
        zh_TW: ['更改{title}向上線條可見性'],
      };
    },
    66118: (e) => {
      e.exports = {
        ar: ['تغيير عرض الخط العلوي {title}'],
        ca_ES: ["canvia l'ample de la línia superior de {title}"],
        cs: 'change {title} up line width',
        de: ['{title} Breite der Aufwärtslinie ändern'],
        el: 'change {title} up line width',
        en: 'change {title} up line width',
        es: ['cambiar el ancho de la línea superior de {title}'],
        fa: 'change {title} up line width',
        fr: ['changer la largeur de la ligne du haut de {title}'],
        he_IL: ['שנה את רוחב הקו העליון של {title}'],
        hu_HU: 'change {title} up line width',
        id_ID: ['ubah lebar garis atas {title}'],
        it: ['cambio spessore linea sup {title}'],
        ja: ['{title}の上昇ラインの幅の変更'],
        ko: ['{title} 업 라인 너비 바꾸기'],
        ms_MY: ['tukar tebal {title} garisan atas'],
        nl_NL: 'change {title} up line width',
        pl: ['zmień szerokość linii wzrostowej dla {title}'],
        pt: ['alterar a largura da linha superior de {title}'],
        ro: 'change {title} up line width',
        ru: ['изменение толщины верхней линии: {title}'],
        sv: ['ändra övre linjebredd för {title}'],
        th: ['เปลี่ยนความกว้าง {title} เส้นขาขึ้น'],
        tr: ['{title} üst çizgi genişliğini dğş'],
        vi: ['điều chỉnh độ rộng đường trên {title}'],
        zh: ['更改{title}向上线条宽度'],
        zh_TW: ['更改{title}向上線條寬度'],
      };
    },
    25253: (e) => {
      e.exports = {
        ar: ['تغيير لون الخط العلوي {title}'],
        ca_ES: ['canvia el color de la línia superior de {title}'],
        cs: 'change {title} up line color',
        de: ['{title} Farbe der Aufwärtslinie ändern'],
        el: 'change {title} up line color',
        en: 'change {title} up line color',
        es: ['cambiar el color de la línea superior de {title}'],
        fa: 'change {title} up line color',
        fr: ['changer la couleur de la ligne du haut de {title}'],
        he_IL: ['שנה את צבע הקו העליון של {title}'],
        hu_HU: 'change {title} up line color',
        id_ID: ['ubah warna garis atas {title}'],
        it: ['cambio colore linea sup {title}'],
        ja: ['{title}の上昇ラインの色の変更'],
        ko: ['{title} 업 라인 컬러 바꾸기'],
        ms_MY: ['tukar warna {title} garisan atas'],
        nl_NL: 'change {title} up line color',
        pl: ['zmień kolor linii wzrostowej dla {title}'],
        pt: ['alterar a cor da linha superior de {title}'],
        ro: 'change {title} up line color',
        ru: ['изменение цвета верхней линии: {title}'],
        sv: ['ändra övre linjefärg för {title}'],
        th: ['เปลี่ยนสี {title} เส้นขาขึ้น'],
        tr: ['{title} üst çizgi rengini dğş'],
        vi: ['điều chỉnh hiển thị đường trên {title}'],
        zh: ['更改{title}向上线条颜色'],
        zh_TW: ['更改{title}向上線條顏色'],
      };
    },
    58003: (e) => {
      e.exports = {
        ar: ['تغيير نمط الخط العلوي {title}'],
        ca_ES: ["canvia l'estil de la línia superior de {title}"],
        cs: 'change {title} up line style',
        de: ['{title} Stil der Aufwärtslinie ändern'],
        el: 'change {title} up line style',
        en: 'change {title} up line style',
        es: ['cambiar el estilo de la línea superior de {title}'],
        fa: 'change {title} up line style',
        fr: ['changer le style de la ligne du haut de {title}'],
        he_IL: ['שנה את סגנון הקו העליון של {title}'],
        hu_HU: 'change {title} up line style',
        id_ID: ['ubah corak garis atas {title}'],
        it: ['cambio stile linea sup {title}'],
        ja: ['{title}の上昇ラインのスタイルの変更'],
        ko: ['{title} 업 라인 스타일 바꾸기'],
        ms_MY: ['tukar gaya {title} garisan atas'],
        nl_NL: 'change {title} up line style',
        pl: ['zmień styl linii wzrostowej dla {title}'],
        pt: ['alterar o estilo da linha superior de {title}'],
        ro: 'change {title} up line style',
        ru: ['изменение стиля верхней линии: {title}'],
        sv: ['ändra övre linjestil för {title}'],
        th: ['เปลี่ยนรูปแบบ {title} เส้นขาขึ้น'],
        tr: ['{title} üst çizgi stilini dğş'],
        vi: ['điều chỉnh kiểu đường trên {title}'],
        zh: ['更改{title}向上线条样式'],
        zh_TW: ['更改{title}向上線條樣式'],
      };
    },
  },
]);
