(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4800],
  {
    91282: (t) => {
      t.exports = ['#1 (çubuk)'];
    },
    1961: (t) => {
      t.exports = ['#1 (fiyat)'];
    },
    12706: (t) => {
      t.exports = ['#1 (fiyat, çubuk)'];
    },
    92195: (t) => {
      t.exports = ['#1 (dikey pozisyon %, çubuk)'];
    },
    66187: (t) => {
      t.exports = ['Medyan'];
    },
    5066: (t) => {
      t.exports = '%';
    },
    89795: (t) => {
      t.exports = ['SaatinAksiYönü'];
    },
    43809: (t) => {
      t.exports = ['Yüzde olarak katsayılar'];
    },
    40054: (t) => {
      t.exports = ['Renk'];
    },
    47737: (t) => {
      t.exports = ['Kompakt istatistik modu'];
    },
    76655: (t) => {
      t.exports = ['Nakit'];
    },
    72171: (t) => {
      t.exports = ['Orta'];
    },
    99120: (t) => {
      t.exports = ['Kanal'];
    },
    36150: (t) => {
      t.exports = ['Açı'];
    },
    38280: (t) => {
      t.exports = ['Açılar'];
    },
    95264: (t) => {
      t.exports = ['Hesap Boyutu'];
    },
    85160: (t) => {
      t.exports = ['Her zaman istatistikleri göster'];
    },
    54189: (t) => {
      t.exports = ['Yaylar'];
    },
    34674: (t) => {
      t.exports = ['Mintiklerde ort. HL'];
    },
    91757: (t) => {
      t.exports = ['Alt'];
    },
    17608: (t) => {
      t.exports = ['Alt etiketler'];
    },
    48848: (t) => {
      t.exports = ['Kenar'];
    },
    72269: (t) => {
      t.exports = ['Kenarlar'];
    },
    27331: (t) => {
      t.exports = ['Arkaplan'];
    },
    19949: (t) => {
      t.exports = ['Çubuk aralığı'];
    },
    81260: (t) => {
      t.exports = ['Izgara'];
    },
    67114: (t) => {
      t.exports = ['Tarih/saat aralığı'];
    },
    75460: (t) => {
      t.exports = ['Mesafe'];
    },
    46211: (t) => {
      t.exports = ['Emoji sabitle'];
    },
    46001: (t) => {
      t.exports = ['Giriş fiyatı'];
    },
    1220: (t) => {
      t.exports = ['Uzat'];
    },
    71116: (t) => {
      t.exports = ['Altı Uzat'];
    },
    45809: (t) => {
      t.exports = ['Sola Uzat'];
    },
    25892: (t) => {
      t.exports = ['Sola uzat'];
    },
    3304: (t) => {
      t.exports = ['Satırları sola genişlet'];
    },
    83095: (t) => {
      t.exports = ['Satırları sağa doğru genişlet'];
    },
    14025: (t) => {
      t.exports = ['Sağa Uzat'];
    },
    74395: (t) => {
      t.exports = ['Sağa uzat'];
    },
    85197: (t) => {
      t.exports = ['Üstü Uzat'];
    },
    17006: (t) => {
      t.exports = ['Font boyutu'];
    },
    31343: (t) => {
      t.exports = ['Hata metni'];
    },
    28565: (t) => {
      t.exports = ['Başarısızlık arka planı'];
    },
    87931: (t) => {
      t.exports = ['Fanlar'];
    },
    39836: (t) => {
      t.exports = ['Log ölçeğine göre Fib seviyeleri'];
    },
    10578: (t) => {
      t.exports = ['Tam daireler'];
    },
    25264: (t) => {
      t.exports = ['YD çubukları'];
    },
    77405: (t) => {
      t.exports = ['Yatay'];
    },
    66049: (t) => {
      t.exports = ['OC çubuklar'];
    },
    27531: (t) => {
      t.exports = ['Birim büyüklüğü'];
    },
    85206: (t) => {
      t.exports = ['Etiket'];
    },
    75332: (t) => {
      t.exports = ['Çizgi rengi'];
    },
    14773: (t) => {
      t.exports = ['Etiket Arkaplanı'];
    },
    37126: (t) => {
      t.exports = ['Etiket metni'];
    },
    79106: (t) => {
      t.exports = ['Kademeler'];
    },
    95610: (t) => {
      t.exports = ['Kademeler çizgisi'];
    },
    19286: (t) => {
      t.exports = ['Sol'];
    },
    79307: (t) => {
      t.exports = ['Sol etiketler'];
    },
    49286: (t) => {
      t.exports = ['Çizgi - YD/2'];
    },
    17676: (t) => {
      t.exports = ['Çizgi - açılış'];
    },
    47669: (t) => {
      t.exports = ['Çizgi - Kapanış'];
    },
    71899: (t) => {
      t.exports = ['Çizgi - yüksek'];
    },
    83394: (t) => {
      t.exports = ['Çizgi - düşük'];
    },
    60489: (t) => {
      t.exports = ['Çizgi rengi'];
    },
    53889: (t) => {
      t.exports = ['Mod'];
    },
    76476: (t) => {
      t.exports = ['Orta'];
    },
    24510: (t) => {
      t.exports = ['Orta nokta'];
    },
    22213: (t) => {
      t.exports = ['Kaynak arkaplan'];
    },
    15500: (t) => {
      t.exports = ['Kaynak sınırı'];
    },
    79238: (t) => {
      t.exports = ['Kaynak metin'];
    },
    37249: (t) => {
      t.exports = ['İstatistikler'];
    },
    28712: (t) => {
      t.exports = ['İstatistiklerin Konumu'];
    },
    50948: (t) => {
      t.exports = ['Durdurma Rengi'];
    },
    56119: (t) => {
      t.exports = ['Durdurma seviyesi'];
    },
    69835: (t) => {
      t.exports = ['Başarı metni'];
    },
    91141: (t) => {
      t.exports = ['Başarı arkaplanı'];
    },
    650: (t) => {
      t.exports = ['Yüzdeler'];
    },
    25684: (t) => {
      t.exports = ['Fiyat'];
    },
    23675: (t) => {
      t.exports = ['Fiyat Etiketi'];
    },
    75675: (t) => {
      t.exports = ['Fiyat etiketleri'];
    },
    16103: (t) => {
      t.exports = ['Fiyatın seviyeleri'];
    },
    46964: (t) => {
      t.exports = ['Fiyat aralığı'];
    },
    59771: (t) => {
      t.exports = ['Fiyat/çubuk oranı'];
    },
    29072: (t) => {
      t.exports = ['Fiyatlar'];
    },
    2635: (t) => {
      t.exports = ['Kar seviyesi'];
    },
    33886: (t) => {
      t.exports = ['Aralıklar ve oran'];
    },
    24186: (t) => {
      t.exports = ['Karşıt'];
    },
    21141: (t) => {
      t.exports = ['Sağ'];
    },
    91367: (t) => {
      t.exports = ['Sağ etiketler'];
    },
    63833: (t) => {
      t.exports = 'Risk';
    },
    95545: (t) => {
      t.exports = ['Dalga'];
    },
    26458: (t) => {
      t.exports = ['Fitil'];
    },
    65994: (t) => {
      t.exports = ['Üst'];
    },
    10209: (t) => {
      t.exports = ['Üst etiketler'];
    },
    98001: (t) => {
      t.exports = ['Hedef arka planı'];
    },
    89258: (t) => {
      t.exports = ['Hedef sınır'];
    },
    45302: (t) => {
      t.exports = ['Hedef Rengi:'];
    },
    74289: (t) => {
      t.exports = ['Hedef metin'];
    },
    17932: (t) => {
      t.exports = ['Metin kaydırma'];
    },
    92960: (t) => {
      t.exports = ['Metin hizalama'];
    },
    90581: (t) => {
      t.exports = ['Metin yönü'];
    },
    55325: (t) => {
      t.exports = ['Zaman etiketi'];
    },
    77838: (t) => {
      t.exports = ['Zaman seviyeleri'];
    },
    2295: (t) => {
      t.exports = ['Şeffaflık'];
    },
    4372: (t) => {
      t.exports = ['Trend çizgisi'];
    },
    12374: (t) => {
      t.exports = ['Tek renk kullan'];
    },
    91322: (t) => {
      t.exports = ['Değerler'];
    },
    25227: (t) => {
      t.exports = ['Varyans'];
    },
    44085: (t) => {
      t.exports = ['Dikey'];
    },
    1670: (t) => {
      t.exports = ['açıyı dğş'];
    },
    54119: (t) => {
      t.exports = ['ok rengini değiştir'];
    },
    72080: (t) => {
      t.exports = ['bayrak rengini dğş'];
    },
    98905: (t) => {
      t.exports = ['üst kenar boşluğunu değiştir'];
    },
    11049: (t) => {
      t.exports = ['Y koordinatı dikey konumu dğş'];
    },
    31804: (t) => {
      t.exports = ['{title} saat yönünün tersine dğş'];
    },
    99128: (t) => {
      t.exports = ['yüzde görünürlüğünde {title} katsayısını dğş'];
    },
    20216: (t) => {
      t.exports = ['{title} rengini dğş'];
    },
    35435: (t) => {
      t.exports = ['{title} kompakt istatistik modunu dğş'];
    },
    550: (t) => {
      t.exports = ['{title} mum kenarlığının rengini dğş'];
    },
    22313: (t) => {
      t.exports = ['{title} mum kenarlığı görünümünü dğş'];
    },
    7373: (t) => {
      t.exports = ['{title} mum kenarlığının rengini dğş'];
    },
    38742: (t) => {
      t.exports = ['{title} mum rengini dğş'];
    },
    42273: (t) => {
      t.exports = ['{title} mum rengini dğş'];
    },
    76054: (t) => {
      t.exports = ['{title} mum fitili rengini dğş'];
    },
    27029: (t) => {
      t.exports = ['{title} mum fitili görünürlüğünü dğş'];
    },
    45537: (t) => {
      t.exports = ['{title} açı görünürlüğünü dğş'];
    },
    31775: (t) => {
      t.exports = ['{title} hesap boyutunu dğş'];
    },
    37913: (t) => {
      t.exports = ['{title} sürekli istatistikleri göster dğş'];
    },
    15521: (t) => {
      t.exports = ['{title} tüm satırların rengini dğş'];
    },
    17466: (t) => {
      t.exports = ['{title} yay {index} çizgi rengini dğş'];
    },
    72307: (t) => {
      t.exports = ['{title} yay {index} çizgi genişliği dğş'];
    },
    13853: (t) => {
      t.exports = ['{title} fan {index} çizgi genişliğini dğş'];
    },
    78680: (t) => {
      t.exports = ['{title} ortalama HL değerini dğş'];
    },
    15802: (t) => {
      t.exports = ['{title} alt etiketlerin görünümünü dğş'];
    },
    36438: (t) => {
      t.exports = ['{title} arka plan şeffaflığını dğş'];
    },
    82465: (t) => {
      t.exports = ['{title} arka plan görünürlüğünü dğş'];
    },
    75312: (t) => {
      t.exports = ['{title} arka plan rengini dğş'];
    },
    39651: (t) => {
      t.exports = ['{title} arka plan rengini dğş 1'];
    },
    78177: (t) => {
      t.exports = ['{title} arka plan rengini dğş 2'];
    },
    42746: (t) => {
      t.exports = ['{title} çubuk aralığı görünümünü dğş'];
    },
    53770: (t) => {
      t.exports = ['{title} ızgara görünümünü dğş'];
    },
    29145: (t) => {
      t.exports = ['{title} ızgara çizgisi rengi dğş'];
    },
    64949: (t) => {
      t.exports = ['{title} ızgara çizgisi stilini dğş'];
    },
    93548: (t) => {
      t.exports = ['{title} ızgara çizgisi genişliğini dğş'];
    },
    15485: (t) => {
      t.exports = ['{title} tarih/saat aralığı görünümünü dğş'];
    },
    3400: (t) => {
      t.exports = ['{title} derecesini dğş'];
    },
    91534: (t) => {
      t.exports = ['{title} aralık görünürlüğünü dğş'];
    },
    65056: (t) => {
      t.exports = ['{title} emojiyi dğş'];
    },
    65899: (t) => {
      t.exports = ['{title} emoji görünürlüğünü dğş'];
    },
    59354: (t) => {
      t.exports = ['{title} giriş fiyatını dğş'];
    },
    1447: (t) => {
      t.exports = ['{title} uzatma dibini dğş'];
    },
    15258: (t) => {
      t.exports = ['{title} sola uzatmayı dğş'];
    },
    896: (t) => {
      t.exports = ['{title} uzatma üstünü dğş'];
    },
    3708: (t) => {
      t.exports = ['sola uzanan {title} dğş'];
    },
    45719: (t) => {
      t.exports = ['sağa uzanan {title} dğş'];
    },
    86647: (t) => {
      t.exports = ['{title} uzantısını değiştir'];
    },
    3156: (t) => {
      t.exports = ['{title} hata metni rengini dğş'];
    },
    49885: (t) => {
      t.exports = ['{title} hata arka plan rengi dğş'];
    },
    89126: (t) => {
      t.exports = ['{title} fan {index} satır görünürlüğünü dğş'];
    },
    30016: (t) => {
      t.exports = ['{title} fan {index} çizgi genişliğini dğş'];
    },
    36147: (t) => {
      t.exports = ['{title} fan {index} çizgi rengini dğş'];
    },
    78142: (t) => {
      t.exports = ['{title} fan görünürlüğünü dğş'];
    },
    79467: (t) => {
      t.exports = ['{title} fan çizgi rengini dğş'];
    },
    45739: (t) => {
      t.exports = ['{title} fib düzeylerini günlük ölçeğine dğş'];
    },
    99670: (t) => {
      t.exports = ['çevrilmiş {title} dğş'];
    },
    35165: (t) => {
      t.exports = ['{title} tam çevre görünürlüğünü dğş'];
    },
    48983: (t) => {
      t.exports = ['{title} resmin arka plan rengini dğş'];
    },
    45025: (t) => {
      t.exports = ['{title} lot boyutunu dğş'];
    },
    81170: (t) => {
      t.exports = ['{title} etiket hizalamasını dğş'];
    },
    22775: (t) => {
      t.exports = ['{title} etiketlerinin yazı tipi boyutunu değiştir'];
    },
    24338: (t) => {
      t.exports = ['{title} etiket görünürlüğünü dğş'];
    },
    32891: (t) => {
      t.exports = ['{title} seviye {index} çizgi katsayısını dğş'];
    },
    85551: (t) => {
      t.exports = ['{title} seviye {index} çizgi rengini dğş'];
    },
    47840: (t) => {
      t.exports = ['{title} seviye {index} çizgi stilini dğş'];
    },
    45463: (t) => {
      t.exports = ['{title} seviye {index} çizgi görünümünü dğş'];
    },
    90098: (t) => {
      t.exports = ['{title} seviye {index} çizgi genişliği dğş'];
    },
    26710: (t) => {
      t.exports = ['{title} seviyelerinin görünümünü dğş'];
    },
    2359: (t) => {
      t.exports = ['{title} sol etiket görünümünü dğş'];
    },
    44643: (t) => {
      t.exports = ['{title} satır genişliği dğş'];
    },
    20563: (t) => {
      t.exports = ['{title} çizgi rengini dğş'];
    },
    66982: (t) => {
      t.exports = ['{title} çizgi stilini dğş'];
    },
    94441: (t) => {
      t.exports = ['{title} modunu dğş'];
    },
    89996: (t) => {
      t.exports = ['{title} orta nokta görünürlüğünü dğş'];
    },
    36618: (t) => {
      t.exports = ['yansıtılmış {title} dğş'];
    },
    18544: (t) => {
      t.exports = ['{title} kaynak arka plan rengini dğş'];
    },
    48035: (t) => {
      t.exports = ['{title} kaynak kenarlık rengi dğş'];
    },
    42286: (t) => {
      t.exports = ['{title} kaynak metin rengini dğş'];
    },
    588: (t) => {
      t.exports = ['{title} istatistik konumu dğş'];
    },
    54659: (t) => {
      t.exports = ['{title} durma rengini dğş'];
    },
    89182: (t) => {
      t.exports = ['{title} durma seviyesini dğş'];
    },
    82224: (t) => {
      t.exports = ['{title} dur fiyatını dğş'];
    },
    88383: (t) => {
      t.exports = ['{title} başarı metin rengini dğş'];
    },
    26967: (t) => {
      t.exports = ['{title} başarı arka plan rengi dğş'];
    },
    45936: (t) => {
      t.exports = ['{title} fiyat etiketi görünümünü dğş'];
    },
    88577: (t) => {
      t.exports = ['{title} fiyat etiketi görünümünü dğş'];
    },
    47045: (t) => {
      t.exports = ['{title} fiyat aralığı görünümünü dğş'];
    },
    56175: (t) => {
      t.exports = ['{title} fiyat görünürlüğünü dğş'];
    },
    44539: (t) => {
      t.exports = ['{title} kâr seviyesini dğş'];
    },
    41646: (t) => {
      t.exports = ['{title} kâr fiyatını dğş'];
    },
    52877: (t) => {
      t.exports = ['{title} tersine dğş'];
    },
    16598: (t) => {
      t.exports = ['{title} sağ etiketlerin görünümünü dğş'];
    },
    31553: (t) => {
      t.exports = ['{title} riskini dğş'];
    },
    40344: (t) => {
      t.exports = ['{title} risk görüntüleme modu dğş'];
    },
    73137: (t) => {
      t.exports = ['{title} en üstteki etiketlerin görünümünü dğş'];
    },
    52387: (t) => {
      t.exports = ['{title} hedef arka plan rengini dğş'];
    },
    6921: (t) => {
      t.exports = ['{title} hedef kenarlık rengini dğş'];
    },
    97573: (t) => {
      t.exports = ['{title} hedef rengini dğş'];
    },
    27634: (t) => {
      t.exports = ['{title} hedef metin rengini dğş'];
    },
    33822: (t) => {
      t.exports = ['{title} zaman etiketi görünümünü dğş'];
    },
    84321: (t) => {
      t.exports = ['{title} şeffaflığı dğş'];
    },
    12355: (t) => {
      t.exports = ['{title} varyans değerini dğş'];
    },
    25937: (t) => {
      t.exports = ['{toolName} etiket hizalamasını dikey dğş'];
    },
    46991: (t) => {
      t.exports = ['{toolName} etiketlerinin yatay hizalama dğş'];
    },
    73080: (t) => {
      t.exports = ['{toolName} etiketlerinin yönünü dğş'];
    },
    24272: (t) => {
      t.exports = ['{toolName} çizgi görünümünü dğş'];
    },
    46404: (t) => {
      t.exports = ['{toolName} çizgi genişliğini dğş'];
    },
    50265: (t) => {
      t.exports = ['{toolName} çizgi rengi dğş'];
    },
    72781: (t) => {
      t.exports = ['sola uzanan {toolName} satırı dğş'];
    },
    84613: (t) => {
      t.exports = ['sağa uzanan {toolName} satırı dğş'];
    },
    62603: (t) => {
      t.exports = ['{toolName} çizgisi sol ucunu dğş'];
    },
    62412: (t) => {
      t.exports = ['{toolName} satırı sağ ucunu dğş'];
    },
    35422: (t) => {
      t.exports = ['{toolName} çizgi stilini dğş'];
    },
    77690: (t) => {
      t.exports = ['{toolName} metnini dğş'];
    },
    69871: (t) => {
      t.exports = ['{toolName} metin görünümü dğş'];
    },
    25878: (t) => {
      t.exports = ['{toolName} metin sarmayı dğş'];
    },
    91832: (t) => {
      t.exports = ['{toolName} metin arka plan rengini dğş'];
    },
    18610: (t) => {
      t.exports = ['{toolName} metin arka plan görünümü dğş'];
    },
    44755: (t) => {
      t.exports = ['{toolName} metin kenarlığı rengi dğş'];
    },
    6324: (t) => {
      t.exports = ['{toolName} metin kenar genişliğini dğş'];
    },
    45529: (t) => {
      t.exports = ['{toolName} metin sınır görünürlüğünü dğş'];
    },
    6500: (t) => {
      t.exports = ['{toolName} metin rengini dğş'];
    },
    51614: (t) => {
      t.exports = ['{toolName} metin tipini kalın dğş'];
    },
    18572: (t) => {
      t.exports = ['{toolName} metin tipini italik olarak dğş'];
    },
    48382: (t) => {
      t.exports = ['{toolName} metin tipi boyutu dğş'];
    },
    21926: (t) => {
      t.exports = ['arka plan rengi'];
    },
    52241: (t) => {
      t.exports = ['arka planlar dolu'];
    },
    70607: (t) => {
      t.exports = ['çizgiler rengi'];
    },
    41075: (t) => {
      t.exports = ['çizgiler stili'];
    },
    73043: (t) => {
      t.exports = ['çizgi genişliği'];
    },
    41437: (t) => {
      t.exports = ['Metin rengi'];
    },
  },
]);
