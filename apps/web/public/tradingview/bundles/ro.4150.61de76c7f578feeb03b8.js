(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [4150],
  {
    74274: (e) => {
      e.exports = 'Cross';
    },
    41596: (e) => {
      e.exports = 'Labels on price scale';
    },
    23545: (e) => {
      e.exports = 'Values in status line';
    },
    39495: (e) => {
      e.exports = 'Circles';
    },
    41389: (e) => {
      e.exports = ['Above Bar'];
    },
    29520: (e) => {
      e.exports = 'Absolute';
    },
    58102: (e) => {
      e.exports = 'Apply Defaults';
    },
    65262: (e) => {
      e.exports = ['Area With Breaks'];
    },
    83760: (e) => {
      e.exports = 'Body';
    },
    48848: (e) => {
      e.exports = 'Border';
    },
    27331: (e) => {
      e.exports = 'Background';
    },
    78626: (e) => {
      e.exports = ['Below Bar'];
    },
    41361: (e) => {
      e.exports = 'Down';
    },
    22192: (e) => {
      e.exports = 'Days';
    },
    31577: (e) => {
      e.exports = 'Developing VA';
    },
    4329: (e) => {
      e.exports = 'Default';
    },
    98938: (e) => {
      e.exports = 'Defaults';
    },
    63099: (e) => {
      e.exports = 'Hours';
    },
    11091: (e) => {
      e.exports = 'Histogram';
    },
    66304: (e) => {
      e.exports = 'Inputs';
    },
    40297: (e) => {
      e.exports = 'Outputs';
    },
    36993: (e) => {
      e.exports = ['Override Min Tick'];
    },
    64606: (e) => {
      e.exports = ['Labels Font'];
    },
    54934: (e) => {
      e.exports = ['Line With Breaks'];
    },
    95543: (e) => {
      e.exports = 'Months';
    },
    28134: (e) => {
      e.exports = 'Minutes';
    },
    18229: (e) => {
      e.exports = ['Save As Default'];
    },
    71129: (e) => {
      e.exports = 'Seconds';
    },
    86520: (e) => {
      e.exports = ['Signal Labels'];
    },
    79511: (e) => {
      e.exports = ['Step Line'];
    },
    64108: (e) => {
      e.exports = 'Step line with breaks';
    },
    67767: (e) => {
      e.exports = ['Step Line With Diamonds'];
    },
    21861: (e) => {
      e.exports = 'Placement';
    },
    73947: (e) => {
      e.exports = 'Precision';
    },
    66596: (e) => {
      e.exports = 'Quantity';
    },
    86672: (e) => {
      e.exports = 'Ranges';
    },
    79782: (e) => {
      e.exports = ['Reset Settings'];
    },
    21594: (e) => {
      e.exports = 'Weeks';
    },
    26458: (e) => {
      e.exports = 'Wick';
    },
    95247: (e) => {
      e.exports = ['Width (% of the Box)'];
    },
    19221: (e) => {
      e.exports = 'Text color';
    },
    7138: (e) => {
      e.exports = ['Trades on Chart'];
    },
    98802: (e) => {
      e.exports = 'Up';
    },
    14414: (e) => {
      e.exports = ['Volume Profile'];
    },
    91322: (e) => {
      e.exports = 'Values';
    },
    20834: (e) => {
      e.exports = ['Change Min Tick'];
    },
    98491: (e) => {
      e.exports = ['Change Char'];
    },
    7378: (e) => {
      e.exports = ['Change Font Size'];
    },
    28691: (e) => {
      e.exports = ['Change Line Style'];
    },
    38361: (e) => {
      e.exports = ['Change Location'];
    },
    51081: (e) => {
      e.exports = ['Change Percent Width'];
    },
    47634: (e) => {
      e.exports = ['Change Placement'];
    },
    15683: (e) => {
      e.exports = ['Change Plot Type'];
    },
    164: (e) => {
      e.exports = ['Change Precision'];
    },
    86888: (e) => {
      e.exports = ['Change Shape'];
    },
    50463: (e) => {
      e.exports = ['Change Value'];
    },
    12628: (e) => {
      e.exports = 'change values visibility';
    },
    13355: (e) => {
      e.exports = 'change {title} days to';
    },
    41377: (e) => {
      e.exports = 'change {title} days from';
    },
    35388: (e) => {
      e.exports = 'change {title} hours from';
    },
    78586: (e) => {
      e.exports = 'change {title} hours to';
    },
    59635: (e) => {
      e.exports = 'change {title} months from';
    },
    74266: (e) => {
      e.exports = 'change {title} months to';
    },
    91633: (e) => {
      e.exports = 'change {title} minutes to';
    },
    15106: (e) => {
      e.exports = 'change {title} minutes from';
    },
    66161: (e) => {
      e.exports = 'change {title} seconds to';
    },
    2822: (e) => {
      e.exports = 'change {title} seconds from';
    },
    21339: (e) => {
      e.exports = 'change {title} weeks from';
    },
    68643: (e) => {
      e.exports = 'change {title} weeks to';
    },
    30810: (e) => {
      e.exports = 'change {title} visibility on ticks';
    },
    24941: (e) => {
      e.exports = 'change {title} visibility on weeks';
    },
    29088: (e) => {
      e.exports = 'change {title} visibility on days';
    },
    68971: (e) => {
      e.exports = 'change {title} visibility on hours';
    },
    64370: (e) => {
      e.exports = 'change {title} visibility on minutes';
    },
    6659: (e) => {
      e.exports = 'change {title} visibility on months';
    },
    29091: (e) => {
      e.exports = 'change {title} visibility on ranges';
    },
    46948: (e) => {
      e.exports = 'change {title} visibility on seconds';
    },
    82211: (e) => {
      e.exports = ['Days'];
    },
    33486: (e) => {
      e.exports = 'days to';
    },
    14077: (e) => {
      e.exports = 'days from';
    },
    3143: (e) => {
      e.exports = ['Hours'];
    },
    84775: (e) => {
      e.exports = 'hours from';
    },
    11255: (e) => {
      e.exports = 'hours to';
    },
    58964: (e) => {
      e.exports = ['Months'];
    },
    71770: (e) => {
      e.exports = 'months from';
    },
    37179: (e) => {
      e.exports = 'months to';
    },
    16465: (e) => {
      e.exports = ['Minutes'];
    },
    72317: (e) => {
      e.exports = 'minutes to';
    },
    25586: (e) => {
      e.exports = 'minutes from';
    },
    32925: (e) => {
      e.exports = 'seconds';
    },
    39017: (e) => {
      e.exports = 'seconds to';
    },
    6049: (e) => {
      e.exports = 'seconds from';
    },
    13604: (e) => {
      e.exports = ['Ranges'];
    },
    93016: (e) => {
      e.exports = 'weeks';
    },
    32002: (e) => {
      e.exports = 'weeks from';
    },
    28091: (e) => {
      e.exports = 'weeks to';
    },
    59523: (e) => {
      e.exports = ['Ticks'];
    },
  },
]);
