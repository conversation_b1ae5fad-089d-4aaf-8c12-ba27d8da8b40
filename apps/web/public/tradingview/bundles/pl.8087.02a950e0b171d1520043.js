(self.webpackChunktradingview = self.webpackChunktradingview || []).push([
  [8087],
  {
    40276: (o) => {
      o.exports = ['Dodaj'];
    },
    53585: (o) => {
      o.exports = ['Dodaj niestandardowy kolor'];
    },
    81865: (o) => {
      o.exports = ['Przez<PERSON><PERSON>ys<PERSON>ć'];
    },
    73755: (o) => {
      o.exports = ['Inny symbol'];
    },
    16936: (o) => {
      o.exports = ['Cofnij'];
    },
    88046: (o) => {
      o.exports = ['Główny symbol wykresu'];
    },
    9898: (o) => {
      o.exports = ['Prawo do udostępniania'];
    },
    20036: (o) => {
      o.exports = ['Anuluj'];
    },
    23398: (o) => {
      o.exports = ['Zmień symbol'];
    },
    94551: (o) => {
      o.exports = ['Wykres'];
    },
    64498: (o) => {
      o.exports = ['Wszystkie źródła'];
    },
    73226: (o) => {
      o.exports = ['Zastosuj'];
    },
    79852: (o) => {
      o.exports = ['Obligacja'];
    },
    56095: (o) => {
      o.exports = ['Pomniejsz'];
    },
    29601: (o) => {
      o.exports = ['Opis'];
    },
    46812: (o) => {
      o.exports = ['Zwiększ'];
    },
    89298: (o) => {
      o.exports = ['Przesunięcie'];
    },
    68988: (o) => {
      o.exports = 'Ok';
    },
    29673: (o) => {
      o.exports = ['Brak giełd spełniających Twoje kryteria'];
    },
    41379: (o) => {
      o.exports = ['Brak symboli spełniających Twoje kryteria'];
    },
    35563: (o) => {
      o.exports = ['Błędny format numeru.'];
    },
    19724: (o) => {
      o.exports = ['Źródła'];
    },
    59877: (o) => {
      o.exports = [
        'Ustaw czas i cenę „{inputInline}” dla „{studyShortDescription}”',
      ];
    },
    18571: (o) => {
      o.exports = [
        'Ustaw czas i cenę „{inputTitle}” dla „{studyShortDescription}”',
      ];
    },
    58552: (o) => {
      o.exports = [
        'Ustaw czas i cenę „{inputTitle} ” dla „{studyShortDescription}”',
      ];
    },
    80481: (o) => {
      o.exports = ['Ustaw czas i cenę dla „{studyShortDescription}”'];
    },
    42917: (o) => {
      o.exports = ['Ustaw godzinę dla „{studyShortDescription}”'];
    },
    6083: (o) => {
      o.exports = ['Ustaw cenę dla „{studyShortDescription}”'];
    },
    52298: (o) => {
      o.exports = ['Szukaj'];
    },
    13269: (o) => {
      o.exports = ['Wybierz źródło'];
    },
    2607: (o) => {
      o.exports = ['Podana wartość nie jest wielokrotnością {max}.'];
    },
    53669: (o) => {
      o.exports = [
        'Podana wartość jest mniejsza niż minimum instrumentu wynoszące {min}.',
      ];
    },
    89053: (o) => {
      o.exports = 'Symbol';
    },
    48490: (o) => {
      o.exports = ['Symbol i opis'];
    },
    99983: (o) => {
      o.exports = ['Wyszukiwanie symboli'];
    },
    54336: (o) => {
      o.exports = ['Usuń kolor'];
    },
    60142: (o) => {
      o.exports = ['Grubość'];
    },
    87592: (o) => {
      o.exports = 'cfd';
    },
    17023: (o) => {
      o.exports = ['Zmień przezroczystość'];
    },
    13066: (o) => {
      o.exports = ['Zmień kolor'];
    },
    95657: (o) => {
      o.exports = ['Zmień grubość'];
    },
    18567: (o) => {
      o.exports = ['zmień właściwość {propertyName}'];
    },
    36962: (o) => {
      o.exports = ['zamknięcie'];
    },
    8448: (o) => {
      o.exports = ['krypto'];
    },
    1328: (o) => {
      o.exports = ['Potwierdzenie wpłaty'];
    },
    88720: (o) => {
      o.exports = ['gospodarka'];
    },
    39512: (o) => {
      o.exports = 'forex';
    },
    81859: (o) => {
      o.exports = ['Kontrakty terminowe'];
    },
    39337: (o) => {
      o.exports = ['maksimum'];
    },
    91815: (o) => {
      o.exports = 'hl2';
    },
    40771: (o) => {
      o.exports = 'hlc3';
    },
    9523: (o) => {
      o.exports = 'hlcc4';
    },
    12754: (o) => {
      o.exports = ['indeks'];
    },
    38071: (o) => {
      o.exports = ['indeksy'];
    },
    12504: (o) => {
      o.exports = 'ohlc4';
    },
    38466: (o) => {
      o.exports = ['otwarcie'];
    },
    3919: (o) => {
      o.exports = 'low';
    },
    36931: (o) => {
      o.exports = ['akcja'];
    },
  },
]);
