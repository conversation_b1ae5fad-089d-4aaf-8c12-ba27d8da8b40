server {
    listen 80 default_server;
    server_name _;

    root /var/www;

    set $redirect_schema "https";

    # 启用gzip压缩
    gzip on;
    gzip_vary on;  # 添加 Vary: Accept-Encoding 头
    gzip_proxied any;  # 对所有代理请求启用压缩
    gzip_comp_level 6;  # 压缩级别（1-9），6是性能和压缩率的平衡点
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        application/x-font-ttf
        application/x-font-otf
        application/vnd.ms-fontobject
        image/svg+xml
        # 注意：不要压缩已经压缩过的格式如 jpg/png/woff2
        font/opentype;
    gzip_min_length 1000;  # 小于1KB的文件不压缩
    gzip_disable "msie6";  # 禁用 IE6 的 gzip（有bug）

    # healthcheck
    location = /status.html {
        return 200;
    }

    # 处理 _next 静态资源
    location /_next/ {
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header X-Content-Type-Options "nosniff";
    }
    
    # 处理其他静态资源
    location ~ ^/(assets|tradingview)/ {
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header X-Content-Type-Options "nosniff";
    }

    # 处理所有请求
    location / {
        add_header x-xss-protection "1; mode=block";
        add_header x-frame-options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
        add_header Service-Worker-Allowed "/";
        add_header Cache-Control "no-cache, no-store, must-revalidate";

        try_files $uri.html $uri/index.html $uri /index.html =404;
    }

    # 404 错误处理
    error_page 404 /404.html;
    location = /404.html {
        internal;
    }
}
