import { reversePrice } from '@byreal/utils';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo } from 'react';

import { LiquidityData } from '@/features/clmm/create-position/components/LiquidityChart/types';
import { usePositionStore } from '@/store/usePositionStore';

import { useFetchPoolInfo } from '../pools/useFetchPoolInfo';
import { usePoolTicks } from '../pools/usePoolTicks';

export const useInitPositionStore = () => {
  const params = useSearchParams();
  const poolId = params.get('id') || '';
  const {
    data: poolData,
    isLoading: fetchPoolLoading,
    isValidating: fetchPoolRefreshing,
    fetchPoolInfo,
  } = useFetchPoolInfo({ id: poolId });
  const { initPage, isReversed, baseTokenInfo } = usePositionStore();
  // const { sdkClient } = useSdk();

  const {
    data: tickData,
    isLoading: fetchLiquidityTicksLoading,
    isValidating: fetchLiquidityTicksRefreshing,
    fetcherMutate: fetcherMutateTicksData,
  } = usePoolTicks({ poolId });

  const liquidityData = useMemo(() => {
    if (fetchLiquidityTicksLoading || tickData.length <= 0) {
      return [] as LiquidityData;
    }
    return isReversed
      ? tickData.map((k) => {
          return {
            ...k,
            price: +reversePrice(k.originalPrice, baseTokenInfo?.decimals ?? 0),
          };
        })
      : tickData;
  }, [fetchLiquidityTicksLoading, tickData, isReversed, baseTokenInfo?.decimals]);

  useEffect(() => {
    refreshPoolInfo();
  }, []);

  const initPositionStore = useCallback(() => {
    if (fetchPoolLoading) return;
    if (poolData.poolInfo && poolData.rpcPoolInfo) {
      // debug = ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
      // poolData.rpcPoolInfo.currentPrice = +poolData.rpcPoolInfo.currentPrice + random(0.001, 0.009);
      // 直接使用 rpcPoolInfo.currentPrice，不需要单独请求
      initPage(poolData.poolInfo, poolData.rpcPoolInfo);
      // 刷新池子
      refreshPoolInfo();
    }
  }, [fetchPoolLoading, initPage, poolData.poolInfo, poolData.rpcPoolInfo]);

  const refreshPoolInfo = useCallback(() => {
    fetchPoolInfo();
    fetcherMutateTicksData();
  }, [fetchPoolInfo, fetcherMutateTicksData]);

  return {
    liquidityData,
    fetchPoolLoading,
    fetchPoolRefreshing,
    fetchLiquidityTicksLoading,
    fetchLiquidityTicksRefreshing,
    initPositionStore,
    refreshPoolInfo,
  };
};
