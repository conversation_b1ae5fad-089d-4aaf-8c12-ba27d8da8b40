'use client';

import { PoolCategory } from '@byreal/clmm-sdk';
import { Box, HStack, VStack } from '@chakra-ui/react';
import { useEffect, useCallback, useMemo } from 'react';

import { PositionManager } from '@/features/clmm/position-manager';
import { useCalculateEstApr } from '@/hooks/position/useCalculateEstApr';
import { useCalculateSelectedPriceRange } from '@/hooks/position/useCalculateSelectedPriceRange';
import { useInitPositionStore } from '@/hooks/position/useInitPositionStore';
import { useOutOfRange } from '@/hooks/position/useOutOfRange';
import { useResponsive } from '@/hooks/responsive/useResponsive';
import { useEvent } from '@/hooks/useEvent';
import { usePositionStore } from '@/store/usePositionStore';
import { TickPriceInfo } from '@/types/position';

import { PositionIncentiveInfo } from '../position-manager/PositionIncentiveInfo';

import KLineChart from './components/KLineChart';
import { KLineChartModal } from './components/KLineChartModal';
import { LiquidityChart } from './components/LiquidityChart';
import { LiquidityPoolStats } from './components/LiquidityPoolStats';
import { PriceRangeAdjustPanel } from './components/PriceRangeAdjustPanel';
import { PriceRangeHeader } from './components/PriceRangeHeader';
// import { PriceRangeIndicator } from './components/PriceRangeIndicator';

const chartHeight = '50vh';

export const CreatePosition = () => {
  const {
    // 不可变更数据
    poolInfo,
    rpcPoolInfo,
    currentPriceInfo,
    baseTokenInfo,
    quoteTokenInfo,
    h24priceRange,
    // ticks,
    selectedPriceRange,
    // initialSelectedPriceRange,
    fullPriceRange,
    baseTokenAmount,
    quoteTokenAmount,
    selectedPercentage,
    setValue,
    isReversed,
    toggleReversed,
    resetState,
  } = usePositionStore();

  const { initPositionStore, fetchPoolLoading, fetchLiquidityTicksLoading, liquidityData } =
    useInitPositionStore();
  const calculateSelectedPriceRange = useCalculateSelectedPriceRange();

  const { isOutOfRange } = useOutOfRange();
  const { isMobileLayout } = useResponsive();

  const { uiApr } = useCalculateEstApr({
    poolInfo,
    rpcPoolInfo,
    baseTokenAmount: +baseTokenAmount,
    quoteTokenAmount: +quoteTokenAmount,
    tickLower: calculateSelectedPriceRange.min.tick,
    tickUpper: calculateSelectedPriceRange.max.tick,
    scene: 'create',
  });

  // uiApr

  useEffect(() => {
    // console.log('111 initPositionStore');
    initPositionStore();
  }, [initPositionStore]);

  const handleMinRangeBlur = useEvent((val: TickPriceInfo) => {
    setValue(
      'selectedPriceRange',
      { min: val, max: selectedPriceRange.max },
      { preserveExactTicks: true }
    );
  });

  const handleMaxRangeBlur = useEvent((val: TickPriceInfo) => {
    setValue(
      'selectedPriceRange',
      { min: selectedPriceRange.min, max: val },
      { preserveExactTicks: true }
    );
  });

  // 处理 KLineChart 价格范围变化
  const handleKlineRangeChange = useCallback(
    (min: TickPriceInfo, max: TickPriceInfo) => {
      setValue('selectedPriceRange', { min, max }, { preserveExactTicks: true });
    },
    [setValue]
  );

  const isChartLoading =
    fetchPoolLoading ||
    fetchLiquidityTicksLoading ||
    !currentPriceInfo ||
    !selectedPriceRange ||
    !baseTokenInfo ||
    !quoteTokenInfo;

  useEffect(() => {
    return () => {
      resetState();
    };
  }, [resetState]);

  const showIncentiveInfo = useMemo(() => {
    return poolInfo?.rewards && poolInfo?.rewards.length > 0;
  }, [poolInfo]);

  return (
    <Box
      display="flex"
      justifyContent="center"
      flexDirection={isMobileLayout ? 'column' : 'row'}
      alignItems={isMobileLayout ? 'center' : 'flex-start'}
      w="full"
      p={isMobileLayout ? '12px 16px' : 4}
      gap={isMobileLayout ? 6 : 4}
    >
      <VStack w="100%" flex={1}>
        {isMobileLayout && showIncentiveInfo && (
          <PositionIncentiveInfo
            rewards={poolInfo!.rewards || []}
            position="relative"
            zIndex={0}
            w="95%"
          />
        )}
        <VStack
          position="relative"
          zIndex={1}
          mt={isMobileLayout && showIncentiveInfo ? -8 : 0}
          align="stretch"
          w="full"
          // maxW={isMobileLayout ? '100%' : '1032px'}
          py={isMobileLayout ? 3 : 6}
          px={isMobileLayout ? 4 : 6}
          rounded="md"
          overflow="hidden"
          border="1px solid"
          borderColor="line.01"
          bg="card.01_bg"
        >
          <LiquidityPoolStats
            liquidityPoolInfo={{
              symbolA: poolInfo?.mintA?.symbol || '',
              symbolB: poolInfo?.mintB?.symbol || '',
              pairIcons: [poolInfo?.mintA?.logoURI || '', poolInfo?.mintB?.logoURI || ''],
              feeRate: poolInfo?.feeRate || '',
              tvl: poolInfo?.tvl || '0',
              volume24h: poolInfo?.volumeUsd24h || '0',
              fees24h: poolInfo?.feeUsd24h || '0',
              apy24h: uiApr || 0,
              mintA: poolInfo?.mintA,
              mintB: poolInfo?.mintB,
              poolAddress: poolInfo?.poolAddress,
              rewards: poolInfo?.rewards,
              category: poolInfo?.category,
            }}
          />
          {/* <Flex flex="1" gap="16px"> */}
          <Box display="flex" flexDirection="column" gap={0}>
            {/* 上部控制区 */}
            <PriceRangeHeader
              borderTop="1px solid"
              borderColor="line.01"
              pt={isMobileLayout ? 5 : 6}
              pb={isMobileLayout ? 4 : 6}
              quoteTokenInfo={quoteTokenInfo}
              baseTokenInfo={baseTokenInfo}
              selectedPercentage={selectedPercentage}
              onToggleReversed={toggleReversed}
              isReversed={isReversed}
              category={poolInfo?.category || PoolCategory.STABLES}
              onSelectedPercentageChange={(percentage: number) =>
                setValue('selectedPercentage', percentage)
              }
            />
            {isMobileLayout && (
              <KLineChartModal title={`${baseTokenInfo?.symbol || ''} Price Chart`} />
            )}
            {/* 主要内容区 */}
            {/* <LiquidityTuner> */}
            <HStack
              mt={isMobileLayout ? 2 : 0}
              direction={isMobileLayout ? 'column' : 'row'}
              gap={isMobileLayout ? '16px' : '24px'}
            >
              {/* 在移动端下不直接显示K线图 */}
              {!isMobileLayout && (
                <KLineChart
                  flex={isMobileLayout ? 'auto' : '1'}
                  h={chartHeight}
                  rounded="xs"
                  overflow="hidden"
                  border="1px solid"
                  borderColor="line.01"
                  onRangeChange={handleKlineRangeChange}
                />
              )}
              <LiquidityChart
                w="100%"
                maxW={isMobileLayout ? '100%' : '200px'}
                h={isMobileLayout ? '240px' : chartHeight}
                currentPriceInfo={currentPriceInfo}
                selectedPriceRange={selectedPriceRange}
                // initialSelectedPriceRange={initialSelectedPriceRange}
                liquidity={liquidityData}
                price24H={[h24priceRange.max, h24priceRange.min]}
                baseTokenInfo={baseTokenInfo}
                quoteTokenInfo={quoteTokenInfo}
                isReversed={isReversed}
                tickSpacing={poolInfo?.config?.tickSpacing || 1}
                fullPriceRange={fullPriceRange}
                isOutOfRange={isOutOfRange}
                isLoading={isChartLoading}
                onBottomRangeInput={handleMinRangeBlur}
                onTopRangeInput={handleMaxRangeBlur}
                // onChange={handleChartChange}
              />
            </HStack>

            {/* {!isMobileLayout && (
            <PriceRangeIndicator
              currentPriceInfo={currentPriceInfo}
              price24H={[h24priceRange.min, h24priceRange.max]}
              quoteTokenInfo={quoteTokenInfo}
              baseTokenInfo={baseTokenInfo}
              isReversed={isReversed}
            />
          )} */}

            <PriceRangeAdjustPanel
              mt={isMobileLayout ? 4 : 6}
              tickSpacing={poolInfo?.config?.tickSpacing || 1}
              onSelectedPriceRangeChange={(priceRange, isExact) =>
                setValue('selectedPriceRange', priceRange, { preserveExactTicks: isExact })
              }
            />
          </Box>
        </VStack>
      </VStack>
      <PositionManager
        w={isMobileLayout ? '100%' : '466px'}
        maxW={isMobileLayout ? '100%' : '466px'}
        type="create"
        mt={isMobileLayout ? '16px' : '0'}
      />
    </Box>
  );
};

export default CreatePosition;
