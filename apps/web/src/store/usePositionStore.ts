import {
  calculatePriceRangeFromTickSpacing,
  calculateTickAlignedPriceRange,
  IPoolLayoutWithId,
  PoolInfo,
  PoolCategory,
} from '@byreal/clmm-sdk';
import { decimalGt, decimalLt, decimalMultiply, toDecimal, reversePrice } from '@byreal/utils';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

import { getDefaultPercentage } from '@/constants/priceRange';
import { PriceInfo, TickPriceInfo } from '@/types/position';
import { PriceRange, TokenInfo } from '@/types/token';

/**
 * 验证并限制价格范围在指定的最小和最大值之间
 */
function validatePriceLimit(
  price: string | number,
  priceRange: { min: TickPriceInfo; max: TickPriceInfo }
): string {
  // price < fullPriceRange.min
  if (decimalLt(price, priceRange.min.price)) {
    return toDecimal(priceRange.min.price).toString();
  }

  // price > fullPriceRange.max
  if (decimalGt(price, priceRange.max.price)) {
    return toDecimal(priceRange.max.price).toString();
  }

  return toDecimal(price).toString();
}

export interface PositionState {
  // 不可变更数据（从接口、RPC节点获取）
  poolInfo: PoolInfo | null;
  rpcPoolInfo: IPoolLayoutWithId | null;
  baseTokenInfo: TokenInfo | null;
  quoteTokenInfo: TokenInfo | null;
  h24priceRange: PriceRange;
  fullPriceRange: {
    min: TickPriceInfo;
    max: TickPriceInfo;
  };
  currentPriceInfo: PriceInfo; // 用于图表当前价格标记
  // initialSelectedPriceRange: {
  //   min: TickPriceInfo;
  //   max: TickPriceInfo;
  // }; // 初始选中的价格范围，用于重置缩放

  // 可变更数据（用户选择数据）
  selectedPercentage: number; // 图表上选中的价格范围百分比
  selectedPriceRange: {
    min: TickPriceInfo;
    max: TickPriceInfo;
  }; // 图表上选中的价格范围

  // 用户输入数据
  baseTokenAmount: string | number;
  quoteTokenAmount: string | number;

  // UI状态
  isInitializing: boolean; // 初始数据加载状态
  isReversed: boolean;
  error: string | null; // 错误信息

  // 交互动作
  initPage: (poolInfo: PoolInfo, rpcPoolInfo: IPoolLayoutWithId) => Promise<void>;
  refreshPoolData: (poolInfo: PoolInfo, rpcPoolInfo: IPoolLayoutWithId) => Promise<void>;
  toggleReversed: () => void;
  setValue: (
    key: keyof PositionState,
    value: any,
    options?: { preserveExactTicks?: boolean }
  ) => void;

  _setPriceRange: (min: string | number, max: string | number) => void;
  resetState: () => void;

  // 可能还需要其他 actions，例如更新流动性数量等
}

const initialState: Omit<
  PositionState,
  'setValue' | '_setPriceRange' | 'initPage' | 'refreshPoolData' | 'resetState' | 'toggleReversed'
> = {
  // 不可变更数据（从接口、RPC节点获取）
  rpcPoolInfo: null,
  poolInfo: null,
  baseTokenInfo: null,
  quoteTokenInfo: null,
  fullPriceRange: {
    min: {
      price: 0,
      tick: 0,
    },
    max: {
      price: 0,
      tick: 0,
    },
  },
  h24priceRange: {
    min: 0,
    max: 0,
  },
  currentPriceInfo: {
    time: Date.now(),
    price: 0,
  },
  // initialSelectedPriceRange: { min: { price: 0, tick: 0, }, max: { price: 0, tick: 0, }, },
  selectedPercentage: 0.05, // 默认 5%
  // 可变更数据（用户选择数据）
  isReversed: false,
  selectedPriceRange: {
    min: { price: 0, tick: 0 },
    max: { price: 0, tick: 0 },
  },
  // 用户录入
  baseTokenAmount: '',
  quoteTokenAmount: '',

  isInitializing: false,
  error: null,
};

function calculatePriceRange(
  min: string | number,
  max: string | number,
  poolInfo: PoolInfo,
  baseTokenInfo: TokenInfo,
  quoteTokenInfo: TokenInfo,
  fullPriceRange: {
    min: TickPriceInfo;
    max: TickPriceInfo;
  },
  isReversed: boolean
) {
  // * 正常：decimals应该是quoteToken的精度
  // * 反向：decimals应该是baseToken的精度
  const decimals = isReversed ? baseTokenInfo.decimals : quoteTokenInfo.decimals;
  const { priceInTickLower, priceInTickUpper } = calculateTickAlignedPriceRange({
    tickSpacing: poolInfo?.config?.tickSpacing || 1,
    mintDecimalsA: baseTokenInfo?.decimals || 9,
    mintDecimalsB: quoteTokenInfo?.decimals || 9,
    startPrice: validatePriceLimit(min, fullPriceRange),
    endPrice: validatePriceLimit(max, fullPriceRange),
  });
  // console.log('priceInTickLower', priceInTickLower);
  // console.log('priceInTickUpper', priceInTickUpper);
  return {
    priceInTickLower: {
      price: priceInTickLower.price.toFixed(decimals),
      tick: priceInTickLower.tick,
    },
    priceInTickUpper: {
      price: priceInTickUpper.price.toFixed(decimals),
      tick: priceInTickUpper.tick,
    },
  };
}

function getInitData(
  poolInfo: PoolInfo,
  baseTokenInfo: TokenInfo,
  quoteTokenInfo: TokenInfo,
  currentPrice: number,
  selectedPercentage: number,
  isReversed: boolean
) {
  const fullPriceRange = calculatePriceRangeFromTickSpacing(
    poolInfo.config.tickSpacing,
    baseTokenInfo.decimals,
    quoteTokenInfo.decimals
  );
  const initMinPriceRange = decimalMultiply(currentPrice, 1 - selectedPercentage);
  const initMaxPriceRange = decimalMultiply(currentPrice, 1 + selectedPercentage);

  const { priceInTickLower, priceInTickUpper } = calculatePriceRange(
    initMinPriceRange.toString(),
    initMaxPriceRange.toString(),
    poolInfo,
    baseTokenInfo,
    quoteTokenInfo,
    fullPriceRange,
    isReversed
  );

  const selectedPriceRange = {
    min: {
      price: priceInTickLower.price,
      tick: priceInTickLower.tick,
    },
    max: {
      price: priceInTickUpper.price,
      tick: priceInTickUpper.tick,
    },
  };

  return {
    fullPriceRange,
    selectedPriceRange,
  };
}

/**
 * 仓位状态管理 Store
 */
export const usePositionStore = create<PositionState>()(
  devtools((set, get) => ({
    ...initialState,
    _setPriceRange: (min: string | number, max: string | number) => {
      const { poolInfo, baseTokenInfo, quoteTokenInfo, fullPriceRange, isReversed } = get();

      // 添加空值检查
      if (!poolInfo || !baseTokenInfo || !quoteTokenInfo) {
        console.warn('Cannot set price range: missing pool or token info');
        return;
      }

      const { priceInTickLower, priceInTickUpper } = calculatePriceRange(
        min,
        max,
        poolInfo,
        baseTokenInfo,
        quoteTokenInfo,
        fullPriceRange,
        isReversed
      );

      set({
        selectedPriceRange: {
          min: {
            price: priceInTickLower.price.toString(),
            tick: priceInTickLower.tick,
          },
          max: {
            price: priceInTickUpper.price.toString(),
            tick: priceInTickUpper.tick,
          },
        },
      });
    },
    setValue: (
      key: keyof PositionState,
      value: any,
      options?: { preserveExactTicks?: boolean }
    ) => {
      const { currentPriceInfo, _setPriceRange } = get();
      switch (key) {
        case 'selectedPercentage': {
          // 简单直接：设置百分比并计算价格范围
          set({ selectedPercentage: value });
          if (value > 0) {
            _setPriceRange(
              decimalMultiply(currentPriceInfo.price, 1 - value).toString(),
              decimalMultiply(currentPriceInfo.price, 1 + value).toString()
            );
          }
          break;
        }
        case 'selectedPriceRange': {
          // 当价格范围被手动调整时，清除百分比状态（表示自定义）
          if (options?.preserveExactTicks) {
            set({ selectedPriceRange: value, selectedPercentage: 0 });
          } else {
            _setPriceRange(value.min.price, value.max.price);
            set({ selectedPercentage: 0 });
          }
          break;
        }
        default:
          set({ [key]: value });
      }
    },
    resetState: () => {
      set(initialState);
    },

    toggleReversed: () => {
      const {
        isReversed,
        poolInfo,
        h24priceRange,
        selectedPriceRange,
        fullPriceRange,
        // initialSelectedPriceRange,
        baseTokenInfo,
        quoteTokenInfo,
        currentPriceInfo,
      } = get();

      // 翻转 isReversed 状态
      const newIsReversed = !isReversed;
      // * 正常：decimals应该是quoteToken的精度
      // * 反向：decimals应该是baseToken的精度
      const decimals = (newIsReversed ? baseTokenInfo?.decimals : quoteTokenInfo?.decimals) || 0;
      // set({ isReversed: newIsReversed });

      if (poolInfo) {
        const currentPrice = +reversePrice(currentPriceInfo.price, decimals);
        const _selectedPriceRange = {
          min: {
            price: reversePrice(selectedPriceRange.max.price, decimals),
            tick: selectedPriceRange.max.tick,
          },
          max: {
            price: reversePrice(selectedPriceRange.min.price, decimals),
            tick: selectedPriceRange.min.tick,
          },
        };
        const _fullPriceRange = {
          min: {
            price: reversePrice(fullPriceRange.max.price, decimals),
            tick: fullPriceRange.max.tick,
          },
          max: {
            price: reversePrice(fullPriceRange.min.price, decimals),
            tick: fullPriceRange.min.tick,
          },
        };
        // const _initialSelectedPriceRange = {
        //   min: {
        //     price: reversePrice(initialSelectedPriceRange.max.price, decimals),
        //     tick: initialSelectedPriceRange.max.tick,
        //   },
        //   max: {
        //     price: reversePrice(initialSelectedPriceRange.min.price, decimals),
        //     tick: initialSelectedPriceRange.min.tick,
        //   },
        // };

        const _h24priceRange = {
          min: reversePrice(h24priceRange.max, decimals),
          max: reversePrice(h24priceRange.min, decimals),
        };

        // console.log('currentPrice', currentPrice, currentPriceInfo.price);
        // console.log('h24priceRange', _h24priceRange, h24priceRange);
        // console.log('fullPriceRange', _fullPriceRange, fullPriceRange);
        // console.log('selectedPriceRange', _selectedPriceRange, selectedPriceRange);
        // console.log(
        //   'initialSelectedPriceRange',
        //   _initialSelectedPriceRange,
        //   initialSelectedPriceRange
        // );

        set({
          isReversed: newIsReversed,
          currentPriceInfo: {
            price: +currentPrice,
            time: Date.now(),
          },
          h24priceRange: _h24priceRange,
          fullPriceRange: _fullPriceRange,
          selectedPriceRange: _selectedPriceRange,
          // initialSelectedPriceRange: _initialSelectedPriceRange,
        });
      } else {
        // 如果没有 poolInfo，只更新 isReversed 状态
        set({ isReversed: newIsReversed });
      }
    },

    /**
     * 初始化页面（首次加载时使用）
     */
    initPage: async (poolInfo: PoolInfo, rpcPoolInfo: IPoolLayoutWithId) => {
      set({ isInitializing: true, error: null });

      try {
        const { isReversed } = get();
        const { mintA: baseTokenInfo, mintB: quoteTokenInfo } = poolInfo;
        const decimals = isReversed ? baseTokenInfo.decimals : quoteTokenInfo.decimals;

        // 初始化默认价格范围
        const selectedPercentage = getDefaultPercentage(poolInfo.category || PoolCategory.DEFAULT);
        const { fullPriceRange, selectedPriceRange } = getInitData(
          poolInfo,
          baseTokenInfo,
          quoteTokenInfo,
          rpcPoolInfo.currentPrice,
          selectedPercentage,
          false
        );

        // 设置所有状态
        set({
          poolInfo,
          rpcPoolInfo,
          baseTokenInfo,
          quoteTokenInfo,
          selectedPercentage,
          fullPriceRange,
          selectedPriceRange,
          currentPriceInfo: {
            price: +rpcPoolInfo.currentPrice.toFixed(decimals),
            time: Date.now(),
          },
          h24priceRange: {
            min: poolInfo.day.ratioLow,
            max: poolInfo.day.ratioHigh,
          },
        });
      } catch (e: any) {
        console.error('Failed to initialize create position page:', e);
        set({
          error: e.message || 'Failed to load pool data',
          isInitializing: false,
          poolInfo: null,
          rpcPoolInfo: null,
        });
      } finally {
        set({ isInitializing: false });
      }
    },

    /**
     * 刷新池子数据（保持用户已选择的价格范围）
     */
    refreshPoolData: async (poolInfo: PoolInfo, rpcPoolInfo: IPoolLayoutWithId) => {
      set({ isInitializing: true, error: null });
      console.log(' refresh pool ');

      try {
        const { isReversed } = get();
        const { mintA: baseTokenInfo, mintB: quoteTokenInfo } = poolInfo;
        const decimals = isReversed ? baseTokenInfo.decimals : quoteTokenInfo.decimals;

        // 只更新池子基本信息，保持用户选择的价格范围
        set({
          poolInfo,
          rpcPoolInfo,
          baseTokenInfo,
          quoteTokenInfo,
          currentPriceInfo: {
            price: +rpcPoolInfo.currentPrice.toFixed(decimals),
            time: Date.now(),
          },
          h24priceRange: {
            min: poolInfo.day.ratioLow,
            max: poolInfo.day.ratioHigh,
          },
        });
      } catch (e: any) {
        console.error('Failed to refresh pool data:', e);
        set({
          error: e.message || 'Failed to refresh pool data',
        });
      } finally {
        set({ isInitializing: false });
      }
    },
  }))
);
