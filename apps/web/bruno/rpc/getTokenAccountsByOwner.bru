meta {
  name: getTokenAccountsByOwner
  type: http
  seq: 1
}

post {
  url: {{host}}
  body: json
  auth: inherit
}

body:json {
  {
    "id": "1",
    "jsonrpc": "2.0",
    "method": "getTokenAccountsByOwner",
    "params": [
      "8pSaeH4X1yJghTzMw4JKaBdN9NxPL2nwN5vtC635h2jL",
      {
        "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
      },
      {
        "encoding": "jsonParsed",
        "commitment": "confirmed"
      }
    ]
  }
}
