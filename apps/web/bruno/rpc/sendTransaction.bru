meta {
  name: sendTransaction
  type: http
  seq: 3
}

post {
  url: {{host}}
  body: json
  auth: inherit
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "method": "sendTransaction",
    "jsonrpc": "2.0",
    "params": [
      "AXG2XQZVVWDUuyVV5dbtvO2gM4X8oR3Fow0KziaDb/BqJbwTW2kB1pUXIcpqmvf7HYWxf1aj3wGMfl6Fcv+wbwGAAQALE1hCkEYHUZxdFLlQYjGtu8C54Zm9S3PiQfmQyBMsgV6vDwJ7hxbNGLQi/IKMTkJCi3BndF7kJxhOwGxNkDetvBhHNgvpmTAREayc8xPaMLryWw4X+Vy8vVDQLt3mrweCJo6RSawMVeyeUeIbZO9HRlwCr4kV2gK7RUyk5/0Q6Vl7pc3oIJcRkT9+0on5AXYlfbFSh2C1Xq+vggQgKGg82MatxCqvDjJUhKZu6UOEwrID0RGD3js55ZuFQFbMFu2Yxvd7U8shVciNxtsyqXCOuMKkVN+S2AmwRGM4AAfCybAA/Yu2qPQAco99O+SWBMHiXpEdYPf603Bjs3G2vjMm9bYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGRm/lIRcy/+ytunLDm+e8jOW7xfcSayxDmzpAAAAABUpTWpkpIQZNJOhxYNo4fHw1td28kruB5B+oQEEFRI0Gm4hX/quBhPtof2NGGMA12sQ53BrrO1WYoPAAAAAAAQbd9uHXZaGT2cvhRs7reawctIXtX1s3kTqM9YV+/wCpBt324e51j94YQl285GzN2rYa/E2DuQ0n/r35KNihi/wtx/HzlaXRRqhwio5PjxqNCL90YyYXP11W0qIrDp3vRULkkXwZMEJTofie9Utn1cKE1bso/B03qlXlel558/zhjJclj04kifG7PRApFI4NgwtaE5na/xCEBI572Nvp+Fm2SqgEvWN1nriUgTNdjiDYXiemzeTMl9d2tBnxh1GSjfBmj/yfO8sPcXz3rIR05qSYHQQWPzjHspvdBMO7xJhBVGrqD1ZdINj6+AF+GsLHPBJWun13ZrlmARoryZDKfgwHCQAFAt0XAgAJAAkDAAAAAAAAAAAQBgABAAsIDAEBCAIAAQwCAAAAQEIPAAAAAAAMAQEBEQ4PABIHAQMCBgQMDQoLDxEFKSsE7QsayR5iQEIPAAAAAAAk9wkAAAAAAAAAAAAAAAAAAAAAAAAAAAABDAMBAAABCQA=",
      {
        "encoding": "base64"
      }
    ],
    "id": "1"
  }
}
