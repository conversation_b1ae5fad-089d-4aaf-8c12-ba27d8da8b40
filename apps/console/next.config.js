//@ts-check

const { composePlugins, withNx } = require('@nx/next');

const basePath = (process.env.NEXT_PUBLIC_PIPELINE_ENV || '').match(/-test-|-perf-/) ? '/byreal-console' : '';

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  // Use this to set Nx-specific options
  // See: https://nx.dev/recipes/next/next-config-setup

  /**
   * @see https://nextjs.org/docs/app/api-reference/config/next-config-js/basePath
   */
  basePath,
  devIndicators: false,
  nx: {
    // NX: Next.js SVGR support is deprecated.
    svgr: false,
  },
  output: 'export',
  typescript: {
    // Set to true to skip type checking during build
    ignoreBuildErrors: true,
  },
  trailingSlash: false,
  images: {
    unoptimized: true,
  },
  webpack: (config, { dev }) => {
    config.resolve.extensionAlias = {
      '.js': ['.js', '.ts', '.tsx'],
    };
    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
