import { Suspense } from 'react';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import ProgramCards from '@/components/ProgramCards';

const ProgramsPage = () => {
  return (
    <ErrorBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <div className="">
          <h1 className="mb-4 text-3xl font-bold">Program Manager</h1>
        </div>
        <ProgramCards />
      </Suspense>
    </ErrorBoundary>
  );
};

export default ProgramsPage;
