'use client';

import { Suspense } from 'react';

import CreateTransaction from '@/components/CreateTransactionButton';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import RefreshButton from '@/components/RefreshButton';
import TransactionsTablePanel from '@/components/TransactionsTablePanel';

export default function TransactionsPage() {
  return (
    <ErrorBoundary>
      <Suspense fallback={<div>Loading ...</div>}>
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h1 className="text-3xl font-bold">Transactions</h1>
            <div className="flex flex-row gap-2">
              <RefreshButton />
              <CreateTransaction />
            </div>
          </div>

          <TransactionsTablePanel />
        </div>
      </Suspense>
    </ErrorBoundary>
  );
}
