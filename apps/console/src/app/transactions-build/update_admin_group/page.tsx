'use client';

import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';
import UpdateAdminGroupButton from '@/components/UpdateAdminGroupButton';

export default function Page() {
  const { register, watch } = useForm();

  const config = watch();

  const updateAmmFieldsConfig = [
    { title: 'fee_keeper', key: 'fee_keeper' },
    {
      title: 'reward_config_manager',
      key: 'reward_config_manager',
    },
    {
      title: 'reward_claim_manager',
      key: 'reward_claim_manager',
    },
    {
      title: 'pool_manager',
      key: 'pool_manager',
    },
    {
      title: 'emergency_manager',
      key: 'emergency_manager',
    },
    { title: 'normal_manager', key: 'normal_manager' },
  ];

  return (
    <div>
      <BackTo href="/transactions-build" rightExtra={<UpdateAdminGroupButton config={{ args: config }} />}>
        Build Transations
      </BackTo>
      <TransactionBuildTitle>update_admin_group</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-5" />
        <InstructionsFieldsResolver title="Args" className="mt-5" register={register} fields={updateAmmFieldsConfig} />
      </div>
    </div>
  );
}
