'use client';

import { useWallet } from '@solana/wallet-adapter-react';
import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import InitAcutionButton from '@/components/InitAcutionButton';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';

export default function Page() {
  const { publicKey } = useWallet();

  const { register: registerAccounts, watch: watchAccounts } = useForm({
    defaultValues: {
      creator: publicKey?.toBase58() || '',
    },
  });
  const { register: registerArgs, watch: wathArgs } = useForm();

  const config = wathArgs();
  const accountsConfig = watchAccounts();

  const accountsFields = [
    { title: 'creator', key: 'creator' },
    { title: 'list_mint', key: 'list_mint' },
    { title: 'quote_mint', key: 'quote_mint' },
    { title: 'custody_authority', key: 'custody_authority' },
  ];
  const argsFields = [
    { title: 'commit_start_time', key: 'commit_start_time', type: 'time' },
    { title: 'commit_end_time', key: 'commit_end_time', type: 'time' },
    { title: 'unit_price1', key: 'bin_unit_price1', type: 'number' },
    { title: 'total_supply1', key: 'total_supply1', type: 'number' },
    { title: 'unit_price2', key: 'bin_unit_price2', type: 'number' },
    { title: 'total_supply2', key: 'total_supply2', type: 'number' },
  ];

  return (
    <div>
      <BackTo
        href="/transactions-build"
        rightExtra={<InitAcutionButton config={{ accounts: accountsConfig, args: config }} />}
      >
        Build Transations
      </BackTo>
      <TransactionBuildTitle>init_acution</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-5" />
        <InstructionsFieldsResolver
          title="Accounts"
          register={registerAccounts}
          fields={accountsFields}
          className="mt-5"
        />
        <InstructionsFieldsResolver title="Args" className="mt-5" register={registerArgs} fields={argsFields} />
      </div>
    </div>
  );
}
