'use client';

import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';
import UpdatePriceButton from '@/components/UpdatePriceButton';

export default function Page() {
  const { register: registerAccounts, watch: watchAccounts } = useForm();
  const { register: registerArgs, watch: wathArgs } = useForm();

  const config = wathArgs();
  const accountsConfig = watchAccounts();

  const accountsFields = [{ title: 'auction', key: 'auction' }];
  const argsFields = [
    { title: 'bin_id', key: 'bin_id', type: 'number' },
    { title: 'unit_price', key: 'unit_price', type: 'number' },
  ];

  return (
    <div>
      <BackTo
        href="/transactions-build"
        rightExtra={<UpdatePriceButton config={{ accounts: accountsConfig, args: config }} />}
      >
        Build Transations
      </BackTo>
      <TransactionBuildTitle>update_price</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-5" />
        <InstructionsFieldsResolver
          title="Accounts"
          register={registerAccounts}
          fields={accountsFields}
          className="mt-5"
        />
        <InstructionsFieldsResolver title="Args" className="mt-5" register={registerArgs} fields={argsFields} />
      </div>
    </div>
  );
}
