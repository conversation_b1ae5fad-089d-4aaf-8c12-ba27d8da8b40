'use client';

import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';
import WithdrawOffchainRewardButton from '@/components/WithdrawOffchainRewardButton';

export default function Page() {
  const { register: registerAccounts, watch: watchAccounts } = useForm();
  const { register: registerArgs, watch: watchArgs } = useForm();

  const config = watchArgs();
  const accountsConfig = watchAccounts();

  const argsFields = [{ title: 'amount', key: 'amount', type: 'number' }];

  const accountsFields = [
    { title: 'pool_id', key: 'pool_id' },
    { title: 'authority', key: 'authority' },
    { title: 'token_mint', key: 'token_mint' },
    { title: 'receiver_token_account', key: 'receiver_token_account' },
  ];

  return (
    <div>
      <BackTo
        href="/transactions-build"
        rightExtra={<WithdrawOffchainRewardButton config={{ accounts: accountsConfig, args: config }} />}
      >
        Build Transations
      </BackTo>
      <TransactionBuildTitle>withdraw_offchain_reward</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-2" />
        <InstructionsFieldsResolver
          title="Accounts"
          className="mt-2"
          register={registerAccounts}
          fields={accountsFields}
        />
        <InstructionsFieldsResolver title="Args" className="mt-2" register={registerArgs} fields={argsFields} />
      </div>
    </div>
  );
}
