'use client';

import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import CreatePoolButton from '@/components/CreatePoolButton';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';

export default function Page() {
  const { register: registerArgs, watch: watchArgs } = useForm();
  const { register: registerAccounts, watch: watchAccounts } = useForm();

  const config = watchArgs();
  const accountsConfig = watchAccounts();

  const argsFields = [
    { title: 'sqrt_price_x64', key: 'sqrt_price_x64', type: 'number' },
    { title: 'open_time', key: 'open_time', type: 'time' },
  ];

  const accountsFields = [
    { title: 'pool_creator', key: 'pool_creator' },
    { title: 'pool_manager', key: 'pool_manager' },
    { title: 'amm_config', key: 'amm_config' },
    { title: 'token_mint_0', key: 'token_mint_0' },
    // { title: 'token_program_0', key: 'token_program_0' },
    { title: 'token_mint_1', key: 'token_mint_1' },
    // { title: 'token_program_1', key: 'token_program_1' },
  ];

  return (
    <div>
      <BackTo
        href="/transactions-build"
        rightExtra={<CreatePoolButton config={{ accounts: accountsConfig, args: config }} />}
      >
        Build Transations
      </BackTo>
      <TransactionBuildTitle>create_pool</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-5" />
        <InstructionsFieldsResolver
          title="Accounts"
          className="mt-5"
          register={registerAccounts}
          fields={accountsFields}
        />
        <InstructionsFieldsResolver title="Args" className="mt-5" register={registerArgs} fields={argsFields} />
      </div>
    </div>
  );
}
