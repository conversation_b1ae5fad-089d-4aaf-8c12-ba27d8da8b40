'use client';

import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';
import UpdateAmmConfigButton from '@/components/UpdateAmmConfigButton';

export default function Page() {
  const { register: registerAccounts, watch: watchAccounts } = useForm();
  const { register: registerArgs, watch: watchArgs } = useForm();

  const config = watchArgs();
  const accountsConfig = watchAccounts();

  const accountsFields = [
    { title: 'amm_config_id', key: 'amm_config_id' },
    { title: 'owner', key: 'owner' },
  ];

  const argsFields = [
    { title: 'param', key: 'param' },
    { title: 'value', key: 'value' },
  ];

  return (
    <div>
      <BackTo
        href="/transactions-build"
        rightExtra={<UpdateAmmConfigButton config={{ accounts: accountsConfig, args: config }} />}
      >
        Build Transations
      </BackTo>
      <TransactionBuildTitle>update_amm_config</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-5" />
        <InstructionsFieldsResolver
          title="Accounts"
          className="mt-5"
          register={registerAccounts}
          fields={accountsFields}
        />
        <InstructionsFieldsResolver title="Args" className="mt-5" register={registerArgs} fields={argsFields} />
      </div>
    </div>
  );
}
