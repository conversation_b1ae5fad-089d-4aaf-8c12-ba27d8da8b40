'use client';

import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';

import CreatePositionButton from '../../../components/CreatePositionButton';

export default function Page() {
  const { register: registerArgs, watch: watchArgs } = useForm();
  const { register: registerAccounts, watch: watchAccounts } = useForm();

  const config = watchArgs();
  const accountsConfig = watchAccounts();

  //accounts: pool_id
  // args: start_price,end_price,base(TokenMint0/TokenMint1),base_amount

  const argsFields = [
    { title: 'start_price', key: 'start_price', type: 'number' },
    { title: 'end_price', key: 'end_price', type: 'number' },
    {
      title: 'base',
      key: 'base',
      type: 'select',
      data: [
        { label: 'TokenMint0', value: 'MintA' },
        { label: 'TokenMint1', value: 'MintB' },
      ],
    },
    { title: 'base_amount', key: 'base_amount', type: 'number' },
  ];

  const accountsFields = [{ title: 'pool_id', key: 'pool_id' }];

  return (
    <div>
      <BackTo
        href="/transactions-build"
        rightExtra={<CreatePositionButton config={{ accounts: accountsConfig, args: config }} />}
      >
        Build Transations
      </BackTo>
      <TransactionBuildTitle>create_pool</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-5" />
        <InstructionsFieldsResolver
          title="Accounts"
          className="mt-5"
          register={registerAccounts}
          fields={accountsFields}
        />
        <InstructionsFieldsResolver title="Args" className="mt-5" register={registerArgs} fields={argsFields} />
      </div>
    </div>
  );
}
