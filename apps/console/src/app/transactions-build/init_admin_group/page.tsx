'use client';

import { useWallet } from '@solana/wallet-adapter-react';
import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import InitAdminGroupButton from '@/components/InitAdminGroupButton';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';

export default function Page() {
  const { publicKey } = useWallet();

  const { register, watch } = useForm({
    defaultValues: {
      fee_keeper: publicKey?.toBase58(),
    },
  });

  const config = watch();

  const argsFields = [
    { title: 'fee_keeper', key: 'fee_keeper' },
    {
      title: 'reward_config_manager',
      key: 'reward_config_manager',
    },
    {
      title: 'reward_claim_manager',
      key: 'reward_claim_manager',
    },
    {
      title: 'pool_manager',
      key: 'pool_manager',
    },
    {
      title: 'emergency_manager',
      key: 'emergency_manager',
    },
    {
      title: 'normal_manager',
      key: 'normal_manager',
    },
  ];

  return (
    <div>
      <BackTo href="/transactions-build" rightExtra={<InitAdminGroupButton config={{ args: config }} />}>
        Build Transations
      </BackTo>
      <TransactionBuildTitle>init_admin_group</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-5" />
        <InstructionsFieldsResolver title="Args" className="mt-5" register={register} fields={argsFields} />
      </div>
    </div>
  );
}
