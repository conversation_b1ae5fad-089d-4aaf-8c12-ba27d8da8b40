'use client';

import { Suspense } from 'react';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import TransactionTemplates from '@/components/TransactionTemplates';

export default function BuildPageLayout() {
  return (
    <ErrorBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h1 className="text-3xl font-bold">Transactions Template</h1>
          </div>
          <TransactionTemplates />
        </div>
      </Suspense>
    </ErrorBoundary>
  );
}
