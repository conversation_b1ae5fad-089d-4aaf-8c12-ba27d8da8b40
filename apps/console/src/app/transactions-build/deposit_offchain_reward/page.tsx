'use client';

import { useForm } from 'react-hook-form';

import BackTo from '@/components/BackTo';
import ClmmProgramIdInput from '@/components/ClmmProgramIdInput';
import DepositOffchainRewardButton from '@/components/DepositOffchainRewardButton';
import InstructionsFieldsResolver from '@/components/InstructionsFieldsResolver';
import TransactionBuildTitle from '@/components/TransactionBuildTitle';

export default function Page() {
  const { register: registerAccounts, watch: watchAccounts } = useForm();
  const { register: registerArgs, watch: watchArgs } = useForm();

  const config = watchArgs();
  const accountsConfig = watchAccounts();

  const accountsFields = [
    { title: 'pool_id', key: 'pool_id' },
    { title: 'authority', key: 'authority' },
    { title: 'token_mint', key: 'token_mint' },
    { title: 'payer_token_account', key: 'payer_token_account' },
  ];

  const argsFields = [{ title: 'amount', key: 'amount', type: 'number' }];

  return (
    <div>
      <BackTo
        href="/transactions-build"
        rightExtra={<DepositOffchainRewardButton config={{ accounts: accountsConfig, args: config }} />}
      >
        Build Transations
      </BackTo>
      <TransactionBuildTitle>deposit_offchain_reward</TransactionBuildTitle>
      <div className="px-3">
        <ClmmProgramIdInput className="mt-5" />
        <InstructionsFieldsResolver
          title="Accounts"
          className="mt-5"
          register={registerAccounts}
          fields={accountsFields}
        />
        <InstructionsFieldsResolver title="Args" className="mt-5" register={registerArgs} fields={argsFields} />
      </div>
    </div>
  );
}
