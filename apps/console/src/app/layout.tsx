import { AlertTriangle, CheckSquare } from 'lucide-react';
import type { Metadata } from 'next';
import { Suspense } from 'react';
import { Toaster } from 'sonner';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import QueryProvider from '@/components/QueryProvider';
import TabNav from '@/components/TabNav';
import { Wallet } from '@/components/Wallet';
import './globals.css';

export const metadata: Metadata = {
  title: 'v4-client',
  description: 'Next app for publick-v4-client',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <QueryProvider>
          <Wallet>
            <div className="flex h-screen min-w-full flex-col bg-white md:flex-row">
              <Suspense>
                <TabNav />
              </Suspense>
              <div className="mt-1 space-y-2 p-3 pb-16 pt-3 md:ml-auto md:w-9/12 md:space-y-4 md:pt-6 overflow-auto">
                <ErrorBoundary>{children}</ErrorBoundary>
              </div>
            </div>

            <Toaster
              expand
              visibleToasts={3}
              icons={{
                error: <AlertTriangle className="h-4 w-4 text-red-600" />,
                success: <CheckSquare className="h-4 w-4 text-green-600" />,
              }}
            />
          </Wallet>
        </QueryProvider>
      </body>
    </html>
  );
}
