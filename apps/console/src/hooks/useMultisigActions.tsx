'use client';

import { BaseInstruction, Chain, SqrtPriceMath, TickMath } from '@byreal/clmm-sdk';
import {
  getResetProgram,
  LaunchpadInstructions,
  MultisigSDK,
  MultisigUtils,
  ProposalInfo,
  ParsedInstruction,
} from '@byreal/multisig-sdk';
import { getAssociatedTokenAddress, getMint, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { Keypair, LAMPORTS_PER_SOL, PublicKey, Transaction } from '@solana/web3.js';
import { useQuery } from '@tanstack/react-query';
import BN from 'bn.js';
import bs58 from 'bs58';
import dayjs from 'dayjs';
import { Decimal } from 'decimal.js';
import invariant from 'invariant';

import { MultisigManager } from '@/lib/constatns';

import { useMultisigAddress } from './useMultisigAddress';

export const createKey = Keypair.fromS<PERSON>ret<PERSON>ey(
  Buffer.from([
    3, 194, 2, 97, 0, 253, 4, 71, 124, 180, 226, 211, 244, 142, 94, 77, 135, 193, 187, 215, 241, 107, 56, 170, 206, 137,
    82, 249, 172, 71, 41, 241, 35, 253, 220, 54, 17, 158, 87, 74, 108, 149, 82, 139, 124, 127, 104, 34, 110, 172, 126,
    38, 188, 15, 201, 46, 117, 139, 56, 118, 84, 41, 42, 199,
  ])
);

// 钱包私钥，测试用
const creator = Keypair.fromSecretKey(
  bs58.decode('2wjauVm3WwJD4KrTJR16vjHfWJFSc5cmdsoxfqJat1CzKtSQyVBL2t8rwpmnLZg96VEpTVMkHgsVo1rePfaYc73F')
);

// 成员私钥，测试用
const wallet2 = Keypair.fromSecretKey(
  bs58.decode('wYsoLXn6SxkSbfEEqQPVYZKAqWWC5MkNV3hq5mLNYuPraBB8rNytarhTnGnpGKFbbAF1xnU9NT659CTmzmK6GYi')
);

export type InitAdminGroupParams = {
  fee_keeper: string;
  reward_config_manager: string;
  reward_claim_manager: string;
  pool_manager: string;
  emergency_manager: string;
  normal_manager: string;
};

export type UpdateAdminGroupParams = InitAdminGroupParams;

export type InitAdminGroupCallbacks = {
  onSuccess?: (sig: string) => void;
  onError?: (error: unknown) => void;
};

export type CreatePoolParams = {
  pool_creator: string;
  pool_manager: string;
  amm_config: string;
  token_mint_0: string;
  token_program_0: string;
  token_mint_1: string;
  token_program_1: string;
  sqrt_price_x64: number;
  open_time: number;
};

export type DepositOffchainRewardParams = {
  pool_id: string;
  payer: string;
  authority: string;
  token_mint: string;
  payer_token_account: string;
  token_program: string;
  amount: number;
};

export type WithdrawOffchainRewardParams = {
  pool_id: string;
  authority: string;
  token_mint: string;
  receiver_token_account: string;
  amount: string;
};

export type UpdatePoolParams = {
  pool_id: string;
  authority: string;
  status: number;
};

export type CreatePositionParams = {
  pool_id: string;
  start_price: number;
  end_price: number;
  base: string;
  base_amount: number;
};

export type InitAcutionParams = {
  creator: string;
  list_mint: string;
  quote_mint: string;
  custody_authority: string;
  commit_start_time: number;
  commit_end_time: number;
  bins: { unit_price: number; total_supply: number }[];
};

export type WithdrawFundsParams = {
  auction: string;
};

export type UpdatePriceParams = {
  auction: string;
  bin_id: number;
  unit_price: number;
};

export type CollectAllFeesParams = WithdrawFundsParams;

// export const useMultisigActions = () => {
//   const { publicKey, sendTransaction } = useWallet();
//   console.log('🚀 ~ useMultisigActions ~ sendTransaction:', publicKey);
//   const { connection } = useConnection();
//   const { multisigAddress } = useMultisigAddress(); // 设置多重签地址
//   return {
//     createPosition: async (params: any) => {},
//   };
// };

export const useMultisigActions = () => {
  const { publicKey, sendTransaction } = useWallet();
  const { connection } = useConnection();
  const { multisigAddress } = useMultisigAddress(); // 设置多重签地址
  // const sdk: any = {};

  // todo 不同的多签钱包负责不同的指令，调用对应指令需要用到对应的vault
  // 硬编码一个钱包对应的指令表

  return {
    /**
     * 本地测试节点使用，创建多重签名，实际环境不需要这一步
     * @returns 创建多签的方法
     */
    createMultisig: async () => {
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });

        invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
        const [multisigPda] = MultisigUtils.getMultisigPda(createKey.publicKey);
        console.log('多重签名 PDA:', multisigPda.toBase58());

        await connection.requestAirdrop(creator.publicKey, 10 * LAMPORTS_PER_SOL);
        console.log('✅ 创建者充值成功');

        const createSignature = await sdk.createMultisig({
          createKey,
          creator,
          threshold: 2, // 需要2个签名
          members: [
            {
              key: creator.publicKey,
              permissions: MultisigUtils.allPermissions(),
            },
            {
              key: wallet2.publicKey,
              permissions: MultisigUtils.createPermissions(['vote']),
            },
          ],
        });
        console.log('✅ 多重签名钱包创建成功:', createSignature);

        console.log('为 Vault 充值...');
        const airdropSignature = await sdk.airdropToVault(10);
        console.log('✅ Vault 充值成功:', airdropSignature);
      } catch (error) {
        console.log('Create multisig error', error);
        throw error;
      }
    },
    // 初始化admin group
    initAdminGroup: async (params: InitAdminGroupParams, callbacks?: InitAdminGroupCallbacks) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const initPrams = {
          feeKeeper: new PublicKey(params?.fee_keeper),
          rewardConfigManager: new PublicKey(params?.reward_config_manager),
          rewardClaimManager: new PublicKey(params?.reward_claim_manager),
          poolManager: new PublicKey(params?.pool_manager),
          emergencyManager: new PublicKey(params?.emergency_manager),
          normalManager: new PublicKey(params?.normal_manager),
        };

        const customInstruction = await BaseInstruction.initAmmAdminGroupInstruction(initPrams);
        const proposalInstructions = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [customInstruction],
          memo: 'Init admin group',
        });
        console.log('🚀 ~ return ~ proposalInstructions:', proposalInstructions);

        const transaction = new Transaction();
        transaction.add(...proposalInstructions.instructions);
        console.log('🚀 ~ return ~ transaction:', transaction);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ 初始化AdminGroup提案发送成功:', signature);
        callbacks?.onSuccess?.(signature);
      } catch (error) {
        console.log('Init admin group error', error);
        callbacks?.onError?.(error);
        throw error;
      }
    },
    // 更新 admin group
    updateAdminGroup: async (params: UpdateAdminGroupParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const {
          fee_keeper,
          reward_config_manager,
          reward_claim_manager,
          pool_manager,
          emergency_manager,
          normal_manager,
        } = params;
        const fields = {
          feeKeeper: fee_keeper,
          rewardConfigManager: reward_config_manager,
          rewardClaimManager: reward_claim_manager,
          poolManager: pool_manager,
          emergencyManager: emergency_manager,
          normalManager: normal_manager,
        };
        const updateParams = Object.fromEntries(
          Object.entries(fields)
            .filter(([, value]) => value)
            .map(([key, value]) => [key, new PublicKey(value!)])
        );
        const customInstruction = await BaseInstruction.updateAmmAdminGroupInstruction(updateParams);
        const proposalInstructions = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [customInstruction],
          memo: 'Update admin group',
        });
        console.log('🚀 ~ return ~ proposalInstructions:', proposalInstructions);

        const transaction = new Transaction();
        transaction.add(...proposalInstructions.instructions);
        console.log('🚀 ~ return ~ transaction:', transaction);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ AdminGroup更新提案发送成功:', signature);
      } catch (error) {
        console.log('Update admin group error', error);
        throw error;
      }
    },
    // 创建 pool
    createPool: async (params: CreatePoolParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const {
          token_mint_0,
          token_mint_1,
          pool_creator,
          pool_manager,
          amm_config,
          sqrt_price_x64: price,
          open_time,
        } = params;
        const token0Mint = new PublicKey(token_mint_0);
        const token1Mint = new PublicKey(token_mint_1);
        const [mint0, mint1] =
          token0Mint.toBuffer().compare(token1Mint.toBuffer()) > 0
            ? [token1Mint, token0Mint]
            : [token0Mint, token1Mint];

        // sqrt_price_x64 = 1000 -> 1mint0 : 1000mint1
        const mintDecimals0 = (await getMint(connection, mint0)).decimals;
        const mintDecimals1 = (await getMint(connection, mint1)).decimals;
        const sqrt_price_x64 = SqrtPriceMath.priceToSqrtPriceX64(new Decimal(price), mintDecimals0, mintDecimals1);

        const instrcution = await BaseInstruction.createPoolInstruction(
          new PublicKey(pool_creator),
          new PublicKey(pool_manager), // sdk.getVaultPda(1),
          new PublicKey(amm_config),
          mint0,
          TOKEN_PROGRAM_ID,
          mint1,
          TOKEN_PROGRAM_ID,
          sqrt_price_x64,
          // new BN('583337074090354317156'),
          new BN(open_time ? open_time : 0)
        );

        console.log('Create Pool Instruction 构建成功：', instrcution);

        const proposalInstructions = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [instrcution],
          memo: 'Create pool',
          vaultIndex: 1,
        });

        const transaction = new Transaction();
        transaction.add(...proposalInstructions.instructions);
        console.log('🚀 ~ return ~ transaction:', transaction);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ 创建Pool提案发送成功:', signature);
      } catch (error) {
        console.log('Create pool error', error);
        throw error;
      }
    },
    // deposit_offchain_reward
    depositOffchainReward: async (params: DepositOffchainRewardParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const instruction = await BaseInstruction.depositOffchainRewardInstruction(
          new PublicKey(params?.pool_id),
          publicKey!, // payer
          new PublicKey(params?.authority), // rewardConfigManager, // authority (需要是 reward_config_manager)
          new PublicKey(params?.token_mint), // tokenMint
          new PublicKey(params?.payer_token_account), // payerTokenAccount
          TOKEN_PROGRAM_ID, // tokenProgram
          new BN(params.amount ? params.amount : 0) // amount
        );

        const proposalInstruction = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [instruction],
          memo: 'Deposit offchain reward',
          vaultIndex: 1,
        });

        const transaction = new Transaction();
        transaction.add(...proposalInstruction.instructions);
        console.log('🚀 ~ return ~ transaction:', transaction);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ Deposit offchain reward提案发送成功:', signature);
      } catch (error) {
        console.log('Deposit offchain reward error', error);
        throw error;
      }
    },
    // withdraw_offchain_reward
    withdrawOffchainReward: async (params: WithdrawOffchainRewardParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const instruction = await BaseInstruction.withdrawOffchainRewardInstruction(
          new PublicKey(params?.pool_id),
          new PublicKey(params?.authority), // rewardConfigManager, // authority (需要是 reward_config_manager)
          new PublicKey(params?.token_mint), // token0Mint
          new PublicKey(params?.receiver_token_account), // receiverTokenAccount
          TOKEN_PROGRAM_ID, // tokenProgram
          new BN(params?.amount ? params.amount : 0) // amount
        );

        const proposalInstruction = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [instruction],
          memo: 'Withdraw offchain reward',
          vaultIndex: 1,
        });

        const transaction = new Transaction();
        transaction.add(...proposalInstruction.instructions);
        console.log('🚀 ~ return ~ transaction:', transaction);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ Withdraw offchain reward提案发送成功:', signature);
      } catch (error) {
        console.log('Withdraw offchain reward error', error);
        throw error;
      }
    },
    // 更新 pool
    updatePool: async (params: UpdatePoolParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const VAULT_INDEX = MultisigManager.update_pool.vaultIndex;
        const instruction = await BaseInstruction.updatePoolStatusInstruction(
          new PublicKey(params?.authority), // authority (emergency_manager)
          new PublicKey(params?.pool_id),
          Number(params?.status)
        );

        const proposalInstruction = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [instruction],
          memo: `更新池状态为: ${params?.status}`,
          vaultIndex: VAULT_INDEX,
        });

        const transaction = new Transaction();
        transaction.add(...proposalInstruction.instructions);
        console.log('🚀 ~ return ~ transaction:', transaction);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ 更新Pool提案发送成功:', signature);
      } catch (error) {
        console.log('更新Pool error', error);
        throw error;
      }
    },
    // 创建 Position
    createPosition: async (params: CreatePositionParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const { pool_id, base, base_amount, start_price, end_price } = params;
        const chain = new Chain({ connection });
        const poolInfo = await chain.getRawPoolInfoByPoolId(pool_id);
        // const baseAmount = new BN(2 * 10 ** poolInfo.mintDecimalsB); // 2 个 TokenB
        const baseAmount = base_amount
          ? new BN(base_amount * 10 ** (base === 'MintA' ? poolInfo.mintDecimalsA : poolInfo.mintDecimalsB))
          : new BN(0);
        const priceInTickLower = TickMath.getTickAlignedPriceDetails(
          new Decimal(start_price),
          poolInfo.tickSpacing,
          poolInfo.mintDecimalsA,
          poolInfo.mintDecimalsB
        );
        const priceInTickUpper = TickMath.getTickAlignedPriceDetails(
          new Decimal(end_price),
          poolInfo.tickSpacing,
          poolInfo.mintDecimalsA,
          poolInfo.mintDecimalsB
        );
        // base -> MintB
        // const amountA = chain.getAmountAFromAmountB({
        //   priceLower: priceInTickLower.price,
        //   priceUpper: priceInTickUpper.price,
        //   amountB: baseAmount,
        //   poolInfo,
        // });
        const amountTarget =
          base === 'MintB'
            ? chain.getAmountAFromAmountB({
                priceLower: priceInTickLower.price,
                priceUpper: priceInTickUpper.price,
                amountB: baseAmount,
                poolInfo,
              })
            : chain.getAmountBFromAmountA({
                priceLower: priceInTickLower.price,
                priceUpper: priceInTickUpper.price,
                amountA: baseAmount,
                poolInfo,
              });
        const amountAWithSlippage = new BN(amountTarget ? amountTarget : 0).mul(new BN(10200)).div(new BN(10000)); // +2% slippage
        const { instructions, signers } = await chain.createPositionInstructions({
          userAddress: sdk.getVaultPda(),
          poolInfo,
          tickLower: priceInTickLower.tick,
          tickUpper: priceInTickUpper.tick,
          base: base as 'MintA' | 'MintB',
          baseAmount,
          otherAmountMax: amountAWithSlippage,
        });
        // throw new Error('暂时无法创建 Position，请联系开发者');
        const proposalInstruction = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions,
          memo: `创建Position`,
        });
        const transaction = new Transaction();
        transaction.add(...proposalInstruction.instructions);
        const signature = await sendTransaction(transaction, connection);
        console.log('✅ 创建Position提案发送成功:', signature);
        // 只会生成一个signer,签名成功后保存到本地
        if (signers?.length) {
          const curIdx = await (await sdk.getProposals()).length;
          console.log('🚀 ~ createPosition: ~ curIdx:', curIdx);
          console.log('🚀 ~ createPosition: ~ signers:', bs58.encode(signers[0].secretKey));
          localStorage.setItem(`position_key_${curIdx}`, bs58.encode(signers[0].secretKey));
        }
      } catch (error) {
        console.log('创建Position error', error);
        throw error;
      }
    },
    // 初始化拍卖
    initAcution: async (params: InitAcutionParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const listMint = new PublicKey(params.list_mint);
        const quoteMint = new PublicKey(params.quote_mint);
        const payerListTokenAcc = await getAssociatedTokenAddress(listMint, creator.publicKey);
        const instruction = await LaunchpadInstructions.initAuctionInstruction({
          payer: publicKey!,
          payerListTokenAccount: payerListTokenAcc,
          listMint,
          quoteMint,
          listTokenProgram: TOKEN_PROGRAM_ID,
          quoteTokenProgram: TOKEN_PROGRAM_ID,
          commitStartTime: new BN(dayjs(params.commit_start_time ? params.commit_start_time : Date.now()).unix()),
          commitEndTime: new BN(dayjs(params.commit_end_time ? params.commit_end_time : Date.now()).unix()),
          custodyAuthority: creator.publicKey,
          whitelistAuthority: null,
          userCommitCapLimit: null,
          claimFeeBasis: null,
          // bins: [
          //   { unitPrice: new BN(1_000_000), totalSupply: new BN(10 * 10 ** 6) },
          //   { unitPrice: new BN(2_000_000), totalSupply: new BN(10 * 10 ** 6) },
          // ],
          bins: params?.bins.map((bin) => ({
            unitPrice: new BN(bin.unit_price ? bin.unit_price : 0),
            totalSupply: new BN(bin.total_supply ? bin.total_supply : 0),
          })),
        });

        const proposalInstruction = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [instruction],
          memo: 'Init Auction',
        });

        const transaction = new Transaction();
        transaction.add(...proposalInstruction.instructions);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ 初始化拍卖提案发送成功:', signature);
      } catch (error) {
        console.log('初始化拍卖 error', error);
        throw error;
      }
    },
    // 更新拍卖价格
    updatePrice: async (params: UpdatePriceParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const { auction, bin_id, unit_price } = params;
        const instruction = await LaunchpadInstructions.updatePriceInstruction(
          new PublicKey(auction),
          bin_id,
          new BN(unit_price ? unit_price : 0)
        );

        const proposalInstruction = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [instruction],
          memo: `update price bin ${bin_id} to ${unit_price.toString()}`,
        });

        const transaction = new Transaction();
        transaction.add(...proposalInstruction.instructions);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ 更新价格提案发送成功:', signature);
      } catch (error) {
        console.log('updatePrice error', error);
        throw error;
      }
    },
    // 提取资金
    withdrawFunds: async (params: WithdrawFundsParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const program = getResetProgram(connection);
        const auctionData = await program.account.auction.fetch(params?.auction);

        // 准备用户（creator）的 ATA
        const [userListTokenAccount, userQuoteTokenAccount] = await Promise.all([
          getAssociatedTokenAddress(auctionData.listMint, publicKey!),
          getAssociatedTokenAddress(auctionData.quoteMint, publicKey!),
        ]);

        const instruction = await LaunchpadInstructions.withdrawFundsInstruction(
          publicKey,
          new PublicKey(params?.auction),
          userListTokenAccount,
          userQuoteTokenAccount,
          TOKEN_PROGRAM_ID,
          TOKEN_PROGRAM_ID,
          connection
        );

        // 在 initAuction 中等待 commit_end_time 后，才能执行
        const proposalInstruction = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [instruction],
          memo: 'Withdraw Funds',
        });

        const transaction = new Transaction();
        transaction.add(...proposalInstruction.instructions);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ 提取资金提案发送成功:', signature);
      } catch (error) {
        console.log('withdrawFunds error', error);
        throw error;
      }
    },
    // 收集所有费用
    collectAllFees: async (params: CollectAllFeesParams) => {
      invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
      invariant(publicKey, 'Wallet ID not found');
      try {
        // sdk1,sdk2,sdk3分别对应不同的多签钱包
        const sdk = new MultisigSDK({
          connection,
          multisigPda: new PublicKey(multisigAddress!),
        });
        const program = getResetProgram(connection);
        const auctionData = await program.account.auction.fetch(params?.auction);

        // 准备用户（creator）的 ATA
        const [userListTokenAccount, userQuoteTokenAccount] = await Promise.all([
          getAssociatedTokenAddress(auctionData.listMint, publicKey!),
          getAssociatedTokenAddress(auctionData.quoteMint, publicKey!),
        ]);

        const instruction = await LaunchpadInstructions.collectAllFeesInstruction(
          publicKey,
          new PublicKey(params?.auction),
          userListTokenAccount,
          userQuoteTokenAccount,
          TOKEN_PROGRAM_ID,
          TOKEN_PROGRAM_ID,
          connection
        );

        const proposalInstruction = await sdk.createProposalInstructions({
          creator: publicKey!,
          instructions: [instruction],
          memo: 'Collect All Fees',
        });

        const transaction = new Transaction();
        transaction.add(...proposalInstruction.instructions);

        const signature = await sendTransaction(transaction, connection);
        console.log('✅ 收集所有费用提案发送成功:', signature);
      } catch (error) {
        console.log('CollectFees error', error);
        throw error;
      }
    },
  };
};

export const useProposalDetail = (transactionIndex: number) => {
  const { multisigAddress } = useMultisigAddress();
  const { connection } = useConnection();

  const fetchProposalDetail = async (): Promise<ProposalInfo | null> => {
    invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
    const sdk = new MultisigSDK({
      connection,
      multisigPda: new PublicKey(multisigAddress!),
    });
    try {
      const proposal = await sdk.getProposalDetail(transactionIndex);
      return proposal;
    } catch (error) {
      console.error('获取提案详情失败:', error);
      throw error;
    }
  };

  const { data: proposalDetail, ...queryResult } = useQuery({
    queryKey: ['proposalDetail', multisigAddress, transactionIndex],
    queryFn: fetchProposalDetail,
    enabled: !!multisigAddress && !!connection && transactionIndex !== undefined,
  });

  return { proposalDetail, ...queryResult };
};

export const useInstrcutionDetail = (transactionIndex: number, extra?: { flag?: boolean }) => {
  const { multisigAddress } = useMultisigAddress();
  const { connection } = useConnection();
  const { flag = true } = extra ?? {};

  const fetchInstructionDetail = async (): Promise<ParsedInstruction[] | null> => {
    invariant(multisigAddress, 'Multisig address not found. Please create a multisig first.');
    const sdk = new MultisigSDK({
      connection,
      multisigPda: new PublicKey(multisigAddress!),
    });
    try {
      const instructions = await sdk.parseInstructionData(BigInt(transactionIndex));
      return instructions;
    } catch (error) {
      console.error('获取指令详情失败:', error);
      throw error;
    }
  };

  const { data: instructionDetail, ...queryResult } = useQuery({
    queryKey: ['instructionDetail', multisigAddress, transactionIndex],
    queryFn: fetchInstructionDetail,
    enabled: !!multisigAddress && !!connection && transactionIndex !== undefined && flag === true,
  });

  return { instructionDetail, ...queryResult };
};
