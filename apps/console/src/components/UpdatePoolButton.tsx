import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { UpdatePoolParams, useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function UpdatePoolButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const router = useRouter();

  const { updatePool } = useMultisigActions();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const updatePoolParams: UpdatePoolParams = {
    ...config?.args,
    ...config?.accounts,
  };

  return (
    <InstructionDialog
      title="update_pool"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(updatePool(updatePoolParams), {
          id: 'update_pool',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
