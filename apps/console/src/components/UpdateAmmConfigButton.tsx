import { useState } from 'react';

import InstructionDialog from './InstructionDialog';

export default function UpdateAmmConfigButton({ config }: { config: Record<string, unknown> }) {
  const [open, setOpen] = useState(false);

  const handleSubmitTransaction = () => {
    setOpen(false);
  };

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  return (
    <InstructionDialog
      title="update_amm_config"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={handleSubmitTransaction}
    />
  );
}
