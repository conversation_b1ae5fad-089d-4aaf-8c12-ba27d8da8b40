'use client';

import { useRouter } from 'next/navigation';

import { useMultisigAddress } from '@/hooks/useMultisigAddress';

import { Button } from './ui/button';

export const ChangeMultisigFromNav = () => {
  const { setMultisigAddress } = useMultisigAddress(); // Use React Query hook
  const router = useRouter();
  const handleChangeMultisig = () => {
    setMultisigAddress.mutate(null); // Wipes out the stored multisig address
    // navigate to home
    router.push('/');
  };

  return (
    <Button className={`mb-2 w-full bg-gray-500`} onClick={handleChangeMultisig}>
      Switch Squad
    </Button>
  );
};
