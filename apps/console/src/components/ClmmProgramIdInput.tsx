'use clinet';

import { cn } from '@/lib/utils';

import { Input } from './ui/input';

interface ProgramIdInputProps {
  className?: string;
}

export default function ProgramIdInput(props: ProgramIdInputProps) {
  const { className } = props;
  return (
    <div className={cn(className)}>
      <div className="text-xl font-bold text-black">Program ID (CLMM)</div>
      <Input
        className="mt-2"
        placeholder="program id"
        value={'45iBNkaENereLKMjLm2LHkF3hpDapf6mnvrM5HWFg9cY'}
        disabled
      />
    </div>
  );
}
