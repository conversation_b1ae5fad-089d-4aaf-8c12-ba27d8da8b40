'use client';

import { ParsedInstruction } from '@byreal/multisig-sdk';
import * as multisig from '@sqds/multisig';
import { ReceiptText } from 'lucide-react';
import dynamic from 'next/dynamic';
import { Suspense, useState } from 'react';

import { useInstrcutionDetail } from '@/hooks/useMultisigActions';

import { Dialog, DialogHeader } from './ui/dialog';
import { DialogTrigger } from './ui/dialog';
import { DialogContent, DialogTitle } from './ui/dialog';

const JsonView = dynamic(() => import('react-json-view'), { ssr: false }); // import('react-json-view')

type ApproveButtonProps = {
  multisigPda: string;
  transactionIndex: number;
  proposal: multisig.generated.Proposal | null;
  programId: string;
};

export default function ProposalDetailButton(props: ApproveButtonProps) {
  const { transactionIndex } = props;
  const [isOpen, setIsOpen] = useState(false);
  const { instructionDetail } = useInstrcutionDetail(transactionIndex, { flag: isOpen });
  const [detail] = !Array.isArray(instructionDetail) ? [] : instructionDetail!;
  const parseDetail = (detail: ParsedInstruction) => {
    if (!detail) return null;
    return {
      ...detail,
      decodedData: detail?.decodedData, // todo 根据instructionName对参数进行解析
    };
  };
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger
        className={`mr-2 h-10 px-4 py-2 bg-primary hover:bg-primary/90 rounded-md text-primary-foreground`}
        onClick={() => setIsOpen(true)}
      >
        <ReceiptText />
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Instruction Detail</DialogTitle>
        </DialogHeader>
        <Suspense fallback={<div>Loading...</div>}>
          <div className="h-[400px] overflow-auto">
            <JsonView displayDataTypes={false} src={parseDetail(detail) || {}} />
          </div>
        </Suspense>
      </DialogContent>
    </Dialog>
  );
}
