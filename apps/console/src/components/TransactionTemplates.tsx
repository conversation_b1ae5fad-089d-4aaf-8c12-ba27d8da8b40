'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';

// import { useMultisigActions } from '@/hooks/useMultisigActions';
import { useMultisigAddress } from '@/hooks/useMultisigAddress';
import { MultisigManager } from '@/lib/constatns';

import { Card, CardDescription, CardTitle } from './ui/card';
import Tabs from './ui/tabs';

export default function TransactionTemplates() {
  const [active, setActive] = useState<string | number>('clmm');
  const templateTabs = [
    { label: 'CLMM合约管理', key: 'clmm', content: <ClmmMenuList /> },
    { label: 'Launchpad合约管理', key: 'launchpad', content: <LaunchpadMenuList /> },
  ];
  return <Tabs tabs={templateTabs} className="mt-4" active={active} onChange={setActive} />;
}
const ClmmMenuList = () => {
  const router = useRouter();
  // const { createMultisig } = useMultisigActions();
  const { multisigAddress } = useMultisigAddress();
  const clmm_menus = [
    // {
    //   title: 'create_multisig',
    //   key: 'create_multisig',
    //   desc: 'Only use in test env',
    //   onClick: async () => await createMultisig(),
    // },
    {
      title: 'init_admin_group',
      key: 'init_amm_config',
      desc: 'Initialize the AMM admin group account',
      onClick: () => {
        router.push('/transactions-build/init_admin_group');
      },
    },
    {
      title: 'update_admin_group',
      key: 'update_admin_group',
      desc: 'Update the AMM admin group account',
      onClick: () => {
        router.push('/transactions-build/update_admin_group');
      },
    },
    {
      title: 'create_pool',
      key: 'create_pool',
      desc: 'Create a pool',
      onClick: () => {
        router.push('/transactions-build/create_pool');
      },
    },
    {
      title: 'deposit_offchain_reward',
      key: 'deposit_offchain_reward',
      desc: 'Account deposit offchain reward',
      onClick: () => {
        router.push('/transactions-build/deposit_offchain_reward');
      },
    },
    {
      title: 'withdraw_offchain_reward',
      key: 'withdraw_offchain_reward',
      desc: 'Account withdraw offchain reward',
      onClick: () => {
        router.push('/transactions-build/withdraw_offchain_reward');
      },
    },
    {
      title: 'update_pool',
      key: 'update_pool',
      desc: 'Update a pool',
      onClick: () => {
        router.push('/transactions-build/update_pool');
      },
    },
    {
      title: 'create_position',
      key: 'create_position',
      desc: 'Create amm position',
      onClick: () => {
        router.push('/transactions-build/create_position');
      },
    },
    // {
    //   title: 'update_amm_config',
    //   key: 'update_amm_config',
    //   desc: 'Update the AMM config',
    //   onClick: () => {
    //     router.push('/transactions-build/update_amm_config');
    //   },
    // },
  ];
  return (
    <div className="flex flex-col gap-4">
      {clmm_menus.map((cl) => (
        <Card
          key={cl.key}
          className="px-4 py-2 cursor-pointer"
          onClick={cl.onClick}
          disabled={MultisigManager?.[cl.key]?.multisigPda.toBase58() !== multisigAddress}
        >
          <CardTitle className="text-lg">{cl.title}</CardTitle>
          <CardDescription className="text-[12px]">{cl.desc}</CardDescription>
        </Card>
      ))}
    </div>
  );
};

const LaunchpadMenuList = () => {
  const router = useRouter();
  const { multisigAddress } = useMultisigAddress();
  const launchpad_menus = [
    {
      title: 'init_acution',
      key: 'init_acution',
      desc: 'Initialize the acution contract',
      onClick: () => {
        router.push('/transactions-build/init_acution');
      },
    },
    {
      title: 'withdraw_funds',
      key: 'withdraw_funds',
      desc: 'Withdraw funds',
      onClick: () => {
        router.push('/transactions-build/withdraw_funds');
      },
    },
    {
      title: 'collect_all_fees',
      key: 'collect_all_fees',
      desc: 'Collect all fees',
      onClick: () => {
        router.push('/transactions-build/collect_all_fees');
      },
    },
    {
      title: 'update_price',
      key: 'update_price',
      desc: 'Update price',
      onClick: () => {
        router.push('/transactions-build/update_price');
      },
    },
  ];
  return (
    <div className="flex flex-col gap-4">
      {launchpad_menus.map((cl) => (
        <Card
          key={cl.key}
          className="px-4 py-2 cursor-pointer"
          onClick={cl.onClick}
          disabled={MultisigManager?.[cl.key]?.multisigPda.toBase58() !== multisigAddress}
        >
          <CardTitle className="text-lg">{cl.title}</CardTitle>
          <CardDescription className="text-[12px]">{cl.desc}</CardDescription>
        </Card>
      ))}
    </div>
  );
};
