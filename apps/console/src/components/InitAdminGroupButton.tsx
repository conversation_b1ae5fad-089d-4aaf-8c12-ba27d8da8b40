'use client';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function InitAdminGroupButton({ config }: { config: { args: any } }) {
  const [open, setOpen] = useState(false);

  const router = useRouter();

  const { initAdminGroup } = useMultisigActions();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  return (
    <InstructionDialog
      title="init_admin_group"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(initAdminGroup(config?.args), {
          id: 'init_admin_group',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
