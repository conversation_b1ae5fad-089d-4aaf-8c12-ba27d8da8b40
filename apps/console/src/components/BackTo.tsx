'use client';

import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

type BackToProps = {
  href: string;
  children?: React.ReactNode;
  rightExtra?: React.ReactNode;
};

export default function BackTo(props: BackToProps) {
  const { href, children, rightExtra } = props;
  return (
    <div className="flex flex-row items-start justify-between">
      {/* <div className=""> */}

      <Link className="text-[20px] flex flex-row items-center gap-1 cursor-pointer" href={href}>
        <ChevronLeft fontSize={20} />
        {children}
      </Link>
      {/* </div> */}
      {rightExtra}
    </div>
  );
}
