import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { DepositOffchainRewardParams, useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function DepositOffchainRewardButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const router = useRouter();

  const { depositOffchainReward } = useMultisigActions();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const depositOffchainRewardParams: DepositOffchainRewardParams = {
    ...config?.args,
    ...config?.accounts,
  };

  return (
    <InstructionDialog
      title="deposit_offchain_reward"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(depositOffchainReward(depositOffchainRewardParams), {
          id: 'deposit_offchain_reward',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
