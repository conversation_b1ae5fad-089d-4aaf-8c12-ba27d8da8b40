'use client';

import React, { Children } from 'react';

import { cn } from '@/lib/utils';

import { Input } from './ui/input';
import Select from './ui/select';

type InstructionsField = {
  key: string;
  title: string;
  type?: string;
  data?: { label: string; value: string }[];
};

interface InstructionsFiledsResolverProps {
  className?: string;
  fields: InstructionsField[];
  onChange?: (data: any) => void;
  register: any;
  title?: string;
}

const InstructionsFieldsResolver = (props: InstructionsFiledsResolverProps) => {
  const { className, fields, register, title } = props;

  return (
    <div className={cn(className)}>
      {title && <div className="text-xl font-bold text-black">{title}</div>}
      <form className={cn('flex flex-col gap-2', { 'mt-2': title })}>
        {fields.map((field) =>
          Children.toArray(
            <div className="flex flex-col">
              <label className="text-gray-500 mb-2">{field.title}</label>
              {field.type === 'number' && <Input {...register(field.key)} type="number" />}
              {field.type === 'time' && <Input {...register(field.key)} type="datetime-local" step={1} />}
              {field.type === 'select' && (
                <Select {...register(field.key)}>
                  {field.data?.map((item) => Children.toArray(<option value={item.value}>{item.label}</option>))}
                </Select>
              )}
              {!field.type && <Input {...register(field.key)} />}
            </div>
          )
        )}
      </form>
    </div>
  );
};
export default InstructionsFieldsResolver;
