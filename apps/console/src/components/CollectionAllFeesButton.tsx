import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { CollectAllFeesParams, useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function CollectionAllFeesButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const { collectAllFees } = useMultisigActions();

  const router = useRouter();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const collectAllFeesParams: CollectAllFeesParams = {
    ...config?.args,
    ...config?.accounts,
  };

  return (
    <InstructionDialog
      title="withdraw_funds"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(collectAllFees(collectAllFeesParams), {
          id: 'collect_all_fees',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
