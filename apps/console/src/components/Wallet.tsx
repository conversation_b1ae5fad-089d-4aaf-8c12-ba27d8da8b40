'use client';

// import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
// import { clusterApiUrl } from '@solana/web3.js';
import React, { FC, useMemo } from 'react';

import '@solana/wallet-adapter-react-ui/styles.css';
import { default_rpc } from '@/lib/constatns';

type Props = {
  children?: React.ReactNode;
};

export const Wallet: FC<Props> = ({ children }) => {
  // const network = WalletAdapterNetwork.Devnet;
  // const network = WalletAdapterNetwork.Mainnet;
  // const { rpcUrl } = useRpcUrl();
  // const endpoint = rpcUrl;

  // const endpoint = useMemo(() => clusterApiUrl(network), [network]);
  const endpoint = default_rpc;

  const wallets = useMemo(() => [], []);

  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>{children}</WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
};
