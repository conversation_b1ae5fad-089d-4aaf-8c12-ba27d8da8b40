'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { CreatePositionParams, useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function CreatePositionButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const { createPosition } = useMultisigActions();

  const router = useRouter();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const createPositionParams: CreatePositionParams = {
    ...config?.args,
    ...config?.accounts,
  };

  return (
    <InstructionDialog
      title="create_pool"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(createPosition(createPositionParams), {
          id: 'create_pool',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
