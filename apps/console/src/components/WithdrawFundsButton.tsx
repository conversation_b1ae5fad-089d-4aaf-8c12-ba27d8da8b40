import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { useMultisigActions, WithdrawFundsParams } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function WithdrawOffchainRewardButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const { withdrawFunds } = useMultisigActions();

  const router = useRouter();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const withdrawFundsParams: WithdrawFundsParams = {
    ...config?.args,
    ...config?.accounts,
  };

  return (
    <InstructionDialog
      title="withdraw_funds"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(withdrawFunds(withdrawFundsParams), {
          id: 'withdraw_funds',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
