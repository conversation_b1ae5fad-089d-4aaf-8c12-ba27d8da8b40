import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { useMultisigActions, WithdrawOffchainRewardParams } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function WithdrawOffchainRewardButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const { withdrawOffchainReward } = useMultisigActions();

  const router = useRouter();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const withdrawOffchainRewardParams: WithdrawOffchainRewardParams = {
    ...config?.args,
    ...config?.accounts,
  };

  return (
    <InstructionDialog
      title="withdraw_offchain_reward"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(withdrawOffchainReward(withdrawOffchainRewardParams), {
          id: 'withdraw_offchain_reward',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
