'use client';

import * as multisig from '@sqds/multisig';

import { useMultisig } from '@/hooks/useServices';
import { useExplorerUrl, useRpcUrl } from '@/hooks/useSettings';

import ApproveButton from './ApproveButton';
import ExecuteButton from './ExecuteButton';
import ProposalDetailButton from './ProposalDetailButton';
import RejectButton from './RejectButton';
import { TableBody, TableCell, TableRow } from './ui/table';

interface ActionButtonsProps {
  multisigPda: string;
  transactionIndex: number;
  // proposalStatus: string;
  proposal: multisig.generated.Proposal | null;
  programId: string;
}

export default function TransactionTable({
  multisigPda,
  transactions,
  programId,
}: {
  multisigPda: string;
  transactions: {
    transactionPda: string;
    proposal: multisig.generated.Proposal | null;
    index: bigint;
  }[];
  programId?: string;
}) {
  const { rpcUrl } = useRpcUrl();
  const { data: multisigConfig } = useMultisig();
  const { explorerUrl } = useExplorerUrl();

  const CreateSolanaExplorerUrl = (publicKey: string, rpcUrl: string): string => {
    const baseUrl = `${explorerUrl}/address/`;
    const clusterQuery = '?cluster=custom&customUrl=';
    const encodedRpcUrl = encodeURIComponent(rpcUrl);
    return `${baseUrl}${publicKey}${clusterQuery}${encodedRpcUrl}`;
  };

  if (transactions.length === 0) {
    return (
      <TableBody>
        <TableRow>
          <TableCell colSpan={5}>No transactions found.</TableCell>
        </TableRow>
      </TableBody>
    );
  }

  return (
    <TableBody>
      {transactions.map((transaction, index) => {
        const stale =
          (multisigConfig && Number(multisigConfig.staleTransactionIndex) > Number(transaction.index)) || false;
        return (
          <TableRow key={index}>
            <TableCell>{Number(transaction.index)}</TableCell>
            <TableCell className="text-blue-500">
              <a
                target={`_blank`}
                href={CreateSolanaExplorerUrl(transaction.transactionPda, rpcUrl!)}
                rel="noopener noreferrer"
              >
                {transaction.transactionPda}
              </a>
            </TableCell>
            <TableCell>{stale ? '(stale)' : transaction.proposal?.status.__kind || 'None'}</TableCell>
            <TableCell>
              {!stale ? (
                <ActionButtons
                  multisigPda={multisigPda!}
                  transactionIndex={Number(transaction.index)}
                  proposal={transaction.proposal}
                  programId={programId ? programId : multisig.PROGRAM_ID.toBase58()}
                />
              ) : (
                <span>Stale</span>
              )}
            </TableCell>
          </TableRow>
        );
      })}
    </TableBody>
  );
}

function ActionButtons({ multisigPda, transactionIndex, proposal, programId }: ActionButtonsProps) {
  return (
    <div className="flex flex-row items-center">
      <ApproveButton
        multisigPda={multisigPda}
        transactionIndex={transactionIndex}
        proposal={proposal}
        programId={programId}
      />
      <RejectButton
        multisigPda={multisigPda}
        transactionIndex={transactionIndex}
        proposal={proposal}
        programId={programId}
      />
      <ExecuteButton
        multisigPda={multisigPda}
        transactionIndex={transactionIndex}
        proposal={proposal}
        programId={programId}
      />
      <ProposalDetailButton
        multisigPda={multisigPda}
        transactionIndex={transactionIndex}
        proposal={proposal}
        programId={programId}
      />
    </div>
  );
}
