import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { CreatePoolParams, useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function CreatePoolButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const { createPool } = useMultisigActions();

  const router = useRouter();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const createPoolParams: CreatePoolParams = {
    pool_creator: config.accounts.pool_creator,
    pool_manager: config.accounts.pool_manager,
    amm_config: config.accounts.amm_config,
    token_mint_0: config.accounts.token_mint_0,
    token_mint_1: config.accounts.token_mint_1,
    token_program_0: config.accounts.token_program_0,
    token_program_1: config.accounts.token_program_1,
    sqrt_price_x64: config.args.sqrt_price_x64,
    open_time: dayjs(config.args.open_time).unix(),
  };

  return (
    <InstructionDialog
      title="create_pool"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(createPool(createPoolParams), {
          id: 'create_pool',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
