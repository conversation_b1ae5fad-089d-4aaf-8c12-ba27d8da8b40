'use client';

// import JsonView from 'react-json-view';

import { Button } from './ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';

interface InstructionDialogProps {
  config: Record<string, any>;
  open: boolean;
  onOpenChange?: (e: boolean) => void;
  title?: string;
  onClickSubmit?: () => void;
}

export default function InstructionDialog(props: InstructionDialogProps) {
  const { config, title, open, onOpenChange, onClickSubmit } = props;

  const handleSubmitTransaction = () => {
    onClickSubmit?.();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={false}>
      <DialogTrigger
        className={`h-10 rounded-md bg-primary px-4 py-2 text-sm text-primary-foreground hover:bg-primary/90`}
        // disabled={!wallet || !wallet.publicKey}
      >
        Create
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{title || 'Mention'}</DialogTitle>
        </DialogHeader>
        <pre className=" bg-gray-600 text-gray-100 text-sm p-4 rounded-xl overflow-x-auto whitespace-pre font-mono">
          {JSON.stringify({ data: config }, null, 2)}
        </pre>
        {/* <div className="h-[400px] overflow-auto">
          <JsonView displayDataTypes={false} src={{ data: config }} />
        </div> */}
        <div className="flex ml-auto">
          <Button onClick={handleSubmitTransaction}>Submit</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
