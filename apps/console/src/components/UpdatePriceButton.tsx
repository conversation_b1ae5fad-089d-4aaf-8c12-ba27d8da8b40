import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { UpdatePriceParams, useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function UpdatePriceButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const router = useRouter();

  const { updatePrice } = useMultisigActions();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const updatePriceParams: UpdatePriceParams = {
    ...config?.args,
    ...config?.accounts,
  };

  return (
    <InstructionDialog
      title="update_pool"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(updatePrice(updatePriceParams), {
          id: 'update_price',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
