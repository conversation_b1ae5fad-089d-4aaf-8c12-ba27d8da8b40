'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { InitAcutionParams, useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function InitAcutionButton({ config }: { config: { args: any; accounts: any } }) {
  const [open, setOpen] = useState(false);

  const { initAcution } = useMultisigActions();

  const router = useRouter();

  const configFormatter = (config: Record<string, unknown>) => {
    return config;
  };

  const initAcutionParams: InitAcutionParams = {
    ...config?.accounts,
    commit_start_time: config?.args?.commit_start_time,
    commit_end_time: config?.args?.commit_end_time,
    bins: [
      {
        unit_price: config?.args?.bin_unit_price1,
        total_supply: config?.args?.total_supply1,
      },
      {
        unit_price: config?.args?.bin_unit_price2,
        total_supply: config?.args?.total_supply2,
      },
    ],
  };

  return (
    <InstructionDialog
      title="init_acution"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(initAcution(initAcutionParams), {
          id: 'init_acution',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
