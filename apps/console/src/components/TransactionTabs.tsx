'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

import Tabs from './ui/tabs';

export default function TransactionTabs() {
  const [active, setActive] = useState<string | number>('/transactions/list');
  const router = useRouter();
  const pathName = usePathname();

  const tabs = [
    // { label: 'Transactions', key: '/transactions/list' },
    { label: 'CLMM合约管理', key: '/transactions/clmm' },
  ];

  useEffect(() => {
    pathName && setActive(pathName);
  }, [pathName]);

  const handleChangeTabs = (key: string | number) => {
    setActive(key);
    router.push(key as string);
  };

  return <Tabs tabs={tabs} active={active} onChange={handleChangeTabs} />;
}
