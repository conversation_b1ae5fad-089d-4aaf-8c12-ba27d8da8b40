'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { Suspense } from 'react';

import TransactionTable from '@/components/TransactionTable';
import { Pagination, PaginationContent, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Table, TableCaption, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useMultisigData } from '@/hooks/useMultisigData';
import { useMultisig, useTransactions } from '@/hooks/useServices';

const TRANSACTIONS_PER_PAGE = 5;

export default function TransactionsTablePanel() {
  const router = useRouter();
  const pageParam = useSearchParams().get('page');
  let page = pageParam ? parseInt(pageParam, 10) : 1;
  if (page < 1) {
    page = 1;
  }
  const { multisigAddress, programId } = useMultisigData();

  const { data } = useMultisig();

  const totalTransactions = Number(data ? data.transactionIndex : 0);
  const totalPages = Math.ceil(totalTransactions / TRANSACTIONS_PER_PAGE);

  const startIndex = totalTransactions - (page - 1) * TRANSACTIONS_PER_PAGE;
  const endIndex = Math.max(startIndex - TRANSACTIONS_PER_PAGE + 1, 1);

  const { data: latestTransactions } = useTransactions(startIndex, endIndex);

  const transactions = (latestTransactions || []).map((transaction) => {
    return {
      ...transaction,
      transactionPda: transaction.transactionPda[0].toBase58(),
    };
  });
  return (
    <>
      <Suspense fallback={<div>Loading...</div>}>
        <Table>
          <TableCaption>A list of your recent transactions.</TableCaption>
          <TableCaption>
            Page: {page} of {totalPages}
          </TableCaption>

          <TableHeader>
            <TableRow>
              <TableHead>Index</TableHead>
              <TableHead>Transaction Address</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <Suspense>
            <TransactionTable
              multisigPda={multisigAddress!}
              transactions={transactions}
              programId={programId!.toBase58()}
            />
          </Suspense>
        </Table>
      </Suspense>

      <Pagination>
        <PaginationContent>
          {page > 1 && (
            <PaginationPrevious
              onClick={() => router.push(`/transactions?page=${page - 1}`)}
              href={`/transactions?page=${page - 1}`}
            />
          )}
          {page < totalPages && (
            <PaginationNext
              href={`/transactions?page=${page + 1}`}
              onClick={() => router.push(`/transactions?page=${page + 1}`)}
            />
          )}
        </PaginationContent>
      </Pagination>
    </>
  );
}
