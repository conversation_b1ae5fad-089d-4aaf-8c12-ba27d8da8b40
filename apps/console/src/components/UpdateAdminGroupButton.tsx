import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { useMultisigActions } from '@/hooks/useMultisigActions';

import InstructionDialog from './InstructionDialog';

export default function UpdateAdminGroupButton({ config }: { config: { args: any } }) {
  const [open, setOpen] = useState(false);

  const router = useRouter();

  const { updateAdminGroup } = useMultisigActions();

  const configFormatter = (config: any) => {
    return config;
  };

  return (
    <InstructionDialog
      title="update_admin_group"
      open={open}
      onOpenChange={setOpen}
      config={configFormatter(config)}
      onClickSubmit={() =>
        toast.promise(updateAdminGroup(config?.args), {
          id: 'update_admin_group',
          loading: 'Building transaction...',
          success: () => {
            setOpen(false);
            router.push('/transactions');
            return 'Transaction proposed.';
          },
          error: (e) => `Failed to propose: ${e}`,
        })
      }
    />
  );
}
