'use client';

import { ArrowDownUp, LucideHome, Settings, Users, Box, Pickaxe } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { ChangeMultisigFromNav } from './ChangeMultisigFromNav';
import ConnectWallet from './ConnectWalletButton';

export default function TabNav() {
  const path = usePathname();
  const tabs = [
    { name: 'Home', icon: <LucideHome />, route: '/' },
    { name: 'Transactions', icon: <ArrowDownUp />, route: '/transactions' },
    { name: 'Configuration', icon: <Users />, route: '/config' },
    { name: 'Programs', icon: <Box />, route: '/programs' },
    { name: 'Build Transactions', icon: <Pickaxe />, route: '/transactions-build' },
    { name: 'Settings', icon: <Settings />, route: '/settings' },
  ];

  return (
    <>
      <aside
        id="sidebar"
        className="z-40 hidden h-auto md:fixed md:left-0 md:top-0 md:block md:h-screen md:w-3/12 lg:w-3/12"
      >
        <div className="flex h-auto flex-col justify-between overflow-y-auto border-slate-200 bg-slate-200 px-3 py-4 md:h-full md:border-r">
          <div>
            <Link href={'/'}>
              <div className="mb-10 flex items-center rounded-lg px-3 py-2 text-slate-900 dark:text-white">
                <Image src={'/logo.png'} width={150} height={54} alt="logo" priority />
              </div>
            </Link>
            <ul className="space-y-2 text-sm font-medium">
              {tabs.map((tab) => (
                <li key={tab.route}>
                  <Link
                    href={tab.route}
                    className={`flex items-center rounded-lg px-4 py-3 text-slate-900 ${
                      (path!.startsWith(`${tab.route}/`) && tab.route !== '/') || tab.route === path
                        ? 'bg-slate-400'
                        : 'hover:bg-slate-400'
                    }`}
                  >
                    {tab.icon}
                    <span className="ml-3 flex-1 whitespace-nowrap text-base text-black">{tab.name}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <ChangeMultisigFromNav />
            <ConnectWallet />
          </div>
        </div>
      </aside>
    </>
  );
}
