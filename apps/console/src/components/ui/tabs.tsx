'use client';

import { cn } from '@/lib/utils';

interface TabsProps {
  tabs?: TabItem[];
  active?: string | number;
  onChange?: (key: string | number) => void;
  className?: string;
}

type TabItem = {
  key: string | number;
  label: string;
  content?: React.ReactNode;
};

export default function Tabs(props: TabsProps) {
  const { tabs, active, onChange, className } = props;

  const tabContent = (tabs || []).find((tab) => tab.key === active)?.content;

  return (
    <div className={cn('w-full', className)}>
      <div className="flex gap-4">
        {(tabs || []).map((tab) => (
          <button
            key={tab.key}
            onClick={() => onChange?.(tab.key)}
            className={`py-2 font-bold transition-colors ${
              active === tab.key ? 'text-black ' : 'text-gray-400 hover:text-black'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      <div className="pt-2">{tabContent}</div>
    </div>
  );
}
