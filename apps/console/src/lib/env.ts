enum PIPELINE_ENV {
  sbuTest5 = 'sbu-test-5',
  sbuTest7 = 'sbu-test-7',
  testnet = 'Testnet',
  mainnet = 'cht-mainnet',
  preFrontend = 'pre-frontend',
  bybitPerf1 = 'bybit-perf-1',
}

export const isSbuTest5 = process.env.NEXT_PUBLIC_PIPELINE_ENV === PIPELINE_ENV.sbuTest5;
export const isDev = process.env.NODE_ENV === 'development';
export const isMainnet =
  process.env.NEXT_PUBLIC_PIPELINE_ENV === PIPELINE_ENV.mainnet ||
  process.env.NEXT_PUBLIC_PIPELINE_ENV === PIPELINE_ENV.preFrontend;
