import { MultisigUtils } from '@byreal/multisig-sdk';
import { Keypair, PublicKey } from '@solana/web3.js';

import { isDev, isMainnet, isSbuTest5 } from './env';

// 1116SSyXfssHNSdNTp4kPA9xAi4AfcZv7bNrgfpCzdn
export const multisigCreateKey1 = Keypair.fromSecretKey(
  Buffer.from([
    141, 128, 193, 8, 204, 123, 181, 18, 99, 30, 146, 48, 50, 10, 1, 236, 6, 16, 88, 126, 137, 24, 238, 91, 146, 190,
    55, 159, 233, 102, 85, 182, 0, 0, 0, 119, 205, 228, 95, 203, 36, 80, 29, 53, 100, 141, 198, 72, 150, 187, 32, 0, 86,
    82, 133, 13, 251, 158, 18, 234, 23, 82, 80, 1,
  ])
);

// 222SZ4aZDvtfEKJ4oKoTTZZQL8RsyWm8sRoaYeN7A2mV
export const multisigCreateKey2 = Keypair.fromS<PERSON>ret<PERSON>ey(
  Buffer.from([
    88, 22, 103, 181, 216, 134, 18, 90, 87, 154, 7, 229, 237, 68, 149, 57, 133, 162, 216, 11, 0, 96, 112, 78, 223, 25,
    217, 197, 162, 49, 173, 234, 15, 30, 229, 134, 199, 111, 131, 201, 94, 62, 255, 84, 87, 29, 65, 81, 57, 39, 87, 176,
    222, 40, 197, 113, 245, 151, 157, 129, 242, 100, 144, 224,
  ])
);

// 333z87agH6ARScHWjcZADZkK1SEeFxcrTpTs82fphy6Z
export const multisigCreateKey3 = Keypair.fromSecretKey(
  Buffer.from([
    245, 239, 182, 100, 191, 138, 82, 185, 87, 161, 45, 151, 226, 31, 227, 189, 177, 19, 240, 181, 127, 28, 35, 231, 56,
    229, 237, 147, 7, 195, 79, 158, 30, 61, 233, 21, 17, 209, 160, 81, 224, 197, 201, 49, 115, 102, 143, 25, 190, 61,
    115, 92, 177, 175, 151, 15, 126, 247, 142, 6, 73, 160, 52, 210,
  ])
);

const onlineEndpoint = 'https://summer-dry-gadget.solana-mainnet.quiknode.pro/2cd2791930b76d348c55458ab3aeb65a84ee27af';
// 'https://indulgent-capable-rain.solana-mainnet.quiknode.pro/2dcbf77741cee0872100c187b0d2ad9cf880cf43/'
// 'https://dawn-young-wish.solana-mainnet.quiknode.pro/4987c6d97918d4afba03bd419125ddcb62371bc1/
const endpoint = isDev ? 'http://127.0.0.1:8899' : onlineEndpoint;
export const default_rpc = endpoint;

const [multisigPda1] = MultisigUtils.getMultisigPda(multisigCreateKey1.publicKey);
const [multisigPda2] = MultisigUtils.getMultisigPda(multisigCreateKey2.publicKey);
const [multisigPda3] = MultisigUtils.getMultisigPda(multisigCreateKey3.publicKey);

const TEST5_MultisigPda = 'Eh9AmNTqGB4u9qDnr9h5GToUXeH9QeY4fLmEg24UY8Wz';

const Mainnet_Multisig = {
  pda1: 'dPjB4DUfpMLnboHZpnTvgfToG1qjtvDK3CFndJXMeEK',
  pda2: '4FGqsWdoDvjUAM5szUetsiVxgpUJehEqPpAaGRkWMq6E',
  pda3: 'FARHmdCAd88pdaN6nsdMNDC6ZU8VHpJZwVnWxCm4bkGP',
};

export const multisigPdas = isMainnet
  ? [
      { label: `${Mainnet_Multisig.pda1}(升级合约&紧急管理 多签钱包)`, pda: Mainnet_Multisig.pda1 },
      { label: `${Mainnet_Multisig.pda2}(CLMM合约超级管理员和资金管理 多签钱包)`, pda: Mainnet_Multisig.pda2 },
      { label: `${Mainnet_Multisig.pda3}(普通技术管理 多签钱包)`, pda: Mainnet_Multisig.pda3 },
    ]
  : isSbuTest5
  ? [
      { label: `${TEST5_MultisigPda}(升级合约&紧急管理 多签钱包)`, pda: TEST5_MultisigPda },
      { label: `${TEST5_MultisigPda}(CLMM合约超级管理员和资金管理 多签钱包)`, pda: TEST5_MultisigPda },
      { label: `${TEST5_MultisigPda}(普通技术管理 多签钱包)`, pda: TEST5_MultisigPda },
    ]
  : [
      { label: `${multisigPda1.toBase58()}(升级合约&紧急管理 多签钱包)`, pda: multisigPda1.toBase58() },
      { label: `${multisigPda2.toBase58()}(CLMM合约超级管理员和资金管理 多签钱包)`, pda: multisigPda2.toBase58() },
      { label: `${multisigPda3.toBase58()}(普通技术管理 多签钱包)`, pda: multisigPda3.toBase58() },
    ];

export { multisigPda1, multisigPda2, multisigPda3 };

// 主网环境中替换成多签钱包的pda地址 -> new PublicKey(pdaAddress)
const MAINNET_MultisigManager = {
  init_admin_group: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 0,
  },
  update_admin_group: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 0,
  },
  create_pool: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda3),
    vaultIndex: 0,
  },
  deposit_offchain_reward: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 2,
  },
  withdraw_offchain_reward: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 2,
  },
  update_pool: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda1),
    vaultIndex: 1,
  },
  create_position: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 4,
  },
  init_acution: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 3,
  },
  withdraw_funds: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 3,
  },
  collect_all_fees: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 3,
  },
  update_price: {
    multisigPda: new PublicKey(Mainnet_Multisig.pda2),
    vaultIndex: 3,
  },
};

const DEV_MultisigManager = {
  init_admin_group: {
    multisigPda: multisigPda2,
    vaultIndex: 0,
  },
  update_admin_group: {
    multisigPda: multisigPda2,
    vaultIndex: 0,
  },
  create_pool: {
    multisigPda: multisigPda3,
    vaultIndex: 0,
  },
  deposit_offchain_reward: {
    multisigPda: multisigPda2,
    vaultIndex: 2,
  },
  withdraw_offchain_reward: {
    multisigPda: multisigPda2,
    vaultIndex: 2,
  },
  update_pool: {
    multisigPda: multisigPda1,
    vaultIndex: 1,
  },
  create_position: {
    multisigPda: multisigPda2,
    vaultIndex: 4,
  },
  init_acution: {
    multisigPda: multisigPda2,
    vaultIndex: 3,
  },
  withdraw_funds: {
    multisigPda: multisigPda2,
    vaultIndex: 3,
  },
  collect_all_fees: {
    multisigPda: multisigPda2,
    vaultIndex: 3,
  },
  update_price: {
    multisigPda: multisigPda2,
    vaultIndex: 3,
  },
} as Record<string, { multisigPda: PublicKey; vaultIndex: number }>;

const TEST5_DEV_MultisigManager = {
  init_admin_group: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 0,
  },
  update_admin_group: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 0,
  },
  create_pool: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 0,
  },
  deposit_offchain_reward: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 2,
  },
  withdraw_offchain_reward: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 2,
  },
  update_pool: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 1,
  },
  create_position: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 4,
  },
  init_acution: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 3,
  },
  withdraw_funds: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 3,
  },
  collect_all_fees: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 3,
  },
  update_price: {
    multisigPda: new PublicKey(TEST5_MultisigPda),
    vaultIndex: 3,
  },
};

export const MultisigManager = (
  isMainnet ? MAINNET_MultisigManager : isSbuTest5 ? TEST5_DEV_MultisigManager : DEV_MultisigManager
) as Record<string, { multisigPda: PublicKey; vaultIndex: number }>;
