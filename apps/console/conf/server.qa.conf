server {
    listen 80 default_server;
    server_name _;

    root /var/www;

    set $redirect_schema "https";
    if ($host ~* "-test-") {
        set $redirect_schema "http";
    }

    # 启用gzip压缩
    gzip on;
    gzip_vary on;  # 添加 Vary: Accept-Encoding 头
    gzip_proxied any;  # 对所有代理请求启用压缩
    gzip_comp_level 6;  # 压缩级别（1-9），6是性能和压缩率的平衡点
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        application/x-font-ttf
        application/x-font-otf
        application/vnd.ms-fontobject
        image/svg+xml
        # 注意：不要压缩已经压缩过的格式如 jpg/png/woff2
        font/opentype;
    gzip_min_length 1000;  # 小于1KB的文件不压缩

    # healthcheck
    location = /status.html {
        return 200;
    }

    # 处理 /byreal-console 下的静态资源 - 必须在通用 /byreal-console location 之前
    location ~ ^/byreal-console/_next/(.*)$ {
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header X-Content-Type-Options "nosniff";
        alias /var/www/_next/$1;
    }
    
    # 处理 /byreal-console 下的其他静态资源
    location ~ ^/byreal-console/(assets|tradingview)/(.*)$ {
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header X-Content-Type-Options "nosniff";
        alias /var/www/$1/$2;
    }

    # 处理 /byreal-console 路径下的页面请求
    location /byreal-console {
        # 设置默认的安全头
        add_header x-xss-protection "1; mode=block";
        add_header x-frame-options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
        add_header Service-Worker-Allowed "/byreal-console";
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        
        # 使用 alias 并去掉 /byreal-console 前缀来查找文件
        alias /var/www/;
        
        try_files $uri.html $uri/index.html $uri /index.html =404;
    }

    # 处理根路径请求
    location = / {
        return 301 /byreal-console/;
    }
    
    # 处理不带 /byreal-console 前缀的直接访问（内部文件）
    location / {
        internal;
        try_files $uri =404;
    }

    # 404 错误处理
    error_page 404 /404.html;
    location = /404.html {
        internal;
    }
}