map $http_accept $_avif {
    ~image/avif           .avif;
    ~image/webp           .webp;
    default               '';
}

map $http_accept $_webp {
    ~image/webp           .webp;
    default               '';
}

map $http_accept_language $defaultLang {
    default en;
    ~en en;
    ~id id-ID;
    ~th th-TH;
    ~pt-BR pt-BR;
    ~pt-PT pt-PT;
    ~ro-RO ro-RO;
    ~es es-ES;
    ~ja ja-JP;
    ~ru ru-RU;
    ~vi vi-VN;
    ~zh-HK zh-HK;
    ~en-HK en-HK;
    ~zh-CN zh-MY;
    ~zh-MY zh-MY;
    ~zh-TW zh-TW;
    ~fil fil-PH;
    ~kk kk-KZ;
    ~uk uk-UA;
    ~es-MX es-MX;
    ~es-AR es-AR;
    ~ar ar-SA;
    ~nl nl-NL;
    ~pl pl-PL;
    ~en-NL en-NL;
    ~en-TR en-TR;
}

map '$http_host:http_accept_language' $lang {
    default $defaultLang;
    # *.bybit.com.hk:en en-HK;
    # *.bybit.com.hk:zh zh-HK;
    # *.bybit.nl:en en-NL;
    # *.bybit.nl:nl nl-NL;
    # *.bybit-tr.com:en en-TR;
    # *.bybit-tr.com:tr tr-TUR;
}

map $status $cache_header {
    default "max-age=31536000";
    404     "no-store";
}
