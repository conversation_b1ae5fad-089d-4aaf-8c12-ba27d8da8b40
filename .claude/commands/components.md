# Figma to Theme Mapping Guide

## Color Mappings

### Text Colors

| Figma Color    | Hex Value | Theme Token      | CSS Variable       | Description                                                              |
| -------------- | --------- | ---------------- | ------------------ | ------------------------------------------------------------------------ |
| Primary Text   | #3f485a   | `colors.ink.900` | `{colors.ink.900}` | Main text color (semantic token: `text.gray.secondary`)                  |
| Secondary Text | #75829e   | `colors.ink.800` | `{colors.ink.800}` | Secondary text color (semantic token: `text.gray.description`)           |
| Success/Accent | #6deaa3   | -                | -                  | No direct mapping found, closest is `colors.success.700_chart` (#00B098) |

### Color Token Usage in Components

```tsx
// Using semantic tokens (recommended)
<Text color="text.gray.secondary">Primary text</Text> // #3f485a
<Text color="text.gray.description">Secondary text</Text> // #75829e

// Using direct color tokens
<Text color="ink.900">Primary text</Text> // #3f485a
<Text color="ink.800">Secondary text</Text> // #75829e

// For the green accent color, use custom value or define new token
<Text color="#6deaa3">Green accent text</Text>
```

## Typography System

### Font Family

- **Primary Font**: Poppins (configured in layout.tsx)
- **CSS Variable**: `--font-poppins`
- **Usage**: Applied globally via `fontFamily: 'var(--font-poppins)'` in theme config

### Font Sizes & Weights

| Figma Style          | Font Size | Font Weight  | Theme Token                           | Text Style |
| -------------------- | --------- | ------------ | ------------------------------------- | ---------- |
| Poppins Medium 14px  | 14px      | 500 (Medium) | `fontSizes.sm` + `fontWeights.medium` | `t6`       |
| Poppins Regular 12px | 12px      | 400 (Normal) | `fontSizes.xs` + `fontWeights.normal` | `c7`       |

### Typography Usage

```tsx
// Using predefined text styles (recommended)
<Text textStyle="t6">Poppins Medium 14px text</Text>
<Text textStyle="c7">Poppins Regular 12px text</Text>

// Using individual tokens
<Text fontSize="sm" fontWeight="medium">Poppins Medium 14px text</Text>
<Text fontSize="xs" fontWeight="normal">Poppins Regular 12px text</Text>
```

### Available Text Styles

**Headings (Semibold)**

- `h1`: 32px, semibold, line-height 44px
- `h2`: 24px, semibold, line-height 32px
- `h3`: 20px, semibold, line-height 28px
- `h4`: 18px, semibold, line-height 26px
- `h5`: 16px, semibold, line-height 24px
- `h6`: 14px, semibold, line-height 22px

**Text (Medium)**

- `t3`: 20px, medium, line-height 28px
- `t4`: 18px, medium, line-height 26px
- `t5`: 16px, medium, line-height 24px
- `t6`: 14px, medium, line-height 22px ✓ (Matches Figma)
- `t7`: 12px, medium, line-height 18px

**Content (Normal)**

- `c6`: 14px, normal, line-height 22px
- `c7`: 12px, normal, line-height 18px ✓ (Matches Figma)
- `c8`: 10px, normal, line-height 16px

## Spacing System

Chakra UI v3 uses a default spacing scale. Common values:

| Token | Value          | Usage                |
| ----- | -------------- | -------------------- |
| 0     | 0px            | No spacing           |
| 1     | 0.25rem (4px)  | Minimal spacing      |
| 2     | 0.5rem (8px)   | Small spacing        |
| 3     | 0.75rem (12px) | Small-medium spacing |
| 4     | 1rem (16px)    | Medium spacing       |
| 5     | 1.25rem (20px) | Medium-large spacing |
| 6     | 1.5rem (24px)  | Large spacing        |
| 8     | 2rem (32px)    | Extra large spacing  |
| 10    | 2.5rem (40px)  | XXL spacing          |

### Spacing Usage

```tsx
// Using spacing tokens
<Box p={4} mb={2}>Content with padding 16px and margin-bottom 8px</Box>
<Stack gap={3}>Items with 12px gap between</Stack>

// Using raw values
<Box p="16px" mb="8px">Content with specific pixel values</Box>
```

## Implementation Example

```tsx
import { Box, Text, Stack } from '@chakra-ui/react';

// Component using theme tokens
function ExampleComponent() {
  return (
    <Box p={4} bg="backgroundLight">
      <Stack gap={3}>
        <Text textStyle="t6" color="text.gray.secondary">
          Primary label text (14px Medium)
        </Text>
        <Text textStyle="c7" color="text.gray.description">
          Secondary content text (12px Regular)
        </Text>
        <Text fontSize="sm" fontWeight="medium" color="#6deaa3">
          Custom green accent text
        </Text>
      </Stack>
    </Box>
  );
}
```

## Notes

1. **Missing Green Color**: The exact green color `#6deaa3` is not in the current theme. Consider:

   - Adding it as a new token in the theme
   - Using the closest existing green (`colors.success.700_chart`: #00B098)
   - Using it as a hardcoded value where needed

2. **Font Loading**: Poppins font weights 100-900 are loaded, but only 400 (normal), 500 (medium), 600 (semibold), and 700 (bold) are defined in the theme tokens.

3. **Dark/Light Mode**: The theme supports both dark and light modes with different color values. The mappings above are based on the semantic tokens which automatically adjust.

---

## Original Command Template

使用 Chakra UI v3 用法，结合以上主题设置，根据 figma 设计稿中的参数：$ARGUMENTS，开发一个组件。注意：

- 先拆分成任务项，与我确认需求及新组件路径后，再启动开发
- 完成组件后，在 app/[locale]/demo 路径下展示 demo
